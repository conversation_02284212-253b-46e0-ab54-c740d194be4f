-- Database Fix: Convert parameter_type column from string to integer format
-- Usage: Run this script to fix parameter_type column format issues
-- This script handles both existing string values and ensures proper integer format

-- First, check current parameter_type column format
SELECT 
    column_name, 
    data_type, 
    is_nullable 
FROM information_schema.columns 
WHERE table_name = 'parameters' AND column_name = 'parameter_type';

-- Display current parameter_type values
SELECT 
    id,
    parameter_code,
    parameter_type,
    CASE 
        WHEN parameter_type ~ '^[0-9]+$' THEN 'INTEGER_FORMAT'
        ELSE 'STRING_FORMAT'
    END as current_format
FROM parameters
ORDER BY parameter_code;

-- Update string values to integer equivalents
-- This handles the common case where parameter_type is stored as strings
UPDATE parameters 
SET parameter_type = CASE 
    WHEN LOWER(TRIM(parameter_type)) = 'string' THEN '1'
    WHEN LOWER(TRIM(parameter_type)) = 'number' THEN '2'
    WHEN LOWER(TRIM(parameter_type)) = 'decimal' THEN '3'
    WHEN LOWER(TRIM(parameter_type)) = 'date' THEN '4'
    WHEN LOWER(TRIM(parameter_type)) = 'boolean' THEN '5'
    WHEN parameter_type ~ '^[1-5]$' THEN parameter_type  -- Already correct
    ELSE '1'  -- Default to String type for unknown values
END
WHERE parameter_type IS NOT NULL;

-- Set any NULL parameter_type values to String (1)
UPDATE parameters 
SET parameter_type = '1' 
WHERE parameter_type IS NULL;

-- Display results after conversion
SELECT 
    parameter_type,
    CASE parameter_type
        WHEN '1' THEN 'String'
        WHEN '2' THEN 'Number'
        WHEN '3' THEN 'Decimal'
        WHEN '4' THEN 'Date'
        WHEN '5' THEN 'Boolean'
        ELSE 'Unknown'
    END as type_name,
    COUNT(*) as count
FROM parameters 
GROUP BY parameter_type 
ORDER BY parameter_type;

-- Display all parameters with their corrected types
SELECT 
    id,
    parameter_code,
    parameter_value,
    parameter_type,
    CASE parameter_type
        WHEN '1' THEN 'String'
        WHEN '2' THEN 'Number'
        WHEN '3' THEN 'Decimal'
        WHEN '4' THEN 'Date'
        WHEN '5' THEN 'Boolean'
        ELSE 'Unknown'
    END as type_name,
    purpose
FROM parameters 
ORDER BY parameter_code;
