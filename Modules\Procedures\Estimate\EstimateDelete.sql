-- EstimateDelete.sql
-- Delete operations for estimates
-- Simple, focused operations for data removal

-- [DeleteEstimate] --
-- Delete estimate and all its details (cascading delete)
-- Delete details first due to foreign key constraints
DELETE FROM estimatedetails 
WHERE estimate_id = @estimate_id;

DELETE FROM estimateheader 
WHERE id = @estimate_id;
-- [End] --

-- [DeleteDetail] --
-- Delete a specific estimate detail
DELETE FROM estimatedetails 
WHERE id = @detail_id 
AND estimate_id = @estimate_id;
-- [End] --

-- [DeleteAllDetails] --
-- Delete all details for an estimate (keeping header)
DELETE FROM estimatedetails 
WHERE estimate_id = @estimate_id;
-- [End] --
