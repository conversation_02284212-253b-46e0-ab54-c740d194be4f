-- EstimateUtilities.sql
-- Utility queries for estimate operations
-- Specialized functions and helper queries

-- [GetNextEstimateNumber] --
-- Generate next available estimate number in format EST-YY-NNNNN
WITH latest_estimate AS (
    SELECT
        estimate_no AS estimate_number,
        CASE
            WHEN estimate_no ~ '^EST-[0-9]{2}-[0-9]{5}$' THEN
                SPLIT_PART(estimate_no, '-', 2)
            ELSE
                TO_CHAR(CURRENT_DATE, 'YY')
        END AS year_part,
        CASE
            WHEN estimate_no ~ '^EST-[0-9]{2}-[0-9]{5}$' THEN
                CAST(SPLIT_PART(estimate_no, '-', 3) AS INTEGER)
            ELSE
                0
        END AS sequence_part
    FROM
        estimateheader
    WHERE
        estimate_no IS NOT NULL
),
current_year AS (
    SELECT TO_CHAR(CURRENT_DATE, 'YY') AS year_part
),
max_sequence_current_year AS (
    SELECT
        COALESCE(MAX(sequence_part), 0) AS max_seq
    FROM
        latest_estimate
    WHERE
        year_part = (SELECT year_part FROM current_year)
),
next_number AS (
    SELECT
        CASE
            WHEN (SELECT max_seq FROM max_sequence_current_year) > 0 THEN
                (SELECT max_seq FROM max_sequence_current_year) + 1
            ELSE 1
        END AS next_seq
)
SELECT
    'EST-' || (SELECT year_part FROM current_year) || '-' ||
    LPAD((SELECT next_seq FROM next_number)::TEXT, 5, '0') AS next_estimate_number;
-- [End] --

-- [GetEstimateStats] --
-- Get statistics about estimates
SELECT
    COUNT(*) AS total_estimates,
    COUNT(CASE WHEN status = 'Active' THEN 1 END) AS active_estimates,
    COUNT(CASE WHEN status = 'Completed' THEN 1 END) AS completed_estimates,
    COUNT(CASE WHEN status = 'Cancelled' THEN 1 END) AS cancelled_estimates,
    MIN(date) AS earliest_date,
    MAX(date) AS latest_date,
    COUNT(CASE WHEN date >= CURRENT_DATE - INTERVAL '30 days' THEN 1 END) AS recent_estimates
FROM
    estimateheader
WHERE
    estimate_no IS NOT NULL;
-- [End] --

-- [GetNextDetailId] --
-- Get next available detail ID
SELECT COALESCE(MAX(id), 0) + 1 AS next_detail_id
FROM estimatedetails;
-- [End] --

-- [ValidateEstimateNumber] --
-- Check if estimate number already exists
SELECT
    CASE
        WHEN COUNT(*) > 0 THEN 'EXISTS'
        ELSE 'AVAILABLE'
    END AS status,
    COUNT(*) AS count
FROM
    estimateheader
WHERE
    UPPER(TRIM(estimate_no)) = UPPER(TRIM(@estimate_number));
-- [End] --

-- [UpdateEstimateStatus] --
-- Update only the status field of an estimate
UPDATE estimateheader
SET
    status = @status,
    modify_at = CURRENT_TIMESTAMP
WHERE
    id = @estimate_id;
-- [End] --
