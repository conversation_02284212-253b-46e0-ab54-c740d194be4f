using System;
using System.Collections.Generic;
using System.Diagnostics;
using ProManage.Modules.Services;
using ProManage.Modules.Connections;
using ProManage.Modules.Models.PermissionManagementForm;
using Npgsql;

namespace ProManage.Tests
{
    /// <summary>
    /// Simple test runner to verify our permission system fixes
    /// </summary>
    public class TestRunner
    {
        private const int TestUserId = 999;
        private const string TestFormName = "TestForm";

        public static void RunTests()
        {
            var logFile = "test-results.txt";

            try
            {
                using (var writer = new System.IO.StreamWriter(logFile, false))
                {
                    WriteLog(writer, "Starting Permission System Test Runner...");

                    // Test 0: Database Schema Setup
                    WriteLog(writer, "\n=== Test 0: Database Schema Setup ===");
                    TestDatabaseSchemaSetup(writer);

                    // Test 1: Apply Schema Fixes
                    WriteLog(writer, "\n=== Test 1: Apply Schema Fixes ===");
                    TestApplySchemaFixes(writer);

                    // Test 2: Database Schema Column Fix
                    WriteLog(writer, "\n=== Test 2: Database Schema Column Fix ===");
                    TestDatabaseSchemaFix(writer);

                    // Test 3: Test User Creation
                    WriteLog(writer, "\n=== Test 3: Test User Creation ===");
                    TestUserCreation(writer);

                    // Test 4: Role Creation with Unique Names
                    WriteLog(writer, "\n=== Test 4: Role Creation with Unique Names ===");
                    TestRoleCreation(writer);

                    // Test 5: Exception Type Handling
                    WriteLog(writer, "\n=== Test 5: Exception Type Handling ===");
                    TestExceptionHandling(writer);

                    WriteLog(writer, "\n=== All Tests Completed ===");
                    WriteLog(writer, $"Results saved to: {System.IO.Path.GetFullPath(logFile)}");
                }
            }
            catch (Exception ex)
            {
                var errorMsg = $"Test Runner Error: {ex.Message}\nStack Trace: {ex.StackTrace}";
                Console.WriteLine(errorMsg);
                System.IO.File.WriteAllText("test-error.txt", errorMsg);
            }
        }

        private static void WriteLog(System.IO.StreamWriter writer, string message)
        {
            Console.WriteLine(message);
            writer.WriteLine(message);
            writer.Flush();
        }

        private static void TestDatabaseSchemaSetup(System.IO.StreamWriter writer)
        {
            try
            {
                using (var connection = DatabaseConnectionManager.Instance.CreateNewConnection())
                {
                    connection.Open();

                    // Check if permission tables exist
                    const string checkTablesQuery = @"
                        SELECT table_name
                        FROM information_schema.tables
                        WHERE table_schema = 'public'
                        AND table_name IN ('user_permissions', 'global_permissions', 'role_permissions', 'roles')";

                    using (var command = new NpgsqlCommand(checkTablesQuery, connection))
                    {
                        using (var reader = command.ExecuteReader())
                        {
                            var existingTables = new List<string>();
                            while (reader.Read())
                            {
                                existingTables.Add(reader.GetString(0));
                            }

                            WriteLog(writer, $"✓ Found existing tables: {string.Join(", ", existingTables)}");

                            if (existingTables.Count < 4)
                            {
                                WriteLog(writer, "⚠ Some permission tables are missing. You may need to run the RBAC schema setup.");
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                WriteLog(writer, $"✗ Database schema setup check failed: {ex.Message}");
            }
        }

        private static void TestApplySchemaFixes(System.IO.StreamWriter writer)
        {
            try
            {
                // Apply schema fixes
                if (RBACDatabaseSetup.SetupCompleteRBACSystem(out string errorMessage))
                {
                    WriteLog(writer, "✓ Schema fixes applied successfully");
                }
                else
                {
                    WriteLog(writer, $"✗ Schema fixes failed: {errorMessage}");
                }
            }
            catch (Exception ex)
            {
                WriteLog(writer, $"✗ Schema fixes test failed: {ex.Message}");
            }
        }

        private static void TestDatabaseSchemaFix(System.IO.StreamWriter writer)
        {
            try
            {
                // Test if we can query user permissions without column errors
                var permissions = PermissionDatabaseService.GetUserPermissions(TestUserId);
                WriteLog(writer, $"✓ Successfully queried user permissions (found {permissions.Count} records)");

                // Test if we can query global permissions without column errors
                var globalPermissions = PermissionDatabaseService.GetGlobalPermissions(TestUserId);
                WriteLog(writer, $"✓ Successfully queried global permissions");
            }
            catch (Exception ex)
            {
                WriteLog(writer, $"✗ Database schema test failed: {ex.Message}");
            }
        }

        private static void TestUserCreation(System.IO.StreamWriter writer)
        {
            try
            {
                using (var connection = DatabaseConnectionManager.Instance.CreateNewConnection())
                {
                    connection.Open();

                    // Check if test user exists
                    const string checkQuery = "SELECT COUNT(*) FROM users WHERE user_id = @userId";
                    using (var command = new NpgsqlCommand(checkQuery, connection))
                    {
                        command.Parameters.AddWithValue("@userId", TestUserId);
                        var count = Convert.ToInt32(command.ExecuteScalar());

                        if (count == 0)
                        {
                            // Create test user with password_salt
                            const string insertQuery = @"
                                INSERT INTO users (user_id, username, full_name, email, password_hash, password_salt, is_active, created_date)
                                VALUES (@userId, @username, @fullName, @email, @passwordHash, @passwordSalt, @isActive, @createdDate)";

                            using (var insertCommand = new NpgsqlCommand(insertQuery, connection))
                            {
                                insertCommand.Parameters.AddWithValue("@userId", TestUserId);
                                insertCommand.Parameters.AddWithValue("@username", "testuser999");
                                insertCommand.Parameters.AddWithValue("@fullName", "Test User 999");
                                insertCommand.Parameters.AddWithValue("@email", "<EMAIL>");
                                insertCommand.Parameters.AddWithValue("@passwordHash", "test_password_hash");
                                insertCommand.Parameters.AddWithValue("@passwordSalt", "test_salt");
                                insertCommand.Parameters.AddWithValue("@isActive", true);
                                insertCommand.Parameters.AddWithValue("@createdDate", DateTime.Now);

                                insertCommand.ExecuteNonQuery();
                                WriteLog(writer, "✓ Test user created successfully");
                            }
                        }
                        else
                        {
                            WriteLog(writer, "✓ Test user already exists");
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                WriteLog(writer, $"✗ Test user creation failed: {ex.Message}");
            }
        }

        private static void TestRoleCreation(System.IO.StreamWriter writer)
        {
            try
            {
                // Test creating role with unique name
                var roleRequest = new RoleCreateRequest
                {
                    RoleName = $"TestRole_{Guid.NewGuid().ToString("N").Substring(0, 8)}",
                    Description = "Test role for validation",
                    IsActive = true
                };

                var roleId = PermissionDatabaseService.CreateRole(roleRequest);
                if (roleId > 0)
                {
                    WriteLog(writer, $"✓ Role created successfully with ID: {roleId}");
                }
                else
                {
                    WriteLog(writer, "✗ Role creation returned invalid ID");
                }
            }
            catch (Exception ex)
            {
                WriteLog(writer, $"✗ Role creation test failed: {ex.Message}");
            }
        }

        private static void TestExceptionHandling(System.IO.StreamWriter writer)
        {
            try
            {
                // Test duplicate role creation to verify exception type
                var roleName = $"DuplicateTest_{Guid.NewGuid().ToString("N").Substring(0, 8)}";
                var roleRequest1 = new RoleCreateRequest { RoleName = roleName, Description = "First role" };
                var roleRequest2 = new RoleCreateRequest { RoleName = roleName, Description = "Duplicate role" };

                // Create first role
                PermissionDatabaseService.CreateRole(roleRequest1);
                WriteLog(writer, "✓ First role created successfully");

                // Try to create duplicate - should throw PostgresException
                try
                {
                    PermissionDatabaseService.CreateRole(roleRequest2);
                    WriteLog(writer, "✗ Duplicate role creation should have thrown exception");
                }
                catch (PostgresException)
                {
                    WriteLog(writer, "✓ PostgresException thrown correctly for duplicate role");
                }
                catch (Exception ex)
                {
                    WriteLog(writer, $"✗ Wrong exception type: {ex.GetType().Name} - {ex.Message}");
                }
            }
            catch (Exception ex)
            {
                WriteLog(writer, $"✗ Exception handling test failed: {ex.Message}");
            }
        }
    }
}
