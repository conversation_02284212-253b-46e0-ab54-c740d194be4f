// ParameterCacheModel - Data model for parameter cache serialization
// Usage: Represents cached parameter data for JSON serialization and persistence

using System;
using System.Collections.Generic;
using System.Text.Json.Serialization;

namespace ProManage.Modules.Services
{
    /// <summary>
    /// Model representing cached parameter data for JSON serialization
    /// Used for persisting parameter cache to file and loading from file
    /// </summary>
    public class ParameterCacheModel
    {
        /// <summary>
        /// Dictionary containing parameter code-value pairs
        /// Key: Parameter code (e.g., "CURRENCY")
        /// Value: Parameter value (e.g., "USD")
        /// </summary>
        [JsonPropertyName("parameters")]
        public Dictionary<string, string> Parameters { get; set; }

        /// <summary>
        /// Timestamp when the cache was last updated from database
        /// Used to determine cache freshness and validity
        /// </summary>
        [JsonPropertyName("lastUpdated")]
        public DateTime LastUpdated { get; set; }

        /// <summary>
        /// Number of parameters in the cache
        /// Used for validation and monitoring
        /// </summary>
        [JsonPropertyName("parameterCount")]
        public int ParameterCount { get; set; }

        /// <summary>
        /// Version of the cache format for future compatibility
        /// </summary>
        [JsonPropertyName("cacheVersion")]
        public string CacheVersion { get; set; }

        /// <summary>
        /// Default constructor for JSON deserialization
        /// </summary>
        public ParameterCacheModel()
        {
            Parameters = new Dictionary<string, string>();
            LastUpdated = DateTime.Now;
            ParameterCount = 0;
            CacheVersion = "1.0";
        }

        /// <summary>
        /// Constructor with parameter dictionary
        /// </summary>
        /// <param name="parameters">Dictionary of parameter code-value pairs</param>
        public ParameterCacheModel(Dictionary<string, string> parameters)
        {
            Parameters = parameters ?? new Dictionary<string, string>();
            LastUpdated = DateTime.Now;
            ParameterCount = Parameters.Count;
            CacheVersion = "1.0";
        }

        /// <summary>
        /// Validates the cache model data
        /// </summary>
        /// <returns>True if valid, false otherwise</returns>
        public bool IsValid()
        {
            try
            {
                // Check if parameters dictionary is not null
                if (Parameters == null)
                    return false;

                // Check if parameter count matches actual count
                if (ParameterCount != Parameters.Count)
                    return false;

                // Check if last updated is reasonable (not in future, not too old)
                if (LastUpdated > DateTime.Now.AddMinutes(5))
                    return false;

                // Check if last updated is not older than 30 days (reasonable cache age)
                if (LastUpdated < DateTime.Now.AddDays(-30))
                    return false;

                // Check cache version is supported
                if (string.IsNullOrWhiteSpace(CacheVersion))
                    return false;

                return true;
            }
            catch
            {
                return false;
            }
        }

        /// <summary>
        /// Updates the parameter count to match the actual dictionary count
        /// Call this after modifying the Parameters dictionary
        /// </summary>
        public void UpdateParameterCount()
        {
            ParameterCount = Parameters?.Count ?? 0;
        }

        /// <summary>
        /// Checks if the cache is stale based on age
        /// </summary>
        /// <param name="maxAgeMinutes">Maximum age in minutes before cache is considered stale</param>
        /// <returns>True if cache is stale, false otherwise</returns>
        public bool IsStale(int maxAgeMinutes = 60)
        {
            return DateTime.Now.Subtract(LastUpdated).TotalMinutes > maxAgeMinutes;
        }

        /// <summary>
        /// Gets a parameter value by code
        /// </summary>
        /// <param name="parameterCode">The parameter code to look up</param>
        /// <returns>Parameter value if found, null otherwise</returns>
        public string GetParameter(string parameterCode)
        {
            if (Parameters == null || string.IsNullOrWhiteSpace(parameterCode))
                return null;

            Parameters.TryGetValue(parameterCode.ToUpper(), out string value);
            return value;
        }

        /// <summary>
        /// Checks if a parameter exists in the cache
        /// </summary>
        /// <param name="parameterCode">The parameter code to check</param>
        /// <returns>True if parameter exists, false otherwise</returns>
        public bool HasParameter(string parameterCode)
        {
            if (Parameters == null || string.IsNullOrWhiteSpace(parameterCode))
                return false;

            return Parameters.ContainsKey(parameterCode.ToUpper());
        }

        /// <summary>
        /// Adds or updates a parameter in the cache
        /// </summary>
        /// <param name="parameterCode">The parameter code</param>
        /// <param name="parameterValue">The parameter value</param>
        public void SetParameter(string parameterCode, string parameterValue)
        {
            if (Parameters == null)
                Parameters = new Dictionary<string, string>();

            if (string.IsNullOrWhiteSpace(parameterCode))
                return;

            Parameters[parameterCode.ToUpper()] = parameterValue ?? string.Empty;
            UpdateParameterCount();
        }

        /// <summary>
        /// Removes a parameter from the cache
        /// </summary>
        /// <param name="parameterCode">The parameter code to remove</param>
        /// <returns>True if parameter was removed, false if it didn't exist</returns>
        public bool RemoveParameter(string parameterCode)
        {
            if (Parameters == null || string.IsNullOrWhiteSpace(parameterCode))
                return false;

            bool removed = Parameters.Remove(parameterCode.ToUpper());
            if (removed)
                UpdateParameterCount();

            return removed;
        }

        /// <summary>
        /// Clears all parameters from the cache
        /// </summary>
        public void Clear()
        {
            Parameters?.Clear();
            UpdateParameterCount();
        }

        /// <summary>
        /// Returns a string representation of the cache model
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"ParameterCache: {ParameterCount} parameters, Last Updated: {LastUpdated:yyyy-MM-dd HH:mm:ss}";
        }
    }
}
