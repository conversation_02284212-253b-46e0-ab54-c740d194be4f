using System;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Windows.Forms;
using ProManage.Modules.Models.ParametersForm;
using ProManage.Modules.Data.ParametersForm;

namespace ProManage.Forms
{
    /// <summary>
    /// Parameter Entry Form - Popup dialog for adding new parameters or editing existing ones
    /// </summary>
    public partial class ParamEntryForm : Form
    {
        /// <summary>
        /// Gets the newly created parameter ID after successful save
        /// </summary>
        public int NewParameterId { get; private set; }

        /// <summary>
        /// Gets or sets the parameter being edited (null for new parameters)
        /// </summary>
        private ParametersFormModel EditingParameter { get; set; }

        /// <summary>
        /// Gets whether this form is in edit mode
        /// </summary>
        public bool IsEditMode => EditingParameter != null;

        public ParamEntryForm()
        {
            InitializeComponent();
            SetupForm();
            SetupParameterTypeComboBox();
            SetupTypeSpecificControls();
            SetupValidationEvents();
        }

        /// <summary>
        /// Sets up form properties and behavior
        /// </summary>
        private void SetupForm()
        {
            try
            {
                // Form properties
                this.FormBorderStyle = FormBorderStyle.FixedDialog;
                this.MaximizeBox = false;
                this.MinimizeBox = false;
                this.ShowInTaskbar = false;
                this.StartPosition = FormStartPosition.CenterParent;
                this.Text = "Add New Parameter";
                this.Size = new Size(400, 230);

                // Set tab order
                txtParameterCode.TabIndex = 0;
                comboBoxEdit1.TabIndex = 1;
                pnlValueContainer.TabIndex = 2;
                txtPurpose.TabIndex = 3;
                btnSave.TabIndex = 4;
                btnCancel.TabIndex = 5;

                // Set focus to first field
                this.ActiveControl = txtParameterCode;

                Debug.WriteLine("ParamEntryForm setup completed");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in SetupForm: {ex.Message}");
                MessageBox.Show($"Error setting up form: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Sets up the parameter type combo box with available parameter types
        /// </summary>
        private void SetupParameterTypeComboBox()
        {
            try
            {
                Debug.WriteLine("Setting up parameter type combo box");

                // Clear existing items
                comboBoxEdit1.Properties.Items.Clear();

                // Get all parameter types and add them to the combo box
                var parameterTypes = ParameterTypeHelper.GetAllTypes();
                foreach (var type in parameterTypes)
                {
                    comboBoxEdit1.Properties.Items.Add(new ParameterTypeItem(type.Key, type.Value));
                }

                // Set default selection to String
                if (comboBoxEdit1.Properties.Items.Count > 0)
                {
                    comboBoxEdit1.SelectedIndex = 0; // String is first (index 0)
                }

                // DevExpress ComboBoxEdit doesn't use DisplayMember/ValueMember like standard ComboBox
                // Items are already added as ParameterTypeItem objects, so we can access them directly

                // Add event handler for selection change
                comboBoxEdit1.SelectedIndexChanged += ComboBoxEdit1_SelectedIndexChanged;

                Debug.WriteLine($"Parameter type combo box setup completed with {comboBoxEdit1.Properties.Items.Count} items");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error setting up parameter type combo box: {ex.Message}");
                MessageBox.Show($"Error setting up parameter types: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Sets up type-specific controls and their initial state
        /// </summary>
        private void SetupTypeSpecificControls()
        {
            try
            {
                // Setup Boolean ComboBox items
                cmbBooleanValue.Properties.Items.Clear();
                cmbBooleanValue.Properties.Items.Add("True");
                cmbBooleanValue.Properties.Items.Add("False");
                
                // Setup Date ComboBox with format patterns only
                cmbDateValue.Properties.Items.Clear();
                // Add only format patterns for user guidance
                cmbDateValue.Properties.Items.Add("DD-MM-YYYY");
                cmbDateValue.Properties.Items.Add("DD-MMM-YYYY");
                cmbDateValue.Properties.Items.Add("DD-MM-YY");
                cmbDateValue.Properties.Items.Add("MM/DD/YYYY");
                cmbDateValue.Properties.Items.Add("YYYY-MM-DD");
                cmbDateValue.Properties.Items.Add("MMM DD, YYYY");
                
                // Allow users to type their own dates as well
                cmbDateValue.Properties.TextEditStyle = DevExpress.XtraEditors.Controls.TextEditStyles.Standard;

                // Hide all value controls initially
                HideAllValueControls();

                // Show String control by default (will be changed when type is selected)
                ShowControlForType(ParameterType.String);

                Debug.WriteLine("Type-specific controls setup completed");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error setting up type-specific controls: {ex.Message}");
            }
        }

        /// <summary>
        /// Sets up validation events for real-time feedback
        /// </summary>
        private void SetupValidationEvents()
        {
            try
            {
                // String value events
                txtStringValue.TextChanged += (s, e) => ValidateCurrentInput();
                txtStringValue.KeyPress += TxtStringValue_KeyPress;

                // Number value events
                txtNumberValue.TextChanged += (s, e) => ValidateCurrentInput();
                txtNumberValue.KeyPress += TxtNumberValue_KeyPress;

                // Decimal value events
                txtDecimalValue.TextChanged += (s, e) => ValidateCurrentInput();
                txtDecimalValue.KeyPress += TxtDecimalValue_KeyPress;

                // Date value events
                cmbDateValue.SelectedIndexChanged += CmbDateValue_SelectedIndexChanged;
                cmbDateValue.EditValueChanged += (s, e) => ValidateCurrentInput();

                // Boolean value events
                cmbBooleanValue.SelectedIndexChanged += (s, e) => ValidateCurrentInput();

                // Object value events
                btnBrowseObject.Click += BtnBrowseObject_Click;

                Debug.WriteLine("Validation events setup completed");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error setting up validation events: {ex.Message}");
            }
        }

        /// <summary>
        /// Hides all value input controls
        /// </summary>
        private void HideAllValueControls()
        {
            txtStringValue.Visible = false;
            txtNumberValue.Visible = false;
            txtDecimalValue.Visible = false;
            cmbDateValue.Visible = false;
            cmbBooleanValue.Visible = false;
            txtObjectValue.Visible = false;
            btnBrowseObject.Visible = false;
        }

        /// <summary>
        /// Shows the appropriate control for the specified parameter type
        /// </summary>
        /// <param name="parameterType">The parameter type</param>
        private void ShowControlForType(ParameterType parameterType)
        {
            try
            {
                // Hide all controls first
                HideAllValueControls();

                // Show the appropriate control
                switch (parameterType)
                {
                    case ParameterType.String:
                        txtStringValue.Visible = true;
                        txtStringValue.Enabled = true;
                        txtStringValue.ReadOnly = false;
                        txtStringValue.Focus();
                        Debug.WriteLine("String control activated - Visible: " + txtStringValue.Visible + ", Enabled: " + txtStringValue.Enabled + ", ReadOnly: " + txtStringValue.ReadOnly);
                        break;
                    case ParameterType.Number:
                        txtNumberValue.Visible = true;
                        txtNumberValue.Enabled = true;
                        txtNumberValue.ReadOnly = false;
                        txtNumberValue.Focus();
                        Debug.WriteLine("Number control activated - Visible: " + txtNumberValue.Visible + ", Enabled: " + txtNumberValue.Enabled + ", ReadOnly: " + txtNumberValue.ReadOnly);
                        break;
                    case ParameterType.Decimal:
                        txtDecimalValue.Visible = true;
                        txtDecimalValue.Enabled = true;
                        txtDecimalValue.ReadOnly = false;
                        txtDecimalValue.Focus();
                        Debug.WriteLine("Decimal control activated - Visible: " + txtDecimalValue.Visible + ", Enabled: " + txtDecimalValue.Enabled + ", ReadOnly: " + txtDecimalValue.ReadOnly);
                        break;
                    case ParameterType.Date:
                        cmbDateValue.Visible = true;
                        cmbDateValue.Focus();
                        // Set helpful message for date input
                        SetValidationStatus("Enter a date, date format, or select from dropdown", Color.Blue);
                        break;
                    case ParameterType.Boolean:
                        cmbBooleanValue.Visible = true;
                        cmbBooleanValue.Focus();
                        break;
                    case ParameterType.Object:
                        txtObjectValue.Visible = true;
                        btnBrowseObject.Visible = true;
                        btnBrowseObject.Focus();
                        break;
                }

                // Clear validation status when switching types (except for Date which has helpful message)
                if (parameterType != ParameterType.Date)
                {
                    SetValidationStatus("", Color.Gray);
                }

                Debug.WriteLine($"Switched to control for type: {parameterType}");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error showing control for type {parameterType}: {ex.Message}");
            }
        }

        /// <summary>
        /// Gets the current parameter value from the active control
        /// </summary>
        /// <returns>The current parameter value as string</returns>
        private string GetCurrentValue()
        {
            try
            {
                if (comboBoxEdit1.SelectedItem is ParameterTypeItem selectedItem)
                {
                    switch (selectedItem.ParameterType)
                    {
                        case ParameterType.String:
                            return txtStringValue.Text.Trim();
                        case ParameterType.Number:
                            return txtNumberValue.Text.Trim();
                        case ParameterType.Decimal:
                            return txtDecimalValue.Text.Trim();
                        case ParameterType.Date:
                            return cmbDateValue.Text.Trim();
                        case ParameterType.Boolean:
                            return cmbBooleanValue.Text;
                        case ParameterType.Object:
                            return txtObjectValue.Text.Trim();
                        default:
                            return "";
                    }
                }
                return "";
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error getting current value: {ex.Message}");
                return "";
            }
        }

        /// <summary>
        /// Sets the current parameter value in the active control
        /// </summary>
        /// <param name="value">The value to set</param>
        private void SetCurrentValue(string value)
        {
            try
            {
                if (comboBoxEdit1.SelectedItem is ParameterTypeItem selectedItem)
                {
                    switch (selectedItem.ParameterType)
                    {
                        case ParameterType.String:
                            txtStringValue.Text = value ?? "";
                            break;
                        case ParameterType.Number:
                            txtNumberValue.Text = value ?? "";
                            break;
                        case ParameterType.Decimal:
                            txtDecimalValue.Text = value ?? "";
                            break;
                        case ParameterType.Date:
                            cmbDateValue.Text = value ?? "";
                            break;
                        case ParameterType.Boolean:
                            cmbBooleanValue.Text = value ?? "True";
                            break;
                        case ParameterType.Object:
                            txtObjectValue.Text = value ?? "";
                            break;
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error setting current value: {ex.Message}");
            }
        }

        /// <summary>
        /// Handles parameter type selection change
        /// </summary>
        private void ComboBoxEdit1_SelectedIndexChanged(object sender, EventArgs e)
        {
            try
            {
                if (comboBoxEdit1.SelectedItem is ParameterTypeItem selectedItem)
                {
                    Debug.WriteLine($"Parameter type changed to: {selectedItem.DisplayName}");

                    // Show appropriate control for the selected type
                    ShowControlForType(selectedItem.ParameterType);

                    // Validate current value if not empty
                    string currentValue = GetCurrentValue();
                    if (!string.IsNullOrWhiteSpace(currentValue))
                    {
                        ValidateCurrentInput();
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error handling parameter type change: {ex.Message}");
            }
        }

        /// <summary>
        /// Handles date value ComboBox selection - validates the input
        /// </summary>
        private void CmbDateValue_SelectedIndexChanged(object sender, EventArgs e)
        {
            try
            {
                ValidateCurrentInput();
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in date ComboBox selection: {ex.Message}");
            }
        }

        /// <summary>
        /// Validates the current input and provides real-time feedback
        /// </summary>
        private void ValidateCurrentInput()
        {
            try
            {
                string currentValue = GetCurrentValue();

                if (string.IsNullOrWhiteSpace(currentValue))
                {
                    SetValidationStatus("", Color.Gray);
                    SetControlBorderColor(Color.Gray);
                    return;
                }

                if (comboBoxEdit1.SelectedItem is ParameterTypeItem selectedTypeItem)
                {
                    if (ParameterTypeHelper.ValidateValue(currentValue, selectedTypeItem.ParameterType, out string errorMessage))
                    {
                        SetValidationStatus("✓ Valid input", Color.Green);
                        SetControlBorderColor(Color.Green);
                    }
                    else
                    {
                        SetValidationStatus($"⚠ {errorMessage}", Color.Red);
                        SetControlBorderColor(Color.Red);
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in ValidateCurrentInput: {ex.Message}");
            }
        }

        /// <summary>
        /// Sets the validation status message and color
        /// </summary>
        /// <param name="message">The validation message</param>
        /// <param name="color">The message color</param>
        private void SetValidationStatus(string message, Color color)
        {
            if (string.IsNullOrWhiteSpace(message))
            {
                ParamStatus.Visible = false;
            }
            else
            {
                ParamStatus.Text = message;
                ParamStatus.ForeColor = color;
                ParamStatus.Visible = true;
            }
        }

        /// <summary>
        /// Sets the border color of the active control
        /// </summary>
        /// <param name="color">The border color</param>
        private void SetControlBorderColor(Color color)
        {
            try
            {
                if (comboBoxEdit1.SelectedItem is ParameterTypeItem selectedItem)
                {
                    switch (selectedItem.ParameterType)
                    {
                        case ParameterType.String:
                            txtStringValue.BackColor = color == Color.Red ? Color.LightPink :
                                                     color == Color.Green ? Color.LightGreen : Color.White;
                            break;
                        case ParameterType.Number:
                            txtNumberValue.BackColor = color == Color.Red ? Color.LightPink :
                                                      color == Color.Green ? Color.LightGreen : Color.White;
                            break;
                        case ParameterType.Decimal:
                            txtDecimalValue.BackColor = color == Color.Red ? Color.LightPink :
                                                       color == Color.Green ? Color.LightGreen : Color.White;
                            break;
                        case ParameterType.Date:
                            // ComboBox doesn't support BackColor change in the same way as TextBox
                            break;
                        case ParameterType.Boolean:
                            // ComboBox doesn't support BackColor change in the same way
                            break;
                        case ParameterType.Object:
                            txtObjectValue.BackColor = color == Color.Red ? Color.LightPink :
                                                      color == Color.Green ? Color.LightGreen : Color.White;
                            break;
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error setting control border color: {ex.Message}");
            }
        }

        /// <summary>
        /// Handles KeyPress event for String value input
        /// </summary>
        private void TxtStringValue_KeyPress(object sender, KeyPressEventArgs e)
        {
            // Allow all characters for string type
            // Length validation is handled in real-time validation
        }

        /// <summary>
        /// Handles KeyPress event for Number value input
        /// </summary>
        private void TxtNumberValue_KeyPress(object sender, KeyPressEventArgs e)
        {
            // Temporarily allow all characters for testing
            Debug.WriteLine($"Number KeyPress: Key={e.KeyChar}, KeyCode={(int)e.KeyChar}, Handled=false");
        }

        /// <summary>
        /// Handles KeyPress event for Decimal value input
        /// </summary>
        private void TxtDecimalValue_KeyPress(object sender, KeyPressEventArgs e)
        {
            // Temporarily allow all characters for testing
            Debug.WriteLine($"Decimal KeyPress: Key={e.KeyChar}, KeyCode={(int)e.KeyChar}, Handled=false");
        }

        /// <summary>
        /// Handles Browse button click for Object type
        /// </summary>
        private void BtnBrowseObject_Click(object sender, EventArgs e)
        {
            try
            {
                using (var openFileDialog = new OpenFileDialog())
                {
                    openFileDialog.Title = "Select File";
                    openFileDialog.Filter = "All Files (*.*)|*.*|Images (*.jpg;*.jpeg;*.png;*.gif;*.bmp)|*.jpg;*.jpeg;*.png;*.gif;*.bmp|Documents (*.pdf;*.doc;*.docx;*.txt)|*.pdf;*.doc;*.docx;*.txt";
                    openFileDialog.FilterIndex = 1;
                    openFileDialog.RestoreDirectory = true;

                    if (openFileDialog.ShowDialog() == DialogResult.OK)
                    {
                        txtObjectValue.Text = openFileDialog.FileName;
                        ValidateCurrentInput();
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in file browser: {ex.Message}");
                MessageBox.Show($"Error selecting file: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Validates the parameter value against the selected type
        /// </summary>
        private bool ValidateParameterValue()
        {
            try
            {
                if (comboBoxEdit1.SelectedItem is ParameterTypeItem selectedItem)
                {
                    string value = GetCurrentValue();
                    if (!string.IsNullOrWhiteSpace(value))
                    {
                        if (!ParameterTypeHelper.ValidateValue(value, selectedItem.ParameterType, out string errorMessage))
                        {
                            // Show validation error
                            MessageBox.Show($"Invalid value for {selectedItem.DisplayName} type: {errorMessage}",
                                "Validation Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                            // Focus will be handled by the active control
                            return false;
                        }
                    }
                }
                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error validating parameter value: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Sets up the form for editing an existing parameter
        /// </summary>
        /// <param name="parameter">The parameter to edit</param>
        public void SetupForEdit(ParametersFormModel parameter)
        {
            try
            {
                Debug.WriteLine($"=== SetupForEdit: Setting up form for parameter ID {parameter.Id} ===");

                // Store the parameter being edited
                EditingParameter = parameter;

                // Change form title
                this.Text = "Edit Parameter";

                // Pre-populate the fields
                txtParameterCode.Text = parameter.ParameterCode ?? "";
                txtPurpose.Text = parameter.Purpose ?? "";

                // Set the parameter type first, then the value
                SetSelectedParameterType(parameter.ParameterType);
                SetCurrentValue(parameter.ParameterValue ?? "");

                // Set focus to first field
                this.ActiveControl = txtParameterCode;

                Debug.WriteLine($"Form setup for editing parameter: {parameter.ParameterCode}");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error setting up form for edit: {ex.Message}");
                MessageBox.Show($"Error setting up edit form: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Handles Save button click - validates and saves parameter
        /// </summary>
        private void BtnSave_Click(object sender, EventArgs e)
        {
            try
            {
                Debug.WriteLine("=== Save button clicked ===");

                // Validate input fields
                if (!ValidateInput())
                {
                    return; // Validation failed, stay on form
                }

                if (IsEditMode)
                {
                    // Update existing parameter
                    EditingParameter.ParameterCode = txtParameterCode.Text.Trim();
                    EditingParameter.ParameterValue = GetCurrentValue();
                    EditingParameter.ParameterType = GetSelectedParameterType();
                    EditingParameter.Purpose = string.IsNullOrWhiteSpace(txtPurpose.Text) ? null : txtPurpose.Text.Trim();
                    EditingParameter.ModifiedAt = DateTime.Now;

                    // Validate model
                    if (!EditingParameter.IsValid())
                    {
                        MessageBox.Show("Parameter data is not valid. Please check your input.", "Validation Error",
                            MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        return;
                    }

                    // Update in database
                    bool success = ParametersFormRepository.UpdateParameter(EditingParameter);
                    if (!success)
                    {
                        MessageBox.Show("Failed to update parameter. Please try again.", "Error",
                            MessageBoxButtons.OK, MessageBoxIcon.Error);
                        return;
                    }

                    Debug.WriteLine($"Parameter updated successfully: ID {EditingParameter.Id}");

                    // Show success message
                    MessageBox.Show("Parameter updated successfully!", "Success",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    // Create new parameter
                    var parameter = new ParametersFormModel
                    {
                        ParameterCode = txtParameterCode.Text.Trim(),
                        ParameterValue = GetCurrentValue(),
                        ParameterType = GetSelectedParameterType(),
                        Purpose = string.IsNullOrWhiteSpace(txtPurpose.Text) ? null : txtPurpose.Text.Trim()
                    };

                    // Validate model
                    if (!parameter.IsValid())
                    {
                        MessageBox.Show("Parameter data is not valid. Please check your input.", "Validation Error",
                            MessageBoxButtons.OK, MessageBoxIcon.Warning);
                        return;
                    }

                    // Save to database
                    int newId = ParametersFormRepository.InsertParameter(parameter);
                    NewParameterId = newId;

                    Debug.WriteLine($"Parameter saved successfully with ID: {newId}");

                    // Show success message
                    MessageBox.Show("Parameter saved successfully!", "Success",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }

                // Close form with OK result
                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error saving parameter: {ex.Message}");
                MessageBox.Show($"Error saving parameter: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Handles Cancel button click - closes form without saving
        /// </summary>
        private void BtnCancel_Click(object sender, EventArgs e)
        {
            try
            {
                Debug.WriteLine("Cancel button clicked");
                this.DialogResult = DialogResult.Cancel;
                this.Close();
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in Cancel button: {ex.Message}");
                this.Close(); // Force close even if error
            }
        }

        /// <summary>
        /// Validates input fields before saving
        /// </summary>
        /// <returns>True if validation passes, false otherwise</returns>
        private bool ValidateInput()
        {
            try
            {
                // Check parameter code (mandatory)
                if (string.IsNullOrWhiteSpace(txtParameterCode.Text))
                {
                    MessageBox.Show("Parameter Code is required.", "Validation Error",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtParameterCode.Focus();
                    return false;
                }

                // Check parameter code length
                if (txtParameterCode.Text.Trim().Length > 100)
                {
                    MessageBox.Show("Parameter Code cannot exceed 100 characters.", "Validation Error",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtParameterCode.Focus();
                    return false;
                }

                // Check parameter value (mandatory)
                string currentValue = GetCurrentValue();
                if (string.IsNullOrWhiteSpace(currentValue))
                {
                    MessageBox.Show("Parameter Value is required.", "Validation Error",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    // Focus will be handled by the active control
                    return false;
                }

                // Check parameter value length
                if (currentValue.Length > 255)
                {
                    MessageBox.Show("Parameter Value cannot exceed 255 characters.", "Validation Error",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    // Focus will be handled by the active control
                    return false;
                }

                // Check purpose length (optional field)
                if (!string.IsNullOrWhiteSpace(txtPurpose.Text) && txtPurpose.Text.Trim().Length > 255)
                {
                    MessageBox.Show("Purpose cannot exceed 255 characters.", "Validation Error",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtPurpose.Focus();
                    return false;
                }

                // Validate parameter value against selected type
                if (!ValidateParameterValue())
                {
                    return false; // Validation failed
                }

                // Check parameter code uniqueness
                string parameterCode = txtParameterCode.Text.Trim();
                int? excludeId = IsEditMode ? EditingParameter.Id : (int?)null;

                if (ParametersFormRepository.CheckParameterCodeExists(parameterCode, excludeId))
                {
                    MessageBox.Show($"Parameter Code '{parameterCode}' already exists. Please use a different code.", "Validation Error",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtParameterCode.Focus();
                    return false;
                }

                Debug.WriteLine("Input validation passed");
                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in ValidateInput: {ex.Message}");
                MessageBox.Show($"Error validating input: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }
        }

        /// <summary>
        /// Handles Enter key press to trigger Save button
        /// </summary>
        protected override bool ProcessDialogKey(Keys keyData)
        {
            if (keyData == Keys.Enter && !txtPurpose.Focused)
            {
                BtnSave_Click(this, EventArgs.Empty);
                return true;
            }
            else if (keyData == Keys.Escape)
            {
                BtnCancel_Click(this, EventArgs.Empty);
                return true;
            }

            return base.ProcessDialogKey(keyData);
        }

        /// <summary>
        /// Sets the selected parameter type in the combo box
        /// </summary>
        /// <param name="parameterType">The parameter type to select</param>
        private void SetSelectedParameterType(ParameterType parameterType)
        {
            try
            {
                for (int i = 0; i < comboBoxEdit1.Properties.Items.Count; i++)
                {
                    if (comboBoxEdit1.Properties.Items[i] is ParameterTypeItem item && item.ParameterType == parameterType)
                    {
                        comboBoxEdit1.SelectedIndex = i;
                        break;
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error setting selected parameter type: {ex.Message}");
            }
        }

        /// <summary>
        /// Gets the currently selected parameter type
        /// </summary>
        /// <returns>The selected parameter type</returns>
        private ParameterType GetSelectedParameterType()
        {
            try
            {
                if (comboBoxEdit1.SelectedItem is ParameterTypeItem selectedItem)
                {
                    return selectedItem.ParameterType;
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error getting selected parameter type: {ex.Message}");
            }

            // Default to String if no selection or error
            return ParameterType.String;
        }

        /// <summary>
        /// Checks if the given text is a date format pattern (not an actual date)
        /// </summary>
        private bool IsFormatPattern(string text)
        {
            if (string.IsNullOrWhiteSpace(text))
                return false;
                
            // List of format patterns we show in the ComboBox
            string[] formatPatterns = {
                "DD-MM-YYYY",
                "DD-MMM-YYYY", 
                "DD-MM-YY",
                "MM/DD/YYYY",
                "YYYY-MM-DD",
                "MMM DD, YYYY"
            };
            
            return Array.Exists(formatPatterns, pattern => pattern.Equals(text, StringComparison.OrdinalIgnoreCase));
        }

        private void ParamEntryForm_Load(object sender, EventArgs e)
        {

        }

        private void txtObjectValue_TextChanged(object sender, EventArgs e)
        {

        }

        private void cmbBooleanValue_SelectedIndexChanged(object sender, EventArgs e)
        {

        }
    }

    /// <summary>
    /// Helper class for parameter type combo box items
    /// </summary>
    public class ParameterTypeItem
    {
        public ParameterType ParameterType { get; set; }
        public string DisplayName { get; set; }

        public ParameterTypeItem(ParameterType parameterType, string displayName)
        {
            ParameterType = parameterType;
            DisplayName = displayName;
        }

        public override string ToString()
        {
            return DisplayName;
        }
    }
}
