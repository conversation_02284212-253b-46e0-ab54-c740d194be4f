# ProManage Dependencies Organization

This folder contains the organization of external dependencies used by the ProManage application.

## Structure

- `DevExpress/`: Contains DevExpress UI components and libraries
- `System/`: Contains system-level dependencies
- `Syncfusion/`: Contains Syncfusion components and libraries
- `NuGet/`: Contains any additional NuGet-related files

## Package Management

All actual package references are managed via the standard NuGet package management system, with packages stored in the `packages` folder at the project root. The folders in this directory are primarily for organizational purposes to keep the project root clean.

## Reference Paths

The project file (`ProManage.csproj`) references assemblies from the `packages` folder directly, ensuring proper NuGet package management and version control.

## Notes on DevExpress Components

DevExpress components are organized according to their functionality:
- Core components (Data, Drawing, etc.)
- UI components (Grid, Navigation, etc.)
- Reports and document processing

## Notes on System Components

System components include essential .NET Framework extensions and utilities:
- System.Buffers
- System.Memory
- System.Numerics.Vectors
- System.Diagnostics.DiagnosticSource
- System.Runtime.CompilerServices.Unsafe