// Event handling logic for EstimateForm - handles button clicks and form events
using System;
using System.Diagnostics;
using System.Windows.Forms;
using ProManage.Modules.Models.EstimateForm;
using ProManage.Modules.Data.EstimateForm;
using ProManage.Modules.UI;
using ProManage.Modules.Connections;
using ProManage.Forms;

namespace ProManage.Modules.Helpers.EstimateForm
{
    /// <summary>
    /// Handles all event-related functionality for EstimateForm
    /// Manages button clicks, form events, and user interactions
    /// </summary>
    public static class EstimateFormEventHandlers
    {
        /// <summary>
        /// Handles the Create New Estimate operation
        /// </summary>
        public static void CreateNewEstimate(ProManage.Forms.EstimateForm form, ref EstimateFormHeaderModel currentEstimate, ref bool isEditMode)
        {
            try
            {
                Debug.WriteLine("=== Creating New Estimate ===");

                // Create a new EstimateFormHeaderModel object
                currentEstimate = new EstimateFormHeaderModel
                {
                    Id = 0, // This indicates it's a new record
                    Status = false, // false = Active/Open/Editable, true = Closed/Locked
                    CreatedAt = DateTime.Now,
                    DocDate = DateTime.Now
                };

                isEditMode = true;

                // Clear the form
                EstimateFormHelper.ClearForm(form);

                // Set default values
                form.dpDocDate.DateTime = DateTime.Now;

                // Get the next sequential estimate number
                try
                {
                    ProgressIndicatorService.Instance.ShowProgress();

                    string nextEstimateNumber = EstimateFormRepository.GetNextEstimateNumber();
                    form.txtEstimate.Text = nextEstimateNumber;

                    Debug.WriteLine($"Generated new estimate number: {nextEstimateNumber}");
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"Error generating estimate number: {ex.Message}");
                    // Use a default format if the repository method fails
                    string defaultNumber = $"EST-{DateTime.Now:yy}-{DateTime.Now:MMdd}-{DateTime.Now:HHmm}";
                    form.txtEstimate.Text = defaultNumber;
                    Debug.WriteLine($"Using default estimate number: {defaultNumber}");
                }
                finally
                {
                    ProgressIndicatorService.Instance.HideProgress();
                }

                // Enable controls for editing
                EstimateFormHelper.EnableControls(form, true);

                // Focus on customer field so user can start entering data
                form.txtCustomer.Focus();

                // Update toggle button to Active mode for new estimate
                form.UpdateToggleState();

                // Update button states
                EstimateFormHelper.UpdateButtonStates(form, isEditMode);

                Debug.WriteLine("New estimate ready for data entry - Toggle set to Active mode");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error creating new estimate: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                Debug.WriteLine($"Error in CreateNewEstimate: {ex.Message}");
            }
        }

        /// <summary>
        /// Handles the Edit Current Estimate operation
        /// </summary>
        public static void EditCurrentEstimate(ProManage.Forms.EstimateForm form, EstimateFormHeaderModel currentEstimate, ref bool isEditMode)
        {
            try
            {
                if (currentEstimate == null || currentEstimate.Id <= 0)
                {
                    MessageBox.Show("Please select an estimate to edit.", "Information",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                Debug.WriteLine($"=== Editing Estimate ID: {currentEstimate.Id} ===");
                Debug.WriteLine($"Edit: Grid currently has {form.GridDataTable?.Rows?.Count ?? 0} rows");

                // DON'T reload or clear grid data - just switch to edit mode
                // The grid should already have the correct data from when the estimate was loaded

                isEditMode = true;
                EstimateFormHelper.EnableControls(form, true);
                EstimateFormHelper.UpdateButtonStates(form, isEditMode);

                // Focus on customer field for editing
                form.txtCustomer.Focus();

                Debug.WriteLine($"Edit: Estimate is now in edit mode with {form.GridDataTable?.Rows?.Count ?? 0} grid rows preserved");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error entering edit mode: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                Debug.WriteLine($"Error in EditCurrentEstimate: {ex.Message}");
            }
        }

        /// <summary>
        /// Handles the Save Current Estimate operation
        /// </summary>
        public static void SaveCurrentEstimate(ProManage.Forms.EstimateForm form, ref EstimateFormHeaderModel currentEstimate, ref bool isEditMode)
        {
            try
            {
                // Validate that we have data to save
                if (currentEstimate == null)
                {
                    MessageBox.Show("No estimate data to save.", "Save Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // Validate required fields
                if (string.IsNullOrWhiteSpace(form.txtEstimate.Text))
                {
                    MessageBox.Show("Estimate number is required.", "Validation Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    form.txtEstimate.Focus();
                    return;
                }

                if (string.IsNullOrWhiteSpace(form.txtCustomer.Text))
                {
                    MessageBox.Show("Customer name is required.", "Validation Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    form.txtCustomer.Focus();
                    return;
                }

                Debug.WriteLine("=== Starting Save Operation ===");
                Debug.WriteLine($"Current Estimate ID: {currentEstimate.Id}");
                Debug.WriteLine($"Is New Record: {currentEstimate.Id <= 0}");

                // Save header data from form controls to EstimateFormHeaderModel object
                EstimateFormDataMapper.MapFormToModel(form, currentEstimate);

                Debug.WriteLine($"Header Data - EstimateNo: {currentEstimate.EstimateNo}, Customer: {currentEstimate.CustomerName}");

                // Get grid data and add to estimate details collection
                // For new estimates, use 0 initially, it will be updated after save
                var gridDetails = EstimateFormDataMapper.MapGridToDetails(form, currentEstimate.Id);

                Debug.WriteLine($"Total valid details from grid: {gridDetails.Count}");

                // Set the details on the estimate object
                currentEstimate.Details = gridDetails;

                // For new estimates, we'll need to update the EstimateId after the header is saved
                bool isNewEstimate = currentEstimate.Id <= 0;

                // Validate complete estimate data before saving
                if (!EstimateFormValidation.ValidateEstimateData(currentEstimate, form.GridDataTable))
                {
                    Debug.WriteLine("=== Save Cancelled: Validation Failed ===");
                    return; // Validation failed, don't proceed with save
                }

                // Show progress indicator
                ProgressIndicatorService.Instance.ShowProgress();

                // Save to database using EstimateFormRepository
                bool success = EstimateFormRepository.SaveEstimate(currentEstimate);

                if (success)
                {
                    Debug.WriteLine("=== Save Successful ===");

                    // Exit edit mode
                    isEditMode = false;
                    EstimateFormHelper.EnableControls(form, false);

                    // Update the current estimate's details collection with what was just saved
                    currentEstimate.Details = gridDetails;

                    // For new estimates, update the EstimateId in the details collection
                    if (isNewEstimate && currentEstimate.Id > 0)
                    {
                        Debug.WriteLine($"Post-Save: Updating EstimateId from 0 to {currentEstimate.Id} for {gridDetails.Count} details");
                        foreach (var detail in gridDetails)
                        {
                            detail.EstimateId = currentEstimate.Id;
                        }
                    }

                    // Set the grid to read-only mode but keep the data visible
                    EstimateFormGridManager.SetGridEditMode(form, false);

                    // Ensure the grid display is refreshed to show the data properly
                    EstimateFormDataMapper.RefreshGridDisplay(form);

                    Debug.WriteLine($"=== Post-Save: Grid preserved with {form.GridDataTable?.Rows?.Count ?? 0} rows ===");

                    EstimateFormHelper.UpdateButtonStates(form, isEditMode);
                    MessageBox.Show($"Estimate '{currentEstimate.EstimateNo}' saved successfully with {gridDetails.Count} detail items.",
                                  "Save Successful", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    Debug.WriteLine("=== Save Failed ===");
                    MessageBox.Show("Failed to save estimate. Please check your data and database connection, then try again.",
                                  "Save Failed", MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"=== Save Error: {ex.Message} ===");
                if (ex.InnerException != null)
                {
                    Debug.WriteLine($"Inner Exception: {ex.InnerException.Message}");
                }
                Debug.WriteLine($"Stack Trace: {ex.StackTrace}");

                MessageBox.Show($"Error saving estimate: {ex.Message}\n\nPlease check the application logs for more details.",
                              "Save Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                ProgressIndicatorService.Instance.HideProgress();
            }
        }

        /// <summary>
        /// Handles the Cancel Current Operation
        /// </summary>
        public static void CancelCurrentOperation(ProManage.Forms.EstimateForm form, ref EstimateFormHeaderModel currentEstimate, ref bool isEditMode)
        {
            try
            {
                Debug.WriteLine("=== Cancelling Current Operation ===");

                isEditMode = false;
                EstimateFormHelper.EnableControls(form, false);

                // Reset the form
                EstimateFormHelper.ClearForm(form);
                currentEstimate = null;

                // Try to load the first estimate if available
                try
                {
                    var firstEstimate = EstimateFormNavigation.NavigateToFirst();
                    if (firstEstimate != null)
                    {
                        EstimateFormNavigation.LoadEstimateDetails(firstEstimate);
                    }
                    if (firstEstimate != null)
                    {
                        EstimateFormDataMapper.LoadEstimateToForm(form, firstEstimate);
                        currentEstimate = firstEstimate;
                    }
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"Error loading first estimate after cancel: {ex.Message}");
                    // Continue with cleared form if no estimates available
                }

                EstimateFormHelper.UpdateButtonStates(form, isEditMode);

                // Display informational message
                MessageBox.Show("Operation cancelled. Form has been reset.",
                    "Operation Cancelled", MessageBoxButtons.OK, MessageBoxIcon.Information);

                Debug.WriteLine("Cancel operation completed");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error cancelling operation: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                Debug.WriteLine($"Error in CancelCurrentOperation: {ex.Message}");
            }
        }

        /// <summary>
        /// Handles the Delete Current Estimate operation
        /// </summary>
        public static void DeleteCurrentEstimate(ProManage.Forms.EstimateForm form, ref EstimateFormHeaderModel currentEstimate, ref bool isEditMode)
        {
            try
            {
                if (currentEstimate?.Id <= 0)
                {
                    MessageBox.Show("Please select an estimate to delete.", "Information",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                Debug.WriteLine($"=== Deleting Estimate ID: {currentEstimate.Id} ===");

                // Confirm deletion with user
                var result = MessageBox.Show(
                    $"Are you sure you want to delete estimate {currentEstimate.EstimateNo}?\n\nThis action cannot be undone.",
                    "Confirm Delete",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);

                if (result != DialogResult.Yes)
                {
                    Debug.WriteLine("Delete operation cancelled by user");
                    return;
                }

                try
                {
                    ProgressIndicatorService.Instance.ShowProgress();

                    bool success = EstimateFormRepository.DeleteEstimate(currentEstimate.Id);

                    if (success)
                    {
                        Debug.WriteLine("=== Delete Successful ===");

                        MessageBox.Show($"Estimate '{currentEstimate.EstimateNo}' deleted successfully.",
                                      "Delete Successful", MessageBoxButtons.OK, MessageBoxIcon.Information);

                        // Clear the form and try to load another estimate
                        EstimateFormHelper.ClearForm(form);
                        currentEstimate = null;
                        isEditMode = false;

                        // Try to load the first available estimate
                        try
                        {
                            var firstEstimate = EstimateFormNavigation.NavigateToFirst();
                            if (firstEstimate != null)
                            {
                                EstimateFormNavigation.LoadEstimateDetails(firstEstimate);
                            }
                            if (firstEstimate != null)
                            {
                                EstimateFormDataMapper.LoadEstimateToForm(form, firstEstimate);
                                currentEstimate = firstEstimate;
                            }
                        }
                        catch (Exception ex)
                        {
                            Debug.WriteLine($"Error loading first estimate after delete: {ex.Message}");
                            // Continue with cleared form if no estimates available
                        }

                        EstimateFormHelper.UpdateButtonStates(form, isEditMode);
                    }
                    else
                    {
                        Debug.WriteLine("=== Delete Failed ===");
                        MessageBox.Show("Failed to delete estimate. Please check the database connection and try again.",
                                      "Delete Failed", MessageBoxButtons.OK, MessageBoxIcon.Error);
                    }
                }
                finally
                {
                    ProgressIndicatorService.Instance.HideProgress();
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"=== Delete Error: {ex.Message} ===");
                MessageBox.Show($"Error deleting estimate: {ex.Message}",
                              "Delete Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}
