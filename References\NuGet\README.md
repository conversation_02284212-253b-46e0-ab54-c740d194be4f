# NuGet Configuration

This folder contains NuGet-related configuration and tools for the ProManage application.

## Contents

- NuGet configuration files
- Custom NuGet repository settings
- Package restore scripts

## NuGet Management

The ProManage application uses NuGet for package management, with:
- packages.config at the project root for dependency declarations
- The packages folder for storing downloaded packages
- NuGet.config for configuring package sources