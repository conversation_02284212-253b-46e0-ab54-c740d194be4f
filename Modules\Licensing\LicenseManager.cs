using System;
using System.Diagnostics;
using System.IO;
using System.Reflection;

namespace ProManage.Modules.Licensing
{
    /// <summary>
    /// Manages application licensing
    /// </summary>
    public static class LicenseManager
    {
        // Current license usage mode
        private static LicenseUsageMode _usageMode = LicenseUsageMode.Runtime;

        /// <summary>
        /// Gets the current license usage mode
        /// </summary>
        public static LicenseUsageMode UsageMode
        {
            get
            {
                // Check if we're in design mode by checking if the process name contains "devenv" or "VisualStudio"
                if (_usageMode == LicenseUsageMode.Runtime)
                {
                    string processName = Process.GetCurrentProcess().ProcessName.ToLower();
                    if (processName.Contains("devenv") || processName.Contains("visualstudio"))
                    {
                        _usageMode = LicenseUsageMode.Designtime;
                    }
                }
                
                return _usageMode;
            }
        }

        /// <summary>
        /// Validates the application license
        /// </summary>
        /// <returns>True if license is valid, false otherwise</returns>
        public static bool ValidateLicense()
        {
            try
            {
                // In a real application, this would check a license file or online validation
                // For this example, we'll just return true
                Debug.WriteLine("License validated successfully");
                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error validating license: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Gets the license expiration date
        /// </summary>
        /// <returns>The license expiration date or DateTime.MaxValue if perpetual</returns>
        public static DateTime GetLicenseExpirationDate()
        {
            try
            {
                // In a real application, this would read from a license file
                // For this example, we'll just return a date 1 year from now
                return DateTime.Now.AddYears(1);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error getting license expiration date: {ex.Message}");
                return DateTime.Now;
            }
        }

        /// <summary>
        /// Gets the licensed user or company name
        /// </summary>
        /// <returns>The licensed user or company name</returns>
        public static string GetLicensedUser()
        {
            try
            {
                // In a real application, this would read from a license file
                // For this example, we'll just return a placeholder
                return "ProManage User";
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error getting licensed user: {ex.Message}");
                return string.Empty;
            }
        }
    }
}
