using System;
using System.ComponentModel;
using System.Diagnostics;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using ProManage.Modules.Helpers;
using ProManage.Modules.Models.PermissionManagementForm;

namespace ProManage.Modules.Base
{
    /// <summary>
    /// Base form class with automatic permission checking and enforcement
    /// All forms requiring permission checks should inherit from this class
    /// </summary>
    public partial class BasePermissionForm : XtraForm
    {
        #region Private Fields

        private string _formName;
        private EffectivePermission _formPermission;
        private bool _permissionsApplied = false;

        #endregion

        #region Public Properties

        /// <summary>
        /// Gets or sets the form name for permission checking
        /// </summary>
        [Category("Permission")]
        [Description("The form name used for permission checking")]
        public string FormName
        {
            get => _formName;
            set
            {
                _formName = value;
                if (!DesignMode && !string.IsNullOrEmpty(value))
                {
                    LoadFormPermissions();
                }
            }
        }

        /// <summary>
        /// Gets the effective permission for this form
        /// </summary>
        [Browsable(false)]
        public EffectivePermission FormPermission => _formPermission;

        /// <summary>
        /// Gets whether the form is in read-only mode due to permissions
        /// </summary>
        [Browsable(false)]
        public bool IsReadOnlyMode => _formPermission != null && 
            !_formPermission.EditPermission && !_formPermission.NewPermission;

        /// <summary>
        /// Gets whether permissions have been applied to this form
        /// </summary>
        [Browsable(false)]
        public bool PermissionsApplied => _permissionsApplied;

        #endregion

        #region Events

        /// <summary>
        /// Event fired when permissions are loaded for the form
        /// </summary>
        public event EventHandler<PermissionLoadedEventArgs> PermissionLoaded;

        /// <summary>
        /// Event fired when a permission check fails
        /// </summary>
        public event EventHandler<PermissionDeniedEventArgs> PermissionDenied;

        #endregion

        #region Constructor

        public BasePermissionForm()
        {
            InitializeComponent();
            
            // Set default form name to class name
            _formName = this.GetType().Name;
            
            // Load permissions when form loads (not in design mode)
            this.Load += BasePermissionForm_Load;
        }

        #endregion

        #region Form Events

        /// <summary>
        /// Handle form load event
        /// </summary>
        private void BasePermissionForm_Load(object sender, EventArgs e)
        {
            try
            {
                if (!DesignMode)
                {
                    LoadFormPermissions();
                    ApplyPermissions();
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in BasePermissionForm_Load: {ex.Message}");
            }
        }

        #endregion

        #region Permission Methods

        /// <summary>
        /// Load permissions for this form
        /// </summary>
        public virtual void LoadFormPermissions()
        {
            try
            {
                if (DesignMode || string.IsNullOrEmpty(_formName))
                {
                    return;
                }

                _formPermission = FormPermissionHelper.GetFormPermission(_formName);
                
                // Fire permission loaded event
                PermissionLoaded?.Invoke(this, new PermissionLoadedEventArgs(_formPermission));
                
                Debug.WriteLine($"Permissions loaded for form: {_formName}");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error loading form permissions: {ex.Message}");
            }
        }

        /// <summary>
        /// Apply permissions to the form and its controls
        /// </summary>
        public virtual void ApplyPermissions()
        {
            try
            {
                if (DesignMode || _formPermission == null)
                {
                    return;
                }

                // Apply form-level permissions
                FormPermissionHelper.ApplyFormPermissions(this, _formName);
                
                // Mark permissions as applied
                _permissionsApplied = true;
                
                Debug.WriteLine($"Permissions applied to form: {_formName}");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error applying permissions: {ex.Message}");
            }
        }

        /// <summary>
        /// Check if user has permission for a specific operation
        /// </summary>
        /// <param name="operation">The operation to check</param>
        /// <param name="showMessage">Whether to show error message if denied</param>
        /// <returns>True if permission is granted</returns>
        public bool HasPermission(PermissionOperation operation, bool showMessage = true)
        {
            try
            {
                return FormPermissionHelper.ValidatePermission(_formName, operation, showMessage);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error checking permission: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Validate permission for New operation
        /// </summary>
        /// <param name="showMessage">Whether to show error message if denied</param>
        /// <returns>True if permission is granted</returns>
        public bool ValidateNewPermission(bool showMessage = true)
        {
            bool hasPermission = HasPermission(PermissionOperation.New, showMessage);
            if (!hasPermission)
            {
                PermissionDenied?.Invoke(this, new PermissionDeniedEventArgs(PermissionOperation.New));
            }
            return hasPermission;
        }

        /// <summary>
        /// Validate permission for Edit operation
        /// </summary>
        /// <param name="showMessage">Whether to show error message if denied</param>
        /// <returns>True if permission is granted</returns>
        public bool ValidateEditPermission(bool showMessage = true)
        {
            bool hasPermission = HasPermission(PermissionOperation.Edit, showMessage);
            if (!hasPermission)
            {
                PermissionDenied?.Invoke(this, new PermissionDeniedEventArgs(PermissionOperation.Edit));
            }
            return hasPermission;
        }

        /// <summary>
        /// Validate permission for Delete operation
        /// </summary>
        /// <param name="showMessage">Whether to show error message if denied</param>
        /// <returns>True if permission is granted</returns>
        public bool ValidateDeletePermission(bool showMessage = true)
        {
            bool hasPermission = HasPermission(PermissionOperation.Delete, showMessage);
            if (!hasPermission)
            {
                PermissionDenied?.Invoke(this, new PermissionDeniedEventArgs(PermissionOperation.Delete));
            }
            return hasPermission;
        }

        /// <summary>
        /// Validate permission for Print operation
        /// </summary>
        /// <param name="showMessage">Whether to show error message if denied</param>
        /// <returns>True if permission is granted</returns>
        public bool ValidatePrintPermission(bool showMessage = true)
        {
            bool hasPermission = HasPermission(PermissionOperation.Print, showMessage);
            if (!hasPermission)
            {
                PermissionDenied?.Invoke(this, new PermissionDeniedEventArgs(PermissionOperation.Print));
            }
            return hasPermission;
        }

        /// <summary>
        /// Refresh permissions for this form
        /// </summary>
        public virtual void RefreshPermissions()
        {
            try
            {
                FormPermissionHelper.RefreshPermissionCache();
                LoadFormPermissions();
                ApplyPermissions();
                
                Debug.WriteLine($"Permissions refreshed for form: {_formName}");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error refreshing permissions: {ex.Message}");
            }
        }

        /// <summary>
        /// Get permission summary for this form
        /// </summary>
        /// <returns>Permission summary text</returns>
        public string GetPermissionSummary()
        {
            try
            {
                return FormPermissionHelper.GetPermissionSummary(_formName);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error getting permission summary: {ex.Message}");
                return "Error retrieving permissions";
            }
        }

        #endregion

        #region Protected Methods

        /// <summary>
        /// Override to provide custom permission application logic
        /// </summary>
        protected virtual void OnPermissionLoaded()
        {
            // Override in derived classes for custom logic
        }

        /// <summary>
        /// Override to handle permission denied scenarios
        /// </summary>
        /// <param name="operation">The denied operation</param>
        protected virtual void OnPermissionDenied(PermissionOperation operation)
        {
            // Override in derived classes for custom logic
        }

        #endregion
    }

    #region Event Args Classes

    /// <summary>
    /// Event arguments for permission loaded events
    /// </summary>
    public class PermissionLoadedEventArgs : EventArgs
    {
        public EffectivePermission Permission { get; }

        public PermissionLoadedEventArgs(EffectivePermission permission)
        {
            Permission = permission;
        }
    }

    /// <summary>
    /// Event arguments for permission denied events
    /// </summary>
    public class PermissionDeniedEventArgs : EventArgs
    {
        public PermissionOperation Operation { get; }

        public PermissionDeniedEventArgs(PermissionOperation operation)
        {
            Operation = operation;
        }
    }

    #endregion
}
