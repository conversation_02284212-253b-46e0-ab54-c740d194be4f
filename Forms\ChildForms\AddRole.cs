using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Diagnostics;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using DevExpress.XtraEditors;

namespace ProManage.Forms.ChildForms
{
    /// <summary>
    /// Enhanced Add Role form with improved UI and validation
    /// </summary>
    public partial class AddRole : XtraForm
    {
        #region Private Fields

        private bool _hasUnsavedChanges = false;

        #endregion

        #region Constructor

        public AddRole()
        {
            try
            {
                Debug.WriteLine("AddRole constructor started");
                InitializeComponent();
                Debug.WriteLine("InitializeComponent completed");
                InitializeForm();
                Debug.WriteLine("InitializeForm completed");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Exception in AddRole constructor: {ex.Message}\n{ex.StackTrace}");
                MessageBox.Show($"Error creating AddRole form: {ex.Message}\n\nStack Trace: {ex.StackTrace}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region Initialization

        /// <summary>
        /// Initialize form settings and event handlers
        /// </summary>
        private void InitializeForm()
        {
            try
            {
                // Configure MenuRibbon UC
                if (menuRibbon1 != null)
                {
                    menuRibbon1.FormName = "AddRole";
                    menuRibbon1.CurrentUserId = 1; // Will be set dynamically
                    menuRibbon1.HasUnsavedChanges = false;
                    menuRibbon1.IsEditMode = true;

                    // Wire up MenuRibbon events
                    menuRibbon1.SaveClicked += MenuRibbon_SaveClicked;
                    menuRibbon1.CancelClicked += MenuRibbon_CancelClicked;
                }

                // Wire up button events
                if (menuRibbon1?.BarButtonItemSave != null)
                    menuRibbon1.BarButtonItemSave.ItemClick += BtnSave_Click;
                if (menuRibbon1?.BarButtonItemCancel != null)
                    menuRibbon1.BarButtonItemCancel.ItemClick += BtnCancel_Click;

                // Wire up change tracking events
                txtRoleName.EditValueChanged += Control_ValueChanged;
                txtDescription.EditValueChanged += Control_ValueChanged;
                chkActive.CheckedChanged += Control_ValueChanged;

                // Set initial focus
                txtRoleName.Focus();

                Debug.WriteLine("AddRole form initialized successfully");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error initializing AddRole form: {ex.Message}");
                XtraMessageBox.Show($"Error initializing form: {ex.Message}", "Initialization Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        #endregion

        #region Event Handlers

        private void AddRole_Load(object sender, EventArgs e)
        {
            try
            {
                // Set default values
                chkActive.Checked = true;
                _hasUnsavedChanges = false;
                UpdateFormState();

                Debug.WriteLine("AddRole form loaded successfully");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in AddRole_Load: {ex.Message}");
            }
        }

        private void txtRoleName_EditValueChanged(object sender, EventArgs e)
        {
            // This is handled by the generic Control_ValueChanged event
        }

        /// <summary>
        /// Handle value changes for change tracking
        /// </summary>
        private void Control_ValueChanged(object sender, EventArgs e)
        {
            try
            {
                _hasUnsavedChanges = true;
                UpdateFormState();
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in Control_ValueChanged: {ex.Message}");
            }
        }

        /// <summary>
        /// Handle Save button click
        /// </summary>
        private void BtnSave_Click(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            SaveRole();
        }

        /// <summary>
        /// Handle Cancel button click
        /// </summary>
        private void BtnCancel_Click(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            CancelOperation();
        }

        /// <summary>
        /// Handle MenuRibbon Save click
        /// </summary>
        private void MenuRibbon_SaveClicked(object sender, EventArgs e)
        {
            SaveRole();
        }

        /// <summary>
        /// Handle MenuRibbon Cancel click
        /// </summary>
        private void MenuRibbon_CancelClicked(object sender, EventArgs e)
        {
            CancelOperation();
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// Update form state based on current data
        /// </summary>
        private void UpdateFormState()
        {
            try
            {
                // Update MenuRibbon state
                if (menuRibbon1 != null)
                {
                    menuRibbon1.HasUnsavedChanges = _hasUnsavedChanges;
                }

                // Enable/disable save button based on validation
                bool isValid = ValidateForm();
                menuRibbon1.BarButtonItemSave.Enabled = isValid && _hasUnsavedChanges;

                // Update form title
                this.Text = $"ProManage - Create New Role{(_hasUnsavedChanges ? " *" : "")}";
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error updating form state: {ex.Message}");
            }
        }

        /// <summary>
        /// Validate form data
        /// </summary>
        /// <returns>True if form is valid</returns>
        private bool ValidateForm()
        {
            try
            {
                // Role name is required
                if (string.IsNullOrWhiteSpace(txtRoleName.Text))
                {
                    return false;
                }

                // Role name should be reasonable length
                if (txtRoleName.Text.Trim().Length < 2)
                {
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error validating form: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Save the role
        /// </summary>
        private void SaveRole()
        {
            try
            {
                // Validate form
                if (!ValidateForm())
                {
                    XtraMessageBox.Show("Please enter a valid role name (minimum 2 characters).", "Validation Error",
                        MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    txtRoleName.Focus();
                    return;
                }

                // TODO: Implement actual save logic here
                // For now, just show success message
                XtraMessageBox.Show($"Role '{txtRoleName.Text.Trim()}' created successfully!", "Success",
                    MessageBoxButtons.OK, MessageBoxIcon.Information);

                // Reset form state
                _hasUnsavedChanges = false;
                UpdateFormState();

                // Close the form
                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error saving role: {ex.Message}");
                XtraMessageBox.Show($"Error saving role: {ex.Message}", "Save Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Cancel the operation
        /// </summary>
        private void CancelOperation()
        {
            try
            {
                // Check for unsaved changes
                if (_hasUnsavedChanges)
                {
                    var result = XtraMessageBox.Show("You have unsaved changes. Are you sure you want to cancel?",
                        "Unsaved Changes", MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                    if (result != DialogResult.Yes)
                    {
                        return;
                    }
                }

                this.DialogResult = DialogResult.Cancel;
                this.Close();
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error canceling operation: {ex.Message}");
            }
        }

        #endregion
    }
}
