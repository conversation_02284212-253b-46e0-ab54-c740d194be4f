-- Database-Verification-Commands.sql
-- SQL commands to verify and fix database structure for permission system
-- PostgreSQL syntax for ProManage application

-- ============================================================================
-- VERIFICATION COMMANDS
-- ============================================================================

-- [VerifyTableStructure] --
-- Verify all permission-related tables exist with correct columns
SELECT 
    table_name,
    column_name,
    data_type,
    is_nullable,
    column_default
FROM information_schema.columns 
WHERE table_name IN ('users', 'roles', 'user_permissions', 'role_permissions', 'global_permissions')
ORDER BY table_name, ordinal_position;
-- [End] --

-- [CheckDataCounts] --
-- Check record counts in all permission tables
SELECT 'users' as table_name, COUNT(*) as record_count FROM users
UNION ALL
SELECT 'roles' as table_name, COUNT(*) as record_count FROM roles
UNION ALL
SELECT 'user_permissions' as table_name, COUNT(*) as record_count FROM user_permissions
UNION ALL
SELECT 'role_permissions' as table_name, COUNT(*) as record_count FROM role_permissions
UNION ALL
SELECT 'global_permissions' as table_name, COUNT(*) as record_count FROM global_permissions;
-- [End] --

-- [VerifyActiveData] --
-- Check active users and roles
SELECT 'Active Users' as data_type, COUNT(*) as count FROM users WHERE is_active = true
UNION ALL
SELECT 'Active Roles' as data_type, COUNT(*) as count FROM roles WHERE is_active = true
UNION ALL
SELECT 'Users with Roles' as data_type, COUNT(*) as count FROM users WHERE role_id IS NOT NULL
UNION ALL
SELECT 'Users without Roles' as data_type, COUNT(*) as count FROM users WHERE role_id IS NULL;
-- [End] --

-- [TestQueries] --
-- Test the exact queries used by the application

-- Test GetAllRoles query
SELECT role_id, role_name, description, is_active, created_date, updated_date 
FROM roles 
WHERE is_active = true 
ORDER BY role_name;

-- Test GetAllUsers query
SELECT u.user_id, u.username, u.full_name, u.role_id, r.role_name, u.is_active
FROM users u
LEFT JOIN roles r ON u.role_id = r.role_id
WHERE u.is_active = true
ORDER BY u.username;

-- Test GetRolePermissions query (using first active role)
SELECT perm_id, role_id, form_name, read_permission, new_permission, 
       edit_permission, delete_permission, print_permission, created_date
FROM role_permissions 
WHERE role_id = (SELECT role_id FROM roles WHERE is_active = true ORDER BY role_id LIMIT 1)
ORDER BY form_name;

-- Test GetUserPermissions query (using first active user)
SELECT perm_id, user_id, form_name, read_permission, new_permission,
       edit_permission, delete_permission, print_permission, created_date
FROM user_permissions
WHERE user_id = (SELECT user_id FROM users WHERE is_active = true ORDER BY user_id LIMIT 1)
ORDER BY form_name;

-- Test GetGlobalPermissions query (using first active user)
SELECT perm_id, user_id, can_create_users, can_edit_users,
       can_delete_users, can_print_users, created_date
FROM global_permissions
WHERE user_id = (SELECT user_id FROM users WHERE is_active = true ORDER BY user_id LIMIT 1);
-- [End] --

-- ============================================================================
-- DIAGNOSTIC COMMANDS
-- ============================================================================

-- [DiagnosePermissionIssues] --
-- Identify common permission system issues

-- Check for users without roles
SELECT user_id, username, full_name, 'No role assigned' as issue
FROM users 
WHERE role_id IS NULL AND is_active = true;

-- Check for users with invalid roles
SELECT u.user_id, u.username, u.role_id, 'Invalid role ID' as issue
FROM users u
LEFT JOIN roles r ON u.role_id = r.role_id
WHERE u.role_id IS NOT NULL AND r.role_id IS NULL AND u.is_active = true;

-- Check for users with inactive roles
SELECT u.user_id, u.username, r.role_name, 'Inactive role' as issue
FROM users u
INNER JOIN roles r ON u.role_id = r.role_id
WHERE r.is_active = false AND u.is_active = true;

-- Check for orphaned permissions
SELECT 'role_permissions' as table_name, COUNT(*) as orphaned_count
FROM role_permissions rp
LEFT JOIN roles r ON rp.role_id = r.role_id
WHERE r.role_id IS NULL
UNION ALL
SELECT 'user_permissions' as table_name, COUNT(*) as orphaned_count
FROM user_permissions up
LEFT JOIN users u ON up.user_id = u.user_id
WHERE u.user_id IS NULL
UNION ALL
SELECT 'global_permissions' as table_name, COUNT(*) as orphaned_count
FROM global_permissions gp
LEFT JOIN users u ON gp.user_id = u.user_id
WHERE u.user_id IS NULL;
-- [End] --

-- ============================================================================
-- CLEANUP COMMANDS (USE WITH CAUTION)
-- ============================================================================

-- [CleanupOrphanedRecords] --
-- Remove orphaned permission records
-- WARNING: This will permanently delete data. Use with caution!

-- Remove orphaned role permissions
-- DELETE FROM role_permissions 
-- WHERE role_id NOT IN (SELECT role_id FROM roles);

-- Remove orphaned user permissions
-- DELETE FROM user_permissions 
-- WHERE user_id NOT IN (SELECT user_id FROM users);

-- Remove orphaned global permissions
-- DELETE FROM global_permissions 
-- WHERE user_id NOT IN (SELECT user_id FROM users);
-- [End] --

-- ============================================================================
-- SAMPLE DATA VERIFICATION
-- ============================================================================

-- [ViewSampleData] --
-- View sample data from each table

-- Sample users
SELECT user_id, username, full_name, role_id, is_active
FROM users 
ORDER BY user_id 
LIMIT 5;

-- Sample roles
SELECT role_id, role_name, description, is_active
FROM roles 
ORDER BY role_id 
LIMIT 5;

-- Sample role permissions
SELECT rp.role_id, r.role_name, rp.form_name, rp.read_permission, rp.new_permission
FROM role_permissions rp
INNER JOIN roles r ON rp.role_id = r.role_id
ORDER BY rp.role_id, rp.form_name
LIMIT 10;

-- Sample user permissions (if any)
SELECT up.user_id, u.username, up.form_name, up.read_permission, up.new_permission
FROM user_permissions up
INNER JOIN users u ON up.user_id = u.user_id
ORDER BY up.user_id, up.form_name
LIMIT 10;

-- Sample global permissions (if any)
SELECT gp.user_id, u.username, gp.can_create_users, gp.can_edit_users, gp.can_delete_users
FROM global_permissions gp
INNER JOIN users u ON gp.user_id = u.user_id
ORDER BY gp.user_id
LIMIT 5;
-- [End] --

-- ============================================================================
-- PERFORMANCE CHECK
-- ============================================================================

-- [CheckPerformance] --
-- Check query performance for main permission queries

EXPLAIN ANALYZE
SELECT role_id, role_name, description, is_active, created_date, updated_date 
FROM roles 
WHERE is_active = true 
ORDER BY role_name;

EXPLAIN ANALYZE
SELECT u.user_id, u.username, u.full_name, u.role_id, r.role_name, u.is_active
FROM users u
LEFT JOIN roles r ON u.role_id = r.role_id
WHERE u.is_active = true
ORDER BY u.username;
-- [End] --
