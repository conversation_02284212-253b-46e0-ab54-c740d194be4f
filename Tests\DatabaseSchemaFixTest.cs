using System;
using System.Diagnostics;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using ProManage.Modules.Connections;
using ProManage.Modules.Services;
using ProManage.Modules.Models.PermissionManagementForm;

namespace ProManage.Tests
{
    /// <summary>
    /// Test class to verify database schema fixes and basic functionality
    /// </summary>
    [TestClass]
    public class DatabaseSchemaFixTest
    {
        [TestMethod]
        public void DatabaseSchema_InitializeAndVerify_ShouldSucceed()
        {
            // Act
            var result = DatabaseSchemaInitializer.InitializeDatabase();
            
            // Assert
            Assert.IsTrue(result, "Database schema initialization should succeed");
            
            // Get schema status for debugging
            var status = DatabaseSchemaInitializer.GetSchemaStatus();
            Debug.WriteLine("Schema Status:");
            Debug.WriteLine(status);
        }
        
        [TestMethod]
        public void PermissionDatabaseService_BasicOperations_ShouldWork()
        {
            // Arrange
            DatabaseSchemaInitializer.InitializeDatabase();
            
            // Test getting roles (should not throw exception)
            var roles = PermissionDatabaseService.GetAllRoles();
            Assert.IsNotNull(roles, "GetAllRoles should return a list (even if empty)");
            
            // Test getting user permissions for non-existent user (should not throw exception)
            var permissions = PermissionDatabaseService.GetUserPermissions(99999);
            Assert.IsNotNull(permissions, "GetUserPermissions should return a list (even if empty)");
            
            // Test getting global permissions for non-existent user (should return null)
            var globalPermissions = PermissionDatabaseService.GetGlobalPermissions(99999);
            // This should return null without throwing an exception
        }
        
        [TestMethod]
        public void PermissionService_BasicPermissionCheck_ShouldWork()
        {
            // Arrange
            DatabaseSchemaInitializer.InitializeDatabase();
            
            // Act - Check permission for non-existent user/form (should return false, not throw)
            var hasPermission = PermissionService.HasPermission(99999, "NonExistentForm", PermissionType.Read);
            
            // Assert
            Assert.IsFalse(hasPermission, "Non-existent user should not have permissions");
        }
        
        [TestMethod]
        public void CreateRole_WithValidData_ShouldSucceed()
        {
            // Arrange
            DatabaseSchemaInitializer.InitializeDatabase();
            
            var roleRequest = new RoleCreateRequest
            {
                RoleName = "TestRole_" + Guid.NewGuid().ToString("N").Substring(0, 8),
                Description = "Test role for schema verification",
                IsActive = true
            };
            
            // Act
            var roleId = PermissionDatabaseService.CreateRole(roleRequest);
            
            // Assert
            Assert.IsTrue(roleId > 0, "Role creation should return a valid role ID");
            
            // Cleanup
            try
            {
                PermissionDatabaseService.DeleteRole(roleId);
            }
            catch
            {
                // Cleanup failure is not critical for this test
            }
        }
        
        [TestMethod]
        public void UpdateUserPermissions_WithValidData_ShouldSucceed()
        {
            // Arrange
            DatabaseSchemaInitializer.InitializeDatabase();
            
            // Create a test user first
            const int testUserId = 99998;
            CreateTestUser(testUserId);
            
            try
            {
                var updates = new System.Collections.Generic.List<UserPermissionUpdate>
                {
                    new UserPermissionUpdate
                    {
                        UserId = testUserId,
                        FormName = "TestForm",
                        ReadPermission = true,
                        NewPermission = false,
                        EditPermission = true,
                        DeletePermission = false,
                        PrintPermission = true
                    }
                };
                
                // Act
                var result = PermissionDatabaseService.UpdateUserPermissions(updates);
                
                // Assert
                Assert.IsTrue(result, "User permission update should succeed");
            }
            finally
            {
                // Cleanup
                CleanupTestUser(testUserId);
            }
        }
        
        private void CreateTestUser(int userId)
        {
            try
            {
                using (var connection = DatabaseConnectionManager.Instance.CreateNewConnection())
                {
                    connection.Open();
                    
                    const string query = @"
                        INSERT INTO users (user_id, username, full_name, email, password_hash, is_active, created_date)
                        VALUES (@userId, @username, @fullName, @email, @passwordHash, @isActive, @createdDate)
                        ON CONFLICT (user_id) DO NOTHING";
                    
                    using (var command = new Npgsql.NpgsqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@userId", userId);
                        command.Parameters.AddWithValue("@username", $"testuser{userId}");
                        command.Parameters.AddWithValue("@fullName", $"Test User {userId}");
                        command.Parameters.AddWithValue("@email", $"testuser{userId}@test.com");
                        command.Parameters.AddWithValue("@passwordHash", "test_hash");
                        command.Parameters.AddWithValue("@isActive", true);
                        command.Parameters.AddWithValue("@createdDate", DateTime.Now);
                        
                        command.ExecuteNonQuery();
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error creating test user: {ex.Message}");
            }
        }
        
        private void CleanupTestUser(int userId)
        {
            try
            {
                // Remove permissions first
                PermissionDatabaseService.RemoveUserPermissions(userId);
                PermissionDatabaseService.RemoveGlobalPermissions(userId);
                
                // Remove user
                using (var connection = DatabaseConnectionManager.Instance.CreateNewConnection())
                {
                    connection.Open();
                    
                    const string query = "DELETE FROM users WHERE user_id = @userId";
                    using (var command = new Npgsql.NpgsqlCommand(query, connection))
                    {
                        command.Parameters.AddWithValue("@userId", userId);
                        command.ExecuteNonQuery();
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error cleaning up test user: {ex.Message}");
            }
        }
    }
}
