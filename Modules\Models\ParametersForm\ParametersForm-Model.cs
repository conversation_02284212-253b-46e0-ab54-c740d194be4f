// ParametersForm Model - Data model for Parameters management
// Usage: Represents parameter data structure for database operations

using System;
using System.ComponentModel.DataAnnotations;

namespace ProManage.Modules.Models.ParametersForm
{
    /// <summary>
    /// Model representing a parameter record in the parameters table
    /// </summary>
    public class ParametersFormModel
    {
        /// <summary>
        /// Primary key - auto-generated ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// Parameter code - unique identifier for the parameter (max 100 chars)
        /// </summary>
        [Required]
        [StringLength(100)]
        public string ParameterCode { get; set; }

        /// <summary>
        /// Parameter value - the actual value of the parameter (max 255 chars)
        /// </summary>
        [Required]
        [StringLength(255)]
        public string ParameterValue { get; set; }

        /// <summary>
        /// Parameter type - indicates the data type of the parameter value
        /// </summary>
        [Required]
        public ParameterType ParameterType { get; set; } = ParameterType.String;

        /// <summary>
        /// Purpose - description of what this parameter is used for (max 255 chars)
        /// </summary>
        [StringLength(255)]
        public string Purpose { get; set; }

        /// <summary>
        /// Created timestamp - when the parameter was first created
        /// </summary>
        public DateTime CreatedAt { get; set; }

        /// <summary>
        /// Modified timestamp - when the parameter was last updated
        /// </summary>
        public DateTime? ModifiedAt { get; set; }

        /// <summary>
        /// Constructor for new parameters
        /// </summary>
        public ParametersFormModel()
        {
            CreatedAt = DateTime.Now;
        }

        /// <summary>
        /// Constructor with basic required fields
        /// </summary>
        /// <param name="parameterCode">The parameter code</param>
        /// <param name="parameterValue">The parameter value</param>
        /// <param name="purpose">Optional purpose description</param>
        /// <param name="parameterType">The parameter type (defaults to String)</param>
        public ParametersFormModel(string parameterCode, string parameterValue, string purpose = null, ParameterType parameterType = ParameterType.String)
        {
            ParameterCode = parameterCode;
            ParameterValue = parameterValue;
            Purpose = purpose;
            ParameterType = parameterType;
            CreatedAt = DateTime.Now;
        }

        /// <summary>
        /// Validates the model data
        /// </summary>
        /// <returns>True if valid, false otherwise</returns>
        public bool IsValid()
        {
            // Basic field validation
            if (string.IsNullOrWhiteSpace(ParameterCode) ||
                string.IsNullOrWhiteSpace(ParameterValue) ||
                ParameterCode.Length > 100 ||
                ParameterValue.Length > 255 ||
                (!string.IsNullOrEmpty(Purpose) && Purpose.Length > 255))
            {
                return false;
            }

            // Validate parameter value against its type
            return ParameterTypeHelper.ValidateValue(ParameterValue, ParameterType, out _);
        }

        /// <summary>
        /// Validates the model data and returns validation error message if invalid
        /// </summary>
        /// <param name="errorMessage">Error message if validation fails</param>
        /// <returns>True if valid, false otherwise</returns>
        public bool IsValid(out string errorMessage)
        {
            errorMessage = null;

            // Basic field validation
            if (string.IsNullOrWhiteSpace(ParameterCode))
            {
                errorMessage = "Parameter code is required";
                return false;
            }

            if (string.IsNullOrWhiteSpace(ParameterValue))
            {
                errorMessage = "Parameter value is required";
                return false;
            }

            if (ParameterCode.Length > 100)
            {
                errorMessage = "Parameter code cannot exceed 100 characters";
                return false;
            }

            if (ParameterValue.Length > 255)
            {
                errorMessage = "Parameter value cannot exceed 255 characters";
                return false;
            }

            if (!string.IsNullOrEmpty(Purpose) && Purpose.Length > 255)
            {
                errorMessage = "Purpose cannot exceed 255 characters";
                return false;
            }

            // Validate parameter value against its type
            if (!ParameterTypeHelper.ValidateValue(ParameterValue, ParameterType, out string typeError))
            {
                errorMessage = $"Parameter value is not valid for type {ParameterTypeHelper.GetDisplayName(ParameterType)}: {typeError}";
                return false;
            }

            return true;
        }

        /// <summary>
        /// Returns a string representation of the parameter
        /// </summary>
        /// <returns>String representation</returns>
        public override string ToString()
        {
            return $"Parameter: {ParameterCode} = {ParameterValue}";
        }

        /// <summary>
        /// Creates a copy of this parameter model
        /// </summary>
        /// <returns>A new instance with the same values</returns>
        public ParametersFormModel Clone()
        {
            return new ParametersFormModel
            {
                Id = this.Id,
                ParameterCode = this.ParameterCode,
                ParameterValue = this.ParameterValue,
                ParameterType = this.ParameterType,
                Purpose = this.Purpose,
                CreatedAt = this.CreatedAt,
                ModifiedAt = this.ModifiedAt
            };
        }
    }
}
