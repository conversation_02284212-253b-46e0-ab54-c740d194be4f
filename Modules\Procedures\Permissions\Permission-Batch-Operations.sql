-- Permission Batch Operations for ProManage RBAC System
-- PostgreSQL stored procedures for batch permission operations
-- Created for Task 07: Permission Database Operations

-- Function to update role permissions in batch using JSON
CREATE OR REPLACE FUNCTION sp_update_role_permissions_batch(
    p_role_id INTEGER,
    p_permissions_json JSONB
)
RETURNS INTEGER AS $$
DECLARE
    permission_record RECORD;
    updated_count INTEGER := 0;
BEGIN
    -- Validate role exists
    IF NOT EXISTS (SELECT 1 FROM roles WHERE role_id = p_role_id) THEN
        RAISE EXCEPTION 'Role with ID % does not exist', p_role_id;
    END IF;

    -- Loop through permissions in JSON array
    FOR permission_record IN 
        SELECT 
            (perm->>'form_name')::TEXT as form_name,
            (perm->>'read_permission')::BOOLEAN as read_permission,
            (perm->>'new_permission')::BOOLEAN as new_permission,
            (perm->>'edit_permission')::BOOLEAN as edit_permission,
            (perm->>'delete_permission')::BOOLEAN as delete_permission,
            (perm->>'print_permission')::BOOLEAN as print_permission
        FROM jsonb_array_elements(p_permissions_json) as perm
    LOOP
        -- Update the permission record
        UPDATE role_permissions 
        SET 
            read_permission = permission_record.read_permission,
            new_permission = permission_record.new_permission,
            edit_permission = permission_record.edit_permission,
            delete_permission = permission_record.delete_permission,
            print_permission = permission_record.print_permission
        WHERE role_id = p_role_id 
        AND form_name = permission_record.form_name;
        
        -- Check if update was successful
        IF FOUND THEN
            updated_count := updated_count + 1;
        END IF;
    END LOOP;

    RETURN updated_count;
EXCEPTION
    WHEN OTHERS THEN
        RAISE EXCEPTION 'Error updating role permissions: %', SQLERRM;
END;
$$ LANGUAGE plpgsql;

-- Function to update user permissions in batch using JSON
CREATE OR REPLACE FUNCTION sp_update_user_permissions_batch(
    p_user_id INTEGER,
    p_permissions_json JSONB
)
RETURNS INTEGER AS $$
DECLARE
    permission_record RECORD;
    updated_count INTEGER := 0;
BEGIN
    -- Validate user exists
    IF NOT EXISTS (SELECT 1 FROM users WHERE user_id = p_user_id) THEN
        RAISE EXCEPTION 'User with ID % does not exist', p_user_id;
    END IF;

    -- Loop through permissions in JSON array
    FOR permission_record IN 
        SELECT 
            (perm->>'form_name')::TEXT as form_name,
            CASE WHEN perm->>'read_permission' = 'null' THEN NULL 
                 ELSE (perm->>'read_permission')::BOOLEAN END as read_permission,
            CASE WHEN perm->>'new_permission' = 'null' THEN NULL 
                 ELSE (perm->>'new_permission')::BOOLEAN END as new_permission,
            CASE WHEN perm->>'edit_permission' = 'null' THEN NULL 
                 ELSE (perm->>'edit_permission')::BOOLEAN END as edit_permission,
            CASE WHEN perm->>'delete_permission' = 'null' THEN NULL 
                 ELSE (perm->>'delete_permission')::BOOLEAN END as delete_permission,
            CASE WHEN perm->>'print_permission' = 'null' THEN NULL 
                 ELSE (perm->>'print_permission')::BOOLEAN END as print_permission
        FROM jsonb_array_elements(p_permissions_json) as perm
    LOOP
        -- Insert or update user permission override
        INSERT INTO user_permissions (
            user_id, form_name, read_permission, new_permission, 
            edit_permission, delete_permission, print_permission
        )
        VALUES (
            p_user_id, permission_record.form_name, 
            permission_record.read_permission, permission_record.new_permission,
            permission_record.edit_permission, permission_record.delete_permission,
            permission_record.print_permission
        )
        ON CONFLICT (user_id, form_name)
        DO UPDATE SET
            read_permission = permission_record.read_permission,
            new_permission = permission_record.new_permission,
            edit_permission = permission_record.edit_permission,
            delete_permission = permission_record.delete_permission,
            print_permission = permission_record.print_permission;
        
        updated_count := updated_count + 1;
    END LOOP;

    RETURN updated_count;
EXCEPTION
    WHEN OTHERS THEN
        RAISE EXCEPTION 'Error updating user permissions: %', SQLERRM;
END;
$$ LANGUAGE plpgsql;

-- Function to copy all permissions from one role to another
CREATE OR REPLACE FUNCTION sp_copy_role_permissions(
    p_source_role_id INTEGER,
    p_target_role_id INTEGER
)
RETURNS INTEGER AS $$
DECLARE
    copied_count INTEGER := 0;
BEGIN
    -- Validate both roles exist
    IF NOT EXISTS (SELECT 1 FROM roles WHERE role_id = p_source_role_id) THEN
        RAISE EXCEPTION 'Source role with ID % does not exist', p_source_role_id;
    END IF;
    
    IF NOT EXISTS (SELECT 1 FROM roles WHERE role_id = p_target_role_id) THEN
        RAISE EXCEPTION 'Target role with ID % does not exist', p_target_role_id;
    END IF;

    -- Delete existing permissions for target role
    DELETE FROM role_permissions WHERE role_id = p_target_role_id;

    -- Copy permissions from source to target role
    INSERT INTO role_permissions (
        role_id, form_name, read_permission, new_permission, 
        edit_permission, delete_permission, print_permission
    )
    SELECT 
        p_target_role_id, form_name, read_permission, new_permission,
        edit_permission, delete_permission, print_permission
    FROM role_permissions
    WHERE role_id = p_source_role_id;

    GET DIAGNOSTICS copied_count = ROW_COUNT;
    
    RETURN copied_count;
EXCEPTION
    WHEN OTHERS THEN
        RAISE EXCEPTION 'Error copying role permissions: %', SQLERRM;
END;
$$ LANGUAGE plpgsql;

-- Function to reset user permissions to role defaults
CREATE OR REPLACE FUNCTION sp_reset_user_to_role_permissions(
    p_user_id INTEGER
)
RETURNS INTEGER AS $$
DECLARE
    deleted_count INTEGER := 0;
BEGIN
    -- Validate user exists
    IF NOT EXISTS (SELECT 1 FROM users WHERE user_id = p_user_id) THEN
        RAISE EXCEPTION 'User with ID % does not exist', p_user_id;
    END IF;

    -- Delete all user permission overrides
    DELETE FROM user_permissions WHERE user_id = p_user_id;
    
    GET DIAGNOSTICS deleted_count = ROW_COUNT;
    
    RETURN deleted_count;
EXCEPTION
    WHEN OTHERS THEN
        RAISE EXCEPTION 'Error resetting user permissions: %', SQLERRM;
END;
$$ LANGUAGE plpgsql;

-- Function to get permission statistics
CREATE OR REPLACE FUNCTION sp_get_permission_statistics()
RETURNS TABLE (
    active_roles INTEGER,
    active_users INTEGER,
    forms_in_system INTEGER,
    user_overrides INTEGER,
    users_with_global_permissions INTEGER
) AS $$
BEGIN
    RETURN QUERY
    SELECT 
        (SELECT COUNT(*)::INTEGER FROM roles WHERE is_active = true),
        (SELECT COUNT(*)::INTEGER FROM users WHERE is_active = true),
        (SELECT COUNT(DISTINCT form_name)::INTEGER FROM role_permissions),
        (SELECT COUNT(*)::INTEGER FROM user_permissions),
        (SELECT COUNT(*)::INTEGER FROM global_permissions);
END;
$$ LANGUAGE plpgsql;

-- Function to validate permission system integrity
CREATE OR REPLACE FUNCTION sp_validate_permission_integrity()
RETURNS TABLE (
    issue_type TEXT,
    issue_description TEXT,
    affected_count INTEGER
) AS $$
BEGIN
    -- Check for users without roles
    RETURN QUERY
    SELECT 
        'USERS_WITHOUT_ROLES'::TEXT,
        'Users without assigned roles'::TEXT,
        COUNT(*)::INTEGER
    FROM users u
    WHERE u.is_active = true 
    AND (u.role_id IS NULL OR NOT EXISTS (SELECT 1 FROM roles r WHERE r.role_id = u.role_id AND r.is_active = true))
    HAVING COUNT(*) > 0;

    -- Check for roles without permissions
    RETURN QUERY
    SELECT 
        'ROLES_WITHOUT_PERMISSIONS'::TEXT,
        'Active roles without any permissions'::TEXT,
        COUNT(*)::INTEGER
    FROM roles r
    WHERE r.is_active = true 
    AND NOT EXISTS (SELECT 1 FROM role_permissions rp WHERE rp.role_id = r.role_id)
    HAVING COUNT(*) > 0;

    -- Check for orphaned user permissions
    RETURN QUERY
    SELECT 
        'ORPHANED_USER_PERMISSIONS'::TEXT,
        'User permissions for non-existent forms'::TEXT,
        COUNT(*)::INTEGER
    FROM user_permissions up
    WHERE NOT EXISTS (
        SELECT 1 FROM role_permissions rp 
        WHERE rp.form_name = up.form_name
    )
    HAVING COUNT(*) > 0;

    -- Check for orphaned global permissions
    RETURN QUERY
    SELECT 
        'ORPHANED_GLOBAL_PERMISSIONS'::TEXT,
        'Global permissions for inactive users'::TEXT,
        COUNT(*)::INTEGER
    FROM global_permissions gp
    WHERE NOT EXISTS (
        SELECT 1 FROM users u 
        WHERE u.user_id = gp.user_id AND u.is_active = true
    )
    HAVING COUNT(*) > 0;
END;
$$ LANGUAGE plpgsql;

-- Function to cleanup orphaned permissions
CREATE OR REPLACE FUNCTION sp_cleanup_orphaned_permissions()
RETURNS TABLE (
    cleanup_type TEXT,
    cleaned_count INTEGER
) AS $$
DECLARE
    user_perms_cleaned INTEGER := 0;
    global_perms_cleaned INTEGER := 0;
BEGIN
    -- Clean orphaned user permissions
    DELETE FROM user_permissions up
    WHERE NOT EXISTS (
        SELECT 1 FROM role_permissions rp 
        WHERE rp.form_name = up.form_name
    );
    GET DIAGNOSTICS user_perms_cleaned = ROW_COUNT;

    -- Clean orphaned global permissions
    DELETE FROM global_permissions gp
    WHERE NOT EXISTS (
        SELECT 1 FROM users u 
        WHERE u.user_id = gp.user_id AND u.is_active = true
    );
    GET DIAGNOSTICS global_perms_cleaned = ROW_COUNT;

    -- Return results
    RETURN QUERY VALUES 
        ('USER_PERMISSIONS'::TEXT, user_perms_cleaned),
        ('GLOBAL_PERMISSIONS'::TEXT, global_perms_cleaned);
END;
$$ LANGUAGE plpgsql;
