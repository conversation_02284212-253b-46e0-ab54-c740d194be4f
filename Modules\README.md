# Modules Folder Organization

This document explains the organization of the Modules folder in the ProManage project.

## Folder Structure

The Modules folder contains the following subfolders:

- **Data**: Contains data access classes and database-related functionality
- **EventHandlers**: Contains event handler classes for forms
- **Helpers**: Contains utility and helper classes
- **Models**: Contains data model classes
- **Procedures**: Contains SQL query files
- **Reports**: Contains reporting functionality
- **UI**: Contains UI-related functionality
- **Validation**: Contains validation-related functionality

## Namespace Structure

All code in the Modules folder uses the following namespace structure:

```csharp
namespace ProManage.Modules.[SubfolderName]
{
    // Code here
}
```

For example:
- `ProManage.Modules.Data`
- `ProManage.Modules.Helpers`
- `ProManage.Modules.Models`

## SQL Query Files

SQL query files are stored in the Modules/Procedures folder, organized by module:

- Modules/Procedures/Estimate
- Modules/Procedures/SQLQuery
- Modules/Procedures/Customer
- Modules/Procedures/Product

These files are automatically copied to the output directory during the build process.

## Best Practices

When adding new code to the project:

1. Place it in the appropriate subfolder under Modules
2. Use the correct namespace (`ProManage.Modules.[SubfolderName]`)
3. Follow the naming conventions established for each subfolder
4. Document the purpose and relationships of each file
