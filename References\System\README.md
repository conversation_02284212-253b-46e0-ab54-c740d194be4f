# System Dependencies

This folder contains system-level .NET Framework dependencies used by the ProManage application.

## Components

These system components provide essential functionality:

- System.Buffers: Efficient buffer management
- System.Memory: Memory management utilities
- System.Numerics.Vectors: SIMD-enabled vector operations
- System.Diagnostics.DiagnosticSource: Diagnostic instrumentation
- System.Runtime.CompilerServices.Unsafe: Low-level memory operations

## Organization

Components are organized by their package name and version.

## Reference Management

All assemblies are referenced in the project via NuGet package references in the packages.config file, with assembly references in the project file pointing to the packages folder.