using System;
using System.Collections.Generic;

namespace ProManage.Modules.Services
{
    /// <summary>
    /// Result model for form synchronization operations between file system and database.
    /// Provides detailed information about forms added, removed, and any errors encountered.
    /// </summary>
    public class FormSyncResult
    {
        /// <summary>
        /// Indicates whether the synchronization operation was successful
        /// </summary>
        public bool Success { get; set; }

        /// <summary>
        /// List of form names that were added to the permission system
        /// </summary>
        public List<string> FormsAdded { get; set; } = new List<string>();

        /// <summary>
        /// List of form names that were removed from the permission system
        /// </summary>
        public List<string> FormsRemoved { get; set; } = new List<string>();

        /// <summary>
        /// List of error messages encountered during synchronization
        /// </summary>
        public List<string> Errors { get; set; } = new List<string>();

        /// <summary>
        /// Timestamp when the synchronization was performed
        /// </summary>
        public DateTime SyncTime { get; set; } = DateTime.Now;

        /// <summary>
        /// Indicates whether any changes were made during synchronization
        /// </summary>
        public bool HasChanges => FormsAdded.Count > 0 || FormsRemoved.Count > 0;

        /// <summary>
        /// Gets a human-readable summary of the synchronization results
        /// </summary>
        /// <returns>Summary string describing the sync operation results</returns>
        public string GetSummary()
        {
            if (!Success)
                return $"Sync failed: {string.Join(", ", Errors)}";

            if (!HasChanges)
                return "No changes detected";

            var summary = new List<string>();
            if (FormsAdded.Count > 0)
                summary.Add($"Added {FormsAdded.Count} forms");
            if (FormsRemoved.Count > 0)
                summary.Add($"Removed {FormsRemoved.Count} forms");

            return string.Join(", ", summary);
        }

        /// <summary>
        /// Gets detailed information about the sync operation
        /// </summary>
        /// <returns>Detailed sync information</returns>
        public string GetDetailedSummary()
        {
            var details = new List<string>();

            details.Add($"Sync Time: {SyncTime:yyyy-MM-dd HH:mm:ss}");
            details.Add($"Success: {Success}");

            if (FormsAdded.Count > 0)
            {
                details.Add($"Forms Added ({FormsAdded.Count}):");
                foreach (var form in FormsAdded)
                {
                    details.Add($"  + {form}");
                }
            }

            if (FormsRemoved.Count > 0)
            {
                details.Add($"Forms Removed ({FormsRemoved.Count}):");
                foreach (var form in FormsRemoved)
                {
                    details.Add($"  - {form}");
                }
            }

            if (Errors.Count > 0)
            {
                details.Add($"Errors ({Errors.Count}):");
                foreach (var error in Errors)
                {
                    details.Add($"  ! {error}");
                }
            }

            return string.Join(Environment.NewLine, details);
        }
    }
}
