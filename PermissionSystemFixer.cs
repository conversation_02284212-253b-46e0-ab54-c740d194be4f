using System;
using System.Collections.Generic;
using ProManage.Modules.Services;
using ProManage.Modules.Connections;
using ProManage.Modules.Models.PermissionManagementForm;
using Npgsql;

namespace ProManage
{
    /// <summary>
    /// Direct test runner to fix permission system issues
    /// </summary>
    public class PermissionSystemFixer
    {
        private const int TestUserId = 999;
        private const int TestRoleId = 999;
        private const string TestFormName = "TestForm";

        public static void Main(string[] args)
        {
            Console.WriteLine("ProManage Permission System Fix Test");
            Console.WriteLine("====================================");

            try
            {
                // Step 1: Apply database schema fixes
                Console.WriteLine("\n1. Applying database schema fixes...");
                ApplySchemaFixes();

                // Step 2: Test basic database operations
                Console.WriteLine("\n2. Testing basic database operations...");
                TestBasicDatabaseOperations();

                // Step 3: Test permission system
                Console.WriteLine("\n3. Testing permission system...");
                TestPermissionSystem();

                Console.WriteLine("\n✓ All tests completed successfully!");
            }
            catch (Exception ex)
            {
                Console.WriteLine($"\n✗ Test failed: {ex.Message}");
                if (ex.InnerException != null)
                {
                    Console.WriteLine($"Inner exception: {ex.InnerException.Message}");
                }
            }

            Console.WriteLine("\nPress any key to exit...");
            Console.ReadKey();
        }

        private static void ApplySchemaFixes()
        {
            using (var connection = DatabaseConnectionManager.Instance.CreateNewConnection())
            {
                connection.Open();
                using (var transaction = connection.BeginTransaction())
                {
                    try
                    {
                        // Fix 1: Rename permission_id to user_permission_id if needed
                        Console.WriteLine("  - Checking user_permissions table structure...");
                        var fixUserPermissionsQuery = @"
                            DO $$
                            BEGIN
                                IF EXISTS (
                                    SELECT 1 FROM information_schema.columns 
                                    WHERE table_schema = 'public' 
                                    AND table_name = 'user_permissions' 
                                    AND column_name = 'permission_id'
                                ) AND NOT EXISTS (
                                    SELECT 1 FROM information_schema.columns 
                                    WHERE table_schema = 'public' 
                                    AND table_name = 'user_permissions' 
                                    AND column_name = 'user_permission_id'
                                ) THEN
                                    ALTER TABLE user_permissions RENAME COLUMN permission_id TO user_permission_id;
                                    RAISE NOTICE 'Renamed permission_id to user_permission_id';
                                END IF;
                            END $$;";

                        using (var cmd = new NpgsqlCommand(fixUserPermissionsQuery, connection, transaction))
                        {
                            cmd.ExecuteNonQuery();
                        }

                        // Fix 2: Add password_salt column if missing
                        Console.WriteLine("  - Checking users table for password_salt...");
                        var fixPasswordSaltQuery = @"
                            DO $$
                            BEGIN
                                IF NOT EXISTS (
                                    SELECT 1 FROM information_schema.columns 
                                    WHERE table_schema = 'public' 
                                    AND table_name = 'users' 
                                    AND column_name = 'password_salt'
                                ) THEN
                                    ALTER TABLE users ADD COLUMN password_salt VARCHAR(255);
                                    RAISE NOTICE 'Added password_salt column';
                                END IF;
                            END $$;";

                        using (var cmd = new NpgsqlCommand(fixPasswordSaltQuery, connection, transaction))
                        {
                            cmd.ExecuteNonQuery();
                        }

                        // Fix 3: Add created_date column if missing
                        Console.WriteLine("  - Checking users table for created_date...");
                        var fixCreatedDateQuery = @"
                            DO $$
                            BEGIN
                                IF NOT EXISTS (
                                    SELECT 1 FROM information_schema.columns 
                                    WHERE table_schema = 'public' 
                                    AND table_name = 'users' 
                                    AND column_name = 'created_date'
                                ) THEN
                                    ALTER TABLE users ADD COLUMN created_date TIMESTAMP DEFAULT CURRENT_TIMESTAMP;
                                    RAISE NOTICE 'Added created_date column';
                                END IF;
                            END $$;";

                        using (var cmd = new NpgsqlCommand(fixCreatedDateQuery, connection, transaction))
                        {
                            cmd.ExecuteNonQuery();
                        }

                        transaction.Commit();
                        Console.WriteLine("  ✓ Schema fixes applied successfully");
                    }
                    catch (Exception ex)
                    {
                        transaction.Rollback();
                        throw new Exception($"Schema fix failed: {ex.Message}", ex);
                    }
                }
            }
        }

        private static void TestBasicDatabaseOperations()
        {
            // Test 1: Create test user and role
            Console.WriteLine("  - Creating test user and role...");
            CreateTestData();

            // Test 2: Test database queries
            Console.WriteLine("  - Testing database queries...");
            var permissions = PermissionDatabaseService.GetUserPermissions(TestUserId);
            Console.WriteLine($"    User permissions query: {permissions.Count} records");

            var globalPermissions = PermissionDatabaseService.GetGlobalPermissions(TestUserId);
            Console.WriteLine($"    Global permissions query: {(globalPermissions != null ? "Success" : "No data")}");

            Console.WriteLine("  ✓ Basic database operations working");
        }

        private static void TestPermissionSystem()
        {
            Console.WriteLine("  - Testing role permission...");
            SetupRolePermission(TestRoleId, TestFormName, read: true);
            var hasRolePermission = PermissionService.HasPermission(TestUserId, TestFormName, PermissionType.Read);
            Console.WriteLine($"    Role permission test: {(hasRolePermission ? "✓ PASS" : "✗ FAIL")}");

            Console.WriteLine("  - Testing user override...");
            SetupUserPermission(TestUserId, TestFormName, read: true);
            var hasUserPermission = PermissionService.HasPermission(TestUserId, TestFormName, PermissionType.Read);
            Console.WriteLine($"    User override test: {(hasUserPermission ? "✓ PASS" : "✗ FAIL")}");

            Console.WriteLine("  - Testing global permission...");
            SetupGlobalPermission(TestUserId, canCreateUsers: true);
            var hasGlobalPermission = PermissionService.HasGlobalPermission(TestUserId, GlobalPermissionType.CanCreateUsers);
            Console.WriteLine($"    Global permission test: {(hasGlobalPermission ? "✓ PASS" : "✗ FAIL")}");

            if (hasRolePermission && hasUserPermission && hasGlobalPermission)
            {
                Console.WriteLine("  ✓ Permission system working correctly");
            }
            else
            {
                Console.WriteLine("  ✗ Permission system has issues");
            }
        }

        private static void CreateTestData()
        {
            CreateTestRole();
            CreateTestUser();
        }

        private static void CreateTestRole()
        {
            try
            {
                using (var connection = DatabaseConnectionManager.Instance.CreateNewConnection())
                {
                    connection.Open();

                    const string checkQuery = "SELECT COUNT(*) FROM roles WHERE role_id = @roleId";
                    using (var checkCommand = new NpgsqlCommand(checkQuery, connection))
                    {
                        checkCommand.Parameters.AddWithValue("@roleId", TestRoleId);
                        var count = Convert.ToInt32(checkCommand.ExecuteScalar());

                        if (count == 0)
                        {
                            const string insertQuery = @"
                                INSERT INTO roles (role_id, role_name, description, is_active, created_date)
                                VALUES (@roleId, @roleName, @description, @isActive, @createdDate)";

                            using (var insertCommand = new NpgsqlCommand(insertQuery, connection))
                            {
                                insertCommand.Parameters.AddWithValue("@roleId", TestRoleId);
                                insertCommand.Parameters.AddWithValue("@roleName", $"TestRole_{TestRoleId}");
                                insertCommand.Parameters.AddWithValue("@description", "Test role for unit testing");
                                insertCommand.Parameters.AddWithValue("@isActive", true);
                                insertCommand.Parameters.AddWithValue("@createdDate", DateTime.Now);
                                insertCommand.ExecuteNonQuery();
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"    Warning: Test role creation failed: {ex.Message}");
            }
        }

        private static void CreateTestUser()
        {
            try
            {
                using (var connection = DatabaseConnectionManager.Instance.CreateNewConnection())
                {
                    connection.Open();

                    const string checkQuery = "SELECT COUNT(*) FROM users WHERE user_id = @userId";
                    using (var checkCommand = new NpgsqlCommand(checkQuery, connection))
                    {
                        checkCommand.Parameters.AddWithValue("@userId", TestUserId);
                        var count = Convert.ToInt32(checkCommand.ExecuteScalar());

                        if (count == 0)
                        {
                            const string insertQuery = @"
                                INSERT INTO users (user_id, username, full_name, email, password_hash, password_salt, role_id, is_active, created_date)
                                VALUES (@userId, @username, @fullName, @email, @passwordHash, @passwordSalt, @roleId, @isActive, @createdDate)";

                            using (var insertCommand = new NpgsqlCommand(insertQuery, connection))
                            {
                                insertCommand.Parameters.AddWithValue("@userId", TestUserId);
                                insertCommand.Parameters.AddWithValue("@username", "testuser999");
                                insertCommand.Parameters.AddWithValue("@fullName", "Test User 999");
                                insertCommand.Parameters.AddWithValue("@email", "<EMAIL>");
                                insertCommand.Parameters.AddWithValue("@passwordHash", "test_password_hash");
                                insertCommand.Parameters.AddWithValue("@passwordSalt", "test_salt");
                                insertCommand.Parameters.AddWithValue("@roleId", TestRoleId);
                                insertCommand.Parameters.AddWithValue("@isActive", true);
                                insertCommand.Parameters.AddWithValue("@createdDate", DateTime.Now);
                                insertCommand.ExecuteNonQuery();
                            }
                        }
                        else
                        {
                            // Update existing user to ensure role assignment
                            const string updateQuery = "UPDATE users SET role_id = @roleId WHERE user_id = @userId";
                            using (var updateCommand = new NpgsqlCommand(updateQuery, connection))
                            {
                                updateCommand.Parameters.AddWithValue("@roleId", TestRoleId);
                                updateCommand.Parameters.AddWithValue("@userId", TestUserId);
                                updateCommand.ExecuteNonQuery();
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Console.WriteLine($"    Warning: Test user creation failed: {ex.Message}");
            }
        }

        private static void SetupRolePermission(int roleId, string formName, bool read = false)
        {
            var permission = new RolePermissionUpdate
            {
                RoleId = roleId,
                FormName = formName,
                ReadPermission = read,
                NewPermission = false,
                EditPermission = false,
                DeletePermission = false,
                PrintPermission = false
            };

            PermissionService.UpdateRolePermissions(roleId, new List<RolePermissionUpdate> { permission });
        }

        private static void SetupUserPermission(int userId, string formName, bool? read = null)
        {
            var permission = new UserPermissionUpdate
            {
                UserId = userId,
                FormName = formName,
                ReadPermission = read,
                NewPermission = null,
                EditPermission = null,
                DeletePermission = null,
                PrintPermission = null
            };
            
            PermissionService.UpdateUserPermissions(userId, new List<UserPermissionUpdate> { permission });
        }

        private static void SetupGlobalPermission(int userId, bool canCreateUsers = false)
        {
            var globalPermission = new GlobalPermissionUpdate
            {
                UserId = userId,
                CanCreateUsers = canCreateUsers,
                CanEditUsers = false,
                CanDeleteUsers = false,
                CanPrintUsers = false
            };
            
            PermissionService.UpdateGlobalPermissions(userId, globalPermission);
        }
    }
}
