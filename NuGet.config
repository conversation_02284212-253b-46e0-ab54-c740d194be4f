﻿<?xml version="1.0" encoding="utf-8"?>
<configuration>  <packageSources>
    <add key="nuget.org" value="https://api.nuget.org/v3/index.json" protocolVersion="3" />
    <add key="DevExpress Local" value="D:\DevExpress 24.1\Components\System\Components\Packages" />
    <add key="ProManage Local" value="E:\Users\Faraz\source\repos\ProManage\packages" />
    <!-- Add this source and configure with your DevExpress credentials when you're ready to download the packages -->
    <!-- <add key="DevExpress NuGet" value="https://nuget.devexpress.com/api" /> -->
  </packageSources>
  <packageRestore>
    <add key="enabled" value="True" />
    <add key="automatic" value="True" />
  </packageRestore>  <bindingRedirects>
    <add key="skip" value="False" />
  </bindingRedirects>  <packageManagement>
    <add key="format" value="0" />
    <add key="disabled" value="False" />
  </packageManagement>
</configuration>
