using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Drawing;
using System.Linq;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using ProManage.Modules.Models.PermissionManagementForm;

namespace ProManage.Modules.Components
{
    /// <summary>
    /// Permission summary panel with statistics and overview information
    /// Provides a comprehensive view of permission distribution and statistics
    /// </summary>
    [ToolboxItem(true)]
    [ToolboxBitmap(typeof(PermissionSummaryPanel))]
    [Description("Permission summary panel with statistics")]
    [Category("ProManage Controls")]
    public partial class PermissionSummaryPanel : UserControl
    {
        #region Private Fields

        private GroupControl grpSummary;
        private LabelControl lblTotalForms;
        private LabelControl lblFullAccess;
        private LabelControl lblReadWrite;
        private LabelControl lblReadOnly;
        private LabelControl lblNoAccess;
        private LabelControl lblRolePermissions;
        private LabelControl lblUserOverrides;
        
        private List<EffectivePermission> _permissions;
        private string _summaryTitle = "Permission Summary";

        #endregion

        #region Public Properties

        /// <summary>
        /// Gets or sets the permissions to summarize
        /// </summary>
        [Browsable(false)]
        public List<EffectivePermission> Permissions
        {
            get => _permissions;
            set
            {
                _permissions = value;
                UpdateSummary();
            }
        }

        /// <summary>
        /// Gets or sets the summary panel title
        /// </summary>
        [Category("Appearance")]
        [Description("The title displayed on the summary panel")]
        public string SummaryTitle
        {
            get => _summaryTitle;
            set
            {
                _summaryTitle = value;
                if (grpSummary != null)
                {
                    grpSummary.Text = value;
                }
            }
        }

        #endregion

        #region Events

        /// <summary>
        /// Event fired when the summary is updated
        /// </summary>
        public event EventHandler SummaryUpdated;

        #endregion

        #region Constructor

        public PermissionSummaryPanel()
        {
            InitializeControls();
        }

        #endregion

        #region Initialization

        /// <summary>
        /// Initialize the controls
        /// </summary>
        private void InitializeControls()
        {
            try
            {
                // Set control properties
                this.Size = new Size(250, 200);
                this.BackColor = Color.Transparent;
                
                // Create group control
                grpSummary = new GroupControl();
                grpSummary.Text = _summaryTitle;
                grpSummary.Dock = DockStyle.Fill;
                
                // Create labels
                CreateLabels();
                
                // Add group to control
                this.Controls.Add(grpSummary);
                
                // Initial summary update
                UpdateSummary();
                
                System.Diagnostics.Debug.WriteLine("PermissionSummaryPanel initialized successfully");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error initializing PermissionSummaryPanel: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Create the summary labels
        /// </summary>
        private void CreateLabels()
        {
            try
            {
                int yPos = 25;
                int spacing = 20;
                
                // Total Forms
                lblTotalForms = CreateLabel("Total Forms: 0", yPos);
                yPos += spacing;
                
                // Permission level counts
                lblFullAccess = CreateLabel("Full Access: 0", yPos, Color.Green);
                yPos += spacing;
                
                lblReadWrite = CreateLabel("Read/Write: 0", yPos, Color.Blue);
                yPos += spacing;
                
                lblReadOnly = CreateLabel("Read Only: 0", yPos, Color.Orange);
                yPos += spacing;
                
                lblNoAccess = CreateLabel("No Access: 0", yPos, Color.Red);
                yPos += spacing + 10;
                
                // Source counts
                lblRolePermissions = CreateLabel("From Role: 0", yPos, Color.DarkGreen);
                yPos += spacing;
                
                lblUserOverrides = CreateLabel("User Overrides: 0", yPos, Color.DarkBlue);
                
                // Add all labels to group
                grpSummary.Controls.AddRange(new Control[] {
                    lblTotalForms, lblFullAccess, lblReadWrite, lblReadOnly, 
                    lblNoAccess, lblRolePermissions, lblUserOverrides
                });
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error creating labels: {ex.Message}");
            }
        }

        /// <summary>
        /// Create a label with specified text and position
        /// </summary>
        /// <param name="text">Label text</param>
        /// <param name="yPos">Y position</param>
        /// <param name="color">Text color (optional)</param>
        /// <returns>Created label control</returns>
        private LabelControl CreateLabel(string text, int yPos, Color? color = null)
        {
            var label = new LabelControl();
            label.Text = text;
            label.Location = new Point(10, yPos);
            label.Size = new Size(220, 16);
            label.Appearance.TextOptions.VAlignment = DevExpress.Utils.VertAlignment.Center;
            
            if (color.HasValue)
            {
                label.Appearance.ForeColor = color.Value;
            }
            
            return label;
        }

        #endregion

        #region Public Methods

        /// <summary>
        /// Update the summary with current permissions
        /// </summary>
        public void UpdateSummary()
        {
            try
            {
                if (_permissions == null || _permissions.Count == 0)
                {
                    ClearSummary();
                    return;
                }

                // Calculate statistics
                var stats = CalculateStatistics();
                
                // Update labels
                UpdateLabels(stats);
                
                // Fire event
                SummaryUpdated?.Invoke(this, EventArgs.Empty);
                
                System.Diagnostics.Debug.WriteLine("Permission summary updated successfully");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error updating summary: {ex.Message}");
            }
        }

        /// <summary>
        /// Clear the summary
        /// </summary>
        public void ClearSummary()
        {
            try
            {
                if (lblTotalForms != null) lblTotalForms.Text = "Total Forms: 0";
                if (lblFullAccess != null) lblFullAccess.Text = "Full Access: 0";
                if (lblReadWrite != null) lblReadWrite.Text = "Read/Write: 0";
                if (lblReadOnly != null) lblReadOnly.Text = "Read Only: 0";
                if (lblNoAccess != null) lblNoAccess.Text = "No Access: 0";
                if (lblRolePermissions != null) lblRolePermissions.Text = "From Role: 0";
                if (lblUserOverrides != null) lblUserOverrides.Text = "User Overrides: 0";
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error clearing summary: {ex.Message}");
            }
        }

        /// <summary>
        /// Get summary statistics as a formatted string
        /// </summary>
        /// <returns>Formatted summary string</returns>
        public string GetSummaryText()
        {
            try
            {
                if (_permissions == null || _permissions.Count == 0)
                {
                    return "No permissions to summarize";
                }

                var stats = CalculateStatistics();
                
                return $"Total: {stats.TotalForms}, " +
                       $"Full: {stats.FullAccess}, " +
                       $"R/W: {stats.ReadWrite}, " +
                       $"R/O: {stats.ReadOnly}, " +
                       $"None: {stats.NoAccess}";
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting summary text: {ex.Message}");
                return "Error generating summary";
            }
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// Calculate permission statistics
        /// </summary>
        /// <returns>Permission statistics</returns>
        private PermissionStatistics CalculateStatistics()
        {
            var stats = new PermissionStatistics();
            
            if (_permissions == null) return stats;
            
            stats.TotalForms = _permissions.Count;
            
            foreach (var permission in _permissions)
            {
                // Count permission levels
                if (permission.ReadPermission && permission.NewPermission && 
                    permission.EditPermission && permission.DeletePermission && permission.PrintPermission)
                {
                    stats.FullAccess++;
                }
                else if (permission.ReadPermission && (permission.NewPermission || permission.EditPermission))
                {
                    stats.ReadWrite++;
                }
                else if (permission.ReadPermission)
                {
                    stats.ReadOnly++;
                }
                else
                {
                    stats.NoAccess++;
                }
                
                // Count sources
                switch (permission.Source)
                {
                    case PermissionSource.Role:
                        stats.RolePermissions++;
                        break;
                    case PermissionSource.UserOverride:
                        stats.UserOverrides++;
                        break;
                }
            }
            
            return stats;
        }

        /// <summary>
        /// Update labels with statistics
        /// </summary>
        /// <param name="stats">Permission statistics</param>
        private void UpdateLabels(PermissionStatistics stats)
        {
            try
            {
                if (lblTotalForms != null) lblTotalForms.Text = $"Total Forms: {stats.TotalForms}";
                if (lblFullAccess != null) lblFullAccess.Text = $"Full Access: {stats.FullAccess}";
                if (lblReadWrite != null) lblReadWrite.Text = $"Read/Write: {stats.ReadWrite}";
                if (lblReadOnly != null) lblReadOnly.Text = $"Read Only: {stats.ReadOnly}";
                if (lblNoAccess != null) lblNoAccess.Text = $"No Access: {stats.NoAccess}";
                if (lblRolePermissions != null) lblRolePermissions.Text = $"From Role: {stats.RolePermissions}";
                if (lblUserOverrides != null) lblUserOverrides.Text = $"User Overrides: {stats.UserOverrides}";
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error updating labels: {ex.Message}");
            }
        }

        #endregion
    }

    #region Helper Classes

    /// <summary>
    /// Permission statistics data
    /// </summary>
    public class PermissionStatistics
    {
        public int TotalForms { get; set; }
        public int FullAccess { get; set; }
        public int ReadWrite { get; set; }
        public int ReadOnly { get; set; }
        public int NoAccess { get; set; }
        public int RolePermissions { get; set; }
        public int UserOverrides { get; set; }
    }

    #endregion
}
