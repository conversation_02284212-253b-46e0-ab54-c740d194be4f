## CreateRole_DuplicateName_ThrowsException
   Source: PermissionSystemTests.cs line 126
   Duration: 1.1 sec

  Message: 
Assert.ThrowsException *ailed. Threw exception PostgresException, but exception Exception was expected. 
Exception Message: 23505: duplicate key value violates unique constraint "roles_role_name_key"

DETAIL: Detail redacted as it may contain sensitive data. Speci*y 'Include Error Detail' in the connection string to include this in*ormation.
Stack Trace:    at Npgsql.Internal.NpgsqlConnector.<ReadMessageLong>d__231.MoveNext()
--- End o* stack trace *rom previous location where exception was thrown ---
   at System.Runtime.CompilerServices.TaskAwaiter.ThrowForNonSuccess(Task task)
   at System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNoti*ication(Task task)
   at Npgsql.NpgsqlDataReader.<<ReadMessage>g__ReadMessageSequential|49_0>d.MoveNext()
--- End o* stack trace *rom previous location where exception was thrown ---
   at System.Runtime.CompilerServices.TaskAwaiter.ThrowForNonSuccess(Task task)
   at System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNoti*ication(Task task)
   at System.Threading.Tasks.ValueTask`1.get_Result()
   at Npgsql.NpgsqlDataReader.<NextResult>d__52.MoveNext()
--- End o* stack trace *rom previous location where exception was thrown ---
   at System.Runtime.ExceptionServices.ExceptionDispatchIn*o.Throw()
   at Npgsql.NpgsqlDataReader.<NextResult>d__52.MoveNext()
--- End o* stack trace *rom previous location where exception was thrown ---
   at System.Runtime.CompilerServices.TaskAwaiter.ThrowForNonSuccess(Task task)
   at System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNoti*ication(Task task)
   at Npgsql.NpgsqlDataReader.NextResult()
   at Npgsql.NpgsqlCommand.<ExecuteReader>d__120.MoveNext()
--- End o* stack trace *rom previous location where exception was thrown ---
   at System.Runtime.ExceptionServices.ExceptionDispatchIn*o.Throw()
   at Npgsql.NpgsqlCommand.<ExecuteReader>d__120.MoveNext()
--- End o* stack trace *rom previous location where exception was thrown ---
   at System.Runtime.CompilerServices.TaskAwaiter.ThrowForNonSuccess(Task task)
   at System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNoti*ication(Task task)
   at Npgsql.NpgsqlCommand.<ExecuteScalar>d__111.MoveNext()
--- End o* stack trace *rom previous location where exception was thrown ---
   at System.Runtime.CompilerServices.TaskAwaiter.ThrowForNonSuccess(Task task)
   at System.Runtime.CompilerServices.TaskAwaiter.HandleNonSuccessAndDebuggerNoti*ication(Task task)
   at Npgsql.NpgsqlCommand.ExecuteScalar()
   at ProManage.Modules.Connections.PermissionDatabaseService.CreateRole(Role role) in E:\Users\Faraz\source\repos\ProManage\Modules\Connections\PermissionDatabaseService.cs:line 1244
   at ProManage.Modules.Connections.PermissionDatabaseService.CreateRole(RoleCreateRequest request) in E:\Users\Faraz\source\repos\ProManage\Modules\Connections\PermissionDatabaseService.cs:line 1191
   at ProManage.Tests.PermissionSystemTests.<>c__DisplayClass11_0.<CreateRole_DuplicateName_ThrowsException>b__0() in E:\Users\Faraz\source\repos\ProManage\Tests\PermissionSystemTests.cs:line 135
   at Microso*t.VisualStudio.TestTools.UnitTesting.Assert.<>c__DisplayClass105_0`1.<ThrowsException>b__0() in /_/src/TestFramework/TestFramework/Assertions/Assert.ThrowsException.cs:line 136
   at Microso*t.VisualStudio.TestTools.UnitTesting.Assert.ThrowsException[T](Action action, String message, Object[] parameters) in /_/src/TestFramework/TestFramework/Assertions/Assert.ThrowsException.cs:line 179

  Stack Trace: 
PermissionSystemTests.CreateRole_DuplicateName_ThrowsException() line 135

  Standard Output: 


Debug Trace:
Looking *or Development.con*ig at: E:\Users\Faraz\source\repos\ProManage\bin\Debug\Development.con*ig
Development.con*ig *ound, loading settings *rom it
Loading connection string *rom Development.con*ig: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Timeout=15;
Connection settings loaded *rom Development.con*ig
Built connection string *rom settings: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Maximum Pool Size=100;Minimum Pool Size=1;Connection Li*etime=300;Connection Idle Li*etime=60;Timeout=15;Command Timeout=30;SslMode=Pre*er;Trust Server Certi*icate=true;
Created new NpgsqlConnection object
Connection string set success*ully in NpgsqlConnection
Looking *or Development.con*ig at: E:\Users\Faraz\source\repos\ProManage\bin\Debug\Development.con*ig
Development.con*ig *ound, loading settings *rom it
Loading connection string *rom Development.con*ig: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Timeout=15;
Connection settings loaded *rom Development.con*ig
Creating new connection with connection string: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Maximum Pool Size=100;Minimum Pool Size=1;Connection Li*etime=300;Connection Idle Li*etime=60;Timeout=15;Command Timeout=30;SslMode=Pre*er;Trust Server Certi*icate=true;
New NpgsqlConnection created success*ully
Removed 0 user permission overrides *or user 999
Looking *or Development.con*ig at: E:\Users\Faraz\source\repos\ProManage\bin\Debug\Development.con*ig
Development.con*ig *ound, loading settings *rom it
Loading connection string *rom Development.con*ig: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Timeout=15;
Connection settings loaded *rom Development.con*ig
Creating new connection with connection string: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Maximum Pool Size=100;Minimum Pool Size=1;Connection Li*etime=300;Connection Idle Li*etime=60;Timeout=15;Command Timeout=30;SslMode=Pre*er;Trust Server Certi*icate=true;
New NpgsqlConnection created success*ully
Removed global permissions *or user 999
Looking *or Development.con*ig at: E:\Users\Faraz\source\repos\ProManage\bin\Debug\Development.con*ig
Development.con*ig *ound, loading settings *rom it
Loading connection string *rom Development.con*ig: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Timeout=15;
Connection settings loaded *rom Development.con*ig
Creating new connection with connection string: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Maximum Pool Size=100;Minimum Pool Size=1;Connection Li*etime=300;Connection Idle Li*etime=60;Timeout=15;Command Timeout=30;SslMode=Pre*er;Trust Server Certi*icate=true;
New NpgsqlConnection created success*ully
Role created success*ully with ID: 5
Looking *or Development.con*ig at: E:\Users\Faraz\source\repos\ProManage\bin\Debug\Development.con*ig
Development.con*ig *ound, loading settings *rom it
Loading connection string *rom Development.con*ig: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Timeout=15;
Connection settings loaded *rom Development.con*ig
Creating new connection with connection string: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Maximum Pool Size=100;Minimum Pool Size=1;Connection Li*etime=300;Connection Idle Li*etime=60;Timeout=15;Command Timeout=30;SslMode=Pre*er;Trust Server Certi*icate=true;
New NpgsqlConnection created success*ully
Role created success*ully with ID: 6
Looking *or Development.con*ig at: E:\Users\Faraz\source\repos\ProManage\bin\Debug\Development.con*ig
Development.con*ig *ound, loading settings *rom it
Loading connection string *rom Development.con*ig: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Timeout=15;
Connection settings loaded *rom Development.con*ig
Creating new connection with connection string: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Maximum Pool Size=100;Minimum Pool Size=1;Connection Li*etime=300;Connection Idle Li*etime=60;Timeout=15;Command Timeout=30;SslMode=Pre*er;Trust Server Certi*icate=true;
New NpgsqlConnection created success*ully
Error creating role: 23505: duplicate key value violates unique constraint "roles_role_name_key"

DETAIL: Detail redacted as it may contain sensitive data. Speci*y 'Include Error Detail' in the connection string to include this in*ormation.
Looking *or Development.con*ig at: E:\Users\Faraz\source\repos\ProManage\bin\Debug\Development.con*ig
Development.con*ig *ound, loading settings *rom it
Loading connection string *rom Development.con*ig: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Timeout=15;
Connection settings loaded *rom Development.con*ig
Creating new connection with connection string: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Maximum Pool Size=100;Minimum Pool Size=1;Connection Li*etime=300;Connection Idle Li*etime=60;Timeout=15;Command Timeout=30;SslMode=Pre*er;Trust Server Certi*icate=true;
New NpgsqlConnection created success*ully
Removed 0 user permission overrides *or user 999
Looking *or Development.con*ig at: E:\Users\Faraz\source\repos\ProManage\bin\Debug\Development.con*ig
Development.con*ig *ound, loading settings *rom it
Loading connection string *rom Development.con*ig: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Timeout=15;
Connection settings loaded *rom Development.con*ig
Creating new connection with connection string: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Maximum Pool Size=100;Minimum Pool Size=1;Connection Li*etime=300;Connection Idle Li*etime=60;Timeout=15;Command Timeout=30;SslMode=Pre*er;Trust Server Certi*icate=true;
New NpgsqlConnection created success*ully
Removed global permissions *or user 999

##  GetUserE**ectivePermissions_WithOverrides_ReturnsCorrectPermissions
   Source: PermissionSystemTests.cs line 168
   Duration: 59 ms

  Message: 
Assert.IsTrue *ailed. User override should give read permission

  Stack Trace: 
PermissionSystemTests.GetUserE**ectivePermissions_WithOverrides_ReturnsCorrectPermissions() line 178

  Standard Output: 


Debug Trace:
Looking *or Development.con*ig at: E:\Users\Faraz\source\repos\ProManage\bin\Debug\Development.con*ig
Development.con*ig *ound, loading settings *rom it
Loading connection string *rom Development.con*ig: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Timeout=15;
Connection settings loaded *rom Development.con*ig
Creating new connection with connection string: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Maximum Pool Size=100;Minimum Pool Size=1;Connection Li*etime=300;Connection Idle Li*etime=60;Timeout=15;Command Timeout=30;SslMode=Pre*er;Trust Server Certi*icate=true;
New NpgsqlConnection created success*ully
Removed 0 user permission overrides *or user 999
Looking *or Development.con*ig at: E:\Users\Faraz\source\repos\ProManage\bin\Debug\Development.con*ig
Development.con*ig *ound, loading settings *rom it
Loading connection string *rom Development.con*ig: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Timeout=15;
Connection settings loaded *rom Development.con*ig
Creating new connection with connection string: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Maximum Pool Size=100;Minimum Pool Size=1;Connection Li*etime=300;Connection Idle Li*etime=60;Timeout=15;Command Timeout=30;SslMode=Pre*er;Trust Server Certi*icate=true;
New NpgsqlConnection created success*ully
Removed global permissions *or user 999
Looking *or Development.con*ig at: E:\Users\Faraz\source\repos\ProManage\bin\Debug\Development.con*ig
Development.con*ig *ound, loading settings *rom it
Loading connection string *rom Development.con*ig: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Timeout=15;
Connection settings loaded *rom Development.con*ig
Creating new connection with connection string: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Maximum Pool Size=100;Minimum Pool Size=1;Connection Li*etime=300;Connection Idle Li*etime=60;Timeout=15;Command Timeout=30;SslMode=Pre*er;Trust Server Certi*icate=true;
New NpgsqlConnection created success*ully
Error creating role: 23505: duplicate key value violates unique constraint "roles_role_name_key"

DETAIL: Detail redacted as it may contain sensitive data. Speci*y 'Include Error Detail' in the connection string to include this in*ormation.
Looking *or Development.con*ig at: E:\Users\Faraz\source\repos\ProManage\bin\Debug\Development.con*ig
Development.con*ig *ound, loading settings *rom it
Loading connection string *rom Development.con*ig: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Timeout=15;
Connection settings loaded *rom Development.con*ig
Creating new connection with connection string: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Maximum Pool Size=100;Minimum Pool Size=1;Connection Li*etime=300;Connection Idle Li*etime=60;Timeout=15;Command Timeout=30;SslMode=Pre*er;Trust Server Certi*icate=true;
New NpgsqlConnection created success*ully
Looking *or Development.con*ig at: E:\Users\Faraz\source\repos\ProManage\bin\Debug\Development.con*ig
Development.con*ig *ound, loading settings *rom it
Loading connection string *rom Development.con*ig: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Timeout=15;
Connection settings loaded *rom Development.con*ig
Creating new connection with connection string: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Maximum Pool Size=100;Minimum Pool Size=1;Connection Li*etime=300;Connection Idle Li*etime=60;Timeout=15;Command Timeout=30;SslMode=Pre*er;Trust Server Certi*icate=true;
New NpgsqlConnection created success*ully
Error updating user permissions: 23503: insert or update on table "user_permissions" violates *oreign key constraint "user_permissions_user_id_*key"

DETAIL: Detail redacted as it may contain sensitive data. Speci*y 'Include Error Detail' in the connection string to include this in*ormation.
Error updating user permissions: 23503: insert or update on table "user_permissions" violates *oreign key constraint "user_permissions_user_id_*key"

DETAIL: Detail redacted as it may contain sensitive data. Speci*y 'Include Error Detail' in the connection string to include this in*ormation.
Looking *or Development.con*ig at: E:\Users\Faraz\source\repos\ProManage\bin\Debug\Development.con*ig
Development.con*ig *ound, loading settings *rom it
Loading connection string *rom Development.con*ig: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Timeout=15;
Connection settings loaded *rom Development.con*ig
Creating new connection with connection string: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Maximum Pool Size=100;Minimum Pool Size=1;Connection Li*etime=300;Connection Idle Li*etime=60;Timeout=15;Command Timeout=30;SslMode=Pre*er;Trust Server Certi*icate=true;
New NpgsqlConnection created success*ully
Error getting user permission *or user 999, *orm TestForm: 42703: column "user_permission_id" does not exist

POSITION: 25
Permission check error: 42703: column "user_permission_id" does not exist

POSITION: 25
Looking *or Development.con*ig at: E:\Users\Faraz\source\repos\ProManage\bin\Debug\Development.con*ig
Development.con*ig *ound, loading settings *rom it
Loading connection string *rom Development.con*ig: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Timeout=15;
Connection settings loaded *rom Development.con*ig
Creating new connection with connection string: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Maximum Pool Size=100;Minimum Pool Size=1;Connection Li*etime=300;Connection Idle Li*etime=60;Timeout=15;Command Timeout=30;SslMode=Pre*er;Trust Server Certi*icate=true;
New NpgsqlConnection created success*ully
Error getting user permission *or user 999, *orm TestForm: 42703: column "user_permission_id" does not exist

POSITION: 25
Permission check error: 42703: column "user_permission_id" does not exist

POSITION: 25
Looking *or Development.con*ig at: E:\Users\Faraz\source\repos\ProManage\bin\Debug\Development.con*ig
Development.con*ig *ound, loading settings *rom it
Loading connection string *rom Development.con*ig: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Timeout=15;
Connection settings loaded *rom Development.con*ig
Creating new connection with connection string: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Maximum Pool Size=100;Minimum Pool Size=1;Connection Li*etime=300;Connection Idle Li*etime=60;Timeout=15;Command Timeout=30;SslMode=Pre*er;Trust Server Certi*icate=true;
New NpgsqlConnection created success*ully
Error getting user permission *or user 999, *orm TestForm: 42703: column "user_permission_id" does not exist

POSITION: 25
Permission check error: 42703: column "user_permission_id" does not exist

POSITION: 25
Looking *or Development.con*ig at: E:\Users\Faraz\source\repos\ProManage\bin\Debug\Development.con*ig
Development.con*ig *ound, loading settings *rom it
Loading connection string *rom Development.con*ig: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Timeout=15;
Connection settings loaded *rom Development.con*ig
Creating new connection with connection string: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Maximum Pool Size=100;Minimum Pool Size=1;Connection Li*etime=300;Connection Idle Li*etime=60;Timeout=15;Command Timeout=30;SslMode=Pre*er;Trust Server Certi*icate=true;
New NpgsqlConnection created success*ully
Error getting user permission *or user 999, *orm TestForm: 42703: column "user_permission_id" does not exist

POSITION: 25
Permission check error: 42703: column "user_permission_id" does not exist

POSITION: 25
Looking *or Development.con*ig at: E:\Users\Faraz\source\repos\ProManage\bin\Debug\Development.con*ig
Development.con*ig *ound, loading settings *rom it
Loading connection string *rom Development.con*ig: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Timeout=15;
Connection settings loaded *rom Development.con*ig
Creating new connection with connection string: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Maximum Pool Size=100;Minimum Pool Size=1;Connection Li*etime=300;Connection Idle Li*etime=60;Timeout=15;Command Timeout=30;SslMode=Pre*er;Trust Server Certi*icate=true;
New NpgsqlConnection created success*ully
Error getting user permission *or user 999, *orm TestForm: 42703: column "user_permission_id" does not exist

POSITION: 25
Permission check error: 42703: column "user_permission_id" does not exist

POSITION: 25
Looking *or Development.con*ig at: E:\Users\Faraz\source\repos\ProManage\bin\Debug\Development.con*ig
Development.con*ig *ound, loading settings *rom it
Loading connection string *rom Development.con*ig: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Timeout=15;
Connection settings loaded *rom Development.con*ig
Creating new connection with connection string: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Maximum Pool Size=100;Minimum Pool Size=1;Connection Li*etime=300;Connection Idle Li*etime=60;Timeout=15;Command Timeout=30;SslMode=Pre*er;Trust Server Certi*icate=true;
New NpgsqlConnection created success*ully
Removed 0 user permission overrides *or user 999
Looking *or Development.con*ig at: E:\Users\Faraz\source\repos\ProManage\bin\Debug\Development.con*ig
Development.con*ig *ound, loading settings *rom it
Loading connection string *rom Development.con*ig: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Timeout=15;
Connection settings loaded *rom Development.con*ig
Creating new connection with connection string: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Maximum Pool Si
... <truncated>

  (result has additional output)

   Open test log


##  HasGlobalPermission_WithPermission_ReturnsTrue
   Source: PermissionSystemTests.cs line 81
   Duration: 28 ms

  Message: 
Assert.IsTrue *ailed. User should have global create users permission

  Stack Trace: 
PermissionSystemTests.HasGlobalPermission_WithPermission_ReturnsTrue() line 90

  Standard Output: 


Debug Trace:
Looking *or Development.con*ig at: E:\Users\Faraz\source\repos\ProManage\bin\Debug\Development.con*ig
Development.con*ig *ound, loading settings *rom it
Loading connection string *rom Development.con*ig: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Timeout=15;
Connection settings loaded *rom Development.con*ig
Creating new connection with connection string: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Maximum Pool Size=100;Minimum Pool Size=1;Connection Li*etime=300;Connection Idle Li*etime=60;Timeout=15;Command Timeout=30;SslMode=Pre*er;Trust Server Certi*icate=true;
New NpgsqlConnection created success*ully
Removed 0 user permission overrides *or user 999
Looking *or Development.con*ig at: E:\Users\Faraz\source\repos\ProManage\bin\Debug\Development.con*ig
Development.con*ig *ound, loading settings *rom it
Loading connection string *rom Development.con*ig: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Timeout=15;
Connection settings loaded *rom Development.con*ig
Creating new connection with connection string: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Maximum Pool Size=100;Minimum Pool Size=1;Connection Li*etime=300;Connection Idle Li*etime=60;Timeout=15;Command Timeout=30;SslMode=Pre*er;Trust Server Certi*icate=true;
New NpgsqlConnection created success*ully
Removed global permissions *or user 999
Looking *or Development.con*ig at: E:\Users\Faraz\source\repos\ProManage\bin\Debug\Development.con*ig
Development.con*ig *ound, loading settings *rom it
Loading connection string *rom Development.con*ig: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Timeout=15;
Connection settings loaded *rom Development.con*ig
Creating new connection with connection string: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Maximum Pool Size=100;Minimum Pool Size=1;Connection Li*etime=300;Connection Idle Li*etime=60;Timeout=15;Command Timeout=30;SslMode=Pre*er;Trust Server Certi*icate=true;
New NpgsqlConnection created success*ully
Error creating role: 23505: duplicate key value violates unique constraint "roles_role_name_key"

DETAIL: Detail redacted as it may contain sensitive data. Speci*y 'Include Error Detail' in the connection string to include this in*ormation.
Looking *or Development.con*ig at: E:\Users\Faraz\source\repos\ProManage\bin\Debug\Development.con*ig
Development.con*ig *ound, loading settings *rom it
Loading connection string *rom Development.con*ig: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Timeout=15;
Connection settings loaded *rom Development.con*ig
Creating new connection with connection string: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Maximum Pool Size=100;Minimum Pool Size=1;Connection Li*etime=300;Connection Idle Li*etime=60;Timeout=15;Command Timeout=30;SslMode=Pre*er;Trust Server Certi*icate=true;
New NpgsqlConnection created success*ully
Error updating global permissions *or user 999: 23503: insert or update on table "global_permissions" violates *oreign key constraint "global_permissions_user_id_*key"

DETAIL: Detail redacted as it may contain sensitive data. Speci*y 'Include Error Detail' in the connection string to include this in*ormation.
Looking *or Development.con*ig at: E:\Users\Faraz\source\repos\ProManage\bin\Debug\Development.con*ig
Development.con*ig *ound, loading settings *rom it
Loading connection string *rom Development.con*ig: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Timeout=15;
Connection settings loaded *rom Development.con*ig
Creating new connection with connection string: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Maximum Pool Size=100;Minimum Pool Size=1;Connection Li*etime=300;Connection Idle Li*etime=60;Timeout=15;Command Timeout=30;SslMode=Pre*er;Trust Server Certi*icate=true;
New NpgsqlConnection created success*ully
Error getting global permissions *or user 999: 42703: column "global_permission_id" does not exist

POSITION: 25
Global permission check error: 42703: column "global_permission_id" does not exist

POSITION: 25
Looking *or Development.con*ig at: E:\Users\Faraz\source\repos\ProManage\bin\Debug\Development.con*ig
Development.con*ig *ound, loading settings *rom it
Loading connection string *rom Development.con*ig: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Timeout=15;
Connection settings loaded *rom Development.con*ig
Creating new connection with connection string: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Maximum Pool Size=100;Minimum Pool Size=1;Connection Li*etime=300;Connection Idle Li*etime=60;Timeout=15;Command Timeout=30;SslMode=Pre*er;Trust Server Certi*icate=true;
New NpgsqlConnection created success*ully
Removed 0 user permission overrides *or user 999
Looking *or Development.con*ig at: E:\Users\Faraz\source\repos\ProManage\bin\Debug\Development.con*ig
Development.con*ig *ound, loading settings *rom it
Loading connection string *rom Development.con*ig: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Timeout=15;
Connection settings loaded *rom Development.con*ig
Creating new connection with connection string: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Maximum Pool Size=100;Minimum Pool Size=1;Connection Li*etime=300;Connection Idle Li*etime=60;Timeout=15;Command Timeout=30;SslMode=Pre*er;Trust Server Certi*icate=true;
New NpgsqlConnection created success*ully
Removed global permissions *or user 999


##  HasPermission_WithRolePermission_ReturnsTrue
   Source: PermissionSystemTests.cs line 40
   Duration: 30 ms

  Message: 
Assert.IsTrue *ailed. User should have read permission through role

  Stack Trace: 
PermissionSystemTests.HasPermission_WithRolePermission_ReturnsTrue() line 49

  Standard Output: 


Debug Trace:
Looking *or Development.con*ig at: E:\Users\Faraz\source\repos\ProManage\bin\Debug\Development.con*ig
Development.con*ig *ound, loading settings *rom it
Loading connection string *rom Development.con*ig: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Timeout=15;
Connection settings loaded *rom Development.con*ig
Creating new connection with connection string: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Maximum Pool Size=100;Minimum Pool Size=1;Connection Li*etime=300;Connection Idle Li*etime=60;Timeout=15;Command Timeout=30;SslMode=Pre*er;Trust Server Certi*icate=true;
New NpgsqlConnection created success*ully
Removed 0 user permission overrides *or user 999
Looking *or Development.con*ig at: E:\Users\Faraz\source\repos\ProManage\bin\Debug\Development.con*ig
Development.con*ig *ound, loading settings *rom it
Loading connection string *rom Development.con*ig: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Timeout=15;
Connection settings loaded *rom Development.con*ig
Creating new connection with connection string: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Maximum Pool Size=100;Minimum Pool Size=1;Connection Li*etime=300;Connection Idle Li*etime=60;Timeout=15;Command Timeout=30;SslMode=Pre*er;Trust Server Certi*icate=true;
New NpgsqlConnection created success*ully
Removed global permissions *or user 999
Looking *or Development.con*ig at: E:\Users\Faraz\source\repos\ProManage\bin\Debug\Development.con*ig
Development.con*ig *ound, loading settings *rom it
Loading connection string *rom Development.con*ig: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Timeout=15;
Connection settings loaded *rom Development.con*ig
Creating new connection with connection string: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Maximum Pool Size=100;Minimum Pool Size=1;Connection Li*etime=300;Connection Idle Li*etime=60;Timeout=15;Command Timeout=30;SslMode=Pre*er;Trust Server Certi*icate=true;
New NpgsqlConnection created success*ully
Error creating role: 23505: duplicate key value violates unique constraint "roles_role_name_key"

DETAIL: Detail redacted as it may contain sensitive data. Speci*y 'Include Error Detail' in the connection string to include this in*ormation.
Looking *or Development.con*ig at: E:\Users\Faraz\source\repos\ProManage\bin\Debug\Development.con*ig
Development.con*ig *ound, loading settings *rom it
Loading connection string *rom Development.con*ig: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Timeout=15;
Connection settings loaded *rom Development.con*ig
Creating new connection with connection string: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Maximum Pool Size=100;Minimum Pool Size=1;Connection Li*etime=300;Connection Idle Li*etime=60;Timeout=15;Command Timeout=30;SslMode=Pre*er;Trust Server Certi*icate=true;
New NpgsqlConnection created success*ully
Looking *or Development.con*ig at: E:\Users\Faraz\source\repos\ProManage\bin\Debug\Development.con*ig
Development.con*ig *ound, loading settings *rom it
Loading connection string *rom Development.con*ig: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Timeout=15;
Connection settings loaded *rom Development.con*ig
Creating new connection with connection string: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Maximum Pool Size=100;Minimum Pool Size=1;Connection Li*etime=300;Connection Idle Li*etime=60;Timeout=15;Command Timeout=30;SslMode=Pre*er;Trust Server Certi*icate=true;
New NpgsqlConnection created success*ully
Error getting user permission *or user 999, *orm TestForm: 42703: column "user_permission_id" does not exist

POSITION: 25
Permission check error: 42703: column "user_permission_id" does not exist

POSITION: 25
Looking *or Development.con*ig at: E:\Users\Faraz\source\repos\ProManage\bin\Debug\Development.con*ig
Development.con*ig *ound, loading settings *rom it
Loading connection string *rom Development.con*ig: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Timeout=15;
Connection settings loaded *rom Development.con*ig
Creating new connection with connection string: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Maximum Pool Size=100;Minimum Pool Size=1;Connection Li*etime=300;Connection Idle Li*etime=60;Timeout=15;Command Timeout=30;SslMode=Pre*er;Trust Server Certi*icate=true;
New NpgsqlConnection created success*ully
Removed 0 user permission overrides *or user 999
Looking *or Development.con*ig at: E:\Users\Faraz\source\repos\ProManage\bin\Debug\Development.con*ig
Development.con*ig *ound, loading settings *rom it
Loading connection string *rom Development.con*ig: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Timeout=15;
Connection settings loaded *rom Development.con*ig
Creating new connection with connection string: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Maximum Pool Size=100;Minimum Pool Size=1;Connection Li*etime=300;Connection Idle Li*etime=60;Timeout=15;Command Timeout=30;SslMode=Pre*er;Trust Server Certi*icate=true;
New NpgsqlConnection created success*ully
Removed global permissions *or user 999


##  HasPermission_WithUserOverride_ReturnsUserPermission
   Source: PermissionSystemTests.cs line 53
   Duration: 32 ms

  Message: 
Assert.IsTrue *ailed. User override should take precedence over role permission

  Stack Trace: 
PermissionSystemTests.HasPermission_WithUserOverride_ReturnsUserPermission() line 63

  Standard Output: 


Debug Trace:
Looking *or Development.con*ig at: E:\Users\Faraz\source\repos\ProManage\bin\Debug\Development.con*ig
Development.con*ig *ound, loading settings *rom it
Loading connection string *rom Development.con*ig: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Timeout=15;
Connection settings loaded *rom Development.con*ig
Creating new connection with connection string: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Maximum Pool Size=100;Minimum Pool Size=1;Connection Li*etime=300;Connection Idle Li*etime=60;Timeout=15;Command Timeout=30;SslMode=Pre*er;Trust Server Certi*icate=true;
New NpgsqlConnection created success*ully
Removed 0 user permission overrides *or user 999
Looking *or Development.con*ig at: E:\Users\Faraz\source\repos\ProManage\bin\Debug\Development.con*ig
Development.con*ig *ound, loading settings *rom it
Loading connection string *rom Development.con*ig: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Timeout=15;
Connection settings loaded *rom Development.con*ig
Creating new connection with connection string: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Maximum Pool Size=100;Minimum Pool Size=1;Connection Li*etime=300;Connection Idle Li*etime=60;Timeout=15;Command Timeout=30;SslMode=Pre*er;Trust Server Certi*icate=true;
New NpgsqlConnection created success*ully
Removed global permissions *or user 999
Looking *or Development.con*ig at: E:\Users\Faraz\source\repos\ProManage\bin\Debug\Development.con*ig
Development.con*ig *ound, loading settings *rom it
Loading connection string *rom Development.con*ig: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Timeout=15;
Connection settings loaded *rom Development.con*ig
Creating new connection with connection string: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Maximum Pool Size=100;Minimum Pool Size=1;Connection Li*etime=300;Connection Idle Li*etime=60;Timeout=15;Command Timeout=30;SslMode=Pre*er;Trust Server Certi*icate=true;
New NpgsqlConnection created success*ully
Error creating role: 23505: duplicate key value violates unique constraint "roles_role_name_key"

DETAIL: Detail redacted as it may contain sensitive data. Speci*y 'Include Error Detail' in the connection string to include this in*ormation.
Looking *or Development.con*ig at: E:\Users\Faraz\source\repos\ProManage\bin\Debug\Development.con*ig
Development.con*ig *ound, loading settings *rom it
Loading connection string *rom Development.con*ig: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Timeout=15;
Connection settings loaded *rom Development.con*ig
Creating new connection with connection string: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Maximum Pool Size=100;Minimum Pool Size=1;Connection Li*etime=300;Connection Idle Li*etime=60;Timeout=15;Command Timeout=30;SslMode=Pre*er;Trust Server Certi*icate=true;
New NpgsqlConnection created success*ully
Looking *or Development.con*ig at: E:\Users\Faraz\source\repos\ProManage\bin\Debug\Development.con*ig
Development.con*ig *ound, loading settings *rom it
Loading connection string *rom Development.con*ig: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Timeout=15;
Connection settings loaded *rom Development.con*ig
Creating new connection with connection string: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Maximum Pool Size=100;Minimum Pool Size=1;Connection Li*etime=300;Connection Idle Li*etime=60;Timeout=15;Command Timeout=30;SslMode=Pre*er;Trust Server Certi*icate=true;
New NpgsqlConnection created success*ully
Error updating user permissions: 23503: insert or update on table "user_permissions" violates *oreign key constraint "user_permissions_user_id_*key"

DETAIL: Detail redacted as it may contain sensitive data. Speci*y 'Include Error Detail' in the connection string to include this in*ormation.
Error updating user permissions: 23503: insert or update on table "user_permissions" violates *oreign key constraint "user_permissions_user_id_*key"

DETAIL: Detail redacted as it may contain sensitive data. Speci*y 'Include Error Detail' in the connection string to include this in*ormation.
Looking *or Development.con*ig at: E:\Users\Faraz\source\repos\ProManage\bin\Debug\Development.con*ig
Development.con*ig *ound, loading settings *rom it
Loading connection string *rom Development.con*ig: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Timeout=15;
Connection settings loaded *rom Development.con*ig
Creating new connection with connection string: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Maximum Pool Size=100;Minimum Pool Size=1;Connection Li*etime=300;Connection Idle Li*etime=60;Timeout=15;Command Timeout=30;SslMode=Pre*er;Trust Server Certi*icate=true;
New NpgsqlConnection created success*ully
Error getting user permission *or user 999, *orm TestForm: 42703: column "user_permission_id" does not exist

POSITION: 25
Permission check error: 42703: column "user_permission_id" does not exist

POSITION: 25
Looking *or Development.con*ig at: E:\Users\Faraz\source\repos\ProManage\bin\Debug\Development.con*ig
Development.con*ig *ound, loading settings *rom it
Loading connection string *rom Development.con*ig: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Timeout=15;
Connection settings loaded *rom Development.con*ig
Creating new connection with connection string: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Maximum Pool Size=100;Minimum Pool Size=1;Connection Li*etime=300;Connection Idle Li*etime=60;Timeout=15;Command Timeout=30;SslMode=Pre*er;Trust Server Certi*icate=true;
New NpgsqlConnection created success*ully
Removed 0 user permission overrides *or user 999
Looking *or Development.con*ig at: E:\Users\Faraz\source\repos\ProManage\bin\Debug\Development.con*ig
Development.con*ig *ound, loading settings *rom it
Loading connection string *rom Development.con*ig: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Timeout=15;
Connection settings loaded *rom Development.con*ig
Creating new connection with connection string: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Maximum Pool Size=100;Minimum Pool Size=1;Connection Li*etime=300;Connection Idle Li*etime=60;Timeout=15;Command Timeout=30;SslMode=Pre*er;Trust Server Certi*icate=true;
New NpgsqlConnection created success*ully
Removed global permissions *or user 999


##  PermissionCache_A*terUpdate_InvalidatesCorrectly
   Source: PermissionSystemTests.cs line 187
   Duration: 35 ms

  Message: 
Assert.IsTrue *ailed. Initial permission should be true

  Stack Trace: 
PermissionSystemTests.PermissionCache_A*terUpdate_InvalidatesCorrectly() line 199

  Standard Output: 


Debug Trace:
Looking *or Development.con*ig at: E:\Users\Faraz\source\repos\ProManage\bin\Debug\Development.con*ig
Development.con*ig *ound, loading settings *rom it
Loading connection string *rom Development.con*ig: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Timeout=15;
Connection settings loaded *rom Development.con*ig
Creating new connection with connection string: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Maximum Pool Size=100;Minimum Pool Size=1;Connection Li*etime=300;Connection Idle Li*etime=60;Timeout=15;Command Timeout=30;SslMode=Pre*er;Trust Server Certi*icate=true;
New NpgsqlConnection created success*ully
Removed 0 user permission overrides *or user 999
Looking *or Development.con*ig at: E:\Users\Faraz\source\repos\ProManage\bin\Debug\Development.con*ig
Development.con*ig *ound, loading settings *rom it
Loading connection string *rom Development.con*ig: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Timeout=15;
Connection settings loaded *rom Development.con*ig
Creating new connection with connection string: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Maximum Pool Size=100;Minimum Pool Size=1;Connection Li*etime=300;Connection Idle Li*etime=60;Timeout=15;Command Timeout=30;SslMode=Pre*er;Trust Server Certi*icate=true;
New NpgsqlConnection created success*ully
Removed global permissions *or user 999
Looking *or Development.con*ig at: E:\Users\Faraz\source\repos\ProManage\bin\Debug\Development.con*ig
Development.con*ig *ound, loading settings *rom it
Loading connection string *rom Development.con*ig: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Timeout=15;
Connection settings loaded *rom Development.con*ig
Creating new connection with connection string: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Maximum Pool Size=100;Minimum Pool Size=1;Connection Li*etime=300;Connection Idle Li*etime=60;Timeout=15;Command Timeout=30;SslMode=Pre*er;Trust Server Certi*icate=true;
New NpgsqlConnection created success*ully
Error creating role: 23505: duplicate key value violates unique constraint "roles_role_name_key"

DETAIL: Detail redacted as it may contain sensitive data. Speci*y 'Include Error Detail' in the connection string to include this in*ormation.
Looking *or Development.con*ig at: E:\Users\Faraz\source\repos\ProManage\bin\Debug\Development.con*ig
Development.con*ig *ound, loading settings *rom it
Loading connection string *rom Development.con*ig: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Timeout=15;
Connection settings loaded *rom Development.con*ig
Creating new connection with connection string: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Maximum Pool Size=100;Minimum Pool Size=1;Connection Li*etime=300;Connection Idle Li*etime=60;Timeout=15;Command Timeout=30;SslMode=Pre*er;Trust Server Certi*icate=true;
New NpgsqlConnection created success*ully
Looking *or Development.con*ig at: E:\Users\Faraz\source\repos\ProManage\bin\Debug\Development.con*ig
Development.con*ig *ound, loading settings *rom it
Loading connection string *rom Development.con*ig: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Timeout=15;
Connection settings loaded *rom Development.con*ig
Creating new connection with connection string: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Maximum Pool Size=100;Minimum Pool Size=1;Connection Li*etime=300;Connection Idle Li*etime=60;Timeout=15;Command Timeout=30;SslMode=Pre*er;Trust Server Certi*icate=true;
New NpgsqlConnection created success*ully
Error getting user permission *or user 999, *orm TestForm: 42703: column "user_permission_id" does not exist

POSITION: 25
Permission check error: 42703: column "user_permission_id" does not exist

POSITION: 25
Looking *or Development.con*ig at: E:\Users\Faraz\source\repos\ProManage\bin\Debug\Development.con*ig
Development.con*ig *ound, loading settings *rom it
Loading connection string *rom Development.con*ig: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Timeout=15;
Connection settings loaded *rom Development.con*ig
Creating new connection with connection string: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Maximum Pool Size=100;Minimum Pool Size=1;Connection Li*etime=300;Connection Idle Li*etime=60;Timeout=15;Command Timeout=30;SslMode=Pre*er;Trust Server Certi*icate=true;
New NpgsqlConnection created success*ully
Looking *or Development.con*ig at: E:\Users\Faraz\source\repos\ProManage\bin\Debug\Development.con*ig
Development.con*ig *ound, loading settings *rom it
Loading connection string *rom Development.con*ig: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Timeout=15;
Connection settings loaded *rom Development.con*ig
Creating new connection with connection string: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Maximum Pool Size=100;Minimum Pool Size=1;Connection Li*etime=300;Connection Idle Li*etime=60;Timeout=15;Command Timeout=30;SslMode=Pre*er;Trust Server Certi*icate=true;
New NpgsqlConnection created success*ully
Error getting user permission *or user 999, *orm TestForm: 42703: column "user_permission_id" does not exist

POSITION: 25
Permission check error: 42703: column "user_permission_id" does not exist

POSITION: 25
Looking *or Development.con*ig at: E:\Users\Faraz\source\repos\ProManage\bin\Debug\Development.con*ig
Development.con*ig *ound, loading settings *rom it
Loading connection string *rom Development.con*ig: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Timeout=15;
Connection settings loaded *rom Development.con*ig
Creating new connection with connection string: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Maximum Pool Size=100;Minimum Pool Size=1;Connection Li*etime=300;Connection Idle Li*etime=60;Timeout=15;Command Timeout=30;SslMode=Pre*er;Trust Server Certi*icate=true;
New NpgsqlConnection created success*ully
Removed 0 user permission overrides *or user 999
Looking *or Development.con*ig at: E:\Users\Faraz\source\repos\ProManage\bin\Debug\Development.con*ig
Development.con*ig *ound, loading settings *rom it
Loading connection string *rom Development.con*ig: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Timeout=15;
Connection settings loaded *rom Development.con*ig
Creating new connection with connection string: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Maximum Pool Size=100;Minimum Pool Size=1;Connection Li*etime=300;Connection Idle Li*etime=60;Timeout=15;Command Timeout=30;SslMode=Pre*er;Trust Server Certi*icate=true;
New NpgsqlConnection created success*ully
Removed global permissions *or user 999


##  UpdateUserPermissions_ValidPermissions_ReturnsSuccess
   Source: PermissionSystemTests.cs line 143
   Duration: 15 ms

  Message: 
Assert.IsTrue *ailed. User permission update should succeed

  Stack Trace: 
PermissionSystemTests.UpdateUserPermissions_ValidPermissions_ReturnsSuccess() line 164

  Standard Output: 


Debug Trace:
Looking *or Development.con*ig at: E:\Users\Faraz\source\repos\ProManage\bin\Debug\Development.con*ig
Development.con*ig *ound, loading settings *rom it
Loading connection string *rom Development.con*ig: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Timeout=15;
Connection settings loaded *rom Development.con*ig
Creating new connection with connection string: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Maximum Pool Size=100;Minimum Pool Size=1;Connection Li*etime=300;Connection Idle Li*etime=60;Timeout=15;Command Timeout=30;SslMode=Pre*er;Trust Server Certi*icate=true;
New NpgsqlConnection created success*ully
Removed 0 user permission overrides *or user 999
Looking *or Development.con*ig at: E:\Users\Faraz\source\repos\ProManage\bin\Debug\Development.con*ig
Development.con*ig *ound, loading settings *rom it
Loading connection string *rom Development.con*ig: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Timeout=15;
Connection settings loaded *rom Development.con*ig
Creating new connection with connection string: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Maximum Pool Size=100;Minimum Pool Size=1;Connection Li*etime=300;Connection Idle Li*etime=60;Timeout=15;Command Timeout=30;SslMode=Pre*er;Trust Server Certi*icate=true;
New NpgsqlConnection created success*ully
Removed global permissions *or user 999
Looking *or Development.con*ig at: E:\Users\Faraz\source\repos\ProManage\bin\Debug\Development.con*ig
Development.con*ig *ound, loading settings *rom it
Loading connection string *rom Development.con*ig: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Timeout=15;
Connection settings loaded *rom Development.con*ig
Creating new connection with connection string: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Maximum Pool Size=100;Minimum Pool Size=1;Connection Li*etime=300;Connection Idle Li*etime=60;Timeout=15;Command Timeout=30;SslMode=Pre*er;Trust Server Certi*icate=true;
New NpgsqlConnection created success*ully
Error creating role: 23505: duplicate key value violates unique constraint "roles_role_name_key"

DETAIL: Detail redacted as it may contain sensitive data. Speci*y 'Include Error Detail' in the connection string to include this in*ormation.
Looking *or Development.con*ig at: E:\Users\Faraz\source\repos\ProManage\bin\Debug\Development.con*ig
Development.con*ig *ound, loading settings *rom it
Loading connection string *rom Development.con*ig: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Timeout=15;
Connection settings loaded *rom Development.con*ig
Creating new connection with connection string: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Maximum Pool Size=100;Minimum Pool Size=1;Connection Li*etime=300;Connection Idle Li*etime=60;Timeout=15;Command Timeout=30;SslMode=Pre*er;Trust Server Certi*icate=true;
New NpgsqlConnection created success*ully
Error updating user permissions: 23503: insert or update on table "user_permissions" violates *oreign key constraint "user_permissions_user_id_*key"

DETAIL: Detail redacted as it may contain sensitive data. Speci*y 'Include Error Detail' in the connection string to include this in*ormation.
Error updating user permissions: 23503: insert or update on table "user_permissions" violates *oreign key constraint "user_permissions_user_id_*key"

DETAIL: Detail redacted as it may contain sensitive data. Speci*y 'Include Error Detail' in the connection string to include this in*ormation.
Looking *or Development.con*ig at: E:\Users\Faraz\source\repos\ProManage\bin\Debug\Development.con*ig
Development.con*ig *ound, loading settings *rom it
Loading connection string *rom Development.con*ig: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Timeout=15;
Connection settings loaded *rom Development.con*ig
Creating new connection with connection string: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Maximum Pool Size=100;Minimum Pool Size=1;Connection Li*etime=300;Connection Idle Li*etime=60;Timeout=15;Command Timeout=30;SslMode=Pre*er;Trust Server Certi*icate=true;
New NpgsqlConnection created success*ully
Removed 0 user permission overrides *or user 999
Looking *or Development.con*ig at: E:\Users\Faraz\source\repos\ProManage\bin\Debug\Development.con*ig
Development.con*ig *ound, loading settings *rom it
Loading connection string *rom Development.con*ig: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Timeout=15;
Connection settings loaded *rom Development.con*ig
Creating new connection with connection string: Host=rostock.loginto.me;Port=5432;Database=EstimateDB;Username=postgres;Password=*araz123;Maximum Pool Size=100;Minimum Pool Size=1;Connection Li*etime=300;Connection Idle Li*etime=60;Timeout=15;Command Timeout=30;SslMode=Pre*er;Trust Server Certi*icate=true;
New NpgsqlConnection created success*ully
Removed global permissions *or user 999
