<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="15.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props" Condition="Exists('$(MSBuildExtensionsPath)\$(MSBuildToolsVersion)\Microsoft.Common.props')" />
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">AnyCPU</Platform>
    <ProjectGuid>{B8D5F5A3-0E7A-4B1D-A7D7-5F7EC7825F0D}</ProjectGuid>
    <OutputType>Library</OutputType>
    <RootNamespace>ProManage.Services</RootNamespace>    <AssemblyName>ProManage.Services</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <Deterministic>true</Deterministic>    <MSBuildArchitecture>x86</MSBuildArchitecture>
    <GenerateResourceUsePreserializedResources>true</GenerateResourceUsePreserializedResources>    <RuntimeIdentifiers>win-x64;win-x86</RuntimeIdentifiers>
    <NoWarn>$(NoWarn);NU1101</NoWarn>
  </PropertyGroup>  <!-- System.Resources.Extensions copy target removed - handled by Directory.Build.targets -->
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <PlatformTarget>x86</PlatformTarget>
    <Prefer32Bit>true</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <PlatformTarget>x86</PlatformTarget>
    <Prefer32Bit>true</Prefer32Bit>
  </PropertyGroup>  <ItemGroup>
    <Reference Include="System" />
    <Reference Include="System.Core" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Net.Http" />    <Reference Include="System.Xml" />    <!-- System.Resources.Extensions reference removed - using PackageReference instead -->
  </ItemGroup>
  <ItemGroup>
    <Compile Include="InfrastructureStubs.cs" />
  </ItemGroup>
  <ItemGroup>
    <Folder Include="Models\" />
  </ItemGroup>  <ItemGroup>
    <PackageReference Include="Microsoft.Bcl.AsyncInterfaces">
      <Version>9.0.5</Version>
    </PackageReference>
    <PackageReference Include="System.Resources.Extensions">
      <Version>8.0.0</Version>
    </PackageReference>
    <PackageReference Include="System.Runtime.CompilerServices.Unsafe">
      <Version>6.0.0</Version>
    </PackageReference>
    <PackageReference Include="System.Text.Json">
      <Version>9.0.5</Version>
    </PackageReference>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
</Project>