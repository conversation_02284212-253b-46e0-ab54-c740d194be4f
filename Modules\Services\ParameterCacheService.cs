// ParameterCacheService - Centralized parameter management service
// Usage: Singleton service for caching and accessing application parameters

using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.IO;
using System.Text.Json;
using System.Threading;
using ProManage.Modules.Data.ParametersForm;

namespace ProManage.Modules.Services
{
    /// <summary>
    /// Singleton service for centralized parameter management
    /// Provides in-memory caching with file persistence for application parameters
    /// </summary>
    public sealed class ParameterCacheService
    {
        #region Singleton Implementation

        private static readonly Lazy<ParameterCacheService> _instance = 
            new Lazy<ParameterCacheService>(() => new ParameterCacheService());

        /// <summary>
        /// Gets the singleton instance of the ParameterCacheService
        /// </summary>
        public static ParameterCacheService Instance => _instance.Value;

        #endregion

        #region Private Fields

        private readonly Dictionary<string, string> _parameterCache;
        private readonly object _lockObject;
        private readonly string _cacheFilePath;
        private bool _isInitialized;
        private DateTime _lastCacheUpdate;

        #endregion

        #region Constructor

        /// <summary>
        /// Private constructor for singleton pattern
        /// </summary>
        private ParameterCacheService()
        {
            _parameterCache = new Dictionary<string, string>();
            _lockObject = new object();
            _isInitialized = false;
            _lastCacheUpdate = DateTime.MinValue;

            // Set cache file path in application directory
            string appDirectory = Path.GetDirectoryName(System.Windows.Forms.Application.ExecutablePath) 
                                 ?? Environment.CurrentDirectory;
            _cacheFilePath = Path.Combine(appDirectory, "parameters.cache");

            Debug.WriteLine($"ParameterCacheService: Cache file path set to {_cacheFilePath}");
        }

        #endregion

        #region Public Properties

        /// <summary>
        /// Gets whether the cache has been initialized
        /// </summary>
        public bool IsLoaded => _isInitialized;

        /// <summary>
        /// Gets the number of parameters in the cache
        /// </summary>
        public int ParameterCount
        {
            get
            {
                lock (_lockObject)
                {
                    return _parameterCache.Count;
                }
            }
        }

        /// <summary>
        /// Gets the last cache update timestamp
        /// </summary>
        public DateTime LastCacheUpdate => _lastCacheUpdate;

        #endregion

        #region Public Methods

        /// <summary>
        /// Initializes the parameter cache by loading from file or database
        /// </summary>
        /// <returns>True if initialization successful, false otherwise</returns>
        public bool Initialize()
        {
            try
            {
                Debug.WriteLine("=== ParameterCacheService.Initialize: Starting ===");

                lock (_lockObject)
                {
                    if (_isInitialized)
                    {
                        Debug.WriteLine("ParameterCacheService already initialized");
                        return true;
                    }

                    // Try to load from cache file first
                    bool loadedFromFile = LoadFromFile();
                    
                    if (loadedFromFile)
                    {
                        // Validate cache age - refresh if older than 1 hour
                        if (DateTime.Now.Subtract(_lastCacheUpdate).TotalHours > 1)
                        {
                            Debug.WriteLine("Cache file is stale, refreshing from database");
                            return RefreshFromDatabase();
                        }
                        else
                        {
                            Debug.WriteLine($"Loaded {_parameterCache.Count} parameters from cache file");
                            _isInitialized = true;
                            return true;
                        }
                    }
                    else
                    {
                        // Cache file doesn't exist or is invalid, load from database
                        Debug.WriteLine("Cache file not available, loading from database");
                        return RefreshFromDatabase();
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error initializing ParameterCacheService: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Gets a parameter value by code
        /// </summary>
        /// <param name="parameterCode">The parameter code to retrieve</param>
        /// <returns>Parameter value if found, null otherwise</returns>
        public string GetParameter(string parameterCode)
        {
            if (string.IsNullOrWhiteSpace(parameterCode))
                return null;

            lock (_lockObject)
            {
                if (!_isInitialized)
                {
                    Debug.WriteLine("ParameterCacheService not initialized, attempting to initialize");
                    if (!Initialize())
                    {
                        Debug.WriteLine("Failed to initialize ParameterCacheService");
                        return null;
                    }
                }

                _parameterCache.TryGetValue(parameterCode.ToUpper(), out string value);
                return value;
            }
        }

        /// <summary>
        /// Gets a parameter value by code with a default fallback value
        /// </summary>
        /// <param name="parameterCode">The parameter code to retrieve</param>
        /// <param name="defaultValue">Default value to return if parameter not found</param>
        /// <returns>Parameter value if found, default value otherwise</returns>
        public string GetParameter(string parameterCode, string defaultValue)
        {
            string value = GetParameter(parameterCode);
            return value ?? defaultValue;
        }

        /// <summary>
        /// Checks if a parameter exists in the cache
        /// </summary>
        /// <param name="parameterCode">The parameter code to check</param>
        /// <returns>True if parameter exists, false otherwise</returns>
        public bool HasParameter(string parameterCode)
        {
            if (string.IsNullOrWhiteSpace(parameterCode))
                return false;

            lock (_lockObject)
            {
                if (!_isInitialized)
                {
                    if (!Initialize())
                        return false;
                }

                return _parameterCache.ContainsKey(parameterCode.ToUpper());
            }
        }

        /// <summary>
        /// Refreshes the cache by reloading parameters from the database
        /// </summary>
        /// <returns>True if refresh successful, false otherwise</returns>
        public bool RefreshCache()
        {
            try
            {
                Debug.WriteLine("=== ParameterCacheService.RefreshCache: Starting ===");
                
                lock (_lockObject)
                {
                    return RefreshFromDatabase();
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error refreshing parameter cache: {ex.Message}");
                return false;
            }
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// Loads parameters from cache file
        /// </summary>
        /// <returns>True if loaded successfully, false otherwise</returns>
        private bool LoadFromFile()
        {
            try
            {
                if (!File.Exists(_cacheFilePath))
                {
                    Debug.WriteLine("Cache file does not exist");
                    return false;
                }

                Debug.WriteLine($"Loading parameters from cache file: {_cacheFilePath}");

                string jsonContent = File.ReadAllText(_cacheFilePath);
                if (string.IsNullOrWhiteSpace(jsonContent))
                {
                    Debug.WriteLine("Cache file is empty");
                    return false;
                }

                var cacheModel = JsonSerializer.Deserialize<ParameterCacheModel>(jsonContent);
                if (cacheModel == null || !cacheModel.IsValid())
                {
                    Debug.WriteLine("Cache file contains invalid data");
                    return false;
                }

                // Load parameters into cache
                _parameterCache.Clear();
                foreach (var parameter in cacheModel.Parameters)
                {
                    _parameterCache[parameter.Key.ToUpper()] = parameter.Value;
                }

                _lastCacheUpdate = cacheModel.LastUpdated;
                Debug.WriteLine($"Successfully loaded {_parameterCache.Count} parameters from cache file");
                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error loading from cache file: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Saves parameters to cache file
        /// </summary>
        /// <returns>True if saved successfully, false otherwise</returns>
        private bool SaveToFile()
        {
            try
            {
                Debug.WriteLine($"Saving parameters to cache file: {_cacheFilePath}");

                var cacheModel = new ParameterCacheModel(_parameterCache)
                {
                    LastUpdated = _lastCacheUpdate
                };

                var options = new JsonSerializerOptions
                {
                    WriteIndented = true
                };

                string jsonContent = JsonSerializer.Serialize(cacheModel, options);
                File.WriteAllText(_cacheFilePath, jsonContent);

                Debug.WriteLine($"Successfully saved {_parameterCache.Count} parameters to cache file");
                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error saving to cache file: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Refreshes cache from database
        /// </summary>
        /// <returns>True if refresh successful, false otherwise</returns>
        private bool RefreshFromDatabase()
        {
            try
            {
                Debug.WriteLine("Refreshing parameters from database");

                // Get parameters from repository
                var parameters = ParametersFormRepository.GetAllParameters();
                if (parameters == null)
                {
                    Debug.WriteLine("Failed to retrieve parameters from database");
                    return false;
                }

                // Clear existing cache and load new parameters
                _parameterCache.Clear();
                foreach (var parameter in parameters)
                {
                    if (!string.IsNullOrWhiteSpace(parameter.ParameterCode))
                    {
                        _parameterCache[parameter.ParameterCode.ToUpper()] = parameter.ParameterValue ?? string.Empty;
                    }
                }

                _lastCacheUpdate = DateTime.Now;
                _isInitialized = true;

                Debug.WriteLine($"Successfully loaded {_parameterCache.Count} parameters from database");

                // Save to cache file for next startup
                SaveToFile();

                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error refreshing from database: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Validates the cache file
        /// </summary>
        /// <returns>True if cache file is valid, false otherwise</returns>
        private bool ValidateCacheFile()
        {
            try
            {
                if (!File.Exists(_cacheFilePath))
                    return false;

                var fileInfo = new FileInfo(_cacheFilePath);

                // Check if file is too old (older than 24 hours)
                if (DateTime.Now.Subtract(fileInfo.LastWriteTime).TotalHours > 24)
                {
                    Debug.WriteLine("Cache file is too old");
                    return false;
                }

                // Check if file size is reasonable (not empty, not too large)
                if (fileInfo.Length == 0 || fileInfo.Length > 10 * 1024 * 1024) // 10MB max
                {
                    Debug.WriteLine($"Cache file size is invalid: {fileInfo.Length} bytes");
                    return false;
                }

                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error validating cache file: {ex.Message}");
                return false;
            }
        }

        #endregion
    }
}
