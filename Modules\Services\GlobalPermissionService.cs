using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.Linq;
using ProManage.Modules.Connections;
using ProManage.Modules.Models.PermissionManagementForm;

namespace ProManage.Modules.Services
{
    /// <summary>
    /// Centralized service for managing global permissions that control MenuRibbon UC across all forms
    /// Global permissions act as first-level filters before form-specific permissions
    /// </summary>
    public static class GlobalPermissionService
    {
        #region Events

        /// <summary>
        /// Event fired when global permissions change for any user
        /// All forms with MenuRibbon UC should subscribe to this event
        /// </summary>
        public static event EventHandler<EventArgs> GlobalPermissionsChanged;

        #endregion

        #region Global Permission Checking

        /// <summary>
        /// Check if user has global permission for specific action
        /// </summary>
        /// <param name="userId">User ID to check</param>
        /// <param name="permissionType">Type of permission (read, new, edit, delete, print)</param>
        /// <returns>True if user has global permission</returns>
        public static bool HasGlobalPermission(int userId, string permissionType)
        {
            try
            {
                if (userId <= 0) return false;

                var globalPermissions = GetGlobalPermissions(userId);
                if (globalPermissions == null) return false;

                string lowerType = permissionType.ToLower();
                switch (lowerType)
                {
                    case "read":
                        return globalPermissions.CanReadUsers;
                    case "create":
                    case "new":
                        return globalPermissions.CanCreateUsers;
                    case "edit":
                        return globalPermissions.CanEditUsers;
                    case "delete":
                        return globalPermissions.CanDeleteUsers;
                    case "print":
                        return globalPermissions.CanPrintUsers;
                    default:
                        return false;
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error checking global permission: {ex.Message}");
                return false; // Default to deny on error
            }
        }

        /// <summary>
        /// Get all global permissions for a user
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>Global permission model or null if not found</returns>
        public static GlobalPermissionModel GetGlobalPermissions(int userId)
        {
            try
            {
                if (userId <= 0) return null;

                var globalPermission = PermissionService.GetGlobalPermissions(userId);
                if (globalPermission == null) return null;

                // Convert from database model to service model
                return new GlobalPermissionModel
                {
                    UserId = globalPermission.UserId,
                    CanReadUsers = true, // Assuming read is always true for existing users
                    CanCreateUsers = globalPermission.CanCreateUsers,
                    CanEditUsers = globalPermission.CanEditUsers,
                    CanDeleteUsers = globalPermission.CanDeleteUsers,
                    CanPrintUsers = globalPermission.CanPrintUsers
                };
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error getting global permissions: {ex.Message}");
                return null;
            }
        }

        #endregion

        #region Global Permission Updates

        /// <summary>
        /// Update global permissions for a user and notify all forms
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="permissions">New global permissions</param>
        /// <returns>True if update successful</returns>
        public static bool UpdateGlobalPermissions(int userId, GlobalPermissionModel permissions)
        {
            try
            {
                if (userId <= 0 || permissions == null) return false;

                // Convert from service model to database model
                var update = new GlobalPermissionUpdate
                {
                    UserId = userId,
                    CanCreateUsers = permissions.CanCreateUsers,
                    CanEditUsers = permissions.CanEditUsers,
                    CanDeleteUsers = permissions.CanDeleteUsers,
                    CanPrintUsers = permissions.CanPrintUsers
                };

                // Update in database
                bool success = PermissionService.UpdateGlobalPermissions(userId, update);
                
                if (success)
                {
                    // Clear cache to force refresh
                    PermissionService.ClearCache();
                    
                    // Notify all forms that global permissions have changed
                    OnGlobalPermissionsChanged(EventArgs.Empty);
                    
                    Debug.WriteLine($"Global permissions updated for user {userId}");
                }

                return success;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error updating global permissions: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Update specific global permission for a user
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="permissionType">Type of permission to update</param>
        /// <param name="hasPermission">New permission value</param>
        /// <returns>True if update successful</returns>
        public static bool UpdateGlobalPermission(int userId, string permissionType, bool hasPermission)
        {
            try
            {
                var currentPermissions = GetGlobalPermissions(userId);
                if (currentPermissions == null)
                {
                    // Create new global permissions record
                    currentPermissions = new GlobalPermissionModel
                    {
                        UserId = userId,
                        CanReadUsers = false,
                        CanCreateUsers = false,
                        CanEditUsers = false,
                        CanDeleteUsers = false,
                        CanPrintUsers = false
                    };
                }

                // Update the specific permission
                string lowerType = permissionType.ToLower();
                switch (lowerType)
                {
                    case "read":
                        currentPermissions.CanReadUsers = hasPermission;
                        break;
                    case "create":
                    case "new":
                        currentPermissions.CanCreateUsers = hasPermission;
                        break;
                    case "edit":
                        currentPermissions.CanEditUsers = hasPermission;
                        break;
                    case "delete":
                        currentPermissions.CanDeleteUsers = hasPermission;
                        break;
                    case "print":
                        currentPermissions.CanPrintUsers = hasPermission;
                        break;
                    default:
                        return false;
                }

                return UpdateGlobalPermissions(userId, currentPermissions);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error updating specific global permission: {ex.Message}");
                return false;
            }
        }

        #endregion

        #region Event Handling

        /// <summary>
        /// Fire the global permissions changed event
        /// </summary>
        /// <param name="args">Event arguments</param>
        private static void OnGlobalPermissionsChanged(EventArgs args)
        {
            try
            {
                GlobalPermissionsChanged?.Invoke(null, args);
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error firing global permissions changed event: {ex.Message}");
            }
        }

        #endregion

        #region Utility Methods

        /// <summary>
        /// Check if user has effective permission (global AND form-specific)
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="formName">Form name</param>
        /// <param name="permissionType">Permission type</param>
        /// <returns>True if user has both global and form-specific permission</returns>
        public static bool HasEffectivePermission(int userId, string formName, string permissionType)
        {
            try
            {
                // First check global permission (first-level filter)
                bool hasGlobal = HasGlobalPermission(userId, permissionType);
                if (!hasGlobal) return false;

                // Then check form-specific permission (second-level filter)
                var formPermissions = PermissionService.GetUserEffectivePermissions(userId, formName);
                if (formPermissions == null) return true; // If no form restrictions, allow

                string lowerType = permissionType.ToLower();
                switch (lowerType)
                {
                    case "read":
                        return formPermissions.CanRead;
                    case "create":
                    case "new":
                        return formPermissions.CanCreate;
                    case "edit":
                        return formPermissions.CanEdit;
                    case "delete":
                        return formPermissions.CanDelete;
                    case "print":
                        return formPermissions.CanPrint;
                    default:
                        return false;
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error checking effective permission: {ex.Message}");
                return false;
            }
        }

        #endregion
    }
}
