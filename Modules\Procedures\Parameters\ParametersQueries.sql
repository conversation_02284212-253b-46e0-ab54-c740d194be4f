-- Parameters Management SQL Queries
-- Usage: Contains all SQL queries for Parameters CRUD operations

-- [GetAllParameters]
-- Description: Retrieves all parameters ordered by parameter_code
SELECT
    id,
    parameter_code,
    parameter_value,
    parameter_type,
    purpose,
    created_at,
    modified_at
FROM parameters
ORDER BY parameter_code ASC;

-- [GetParameterById]
-- Description: Retrieves a specific parameter by ID
-- Parameters: @id (integer)
SELECT
    id,
    parameter_code,
    parameter_value,
    parameter_type,
    purpose,
    created_at,
    modified_at
FROM parameters
WHERE id = @id;

-- [GetParameterByCode]
-- Description: Retrieves a parameter by its code
-- Parameters: @parameter_code (varchar)
SELECT
    id,
    parameter_code,
    parameter_value,
    parameter_type,
    purpose,
    created_at,
    modified_at
FROM parameters
WHERE parameter_code = @parameter_code;

-- [InsertParameter]
-- Description: Inserts a new parameter record
-- Parameters: @parameter_code, @parameter_value, @parameter_type, @purpose
INSERT INTO parameters (
    parameter_code,
    parameter_value,
    parameter_type,
    purpose,
    created_at
) VALUES (
    @parameter_code,
    @parameter_value,
    @parameter_type,
    @purpose,
    CURRENT_TIMESTAMP
) RETURNING id;

-- [UpdateParameter]
-- Description: Updates an existing parameter record
-- Parameters: @id, @parameter_code, @parameter_value, @parameter_type, @purpose
UPDATE parameters
SET
    parameter_code = @parameter_code,
    parameter_value = @parameter_value,
    parameter_type = @parameter_type,
    purpose = @purpose,
    modified_at = CURRENT_TIMESTAMP
WHERE id = @id;

-- [DeleteParameter]
-- Description: Deletes a parameter by ID
-- Parameters: @id (integer)
DELETE FROM parameters
WHERE id = @id;

-- [DeleteParametersByIds]
-- Description: Deletes multiple parameters by their IDs
-- Parameters: @ids (array of integers)
DELETE FROM parameters
WHERE id = ANY(@ids);

-- [CheckParameterCodeExists]
-- Description: Checks if a parameter code already exists (for validation)
-- Parameters: @parameter_code, @exclude_id (optional)
SELECT COUNT(*) as count
FROM parameters
WHERE parameter_code = @parameter_code
AND (@exclude_id IS NULL OR id != @exclude_id);

-- [GetParametersCount]
-- Description: Gets the total count of parameters
SELECT COUNT(*) as total_count
FROM parameters;

-- [SearchParameters]
-- Description: Searches parameters by code, value, or purpose
-- Parameters: @search_term (varchar)
SELECT
    id,
    parameter_code,
    parameter_value,
    parameter_type,
    purpose,
    created_at,
    modified_at
FROM parameters
WHERE
    parameter_code ILIKE '%' || @search_term || '%' OR
    parameter_value ILIKE '%' || @search_term || '%' OR
    purpose ILIKE '%' || @search_term || '%'
ORDER BY parameter_code ASC;
