﻿<?xml version="1.0" encoding="utf-8"?>
<?xml-stylesheet type="text/xsl" href="_UpgradeWizard_Files\UpgradeWizardLog.xslt"?>
<log>
  <event>Get project references.</event>
  <event action="Analyze" type="Success" name="Assembly References" path="E:\Users\Faraz\source\repos\ProManage\ProManage.csproj" />
  <event action="Analyze" type="Success" name="Assembly References" path="E:\Users\Faraz\source\repos\ProManage\App.config" />
  <event action="Analyze" type="Success" name="Http Handlers" path="E:\Users\Faraz\source\repos\ProManage\App.config" />
  <event action="Analyze" type="Success" name="Handlers (IIS7+)" path="E:\Users\Faraz\source\repos\ProManage\App.config" />
  <event action="Analyze" type="Success" name="Config Sections" path="E:\Users\Faraz\source\repos\ProManage\App.config" />
  <event action="Analyze" type="Success" name="Render Extensions" path="E:\Users\Faraz\source\repos\ProManage\App.config" />
  <event action="Analyze" type="Success" name="Session State Providers" path="E:\Users\Faraz\source\repos\ProManage\App.config" />
  <event action="Analyze" type="Success" name="App.config" path="E:\Users\Faraz\source\repos\ProManage\App.config" />
  <event action="Analyze" type="Success" name="Assembly References" path="E:\Users\Faraz\source\repos\ProManage\Development.config" />
  <event action="Analyze" type="Success" name="Http Handlers" path="E:\Users\Faraz\source\repos\ProManage\Development.config" />
  <event action="Analyze" type="Success" name="Handlers (IIS7+)" path="E:\Users\Faraz\source\repos\ProManage\Development.config" />
  <event action="Analyze" type="Success" name="Config Sections" path="E:\Users\Faraz\source\repos\ProManage\Development.config" />
  <event action="Analyze" type="Success" name="Render Extensions" path="E:\Users\Faraz\source\repos\ProManage\Development.config" />
  <event action="Analyze" type="Success" name="Session State Providers" path="E:\Users\Faraz\source\repos\ProManage\Development.config" />
  <event action="Analyze" type="Success" name="Development.config" path="E:\Users\Faraz\source\repos\ProManage\Development.config" />
  <event action="Analyze" type="Success" name="DatabaseForm.resx" path="E:\Users\Faraz\source\repos\ProManage\Forms\DatabaseForm.resx" />
  <event action="Analyze" type="Success" name="EstimateForm.resx" path="E:\Users\Faraz\source\repos\ProManage\Forms\EstimateForm.resx" />
  <event action="Analyze" type="Success" name="UserManagementListForm.resx" path="E:\Users\Faraz\source\repos\ProManage\Forms\ListForms\UserManagementListForm.resx" />
  <event action="Analyze" type="Success" name="LoginForm.resx" path="E:\Users\Faraz\source\repos\ProManage\Forms\LoginForm.resx" />
  <event action="Analyze" type="Success" name="MainFrame.resx" path="E:\Users\Faraz\source\repos\ProManage\Forms\MainFrame.resx" />
  <event action="Analyze" type="Success" name="Form1.resx" path="E:\Users\Faraz\source\repos\ProManage\Forms\NewFolder1\Form1.resx" />
  <event action="Analyze" type="Success" name="ParametersForm.resx" path="E:\Users\Faraz\source\repos\ProManage\Forms\ParametersForm.resx" />
  <event action="Analyze" type="Success" name="MenuRibbon.resx" path="E:\Users\Faraz\source\repos\ProManage\Forms\ReusableForms\MenuRibbon.resx" />
  <event action="Analyze" type="Success" name="ParamEntryForm.resx" path="E:\Users\Faraz\source\repos\ProManage\Forms\ReusableForms\ParamEntryForm.resx" />
  <event action="Analyze" type="Success" name="PrintPreviewForm.resx" path="E:\Users\Faraz\source\repos\ProManage\Forms\ReusableForms\PrintPreviewForm.resx" />
  <event action="Analyze" type="Success" name="SQLQueryForm.resx" path="E:\Users\Faraz\source\repos\ProManage\Forms\SQLQueryForm.resx" />
  <event action="Analyze" type="Success" name="UserMasterForm.resx" path="E:\Users\Faraz\source\repos\ProManage\Forms\UserMasterForm.resx" />
  <event action="Analyze" type="Success" name="EstimateForm-PrintLayout.resx" path="E:\Users\Faraz\source\repos\ProManage\Modules\Reports\Estimate\EstimateForm-PrintLayout.resx" />
  <event action="Analyze" type="Success" name="Assembly References" path="E:\Users\Faraz\source\repos\ProManage\packages.config" />
  <event action="Analyze" type="Success" name="Http Handlers" path="E:\Users\Faraz\source\repos\ProManage\packages.config" />
  <event action="Analyze" type="Success" name="Handlers (IIS7+)" path="E:\Users\Faraz\source\repos\ProManage\packages.config" />
  <event action="Analyze" type="Success" name="Config Sections" path="E:\Users\Faraz\source\repos\ProManage\packages.config" />
  <event action="Analyze" type="Success" name="Render Extensions" path="E:\Users\Faraz\source\repos\ProManage\packages.config" />
  <event action="Analyze" type="Success" name="Session State Providers" path="E:\Users\Faraz\source\repos\ProManage\packages.config" />
  <event action="Analyze" type="Success" name="packages.config" path="E:\Users\Faraz\source\repos\ProManage\packages.config" />
  <event action="Analyze" type="Success" name="Resources.resx" path="E:\Users\Faraz\source\repos\ProManage\Properties\Resources.resx" />
  <event action="Analyze" type="Success" name="ProManage" path="E:\Users\Faraz\source\repos\ProManage\ProManage.csproj" />
  <event>Get project references.</event>
  <event action="Analyze" type="Success" name="Assembly References" path="E:\Users\Faraz\source\repos\ProManage\ProManage.Services\ProManage.Services.csproj" />
  <event action="Analyze" type="Success" name="ProManage.Services" path="E:\Users\Faraz\source\repos\ProManage\ProManage.Services\ProManage.Services.csproj" />
  <event action="Analyze" type="Success" name="ProManage" path="E:\Users\Faraz\source\repos\ProManage\ProManage.sln" />
</log>