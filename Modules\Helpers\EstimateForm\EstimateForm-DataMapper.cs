// EstimateForm Data Mapper - Maps data between form controls and model objects
// Usage: <PERSON><PERSON> loading data to form and extracting data from form

using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using ProManage.Modules.Models.EstimateForm;
using ProManage.Modules.Data.EstimateForm;

namespace ProManage.Modules.Helpers.EstimateForm
{
    public static class EstimateFormDataMapper
    {
        public static void LoadEstimateToForm(dynamic form, EstimateFormHeaderModel estimate)
        {
            try
            {
                Debug.WriteLine($"=== LoadEstimateToForm: Loading estimate ID={estimate.Id}, No={estimate.EstimateNo} ===");

                // Load header data to form controls
                form.txtEstimate.Text = estimate.EstimateNo ?? "";
                form.txtCustomer.Text = estimate.CustomerName ?? "";
                form.txtVehicle.Text = estimate.VehicleModel ?? "";
                form.txtVIN.Text = estimate.VIN ?? "";
                form.cbBrand.Text = estimate.Brand ?? "";
                form.cbLocation.Text = estimate.Location ?? "";
                form.txtSalesman.Text = estimate.SalesmanName ?? "";
                form.dpDocDate.DateTime = estimate.DocDate ?? DateTime.Now;
                form.txtDocRemarks.Text = estimate.Remarks ?? "";

                Debug.WriteLine($"Header data loaded - Customer: {estimate.CustomerName}, Vehicle: {estimate.VehicleModel}");

                // Load details to grid - use already loaded details if available
                Debug.WriteLine($"LoadEstimateToForm: Checking details - estimate.Details is {(estimate.Details == null ? "null" : $"not null with {estimate.Details.Count} items")}");

                if (estimate.Details != null && estimate.Details.Count > 0)
                {
                    Debug.WriteLine($"LoadEstimateToForm: Using pre-loaded details ({estimate.Details.Count} items)");
                    LoadEstimateDetailsToGrid(form, estimate.Details);
                }
                else
                {
                    Debug.WriteLine($"LoadEstimateToForm: Loading details from database for estimate ID {estimate.Id}");
                    LoadEstimateDetailsToGrid(form, estimate.Id);
                }

                // Ensure grid is in read-only mode after loading data (unless form is in edit mode)
                // This ensures the grid stays enabled for navigation but prevents editing
                if (!form.IsEditMode)
                {
                    EstimateFormGridManager.SetGridEditMode(form, false);
                }

                // Update status toggle state
                try
                {
                    // Temporarily disable event handler to prevent triggering during data loading
                    form.tglStatus.CheckedChanged -= form.BarToggleSwitchItem1_CheckedChanged;

                    // Update toggle button state and text based on estimate status
                    // RIGHT (checked/true) = Closed, LEFT (unchecked/false) = Active
                    form.tglStatus.Enabled = true;
                    form.tglStatus.Checked = estimate.Status;
                    form.tglStatus.Caption = estimate.Status ? "Closed" : "Active";

                    // Re-enable event handler
                    form.tglStatus.CheckedChanged += form.BarToggleSwitchItem1_CheckedChanged;

                    // Update button states based on estimate status
                    if (estimate.Status) // Closed
                    {
                        // Disable Edit and Delete buttons when estimate is closed
                        form.BarButtonItemEdit.Enabled = false;
                        form.BarButtonItemDelete.Enabled = false;
                        form.BarButtonItemSave.Enabled = false;
                        form.BarButtonItemCancel.Enabled = false;
                        form.BarButtonItemAddRow.Enabled = false;
                        Debug.WriteLine("Estimate is closed - Edit and Delete buttons disabled during data loading");
                    }
                    else // Active
                    {
                        // Enable normal button states when estimate is active
                        EstimateFormHelper.UpdateButtonStates(form, form.IsEditMode);
                        Debug.WriteLine("Estimate is active - Normal button states applied during data loading");
                    }

                    Debug.WriteLine($"Toggle updated - Status: {estimate.Status}, Caption: {form.tglStatus.Caption}");
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"Warning: Could not update status toggle: {ex.Message}");
                    // Make sure to re-enable event handler even if there was an error
                    try
                    {
                        form.tglStatus.CheckedChanged += form.BarToggleSwitchItem1_CheckedChanged;
                    }
                    catch { }
                }

                Debug.WriteLine($"=== LoadEstimateToForm: Completed for estimate {estimate.EstimateNo} ===");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in LoadEstimateToForm: {ex.Message}");
                throw new Exception($"Error loading estimate to form: {ex.Message}", ex);
            }
        }

        public static void LoadEstimateDetailsToGrid(dynamic form, int estimateId)
        {
            try
            {
                Debug.WriteLine($"=== LoadEstimateDetailsToGrid: Loading details for estimate ID {estimateId} ===");

                // Clear existing data
                form.GridDataTable.Clear();

                // Get details from repository
                var details = EstimateFormRepository.GetEstimateDetailsById(estimateId);
                Debug.WriteLine($"Retrieved {details.Count} detail records from repository");

                // Use the common method to populate grid
                PopulateGridWithDetails(form, details);

                Debug.WriteLine($"=== LoadEstimateDetailsToGrid: Loaded {details.Count} details successfully ===");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"ERROR in LoadEstimateDetailsToGrid: {ex.Message}");
                throw new Exception($"Error loading estimate details to grid: {ex.Message}", ex);
            }
        }

        public static void LoadEstimateDetailsToGrid(dynamic form, List<EstimateFormDetailModel> details)
        {
            try
            {
                Debug.WriteLine($"=== LoadEstimateDetailsToGrid: Loading {details.Count} pre-loaded details ===");

                // Clear existing data
                form.GridDataTable.Clear();

                // Use the common method to populate grid
                PopulateGridWithDetails(form, details);

                Debug.WriteLine($"=== LoadEstimateDetailsToGrid: Loaded {details.Count} details successfully ===");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"ERROR in LoadEstimateDetailsToGrid: {ex.Message}");
                throw new Exception($"Error loading estimate details to grid: {ex.Message}", ex);
            }
        }

        private static void PopulateGridWithDetails(dynamic form, List<EstimateFormDetailModel> details)
        {
            Debug.WriteLine($"=== PopulateGridWithDetails: Starting with {details.Count} details ===");

            // Add details to grid
            int serialNumber = 1;
            foreach (var detail in details)
            {
                var row = form.GridDataTable.NewRow();
                row["SerialNumber"] = serialNumber++;
                row["PartNumber"] = detail.PartNo ?? "";
                row["Description"] = detail.Description ?? "";
                row["Quantity"] = detail.Qty ?? 0;
                row["OEPrice"] = detail.OEPrice ?? 0;
                row["AFMPrice"] = detail.AFMPrice ?? 0;
                row["Remarks"] = detail.Remarks ?? "";
                row["Status"] = detail.ApproveStatus ?? false;

                form.GridDataTable.Rows.Add(row);
                Debug.WriteLine($"PopulateGridWithDetails: Added row {serialNumber - 1}: {detail.PartNo} - {detail.Description} - Status: {detail.ApproveStatus}");
            }

            Debug.WriteLine($"PopulateGridWithDetails: Grid now has {form.GridDataTable.Rows.Count} rows");

            // Refresh the grid display
            RefreshGridDisplay(form);

            Debug.WriteLine($"=== PopulateGridWithDetails: Completed ===");
        }

        public static void RefreshGridDisplay(dynamic form)
        {
            try
            {
                Debug.WriteLine($"=== RefreshGridDisplay: Starting - Grid has {form.GridDataTable?.Rows?.Count ?? 0} rows ===");

                form.GridControl1.RefreshDataSource();
                form.GridView1.RefreshData();
                form.GridView1.LayoutChanged();

                Debug.WriteLine($"=== RefreshGridDisplay: Completed - Grid should now display {form.GridDataTable?.Rows?.Count ?? 0} rows ===");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"ERROR in RefreshGridDisplay: {ex.Message}");
                Debug.WriteLine($"Stack trace: {ex.StackTrace}");
                // Don't throw - this is not critical
            }
        }

        public static EstimateFormHeaderModel ExtractEstimateFromForm(dynamic form)
        {
            try
            {
                Debug.WriteLine("=== ExtractEstimateFromForm: Starting ===");

                var estimate = new EstimateFormHeaderModel
                {
                    EstimateNo = form.txtEstimate.Text?.Trim(),
                    CustomerName = form.txtCustomer.Text?.Trim(),
                    VehicleModel = form.txtVehicle.Text?.Trim(),
                    VIN = form.txtVIN.Text?.Trim(),
                    Brand = form.cbBrand.Text?.Trim(),
                    Location = form.cbLocation.Text?.Trim(),
                    SalesmanName = form.txtSalesman.Text?.Trim(),
                    DocDate = form.dpDocDate.DateTime,
                    Remarks = form.txtDocRemarks.Text?.Trim(),
                    Status = true, // true = Open/Editable, false = Closed/Locked
                    CreatedAt = DateTime.Now
                };

                // Extract details from grid
                estimate.Details = ExtractDetailsFromGrid(form);

                Debug.WriteLine($"Extracted estimate: {estimate.EstimateNo} with {estimate.Details.Count} details");
                Debug.WriteLine("=== ExtractEstimateFromForm: Completed ===");

                return estimate;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in ExtractEstimateFromForm: {ex.Message}");
                throw new Exception($"Error extracting estimate from form: {ex.Message}", ex);
            }
        }

        public static List<EstimateFormDetailModel> ExtractDetailsFromGrid(dynamic form)
        {
            try
            {
                Debug.WriteLine("=== ExtractDetailsFromGrid: Starting ===");

                var details = new List<EstimateFormDetailModel>();
                var gridDataTable = form.GridDataTable as DataTable;

                if (gridDataTable == null)
                {
                    Debug.WriteLine("GridDataTable is null, returning empty details list");
                    return details;
                }

                int serialNumber = 1;
                foreach (DataRow row in gridDataTable.Rows)
                {
                    // Skip empty rows
                    if (IsEmptyRow(row))
                    {
                        Debug.WriteLine($"Skipping empty row {serialNumber}");
                        continue;
                    }

                    var detail = new EstimateFormDetailModel
                    {
                        SerialNo = serialNumber++,
                        PartNo = GetStringValue(row, "PartNumber"),
                        Description = GetStringValue(row, "Description"),
                        Qty = GetIntValue(row, "Quantity"),
                        OEPrice = GetDecimalValue(row, "OEPrice"),
                        AFMPrice = GetDecimalValue(row, "AFMPrice"),
                        Remarks = GetStringValue(row, "Remarks"),
                        ApproveStatus = GetBooleanValue(row, "Status")
                    };

                    details.Add(detail);
                    Debug.WriteLine($"Extracted detail {detail.SerialNo}: {detail.PartNo} - {detail.Description} - Status: {detail.ApproveStatus}");
                }

                Debug.WriteLine($"=== ExtractDetailsFromGrid: Extracted {details.Count} valid details ===");
                return details;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in ExtractDetailsFromGrid: {ex.Message}");
                throw new Exception($"Error extracting details from grid: {ex.Message}", ex);
            }
        }

        private static bool IsEmptyRow(DataRow row)
        {
            try
            {
                string partNo = GetStringValue(row, "PartNumber");
                string description = GetStringValue(row, "Description");
                int? qty = GetIntValue(row, "Quantity");
                decimal? oePrice = GetDecimalValue(row, "OEPrice");
                decimal? afmPrice = GetDecimalValue(row, "AFMPrice");

                // Row is empty if all key fields are empty/zero
                return string.IsNullOrWhiteSpace(partNo) &&
                       string.IsNullOrWhiteSpace(description) &&
                       (qty == null || qty == 0) &&
                       (oePrice == null || oePrice == 0) &&
                       (afmPrice == null || afmPrice == 0);
            }
            catch
            {
                return true; // If we can't read the row, consider it empty
            }
        }

        private static string GetStringValue(DataRow row, string columnName)
        {
            try
            {
                if (row[columnName] == null || row[columnName] == DBNull.Value)
                    return null;
                return row[columnName].ToString()?.Trim();
            }
            catch
            {
                return null;
            }
        }

        private static int? GetIntValue(DataRow row, string columnName)
        {
            try
            {
                if (row[columnName] == null || row[columnName] == DBNull.Value)
                    return null;

                if (int.TryParse(row[columnName].ToString(), out int result))
                    return result;

                if (decimal.TryParse(row[columnName].ToString(), out decimal decimalResult))
                    return (int)decimalResult;

                return null;
            }
            catch
            {
                return null;
            }
        }

        private static decimal? GetDecimalValue(DataRow row, string columnName)
        {
            try
            {
                if (row[columnName] == null || row[columnName] == DBNull.Value)
                    return null;

                if (decimal.TryParse(row[columnName].ToString(), out decimal result))
                    return result;

                return null;
            }
            catch
            {
                return null;
            }
        }

        private static bool? GetBooleanValue(DataRow row, string columnName)
        {
            try
            {
                if (row[columnName] == null || row[columnName] == DBNull.Value)
                    return false;

                if (bool.TryParse(row[columnName].ToString(), out bool result))
                    return result;

                // Handle numeric values (0 = false, 1 = true)
                if (int.TryParse(row[columnName].ToString(), out int intResult))
                    return intResult != 0;

                return false;
            }
            catch
            {
                return false;
            }
        }

        public static void MapFormToModel(dynamic form, EstimateFormHeaderModel estimate)
        {
            try
            {
                Debug.WriteLine("=== MapFormToModel: Starting ===");

                estimate.EstimateNo = form.txtEstimate.Text?.Trim();
                estimate.CustomerName = form.txtCustomer.Text?.Trim();
                estimate.VehicleModel = form.txtVehicle.Text?.Trim();
                estimate.VIN = form.txtVIN.Text?.Trim();
                estimate.Brand = form.cbBrand.Text?.Trim();
                estimate.Location = form.cbLocation.Text?.Trim();
                estimate.SalesmanName = form.txtSalesman.Text?.Trim();
                estimate.DocDate = form.dpDocDate.DateTime;
                estimate.Remarks = form.txtDocRemarks.Text?.Trim();
                // Note: Status is managed by the toggle button, don't override it here

                Debug.WriteLine($"Mapped form data to model: {estimate.EstimateNo} - {estimate.CustomerName}");
                Debug.WriteLine("=== MapFormToModel: Completed ===");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in MapFormToModel: {ex.Message}");
                throw new Exception($"Error mapping form to model: {ex.Message}", ex);
            }
        }

        public static List<EstimateFormDetailModel> MapGridToDetails(dynamic form, int estimateId)
        {
            try
            {
                Debug.WriteLine("=== MapGridToDetails: Starting ===");

                var details = ExtractDetailsFromGrid(form);

                // Set the estimate ID for all details
                foreach (var detail in details)
                {
                    detail.EstimateId = estimateId;
                }

                Debug.WriteLine($"Mapped {details.Count} details with estimate ID {estimateId}");
                Debug.WriteLine("=== MapGridToDetails: Completed ===");

                return details;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in MapGridToDetails: {ex.Message}");
                throw new Exception($"Error mapping grid to details: {ex.Message}", ex);
            }
        }
    }
}
