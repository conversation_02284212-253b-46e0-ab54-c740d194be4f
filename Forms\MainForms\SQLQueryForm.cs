using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.Drawing;
using System.IO;
using System.Windows.Forms;
using DevExpress.Utils;
using DevExpress.XtraEditors;
using DevExpress.XtraEditors.Controls;
using DevExpress.XtraGrid.Views.Grid;
using ProManage.Modules.Connections;
using ProManage.Modules.Data;
using ProManage.Modules.Helpers;
using ProManage.Modules.UI;

namespace ProManage.Forms
{
    /// <summary>
    /// Form for executing SQL queries against the database.
    /// Provides a query editor, results grid, and table selection functionality.
    /// </summary>
    public partial class SQLQueryForm : Form
    {
        // Store currently selected table
        private string selectedTable = string.Empty;

        // Stopwatch for measuring query execution time
        private readonly Stopwatch _stopwatch = new Stopwatch();

        // Enum for status message types
        private enum StatusType
        {
            Info,
            Success,
            Warning,
            Error
        }

        /// <summary>
        /// Initializes a new instance of the SQLQueryForm class.
        /// </summary>
        public SQLQueryForm()
        {
            // Initialize components first
            InitializeComponent();

            // Then set up the form
            SetupForm();

            // Set form title
            UpdateFormTitle();

            // Load tables on form load
            LoadTables();

            // Set form to dock fill when used as MDI child
            this.Dock = DockStyle.Fill;
            this.FormBorderStyle = FormBorderStyle.None;
        }

        /// <summary>
        /// Sets up the form with initial values and styles
        /// </summary>
        private void SetupForm()
        {
            // Wire up event handlers
            btnLoadTables.Click += btnLoadTables_Click;
            btnExecute.Click += btnExecute_Click;
            btnClear.Click += btnClear_Click;
            cboTables.SelectedIndexChanged += cboTables_SelectedIndexChanged;
            btnLoad.Click += btnLoad_Click; // Load button for loading selected table

            // Set up grid view
            gridViewResults.OptionsBehavior.Editable = false;
            gridViewResults.OptionsView.ShowGroupPanel = false;
            gridViewResults.OptionsView.ShowIndicator = false;

            // Add grid event handlers
            gridResults.ProcessGridKey += GridResults_ProcessGridKey;
            gridResults.DataSourceChanged += GridResults_DataSourceChanged;

            // Set up text editor
            txtQuery.Properties.ScrollBars = ScrollBars.Both;
            txtQuery.Properties.WordWrap = false;
        }

        /// <summary>
        /// Handles the Load Tables button click
        /// </summary>
        private void btnLoadTables_Click(object sender, EventArgs e)
        {
            LoadTables();
        }

        /// <summary>
        /// Handles the Execute button click
        /// </summary>
        private void btnExecute_Click(object sender, EventArgs e)
        {
            ExecuteQuery();
        }

        /// <summary>
        /// Handles the Clear button click
        /// </summary>
        private void btnClear_Click(object sender, EventArgs e)
        {
            ClearResults();
        }

        /// <summary>
        /// Executes the current SQL query
        /// </summary>
        private void ExecuteQuery()
        {
            try
            {
                string query = txtQuery.Text.Trim();
                if (string.IsNullOrEmpty(query))
                {
                    SetStatus("Please enter a query to execute", StatusType.Warning);
                    return;
                }

                ProgressIndicatorService.Instance.ShowProgress();
                _stopwatch.Restart();

                string errorMessage;
                DataTable results;

                // Execute query and get results
                if (QueryExecutor.IsSelectQuery(query))
                {
                    // Check for any table or column name placeholders in the format @name
                    var paramMatches = System.Text.RegularExpressions.Regex.Matches(query, @"@(\w+)");
                    bool hasTableNameParam = false;

                    foreach (System.Text.RegularExpressions.Match match in paramMatches)
                    {
                        string paramName = match.Value;
                        string nameWithoutAt = match.Groups[1].Value;

                        // Check if this looks like a table name parameter
                        if (nameWithoutAt.EndsWith("tableName", StringComparison.OrdinalIgnoreCase) ||
                            nameWithoutAt.EndsWith("table_name", StringComparison.OrdinalIgnoreCase))
                        {
                            hasTableNameParam = true;

                            // Replace with properly quoted table name
                            if (!string.IsNullOrEmpty(selectedTable))
                            {
                                query = query.Replace($"public.{paramName}", $"public.\"{selectedTable}\"");
                                query = query.Replace(paramName, $"\"{selectedTable}\"");
                            }
                        }
                    }

                    // Update the query in the text box if we modified it
                    if (hasTableNameParam)
                    {
                        txtQuery.Text = query;
                    }

                    // Execute the modified query
                    results = QueryExecutor.ExecuteSelectQuery(query, out errorMessage);
                }
                else
                {
                    int rowsAffected = QueryExecutor.ExecuteNonQuery(query, out errorMessage);
                    results = new DataTable();
                    results.Columns.Add("RowsAffected");
                    results.Rows.Add(rowsAffected);
                }

                _stopwatch.Stop();

                if (!string.IsNullOrEmpty(errorMessage))
                {
                    SetStatus($"Error executing query: {errorMessage}", StatusType.Error);
                    return;
                }

                // First fully reset the grid view to ensure clean state
                ResetGridView();

                // Apply the new data source after reset
                gridResults.DataSource = results;

                // Force column recreation to ensure proper schema
                gridViewResults.PopulateColumns();
                gridViewResults.BestFitColumns();

                // Configure columns and refresh grid
                ConfigureGridColumns();
                RefreshGridView();

                // Verify grid is properly showing data and fix if needed
                EnsureGridDataBinding(results);

                string statusMessage = results.Rows.Count == 1 ? "row" : "rows";
                SetStatus($"Query executed successfully. {results.Rows.Count} {statusMessage} returned. ({_stopwatch.ElapsedMilliseconds}ms)",
                    StatusType.Success);
            }
            catch (Exception ex)
            {
                SetStatus($"Error executing query: {ex.Message}", StatusType.Error);
                Debug.WriteLine($"Error in ExecuteQuery: {ex.Message}");
                if (ex.InnerException != null)
                {
                    Debug.WriteLine($"Inner exception: {ex.InnerException.Message}");
                }
            }
            finally
            {
                ProgressIndicatorService.Instance.HideProgress();
            }
        }

        /// <summary>
        /// Handles the Tables combobox selection change
        /// </summary>
        private void cboTables_SelectedIndexChanged(object sender, EventArgs e)
        {
            if (cboTables.EditValue != null)
            {
                // Get and store the selected table name
                selectedTable = cboTables.EditValue.ToString();

                if (!string.IsNullOrEmpty(selectedTable))
                {
                    // Clear the grid completely using our reset method
                    ResetGridView();

                    // Update the form title
                    UpdateFormTitle();

                    // Show what we're about to do
                    SetStatus($"Selected table: {selectedTable}. Click 'Load' button to view data.", StatusType.Info);
                }
            }
        }

        /// <summary>
        /// Loads tables from the database into the tables combobox
        /// </summary>
        private void LoadTables()
        {
            try
            {
                ProgressIndicatorService.Instance.ShowProgress();
                SetStatus("Loading tables...", StatusType.Info);

                string query = File.ReadAllText(Path.Combine(AppDomain.CurrentDomain.BaseDirectory,
                    "Modules", "Procedures", SQLQueries.SQLQuery.MODULE_NAME, $"{SQLQueries.SQLQuery.GET_ALL_TABLES}.sql"));
                string errorMessage;
                DataTable tables = QueryExecutor.ExecuteSelectQuery(query, out errorMessage);

                if (!string.IsNullOrEmpty(errorMessage))
                {
                    SetStatus($"Error loading tables: {errorMessage}", StatusType.Error);
                    return;
                }

                cboTables.Properties.Items.Clear();
                if (tables != null && tables.Rows.Count > 0)
                {
                    foreach (DataRow row in tables.Rows)
                    {
                        cboTables.Properties.Items.Add(row["table_name"].ToString());
                    }
                    SetStatus($"Loaded {tables.Rows.Count} tables", StatusType.Success);

                    // Clear grid and show message
                    gridResults.DataSource = null;
                    txtQuery.Text = "-- Select a table from the dropdown and click 'Load' to view its data";
                }
                else
                {
                    SetStatus("No tables found in database", StatusType.Warning);
                }
            }
            catch (Exception ex)
            {
                SetStatus($"Error loading tables: {ex.Message}", StatusType.Error);
                Debug.WriteLine($"Error in LoadTables: {ex.Message}");
                if (ex.InnerException != null)
                {
                    Debug.WriteLine($"Inner exception: {ex.InnerException.Message}");
                }
            }
            finally
            {
                ProgressIndicatorService.Instance.HideProgress();
            }
        }

        /// <summary>
        /// Configures grid columns with appropriate settings
        /// </summary>
        private void ConfigureGridColumns()
        {
            // First clear existing column settings
            foreach (DevExpress.XtraGrid.Columns.GridColumn col in gridViewResults.Columns)
            {
                col.Summary.Clear();
            }

            // Configure grid view options
            gridViewResults.OptionsView.ShowAutoFilterRow = true;
            gridViewResults.OptionsView.ShowFooter = true;
            gridViewResults.OptionsView.ColumnAutoWidth = false;
            gridViewResults.OptionsView.ShowFilterPanelMode = DevExpress.XtraGrid.Views.Base.ShowFilterPanelMode.ShowAlways;
            gridViewResults.OptionsFind.AllowFindPanel = true;
            gridViewResults.OptionsFind.AlwaysVisible = true;
            gridViewResults.OptionsFind.ShowCloseButton = false;
            gridViewResults.OptionsFind.HighlightFindResults = true;

            // Load table structure to get better column information if we have a selected table
            DataTable columnInfo = null;
            if (!string.IsNullOrEmpty(selectedTable))
            {
                columnInfo = LoadTableStructure(selectedTable);
            }

            foreach (DevExpress.XtraGrid.Columns.GridColumn col in gridViewResults.Columns)
            {
                // Make sure column is visible
                col.Visible = true;
                col.VisibleIndex = gridViewResults.Columns.IndexOf(col);

                // Basic column settings
                col.OptionsColumn.AllowSort = DevExpress.Utils.DefaultBoolean.True;
                col.OptionsFilter.AllowFilter = true;
                col.OptionsColumn.AllowSize = true;
                col.OptionsColumn.AllowMove = true;
                col.AppearanceHeader.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
                col.AppearanceHeader.Font = new Font(col.AppearanceHeader.Font, FontStyle.Bold);
                col.BestFit(); // Auto-size based on content

                // Set minimum width to prevent columns from becoming too narrow
                col.MinWidth = 80;
                if (col.Width < col.MinWidth) col.Width = col.MinWidth;

                // Get column information from our table structure if available
                if (columnInfo != null)
                {
                    // Try to find column info for better formatting
                    DataRow[] colRows = columnInfo.Select($"column_name = '{col.FieldName}'");
                    if (colRows.Length > 0)
                    {
                        string dataType = colRows[0]["data_type"].ToString().ToLower();

                        // Set tooltip with data type information
                        col.ToolTip = $"{col.GetCaption()} ({dataType})";

                        // Configure based on data type from schema
                        if (dataType.Contains("int") || dataType.Contains("serial"))
                        {
                            col.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
                            col.DisplayFormat.FormatString = "N0";
                            col.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
                            col.Summary.Add(DevExpress.Data.SummaryItemType.Count);
                            col.Summary.Add(DevExpress.Data.SummaryItemType.Sum);
                        }
                        else if (dataType.Contains("numeric") || dataType.Contains("decimal") ||
                                 dataType.Contains("double") || dataType.Contains("float") ||
                                 dataType.Contains("real"))
                        {
                            col.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
                            col.DisplayFormat.FormatString = "N2";
                            col.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
                            col.Summary.Add(DevExpress.Data.SummaryItemType.Sum);
                            col.Summary.Add(DevExpress.Data.SummaryItemType.Average);
                        }
                        else if (dataType.Contains("date") || dataType.Contains("time"))
                        {
                            col.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
                            col.DisplayFormat.FormatString = "g";
                            col.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
                        }
                        else if (dataType.Contains("bool"))
                        {
                            col.OptionsColumn.ShowInCustomizationForm = true;
                            col.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
                        }
                        else
                        {
                            // Text-based columns
                            col.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
                        }
                    }
                }
                else
                {
                    // No schema info, use .NET types
                    // Configure based on data type
                    if (col.ColumnType == typeof(decimal) || col.ColumnType == typeof(double) || col.ColumnType == typeof(float))
                    {
                        col.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
                        col.DisplayFormat.FormatString = "N2";
                        col.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
                        col.Summary.Add(DevExpress.Data.SummaryItemType.Sum);
                        col.Summary.Add(DevExpress.Data.SummaryItemType.Average);
                    }
                    else if (col.ColumnType == typeof(int) || col.ColumnType == typeof(long))
                    {
                        col.DisplayFormat.FormatType = DevExpress.Utils.FormatType.Numeric;
                        col.DisplayFormat.FormatString = "N0";
                        col.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Far;
                        col.Summary.Add(DevExpress.Data.SummaryItemType.Count);
                        col.Summary.Add(DevExpress.Data.SummaryItemType.Sum);
                    }
                    else if (col.ColumnType == typeof(DateTime))
                    {
                        col.DisplayFormat.FormatType = DevExpress.Utils.FormatType.DateTime;
                        col.DisplayFormat.FormatString = "g";
                        col.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
                    }
                    else if (col.ColumnType == typeof(bool))
                    {
                        col.OptionsColumn.ShowInCustomizationForm = true;
                        col.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Center;
                    }
                    else
                    {
                        col.AppearanceCell.TextOptions.HAlignment = DevExpress.Utils.HorzAlignment.Near;
                    }

                    // Add tooltips
                    col.ToolTip = $"{col.GetCaption()} ({col.ColumnType.Name})";
                }
            }

            // Configure grid behaviors
            gridViewResults.OptionsBehavior.Editable = false;
            gridViewResults.OptionsBehavior.ReadOnly = true;
            gridViewResults.OptionsBehavior.AllowIncrementalSearch = true;
            gridViewResults.OptionsBehavior.AllowPixelScrolling = DevExpress.Utils.DefaultBoolean.True;

            // Save/restore layout
            string layoutFile = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "GridLayouts", "SQLQueryResults.xml");
            if (File.Exists(layoutFile))
            {
                try
                {
                    gridViewResults.RestoreLayoutFromXml(layoutFile);
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"Error restoring grid layout: {ex.Message}");
                }
            }

            // Handle layout saving when form closes
            this.FormClosing += (s, e) => {
                try
                {
                    string layoutPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "GridLayouts");
                    if (!Directory.Exists(layoutPath))
                    {
                        Directory.CreateDirectory(layoutPath);
                    }
                    gridViewResults.SaveLayoutToXml(layoutFile);
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"Error saving grid layout: {ex.Message}");
                }
            };
        }

        /// <summary>
        /// Clears the results grid and status labels
        /// </summary>
        private void ClearResults()
        {
            // Use our dedicated grid reset method for thorough cleaning
            ResetGridView();

            // Clear status information
            lblStatus.Text = string.Empty;
            txtQuery.Text = string.Empty;

            // Reset selected table
            selectedTable = string.Empty;
            UpdateFormTitle();

            // Let the user know the grid has been cleared
            SetStatus("Grid cleared. Select a table and click 'Load' to view data.", StatusType.Info);
        }

        /// <summary>
        /// Sets the status message with appropriate styling
        /// </summary>
        private void SetStatus(string message, StatusType type)
        {
            Debug.WriteLine($"Setting status: {message} ({type})");
            lblStatus.Text = message;

            switch (type)
            {
                case StatusType.Success:
                    lblStatus.ForeColor = Color.Green;
                    break;
                case StatusType.Warning:
                    lblStatus.ForeColor = Color.Orange;
                    break;
                case StatusType.Error:
                    lblStatus.ForeColor = Color.Red;
                    break;
                default:
                    lblStatus.ForeColor = SystemColors.ControlText;
                    break;
            }
        }

        /// <summary>
        /// Handles the Load button click event to load data for the selected table
        /// </summary>
        private void btnLoad_Click(object sender, EventArgs e)
        {
            // If a table is selected, load its data
            if (cboTables.EditValue != null && !string.IsNullOrEmpty(cboTables.EditValue.ToString()))
            {
                string tableName = cboTables.EditValue.ToString();

                // Store the selected table name
                selectedTable = tableName;

                // Reset grid completely before loading new data
                ResetGridView();

                // Update form title
                UpdateFormTitle();

                // Load the data
                LoadTableData(tableName);
            }
            else
            {
                SetStatus("Please select a table first", StatusType.Warning);
            }
        }

        /// <summary>
        /// Loads data for the specified table
        /// </summary>
        private void LoadTableData(string tableName)
        {
            if (string.IsNullOrEmpty(tableName))
            {
                SetStatus("Table name cannot be empty", StatusType.Warning);
                return;
            }

            try
            {
                ProgressIndicatorService.Instance.ShowProgress();
                _stopwatch.Restart();

                // Create the SQL query with the table name directly substituted
                // We can't use parameters for table names in SQL
                string query = $"SELECT * FROM public.\"{tableName}\"";

                // Set query text in the editor
                txtQuery.Text = $"-- Getting data from table: {tableName}\n{query}";

                string errorMessage;
                // Execute the query without parameters since we've built the query with proper quoting
                DataTable results = QueryExecutor.ExecuteSelectQuery(query, out errorMessage);

                // Log what table we're currently showing
                Debug.WriteLine($"Loaded table: {tableName}");

                // Update form title with current table
                UpdateFormTitle();

                _stopwatch.Stop();

                if (!string.IsNullOrEmpty(errorMessage))
                {
                    SetStatus($"Error loading table data: {errorMessage}", StatusType.Error);
                    return;
                }

                if (results == null)
                {
                    SetStatus($"No data returned from table '{tableName}'", StatusType.Warning);
                    return;
                }

                // First fully reset the grid view to ensure clean state
                ResetGridView();

                // Apply the new data source after reset
                gridResults.DataSource = results;

                // Force column recreation to ensure proper schema
                gridViewResults.PopulateColumns();
                gridViewResults.BestFitColumns();

                // Configure columns and refresh grid
                ConfigureGridColumns();
                RefreshGridView();

                // Verify grid is properly showing data and fix if needed
                EnsureGridDataBinding(results);

                string statusMessage = results.Rows.Count == 1 ? "row" : "rows";
                lblRowCount.Text = $"Row Count: {results.Rows.Count}";
                lblExecutionTime.Text = $"Execution Time: {_stopwatch.ElapsedMilliseconds}ms";
                SetStatus($"Loaded {results.Rows.Count} {statusMessage} from table '{tableName}' ({_stopwatch.ElapsedMilliseconds}ms)",
                    StatusType.Success);
            }
            catch (Exception ex)
            {
                // Clear any results that might be displayed
                gridResults.DataSource = null;
                RefreshGridView();

                // Show detailed error message
                SetStatus($"Error loading table data: {ex.Message}", StatusType.Error);
                Debug.WriteLine($"Error in LoadTableData: {ex.Message}");
                if (ex.InnerException != null)
                {
                    Debug.WriteLine($"Inner exception: {ex.InnerException.Message}");
                    SetStatus($"Error loading table data: {ex.Message} - {ex.InnerException.Message}", StatusType.Error);
                }
            }
            finally
            {
                ProgressIndicatorService.Instance.HideProgress();
            }
        }

        /// <summary>
        /// Loads structure information for the specified table
        /// </summary>
        private DataTable LoadTableStructure(string tableName)
        {
            if (string.IsNullOrEmpty(tableName))
                return null;

            try
            {
                // Get the SQL query from file
                string queryPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory,
                    "Modules", "Procedures", SQLQueries.SQLQuery.MODULE_NAME, $"{SQLQueries.SQLQuery.GET_TABLE_COLUMNS}.sql");

                if (!File.Exists(queryPath))
                {
                    Debug.WriteLine($"Query file not found: {SQLQueries.SQLQuery.GET_TABLE_COLUMNS}.sql");
                    return null;
                }

                string query = File.ReadAllText(queryPath);

                // Create parameters dictionary
                var parameters = new Dictionary<string, object>
                {
                    { "@table_name", tableName }
                };

                string errorMessage;
                DataTable results = QueryExecutor.ExecuteSelectQueryWithParams(query, parameters, out errorMessage);

                if (!string.IsNullOrEmpty(errorMessage))
                {
                    Debug.WriteLine($"Error loading table structure: {errorMessage}");
                    return null;
                }

                return results;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in LoadTableStructure: {ex.Message}");
                if (ex.InnerException != null)
                {
                    Debug.WriteLine($"Inner exception: {ex.InnerException.Message}");
                }
                return null;
            }
        }

        /// <summary>
        /// Ensures the data grid is properly refreshed after data source changes
        /// </summary>
        private void RefreshGridView()
        {
            try
            {
                // Clear any filter and sort before refreshing
                gridViewResults.ClearColumnsFilter();
                gridViewResults.ClearSorting();

                // Update layout and display options to ensure columns are visible
                gridViewResults.LayoutChanged();

                // Force layout refresh - these are key methods for grid refresh
                gridViewResults.RefreshData();
                gridResults.RefreshDataSource();

                // Ensure column visibility and order
                for (int i = 0; i < gridViewResults.Columns.Count; i++)
                {
                    var col = gridViewResults.Columns[i];
                    col.Visible = true;
                    col.VisibleIndex = i;

                    // Ensure column caption and tooltip are set
                    if (string.IsNullOrEmpty(col.Caption))
                    {
                        col.Caption = col.FieldName;
                    }

                    // Ensure it's correctly configured
                    col.OptionsColumn.AllowEdit = false;
                    col.OptionsColumn.ReadOnly = true;
                }

                // Repaint the grid
                gridResults.Refresh();

                // Apply automatic column sizing
                gridViewResults.BestFitColumns();

                // Update row count display if needed
                if (gridResults.DataSource is DataTable dataTable)
                {
                    // Set the row count and timing
                    lblRowCount.Text = $"Row Count: {dataTable.Rows.Count}";
                    lblExecutionTime.Text = $"Execution Time: {_stopwatch.ElapsedMilliseconds}ms";

                    // Debug output to confirm data is loaded
                    Debug.WriteLine($"Grid refreshed with {dataTable.Rows.Count} rows and {dataTable.Columns.Count} columns");
                }
                else
                {
                    // Clear stats if no data
                    lblRowCount.Text = string.Empty;
                    lblExecutionTime.Text = string.Empty;
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in RefreshGridView: {ex.Message}");
            }
        }

        /// <summary>
        /// Updates the form title to include the currently selected table
        /// </summary>
        private void UpdateFormTitle()
        {
            if (!string.IsNullOrEmpty(selectedTable))
            {
                this.Text = $"SQL Query - Table: {selectedTable}";
            }
            else
            {
                this.Text = "SQL Query";
            }
        }

        /// <summary>
        /// Fully resets the grid view to ensure clean state before loading new data
        /// </summary>
        private void ResetGridView()
        {
            try
            {
                // Important: Unbind the data source first
                gridResults.DataSource = null;

                // Clear all collections and settings
                gridViewResults.Columns.Clear();
                gridViewResults.ClearColumnsFilter();
                gridViewResults.ClearSorting();
                gridViewResults.ClearSelection();
                gridViewResults.ClearGrouping();

                // Reset any custom grid settings
                gridViewResults.OptionsBehavior.Editable = false;
                gridViewResults.OptionsBehavior.ReadOnly = true;

                // Force layout update to ensure clean state
                gridViewResults.LayoutChanged();
                gridResults.ForceInitialize();

                // Update UI labels
                lblRowCount.Text = string.Empty;
                lblExecutionTime.Text = string.Empty;

                // Force garbage collection to free up any lingering references
                GC.Collect();
                GC.WaitForPendingFinalizers();
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in ResetGridView: {ex.Message}");
            }
        }

        /// <summary>
        /// Checks if the grid is properly showing data and attempts to fix it if not
        /// </summary>
        private void EnsureGridDataBinding(DataTable data)
        {
            try
            {
                // If no columns are showing but we have data
                if (gridViewResults.Columns.Count == 0 && data != null && data.Columns.Count > 0)
                {
                    Debug.WriteLine("Grid appears to have binding issues - fixing...");

                    // Try setting the data source in different ways
                    gridResults.DataMember = null;
                    gridResults.DataSource = null;
                    gridResults.RefreshDataSource();
                    gridViewResults.PopulateColumns();

                    // Now try binding again
                    gridResults.DataSource = data;
                    gridViewResults.PopulateColumns();

                    // If still no columns, try the nuclear option - rebuild the grid
                    if (gridViewResults.Columns.Count == 0)
                    {
                        Debug.WriteLine("Grid still not showing columns - rebuilding grid...");
                        ForceGridRebuild();
                        gridResults.DataSource = data;
                    }

                    // Configure columns
                    ConfigureGridColumns();

                    // Final refresh
                    RefreshGridView();
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in EnsureGridDataBinding: {ex.Message}");
            }
        }

        /// <summary>
        /// Handles grid keyboard events for better navigation
        /// </summary>
        private void GridResults_ProcessGridKey(object sender, KeyEventArgs e)
        {
            // Enable some keyboard shortcuts for better navigation
            if (e.KeyCode == Keys.Home && e.Control)
            {
                // Go to first row
                if (gridViewResults.RowCount > 0)
                    gridViewResults.FocusedRowHandle = 0;
                e.Handled = true;
            }
            else if (e.KeyCode == Keys.End && e.Control)
            {
                // Go to last row
                if (gridViewResults.RowCount > 0)
                    gridViewResults.FocusedRowHandle = gridViewResults.RowCount - 1;
                e.Handled = true;
            }
        }

        /// <summary>
        /// Handles grid data source changes to ensure proper column setup
        /// </summary>
        private void GridResults_DataSourceChanged(object sender, EventArgs e)
        {
            Debug.WriteLine($"Grid data source changed: {(gridResults.DataSource == null ? "null" : "not null")}");

            // If we have a data source, make sure columns are visible and properly sized
            if (gridResults.DataSource != null)
            {
                // Ensure all columns are visible
                foreach (DevExpress.XtraGrid.Columns.GridColumn col in gridViewResults.Columns)
                {
                    col.Visible = true;
                }

                // Best fit columns for ideal display
                gridViewResults.BestFitColumns();
            }
        }

        /// <summary>
        /// Forces a complete grid rebuild by recreating the grid control
        /// Use this as a last resort when normal refreshing doesn't work
        /// </summary>
        private void ForceGridRebuild()
        {
            try
            {
                // Remember the parent
                Control parent = gridResults.Parent;

                // Remember the location and size
                Rectangle bounds = gridResults.Bounds;

                // Remember the dock setting
                DockStyle dock = gridResults.Dock;

                // Remove and dispose the old grid
                parent.Controls.Remove(gridResults);
                gridResults.Dispose();

                // Create a new grid control
                gridResults = new DevExpress.XtraGrid.GridControl();
                gridViewResults = new DevExpress.XtraGrid.Views.Grid.GridView();

                // Set up the new grid
                gridResults.MainView = gridViewResults;
                gridResults.Bounds = bounds;
                gridResults.Dock = dock;
                gridResults.Name = "gridResults";

                // Set up the view
                gridViewResults.GridControl = gridResults;
                gridViewResults.Name = "gridViewResults";
                gridViewResults.OptionsBehavior.Editable = false;
                gridViewResults.OptionsBehavior.ReadOnly = true;

                // Add event handlers
                gridResults.ProcessGridKey += GridResults_ProcessGridKey;
                gridResults.DataSourceChanged += GridResults_DataSourceChanged;

                // Add it to the parent
                parent.Controls.Add(gridResults);

                // Put it at the right z-order
                gridResults.BringToFront();

                Debug.WriteLine("Grid control completely rebuilt");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in ForceGridRebuild: {ex.Message}");
            }
        }
    }
}
