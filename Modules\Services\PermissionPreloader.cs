using System;
using System.Collections.Generic;
using System.Linq;
using ProManage.Modules.Connections;
using ProManage.Modules.Models.PermissionManagementForm;

namespace ProManage.Modules.Services
{
    /// <summary>
    /// Service for preloading user permissions in bulk for fast cache access.
    /// Provides direct database access without caching for initial permission loading.
    /// </summary>
    public class PermissionPreloader
    {
        /// <summary>
        /// Load all permissions for a user into a dictionary for fast access
        /// </summary>
        /// <param name="userId">User ID to load permissions for</param>
        /// <param name="formNames">List of form names to load permissions for</param>
        /// <returns>Dictionary of permission keys to boolean values</returns>
        public Dictionary<string, bool> LoadUserPermissions(int userId, List<string> formNames)
        {
            var permissions = new Dictionary<string, bool>();
            
            try
            {
                // Get user's role information
                var userWithRole = PermissionDatabaseService.GetUserWithRole(userId);
                if (userWithRole == null)
                {
                    // User not found, return empty permissions
                    return permissions;
                }

                // Get role permissions and user overrides
                var rolePermissions = PermissionDatabaseService.GetRolePermissions(userWithRole.RoleId);
                var userPermissions = PermissionDatabaseService.GetUserPermissions(userId);

                foreach (var formName in formNames)
                {
                    var rolePermission = rolePermissions.FirstOrDefault(rp => rp.FormName == formName);
                    var userPermission = userPermissions.FirstOrDefault(up => up.FormName == formName);

                    foreach (PermissionType permType in Enum.GetValues(typeof(PermissionType)))
                    {
                        var key = $"{formName}_{permType}";
                        var hasPermission = CheckPermissionDirect(rolePermission, userPermission, permType);
                        permissions[key] = hasPermission;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error preloading permissions for user {userId}: {ex.Message}");
            }
            
            return permissions;
        }

        /// <summary>
        /// Load permissions for multiple users in bulk
        /// </summary>
        /// <param name="userIds">List of user IDs</param>
        /// <param name="formNames">List of form names</param>
        /// <returns>Dictionary of user ID to permission dictionary</returns>
        public Dictionary<int, Dictionary<string, bool>> LoadMultipleUserPermissions(List<int> userIds, List<string> formNames)
        {
            var result = new Dictionary<int, Dictionary<string, bool>>();

            try
            {
                foreach (var userId in userIds)
                {
                    result[userId] = LoadUserPermissions(userId, formNames);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error preloading permissions for multiple users: {ex.Message}");
            }

            return result;
        }

        /// <summary>
        /// Load global permissions for a user
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>Dictionary of global permission keys to boolean values</returns>
        public Dictionary<string, bool> LoadUserGlobalPermissions(int userId)
        {
            var permissions = new Dictionary<string, bool>();

            try
            {
                var globalPermissions = PermissionDatabaseService.GetGlobalPermissions(userId);
                
                if (globalPermissions != null)
                {
                    permissions["global_CanCreateUsers"] = globalPermissions.CanCreateUsers;
                    permissions["global_CanEditUsers"] = globalPermissions.CanEditUsers;
                    permissions["global_CanDeleteUsers"] = globalPermissions.CanDeleteUsers;
                    permissions["global_CanPrintUsers"] = globalPermissions.CanPrintUsers;
                }
                else
                {
                    // No global permissions, set all to false
                    permissions["global_CanCreateUsers"] = false;
                    permissions["global_CanEditUsers"] = false;
                    permissions["global_CanDeleteUsers"] = false;
                    permissions["global_CanPrintUsers"] = false;
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error preloading global permissions for user {userId}: {ex.Message}");
            }

            return permissions;
        }

        /// <summary>
        /// Get all form names from the permission system
        /// </summary>
        /// <returns>List of form names</returns>
        public List<string> GetAllFormNames()
        {
            var formNames = new List<string>();

            try
            {
                // Get all active roles and their permissions to find all forms
                var roles = PermissionDatabaseService.GetAllRoles();
                var allFormNames = new HashSet<string>();

                foreach (var role in roles)
                {
                    var rolePermissions = PermissionDatabaseService.GetRolePermissions(role.RoleId);
                    foreach (var permission in rolePermissions)
                    {
                        allFormNames.Add(permission.FormName);
                    }
                }

                formNames = allFormNames.ToList();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting all form names: {ex.Message}");
            }

            return formNames;
        }

        /// <summary>
        /// Get frequently accessed forms for cache warming
        /// </summary>
        /// <param name="topCount">Number of top forms to return</param>
        /// <returns>List of frequently accessed form names</returns>
        public List<string> GetFrequentlyAccessedForms(int topCount = 10)
        {
            // For now, return all forms. In a real implementation, this could be based on
            // access logs or usage statistics
            var allForms = GetAllFormNames();
            return allForms.Take(topCount).ToList();
        }

        /// <summary>
        /// Get active user IDs for cache warming
        /// </summary>
        /// <param name="topCount">Number of users to return</param>
        /// <returns>List of active user IDs</returns>
        public List<int> GetActiveUserIds(int topCount = 50)
        {
            var userIds = new List<int>();

            try
            {
                var users = PermissionDatabaseService.GetAllUsersWithPermissions();
                userIds = users.Take(topCount).Select(u => u.UserId).ToList();
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting active user IDs: {ex.Message}");
            }

            return userIds;
        }

        /// <summary>
        /// Direct permission check without caching (for preloading)
        /// </summary>
        /// <param name="rolePermission">Role permission object</param>
        /// <param name="userPermission">User permission override object</param>
        /// <param name="permissionType">Type of permission to check</param>
        /// <returns>True if permission is granted</returns>
        private bool CheckPermissionDirect(RolePermission rolePermission, UserPermission userPermission, PermissionType permissionType)
        {
            // Check user override first
            if (userPermission != null)
            {
                var userValue = GetPermissionValue(userPermission, permissionType);
                if (userValue.HasValue)
                {
                    return userValue.Value;
                }
            }
            
            // Fall back to role permission
            if (rolePermission != null)
            {
                var roleValue = GetPermissionValue(rolePermission, permissionType);
                return roleValue;
            }
            
            return false; // Default deny
        }

        /// <summary>
        /// Get permission value from user permission object
        /// </summary>
        /// <param name="permission">User permission object</param>
        /// <param name="type">Permission type</param>
        /// <returns>Permission value or null if not set</returns>
        private bool? GetPermissionValue(UserPermission permission, PermissionType type)
        {
            if (type == PermissionType.Read)
                return permission.ReadPermission;
            else if (type == PermissionType.New)
                return permission.NewPermission;
            else if (type == PermissionType.Edit)
                return permission.EditPermission;
            else if (type == PermissionType.Delete)
                return permission.DeletePermission;
            else if (type == PermissionType.Print)
                return permission.PrintPermission;
            else
                return null;
        }

        /// <summary>
        /// Get permission value from role permission object
        /// </summary>
        /// <param name="permission">Role permission object</param>
        /// <param name="type">Permission type</param>
        /// <returns>Permission value</returns>
        private bool GetPermissionValue(RolePermission permission, PermissionType type)
        {
            if (type == PermissionType.Read)
                return permission.ReadPermission;
            else if (type == PermissionType.New)
                return permission.NewPermission;
            else if (type == PermissionType.Edit)
                return permission.EditPermission;
            else if (type == PermissionType.Delete)
                return permission.DeletePermission;
            else if (type == PermissionType.Print)
                return permission.PrintPermission;
            else
                return false;
        }
    }
}
