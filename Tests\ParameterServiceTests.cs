// ParameterServiceTests - Unit tests for parameter system startup loading functionality
// Usage: Verifies that parameters are successfully loaded from database into memory cache at application startup

using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using ProManage.Modules.Services;
using ProManage.Modules.Data.ParametersForm;
using ProManage.Modules.Models.ParametersForm;
using ProManage.Modules.Connections;
using ProManage.Modules.Helpers;

namespace ProManage.Tests
{
    /// <summary>
    /// Test class for verifying parameter system startup loading functionality
    /// </summary>
    [TestClass]
    public class ParameterServiceTests
    {
        #region Test Setup and Cleanup

        /// <summary>
        /// Sets up test environment before each test method
        /// </summary>
        [TestInitialize]
        public void TestInitialize()
        {
            Debug.WriteLine("=== ParameterServiceTests: Test Initialize ===");
            
            // Reset the singleton instance by clearing the cache
            // This ensures each test starts with a fresh state
            var instance = UnifiedParameterManager.Instance;
            if (instance.IsInitialized)
            {
                // Force refresh to reset state
                instance.RefreshFromDatabase();
            }
        }

        /// <summary>
        /// Cleans up after each test method
        /// </summary>
        [TestCleanup]
        public void TestCleanup()
        {
            Debug.WriteLine("=== ParameterServiceTests: Test Cleanup ===");
        }

        #endregion

        #region Startup Loading Tests

        /// <summary>
        /// Tests that parameters are successfully loaded from database at startup
        /// </summary>
        [TestMethod]
        public void TestStartupParameterLoading_Success()
        {
            Debug.WriteLine("--- Testing Startup Parameter Loading ---");

            // Arrange
            var parameterManager = UnifiedParameterManager.Instance;
            
            // Act - Initialize the parameter manager (simulating application startup)
            bool initResult = parameterManager.Initialize();

            // Assert
            Assert.IsTrue(initResult, "Parameter manager initialization should succeed");
            Assert.IsTrue(parameterManager.IsInitialized, "Parameter manager should be marked as initialized");
            
            // Verify cache was updated
            Assert.IsTrue(parameterManager.LastCacheUpdate > DateTime.MinValue, 
                "Last cache update timestamp should be set");
            
            Debug.WriteLine($"✓ Initialization successful");
            Debug.WriteLine($"✓ Parameters loaded: {parameterManager.ParameterCount}");
            Debug.WriteLine($"✓ Last cache update: {parameterManager.LastCacheUpdate}");
        }

        /// <summary>
        /// Tests that parameters can be retrieved from memory cache after loading
        /// </summary>
        [TestMethod]
        public void TestParameterRetrievalFromCache_AfterStartup()
        {
            Debug.WriteLine("--- Testing Parameter Retrieval from Cache ---");

            // Arrange
            var parameterManager = UnifiedParameterManager.Instance;
            
            // Act - Initialize
            bool initResult = parameterManager.Initialize();
            Assert.IsTrue(initResult, "Initialization should succeed");

            // Test retrieving various parameter types from cache
            // These should come from memory, not database
            string currency = parameterManager.GetString("CURRENCY", "USD");
            int decimals = parameterManager.GetInt("DECIMALS", 2);
            bool showGst = parameterManager.GetBool("SHOW_GST", false);
            decimal taxRate = parameterManager.GetDecimal("TAX_RATE", 0.0m);

            // Assert - Parameters should be retrievable
            Assert.IsNotNull(currency, "Currency parameter should be retrievable");
            Assert.IsTrue(decimals >= 0, "Decimals parameter should be a valid number");
            
            Debug.WriteLine($"✓ Retrieved currency from cache: {currency}");
            Debug.WriteLine($"✓ Retrieved decimals from cache: {decimals}");
            Debug.WriteLine($"✓ Retrieved show GST from cache: {showGst}");
            Debug.WriteLine($"✓ Retrieved tax rate from cache: {taxRate}");
        }

        /// <summary>
        /// Tests that the system handles empty parameter database gracefully
        /// </summary>
        [TestMethod]
        public void TestStartupWithEmptyDatabase_HandledGracefully()
        {
            Debug.WriteLine("--- Testing Startup with Empty/Unavailable Database ---");

            // Arrange
            var parameterManager = UnifiedParameterManager.Instance;
            
            // Act - Initialize (even if database is empty or unavailable)
            bool initResult = parameterManager.Initialize();

            // Assert - Should still initialize successfully
            Assert.IsTrue(initResult, "Initialization should succeed even with empty database");
            Assert.IsTrue(parameterManager.IsInitialized, "Manager should be initialized");
            
            // Should still work with default values
            string defaultCurrency = parameterManager.GetString("NON_EXISTENT_PARAM", "DEFAULT");
            Assert.AreEqual("DEFAULT", defaultCurrency, "Should return default value for non-existent parameter");
            
            Debug.WriteLine("✓ System initialized successfully with empty/unavailable database");
            Debug.WriteLine("✓ Default values working correctly");
        }

        #endregion

        #region Parameter Type Tests

        /// <summary>
        /// Tests loading and retrieval of different parameter types
        /// </summary>
        [TestMethod]
        public void TestDifferentParameterTypes_LoadAndRetrieve()
        {
            Debug.WriteLine("--- Testing Different Parameter Types ---");

            // Arrange
            var parameterManager = UnifiedParameterManager.Instance;
            
            // Act - Initialize
            parameterManager.Initialize();

            // Test String type
            string companyName = parameterManager.GetString("COMPANY_NAME", "ProManage");
            Assert.IsNotNull(companyName, "String parameter should be retrievable");
            Debug.WriteLine($"✓ String parameter (COMPANY_NAME): {companyName}");

            // Test Number/Integer type
            int pageSize = parameterManager.GetInt("DEFAULT_PAGE_SIZE", 50);
            Assert.IsTrue(pageSize > 0, "Integer parameter should be valid");
            Debug.WriteLine($"✓ Integer parameter (DEFAULT_PAGE_SIZE): {pageSize}");

            // Test Boolean type
            bool showTooltips = parameterManager.GetBool("SHOW_TOOLTIPS", true);
            Debug.WriteLine($"✓ Boolean parameter (SHOW_TOOLTIPS): {showTooltips}");

            // Test Decimal type
            decimal maxDiscount = parameterManager.GetDecimal("MAX_DISCOUNT_PERCENT", 10.0m);
            Assert.IsTrue(maxDiscount >= 0, "Decimal parameter should be valid");
            Debug.WriteLine($"✓ Decimal parameter (MAX_DISCOUNT_PERCENT): {maxDiscount}");

            // Test Date type
            DateTime testDate = parameterManager.GetDateTime("TEST_DATE", DateTime.Now);
            Debug.WriteLine($"✓ DateTime parameter (TEST_DATE): {testDate}");
        }

        #endregion

        #region Key-Value Dictionary Tests

        /// <summary>
        /// Tests that the parameter system works with key-value dictionary approach
        /// </summary>
        [TestMethod]
        public void TestKeyValueDictionaryApproach()
        {
            Debug.WriteLine("--- Testing Key-Value Dictionary Approach ---");

            // Arrange
            var parameterManager = UnifiedParameterManager.Instance;
            
            // Act - Initialize
            parameterManager.Initialize();

            // Test direct key access
            string value1 = parameterManager.GetString("CURRENCY");
            string value2 = parameterManager.GetString("COMPANY_NAME");
            
            // Test case-insensitive key access (keys are stored in uppercase)
            string value3 = parameterManager.GetString("currency");
            string value4 = parameterManager.GetString("Currency");
            
            // Assert - Case insensitive access should work
            Assert.AreEqual(value1, value3, "Case-insensitive access should work");
            Assert.AreEqual(value1, value4, "Mixed case access should work");
            
            // Test parameter existence check
            bool hasParam = parameterManager.HasParameter("CURRENCY");
            Debug.WriteLine($"✓ Has CURRENCY parameter: {hasParam}");
            
            Debug.WriteLine("✓ Key-value dictionary approach working correctly");
            Debug.WriteLine("✓ Case-insensitive key access verified");
        }

        #endregion

        #region Category Access Tests

        /// <summary>
        /// Tests centralized parameter access through category properties
        /// </summary>
        [TestMethod]
        public void TestCentralizedParameterAccess_Categories()
        {
            Debug.WriteLine("--- Testing Centralized Parameter Access via Categories ---");

            // Arrange
            var parameterManager = UnifiedParameterManager.Instance;
            
            // Act - Initialize
            parameterManager.Initialize();

            // Test Currency category
            var currency = parameterManager.Currency;
            Assert.IsNotNull(currency.Symbol, "Currency symbol should be accessible");
            Assert.IsTrue(currency.DecimalPlaces >= 0, "Currency decimal places should be valid");
            Debug.WriteLine($"✓ Currency category - Symbol: {currency.Symbol}, Decimals: {currency.DecimalPlaces}");

            // Test Company category
            var company = parameterManager.Company;
            Assert.IsNotNull(company.Name, "Company name should be accessible");
            Debug.WriteLine($"✓ Company category - Name: {company.Name}");

            // Test UI category
            var ui = parameterManager.UI;
            Assert.IsNotNull(ui.Theme, "UI theme should be accessible");
            Assert.IsTrue(ui.DefaultPageSize > 0, "Default page size should be positive");
            Debug.WriteLine($"✓ UI category - Theme: {ui.Theme}, Page Size: {ui.DefaultPageSize}");

            // Test Business category
            var business = parameterManager.Business;
            Assert.IsTrue(business.SessionTimeoutMinutes > 0, "Session timeout should be positive");
            Debug.WriteLine($"✓ Business category - Session Timeout: {business.SessionTimeoutMinutes} minutes");
        }

        #endregion

        #region Performance Tests

        /// <summary>
        /// Tests that parameter loading doesn't impact startup performance significantly
        /// </summary>
        [TestMethod]
        public void TestStartupPerformance_ParameterLoading()
        {
            Debug.WriteLine("--- Testing Startup Performance ---");

            // Arrange
            var parameterManager = UnifiedParameterManager.Instance;
            var stopwatch = Stopwatch.StartNew();
            
            // Act - Measure initialization time
            bool initResult = parameterManager.Initialize();
            stopwatch.Stop();

            // Assert
            Assert.IsTrue(initResult, "Initialization should succeed");
            Assert.IsTrue(stopwatch.ElapsedMilliseconds < 5000, 
                $"Initialization should complete within 5 seconds (actual: {stopwatch.ElapsedMilliseconds}ms)");
            
            Debug.WriteLine($"✓ Initialization completed in {stopwatch.ElapsedMilliseconds}ms");
            
            // Test subsequent access performance (should be fast from cache)
            stopwatch.Restart();
            for (int i = 0; i < 1000; i++)
            {
                string currency = parameterManager.GetString("CURRENCY", "USD");
            }
            stopwatch.Stop();
            
            double avgAccessTime = (double)stopwatch.ElapsedMilliseconds / 1000;
            Assert.IsTrue(avgAccessTime < 1.0, 
                $"Average parameter access should be under 1ms (actual: {avgAccessTime:F4}ms)");
            
            Debug.WriteLine($"✓ 1000 parameter accesses completed in {stopwatch.ElapsedMilliseconds}ms");
            Debug.WriteLine($"✓ Average access time: {avgAccessTime:F4}ms");
        }

        #endregion

        #region Refresh Tests

        /// <summary>
        /// Tests that parameters can be refreshed from database after initial load
        /// </summary>
        [TestMethod]
        public void TestParameterRefresh_AfterStartup()
        {
            Debug.WriteLine("--- Testing Parameter Refresh Functionality ---");

            // Arrange
            var parameterManager = UnifiedParameterManager.Instance;
            
            // Act - Initial load
            parameterManager.Initialize();
            DateTime firstLoadTime = parameterManager.LastCacheUpdate;
            int firstCount = parameterManager.ParameterCount;
            
            // Wait a moment to ensure timestamp difference
            System.Threading.Thread.Sleep(100);
            
            // Refresh parameters
            parameterManager.RefreshFromDatabase();
            DateTime secondLoadTime = parameterManager.LastCacheUpdate;
            
            // Assert
            Assert.IsTrue(secondLoadTime > firstLoadTime, 
                "Last cache update time should be updated after refresh");
            Assert.IsTrue(parameterManager.IsInitialized, 
                "Manager should remain initialized after refresh");
            
            Debug.WriteLine($"✓ Initial load time: {firstLoadTime}");
            Debug.WriteLine($"✓ Refresh load time: {secondLoadTime}");
            Debug.WriteLine($"✓ Parameters refreshed successfully");
        }

        #endregion

        #region Mock Database Tests

        /// <summary>
        /// Tests parameter loading with mock data (simulating database)
        /// </summary>
        [TestMethod]
        public void TestParameterLoading_WithMockData()
        {
            Debug.WriteLine("--- Testing Parameter Loading with Mock Data ---");

            // This test verifies the loading mechanism works correctly
            // In a real scenario, you would mock the database connection
            
            // Arrange
            var parameterManager = UnifiedParameterManager.Instance;
            
            // Act - Initialize (will load from actual database or empty if not available)
            bool initResult = parameterManager.Initialize();
            
            // Assert basic functionality
            Assert.IsTrue(initResult, "Initialization should always succeed");
            Assert.IsTrue(parameterManager.IsInitialized, "Manager should be initialized");
            
            // Test that default values work when parameters are not in database
            string testParam = parameterManager.GetString("MOCK_TEST_PARAM", "MOCK_DEFAULT");
            Assert.AreEqual("MOCK_DEFAULT", testParam, 
                "Should return default value for non-existent parameter");
            
            // Test type conversions with defaults
            int mockInt = parameterManager.GetInt("MOCK_INT_PARAM", 42);
            Assert.AreEqual(42, mockInt, "Should return default int value");
            
            bool mockBool = parameterManager.GetBool("MOCK_BOOL_PARAM", true);
            Assert.AreEqual(true, mockBool, "Should return default bool value");
            
            decimal mockDecimal = parameterManager.GetDecimal("MOCK_DECIMAL_PARAM", 3.14m);
            Assert.AreEqual(3.14m, mockDecimal, "Should return default decimal value");
            
            Debug.WriteLine("✓ Mock data tests completed successfully");
            Debug.WriteLine("✓ Default value mechanism verified");
        }

        #endregion

        #region Independent Execution Tests

        /// <summary>
        /// Tests that parameter system can run independently without full application
        /// </summary>
        [TestMethod]
        public void TestIndependentExecution_WithoutFullApp()
        {
            Debug.WriteLine("--- Testing Independent Execution ---");

            // This test verifies the parameter system can work in isolation
            // without requiring the full application to be running
            
            // Arrange
            var parameterManager = UnifiedParameterManager.Instance;
            
            // Act - Initialize without any application context
            bool initResult = parameterManager.Initialize();
            
            // Assert
            Assert.IsTrue(initResult, "Should initialize successfully in isolation");
            Assert.IsTrue(parameterManager.IsInitialized, "Should be marked as initialized");
            
            // Verify basic operations work
            string testValue = parameterManager.GetString("TEST_INDEPENDENT", "WORKS");
            Assert.AreEqual("WORKS", testValue, "Basic operations should work");
            
            // Verify categories work
            string currencySymbol = parameterManager.Currency.Symbol;
            Assert.IsNotNull(currencySymbol, "Category access should work");
            
            Debug.WriteLine("✓ Parameter system works independently");
            Debug.WriteLine("✓ No full application context required");
        }

        #endregion
    }
}