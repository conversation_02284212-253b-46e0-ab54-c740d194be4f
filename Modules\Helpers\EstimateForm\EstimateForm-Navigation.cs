// EstimateForm Navigation Service - Clean navigation functionality for estimates
// Usage: Provides navigation operations (First, Previous, Next, Last) with proper state management

using System;
using System.Diagnostics;
using System.Windows.Forms;
using Npgsql;
using ProManage.Modules.Models.EstimateForm;
using ProManage.Modules.Connections;
using ProManage.Modules.Data;
using ProManage.Modules.Data.EstimateForm;
using ProManage.Modules.Helpers;
using ProManage.Modules.UI;

namespace ProManage.Modules.Helpers.EstimateForm
{
    /// <summary>
    /// Navigation service for EstimateForm - handles all navigation operations
    /// </summary>
    public static class EstimateFormNavigation
    {
        #region Navigation Operations

        /// <summary>
        /// Navigates to the first estimate
        /// </summary>
        /// <returns>First estimate or null if none found</returns>
        public static EstimateFormHeaderModel NavigateToFirst()
        {
            Debug.WriteLine("EstimateFormNavigation: Navigating to first estimate");

            try
            {
                ProgressIndicatorService.Instance.ShowProgress();

                string query = SQLQueryLoader.ExtractNamedQuery(
                    SQLQueries.Estimate.MODULE_NAME,
                    "EstimateNavigation",
                    SQLQueries.Estimate.EstimateNavigation.GET_FIRST_ESTIMATE);

                using (var conn = DatabaseConnectionManager.Instance.CreateNewConnection())
                {
                    conn.Open();
                    using (var cmd = new NpgsqlCommand(query, conn))
                    {
                        using (var reader = cmd.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                var estimate = MapEstimateFromReader(reader);
                                Debug.WriteLine($"Found first estimate: ID={estimate.Id}, No={estimate.EstimateNo}");
                                return estimate;
                            }
                        }
                    }
                }

                Debug.WriteLine("No estimates found in database");
                return null;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error navigating to first estimate: {ex.Message}");
                throw new Exception($"Error navigating to first estimate: {ex.Message}", ex);
            }
            finally
            {
                ProgressIndicatorService.Instance.HideProgress();
            }
        }

        /// <summary>
        /// Navigates to the last estimate
        /// </summary>
        /// <returns>Last estimate or null if none found</returns>
        public static EstimateFormHeaderModel NavigateToLast()
        {
            Debug.WriteLine("EstimateFormNavigation: Navigating to last estimate");

            try
            {
                ProgressIndicatorService.Instance.ShowProgress();

                string query = SQLQueryLoader.ExtractNamedQuery(
                    SQLQueries.Estimate.MODULE_NAME,
                    "EstimateNavigation",
                    SQLQueries.Estimate.EstimateNavigation.GET_LAST_ESTIMATE);

                using (var conn = DatabaseConnectionManager.Instance.CreateNewConnection())
                {
                    conn.Open();
                    using (var cmd = new NpgsqlCommand(query, conn))
                    {
                        using (var reader = cmd.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                var estimate = MapEstimateFromReader(reader);
                                Debug.WriteLine($"Found last estimate: ID={estimate.Id}, No={estimate.EstimateNo}");
                                return estimate;
                            }
                        }
                    }
                }

                Debug.WriteLine("No estimates found in database");
                return null;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error navigating to last estimate: {ex.Message}");
                throw new Exception($"Error navigating to last estimate: {ex.Message}", ex);
            }
            finally
            {
                ProgressIndicatorService.Instance.HideProgress();
            }
        }

        /// <summary>
        /// Navigates to the previous estimate before the given ID
        /// </summary>
        /// <param name="currentId">Current estimate ID</param>
        /// <returns>Previous estimate or null if none found</returns>
        public static EstimateFormHeaderModel NavigateToPrevious(int currentId)
        {
            Debug.WriteLine($"EstimateFormNavigation: Navigating to previous estimate before ID {currentId}");

            if (currentId <= 0)
            {
                throw new ArgumentException("Invalid current estimate ID");
            }

            try
            {
                ProgressIndicatorService.Instance.ShowProgress();

                string query = SQLQueryLoader.ExtractNamedQuery(
                    SQLQueries.Estimate.MODULE_NAME,
                    "EstimateNavigation",
                    SQLQueries.Estimate.EstimateNavigation.GET_PREVIOUS_ESTIMATE);

                using (var conn = DatabaseConnectionManager.Instance.CreateNewConnection())
                {
                    conn.Open();
                    using (var cmd = new NpgsqlCommand(query, conn))
                    {
                        cmd.Parameters.AddWithValue("@current_id", currentId);
                        using (var reader = cmd.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                var estimate = MapEstimateFromReader(reader);
                                Debug.WriteLine($"Found previous estimate: ID={estimate.Id}, No={estimate.EstimateNo}");
                                return estimate;
                            }
                        }
                    }
                }

                Debug.WriteLine("No previous estimate found - already at first");
                return null;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error navigating to previous estimate: {ex.Message}");
                throw new Exception($"Error navigating to previous estimate: {ex.Message}", ex);
            }
            finally
            {
                ProgressIndicatorService.Instance.HideProgress();
            }
        }

        /// <summary>
        /// Navigates to the next estimate after the given ID
        /// </summary>
        /// <param name="currentId">Current estimate ID</param>
        /// <returns>Next estimate or null if none found</returns>
        public static EstimateFormHeaderModel NavigateToNext(int currentId)
        {
            Debug.WriteLine($"EstimateFormNavigation: Navigating to next estimate after ID {currentId}");

            if (currentId <= 0)
            {
                throw new ArgumentException("Invalid current estimate ID");
            }

            try
            {
                ProgressIndicatorService.Instance.ShowProgress();

                string query = SQLQueryLoader.ExtractNamedQuery(
                    SQLQueries.Estimate.MODULE_NAME,
                    "EstimateNavigation",
                    SQLQueries.Estimate.EstimateNavigation.GET_NEXT_ESTIMATE);

                using (var conn = DatabaseConnectionManager.Instance.CreateNewConnection())
                {
                    conn.Open();
                    using (var cmd = new NpgsqlCommand(query, conn))
                    {
                        cmd.Parameters.AddWithValue("@current_id", currentId);
                        using (var reader = cmd.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                var estimate = MapEstimateFromReader(reader);
                                Debug.WriteLine($"Found next estimate: ID={estimate.Id}, No={estimate.EstimateNo}");
                                return estimate;
                            }
                        }
                    }
                }

                Debug.WriteLine("No next estimate found - already at last");
                return null;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error navigating to next estimate: {ex.Message}");
                throw new Exception($"Error navigating to next estimate: {ex.Message}", ex);
            }
            finally
            {
                ProgressIndicatorService.Instance.HideProgress();
            }
        }

        #endregion

        #region Helper Methods

        /// <summary>
        /// Loads estimate details for the given estimate
        /// </summary>
        /// <param name="estimate">Estimate to load details for</param>
        public static void LoadEstimateDetails(EstimateFormHeaderModel estimate)
        {
            if (estimate == null || estimate.Id <= 0)
                return;

            try
            {
                ProgressIndicatorService.Instance.ShowProgress();
                try
                {
                    string query = SQLQueryLoader.ExtractNamedQuery(
                        SQLQueries.Estimate.MODULE_NAME,
                        "EstimateNavigation",
                        SQLQueries.Estimate.EstimateNavigation.GET_ESTIMATE_DETAILS);

                    using (var conn = DatabaseConnectionManager.Instance.CreateNewConnection())
                    {
                        conn.Open();
                        using (var cmd = new NpgsqlCommand(query, conn))
                        {
                            cmd.Parameters.AddWithValue("@estimate_id", estimate.Id);
                            using (var reader = cmd.ExecuteReader())
                            {
                                estimate.Details = new System.Collections.Generic.List<EstimateFormDetailModel>();
                                while (reader.Read())
                                {
                                    var detail = MapEstimateDetailFromReader(reader);
                                    estimate.Details.Add(detail);
                                }
                            }
                        }
                    }

                    Debug.WriteLine($"Loaded {estimate.Details.Count} details for estimate {estimate.EstimateNo}");
                }
                finally
                {
                    ProgressIndicatorService.Instance.HideProgress();
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error loading estimate details: {ex.Message}");
                estimate.Details = new System.Collections.Generic.List<EstimateFormDetailModel>();
            }
        }

        /// <summary>
        /// Gets the total count of estimates
        /// </summary>
        /// <returns>Total number of estimates</returns>
        public static int GetEstimateCount()
        {
            try
            {
                string query = SQLQueryLoader.ExtractNamedQuery(
                    SQLQueries.Estimate.MODULE_NAME,
                    "EstimateNavigation",
                    SQLQueries.Estimate.EstimateNavigation.GET_ESTIMATE_COUNT);

                using (var conn = DatabaseConnectionManager.Instance.CreateNewConnection())
                {
                    conn.Open();
                    using (var cmd = new NpgsqlCommand(query, conn))
                    {
                        return Convert.ToInt32(cmd.ExecuteScalar());
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error getting estimate count: {ex.Message}");
                return 0;
            }
        }

        /// <summary>
        /// Gets the position of an estimate in the ordered list
        /// </summary>
        /// <param name="estimateId">The estimate ID</param>
        /// <returns>Position (1-based) or 0 if not found</returns>
        public static int GetEstimatePosition(int estimateId)
        {
            try
            {
                string query = SQLQueryLoader.ExtractNamedQuery(
                    SQLQueries.Estimate.MODULE_NAME,
                    "EstimateNavigation",
                    SQLQueries.Estimate.EstimateNavigation.GET_ESTIMATE_POSITION);

                using (var conn = DatabaseConnectionManager.Instance.CreateNewConnection())
                {
                    conn.Open();
                    using (var cmd = new NpgsqlCommand(query, conn))
                    {
                        cmd.Parameters.AddWithValue("@estimate_id", estimateId);
                        return Convert.ToInt32(cmd.ExecuteScalar());
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error getting estimate position: {ex.Message}");
                return 0;
            }
        }

        /// <summary>
        /// Updates the form title with navigation position
        /// </summary>
        /// <param name="form">The form to update</param>
        /// <param name="currentEstimate">Current estimate</param>
        public static void UpdateNavigationPosition(dynamic form, EstimateFormHeaderModel currentEstimate)
        {
            try
            {
                int totalCount = GetEstimateCount();
                int currentPosition = 0;

                if (currentEstimate != null && currentEstimate.Id > 0)
                {
                    currentPosition = GetEstimatePosition(currentEstimate.Id);
                }

                if (totalCount > 0 && currentPosition > 0)
                {
                    form.Text = $"Estimate Form - Record {currentPosition} of {totalCount}";
                }
                else
                {
                    form.Text = "Estimate Form";
                }

                Debug.WriteLine($"Navigation position updated: {currentPosition} of {totalCount}");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error updating navigation position: {ex.Message}");
                form.Text = "Estimate Form";
            }
        }

        #endregion

        #region Private Helper Methods

        /// <summary>
        /// Maps an estimate from a data reader
        /// </summary>
        private static EstimateFormHeaderModel MapEstimateFromReader(NpgsqlDataReader reader)
        {
            return new EstimateFormHeaderModel
            {
                Id = Convert.ToInt32(reader["id"]),
                EstimateNo = reader["estimate_no"]?.ToString(),
                CustomerName = reader["customer_name"]?.ToString(),
                VIN = reader["vin"]?.ToString(),
                Brand = reader["brand"]?.ToString(),
                DocDate = reader["date"] == DBNull.Value ? (DateTime?)null : Convert.ToDateTime(reader["date"]),
                Location = reader["location"]?.ToString(),
                VehicleModel = reader["vehicle_model"]?.ToString(),
                SalesmanName = reader["salesman_name"]?.ToString(),
                Status = reader["status"] != DBNull.Value && Convert.ToBoolean(reader["status"]),
                Remarks = reader["remarks"]?.ToString(),
                CreatedAt = reader["created_at"] == DBNull.Value ? DateTime.Now : Convert.ToDateTime(reader["created_at"]),
                ModifyAt = reader["modify_at"] == DBNull.Value ? (DateTime?)null : Convert.ToDateTime(reader["modify_at"]),
                Details = new System.Collections.Generic.List<EstimateFormDetailModel>()
            };
        }

        /// <summary>
        /// Maps an estimate detail from a data reader
        /// </summary>
        private static EstimateFormDetailModel MapEstimateDetailFromReader(NpgsqlDataReader reader)
        {
            return new EstimateFormDetailModel
            {
                Id = Convert.ToInt32(reader["id"]),
                EstimateId = Convert.ToInt32(reader["estimate_id"]),
                PartNo = reader["part_no"]?.ToString(),
                Description = reader["description"]?.ToString(),
                Qty = reader["qty"] == DBNull.Value ? (int?)null : Convert.ToInt32(reader["qty"]),
                OEPrice = reader["oe_price"] == DBNull.Value ? (decimal?)null : Convert.ToDecimal(reader["oe_price"]),
                AFMPrice = reader["afm_price"] == DBNull.Value ? (decimal?)null : Convert.ToDecimal(reader["afm_price"]),
                Remarks = reader["remarks"]?.ToString(),
                ApproveStatus = reader["approve_status"] == DBNull.Value ? false : Convert.ToBoolean(reader["approve_status"]),
                CreatedAt = reader["created_at"] == DBNull.Value ? DateTime.Now : Convert.ToDateTime(reader["created_at"]),
                ModifyAt = reader["modify_at"] == DBNull.Value ? (DateTime?)null : Convert.ToDateTime(reader["modify_at"])
            };
        }

        #endregion
    }
}
