# Task 10: Role Master Form Enhancement with MenuRibbon UC

## Objective
Enhance the existing RoleMasterForm to include permission management capabilities and integrate MenuRibbon UC for centralized permission control. Add a permissions tab that allows direct editing of role permissions and integrates with the new RBAC system.

## Priority
**UI MANAGEMENT** - Depends on Tasks 01-09

## Estimated Time
1.5 hours

## Dependencies
- Task 02: Permission Data Models Creation
- Task 06: Core Permission Service Logic
- Task 09: Permission Management Form (3-Tab UI)

## Files to Modify
- `Forms/MainForms/RoleMasterForm.cs` (enhance existing + MenuRibbon UC integration)
- `Forms/MainForms/RoleMasterForm.Designer.cs` (enhance existing + MenuRibbon UC)
- `Forms/ReusableForms/MenuRibbon.cs` (configure for role management context)

## Enhancement Overview

### Current RoleMasterForm Structure
Based on ProManage patterns, the existing form likely has:
- Role name and description fields
- Basic role management functionality
- Save/Cancel/New/Delete buttons

### Enhanced Structure
Add MenuRibbon UC and tab control with:
- **MenuRibbon UC**: Centralized ribbon control with role management context
- **Tab 1: Role Details** (existing functionality)
- **Tab 2: Permissions** (new permission grid)

### MenuRibbon UC Integration
- **Visible Buttons**: New, Edit, Save, Cancel, Delete, Print Preview
- **Hidden Buttons**: Navigation (First, Previous, Next, Last), Grid (Add Row), Status toggle
- **Context**: Role management operations through centralized ribbon

## Implementation Plan

### 1. Add Tab Control to Existing Form

Modify RoleMasterForm.Designer.cs to include:
```csharp
private DevExpress.XtraTab.XtraTabControl tabControlRole;
private DevExpress.XtraTab.XtraTabPage tabPageDetails;
private DevExpress.XtraTab.XtraTabPage tabPagePermissions;
private DevExpress.XtraGrid.GridControl gridControlPermissions;
private DevExpress.XtraGrid.Views.Grid.GridView gridViewPermissions;
private DevExpress.XtraEditors.SimpleButton btnCopyFromRole;
private DevExpress.XtraEditors.SimpleButton btnResetPermissions;
private DevExpress.XtraEditors.ComboBoxEdit cmbCopyFromRole;
private DevExpress.XtraEditors.LabelControl lblCopyFromRole;
```

### 2. Enhance RoleMasterForm.cs

Add these methods and properties:

```csharp
using ProManage.Modules.Services;
using ProManage.Modules.Models;
using ProManage.Modules.Helpers.PermissionManagementForm;

public partial class RoleMasterForm : Form
{
    private readonly PermissionGridHelper _gridHelper;
    private List<RolePermissionDisplay> _currentPermissions;
    private bool _permissionsChanged = false;
    
    // Add to constructor
    private void InitializeEnhancements()
    {
        _gridHelper = new PermissionGridHelper();
        SetupPermissionsTab();
        LoadCopyFromRoleDropdown();
    }
    
    #region Permissions Tab Setup
    
    private void SetupPermissionsTab()
    {
        // Setup permissions grid
        _gridHelper.SetupRolePermissionsGrid(gridControlPermissions, gridViewPermissions);
        gridViewPermissions.CellValueChanged += GridViewPermissions_CellValueChanged;
        
        // Setup copy functionality
        btnCopyFromRole.Click += BtnCopyFromRole_Click;
        btnResetPermissions.Click += BtnResetPermissions_Click;
        
        // Initially disable permissions tab if no role selected
        tabPagePermissions.PageEnabled = false;
    }
    
    private void LoadCopyFromRoleDropdown()
    {
        try
        {
            var roles = GetAllRoles(); // Use existing role loading method
            cmbCopyFromRole.Properties.Items.Clear();
            
            foreach (var role in roles)
            {
                cmbCopyFromRole.Properties.Items.Add(new { Text = role.RoleName, Value = role.RoleId });
            }
            
            cmbCopyFromRole.Properties.DisplayMember = "Text";
            cmbCopyFromRole.Properties.ValueMember = "Value";
        }
        catch (Exception ex)
        {
            MessageBox.Show($"Error loading roles: {ex.Message}", "Error", 
                MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }
    
    #endregion
    
    #region Permission Management
    
    private void LoadRolePermissions(int roleId)
    {
        try
        {
            var dbService = new PermissionDatabaseService();
            var permissions = dbService.GetRolePermissions(roleId);
            
            // Convert to display format
            _currentPermissions = permissions.Select(p => new RolePermissionDisplay
            {
                FormName = p.FormName,
                DisplayName = FormsConfigurationService.GetFormDisplayName(p.FormName),
                ReadPermission = p.ReadPermission,
                NewPermission = p.NewPermission,
                EditPermission = p.EditPermission,
                DeletePermission = p.DeletePermission,
                PrintPermission = p.PrintPermission
            }).ToList();
            
            gridControlPermissions.DataSource = _currentPermissions;
            gridViewPermissions.BestFitColumns();
            
            tabPagePermissions.PageEnabled = true;
            _permissionsChanged = false;
        }
        catch (Exception ex)
        {
            MessageBox.Show($"Error loading role permissions: {ex.Message}", "Error", 
                MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }
    
    private void GridViewPermissions_CellValueChanged(object sender, DevExpress.XtraGrid.Views.Base.CellValueChangedEventArgs e)
    {
        _permissionsChanged = true;
        // Enable save button or mark form as dirty
        EnableSaveButton();
    }
    
    private void BtnCopyFromRole_Click(object sender, EventArgs e)
    {
        if (cmbCopyFromRole.SelectedItem == null)
        {
            MessageBox.Show("Please select a role to copy from.", "Information", 
                MessageBoxButtons.OK, MessageBoxIcon.Information);
            return;
        }
        
        var result = MessageBox.Show(
            "This will replace all current permissions with those from the selected role. Continue?",
            "Confirm Copy",
            MessageBoxButtons.YesNo,
            MessageBoxIcon.Question);
        
        if (result == DialogResult.Yes)
        {
            try
            {
                var sourceRoleId = (int)((dynamic)cmbCopyFromRole.SelectedItem).Value;
                var currentRoleId = GetCurrentRoleId(); // Implement based on existing form logic
                
                var dbService = new PermissionDatabaseService();
                if (dbService.CopyRolePermissions(sourceRoleId, currentRoleId))
                {
                    LoadRolePermissions(currentRoleId);
                    MessageBox.Show("Permissions copied successfully.", "Success", 
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                else
                {
                    MessageBox.Show("Failed to copy permissions.", "Error", 
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error copying permissions: {ex.Message}", "Error", 
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
    
    private void BtnResetPermissions_Click(object sender, EventArgs e)
    {
        var result = MessageBox.Show(
            "This will reset all permissions to default (no access). Continue?",
            "Confirm Reset",
            MessageBoxButtons.YesNo,
            MessageBoxIcon.Question);
        
        if (result == DialogResult.Yes)
        {
            foreach (var permission in _currentPermissions)
            {
                permission.ReadPermission = false;
                permission.NewPermission = false;
                permission.EditPermission = false;
                permission.DeletePermission = false;
                permission.PrintPermission = false;
            }
            
            gridControlPermissions.RefreshDataSource();
            _permissionsChanged = true;
            EnableSaveButton();
        }
    }
    
    #endregion
    
    #region Enhanced Save/Load Logic
    
    // Override or enhance existing save method
    protected override bool SaveRole()
    {
        // Call existing save logic first
        if (!base.SaveRole()) // Assuming base save method exists
            return false;
        
        // Save permissions if changed
        if (_permissionsChanged && _currentPermissions != null)
        {
            return SaveRolePermissions();
        }
        
        return true;
    }
    
    private bool SaveRolePermissions()
    {
        try
        {
            var roleId = GetCurrentRoleId();
            var updates = _currentPermissions.Select(p => new RolePermissionUpdate
            {
                RoleId = roleId,
                FormName = p.FormName,
                ReadPermission = p.ReadPermission,
                NewPermission = p.NewPermission,
                EditPermission = p.EditPermission,
                DeletePermission = p.DeletePermission,
                PrintPermission = p.PrintPermission
            }).ToList();
            
            var dbService = new PermissionDatabaseService();
            var success = dbService.UpdateRolePermissions(updates);
            
            if (success)
            {
                _permissionsChanged = false;
                // Clear permission cache
                PermissionService.ClearPermissionCache();
            }
            
            return success;
        }
        catch (Exception ex)
        {
            MessageBox.Show($"Error saving role permissions: {ex.Message}", "Error", 
                MessageBoxButtons.OK, MessageBoxIcon.Error);
            return false;
        }
    }
    
    // Override or enhance existing load method
    protected override void LoadRole(int roleId)
    {
        // Call existing load logic
        base.LoadRole(roleId); // Assuming base load method exists
        
        // Load permissions
        LoadRolePermissions(roleId);
    }
    
    // Override or enhance existing new role method
    protected override void NewRole()
    {
        // Call existing new logic
        base.NewRole(); // Assuming base new method exists
        
        // Disable permissions tab for new role
        tabPagePermissions.PageEnabled = false;
        _currentPermissions = null;
        _permissionsChanged = false;
    }
    
    #endregion
    
    #region Helper Methods
    
    private int GetCurrentRoleId()
    {
        // Implement based on existing form logic
        // This should return the currently selected/loaded role ID
        return 0; // TODO: Implement based on existing form structure
    }
    
    private void EnableSaveButton()
    {
        // Enable save button or mark form as dirty
        // Implement based on existing form patterns
    }
    
    private List<Role> GetAllRoles()
    {
        // Use existing role loading logic or implement
        var dbService = new PermissionDatabaseService();
        return dbService.GetAllRoles();
    }
    
    #endregion
    
    #region Form Events
    
    // Override form closing to check for unsaved changes
    protected override void OnFormClosing(FormClosingEventArgs e)
    {
        if (_permissionsChanged)
        {
            var result = MessageBox.Show(
                "You have unsaved permission changes. Do you want to save them?",
                "Unsaved Changes",
                MessageBoxButtons.YesNoCancel,
                MessageBoxIcon.Question);
            
            switch (result)
            {
                case DialogResult.Yes:
                    if (!SaveRolePermissions())
                    {
                        e.Cancel = true;
                        return;
                    }
                    break;
                case DialogResult.Cancel:
                    e.Cancel = true;
                    return;
            }
        }
        
        base.OnFormClosing(e);
    }
    
    #endregion
}

// Display model for role permissions grid
public class RolePermissionDisplay
{
    public string FormName { get; set; }
    public string DisplayName { get; set; }
    public bool ReadPermission { get; set; }
    public bool NewPermission { get; set; }
    public bool EditPermission { get; set; }
    public bool DeletePermission { get; set; }
    public bool PrintPermission { get; set; }
}
```

### 3. Designer Changes

Add to RoleMasterForm.Designer.cs:

```csharp
private void InitializePermissionsTab()
{
    // Tab Control
    this.tabControlRole = new DevExpress.XtraTab.XtraTabControl();
    this.tabPageDetails = new DevExpress.XtraTab.XtraTabPage();
    this.tabPagePermissions = new DevExpress.XtraTab.XtraTabPage();
    
    // Permissions Grid
    this.gridControlPermissions = new DevExpress.XtraGrid.GridControl();
    this.gridViewPermissions = new DevExpress.XtraGrid.Views.Grid.GridView();
    
    // Permission Management Buttons
    this.btnCopyFromRole = new DevExpress.XtraEditors.SimpleButton();
    this.btnResetPermissions = new DevExpress.XtraEditors.SimpleButton();
    this.cmbCopyFromRole = new DevExpress.XtraEditors.ComboBoxEdit();
    this.lblCopyFromRole = new DevExpress.XtraEditors.LabelControl();
    
    // Tab Control
    this.tabControlRole.Location = new System.Drawing.Point(12, 12);
    this.tabControlRole.Size = new System.Drawing.Size(760, 400);
    this.tabControlRole.TabPages.AddRange(new DevExpress.XtraTab.XtraTabPage[] {
        this.tabPageDetails,
        this.tabPagePermissions
    });
    
    // Details Tab (move existing controls here)
    this.tabPageDetails.Text = "Role Details";
    
    // Permissions Tab
    this.tabPagePermissions.Text = "Permissions";
    this.tabPagePermissions.Controls.Add(this.gridControlPermissions);
    this.tabPagePermissions.Controls.Add(this.btnCopyFromRole);
    this.tabPagePermissions.Controls.Add(this.btnResetPermissions);
    this.tabPagePermissions.Controls.Add(this.cmbCopyFromRole);
    this.tabPagePermissions.Controls.Add(this.lblCopyFromRole);
    
    // Permissions Grid
    this.gridControlPermissions.Location = new System.Drawing.Point(10, 50);
    this.gridControlPermissions.Size = new System.Drawing.Size(740, 300);
    this.gridControlPermissions.MainView = this.gridViewPermissions;
    
    // Copy From Role Controls
    this.lblCopyFromRole.Location = new System.Drawing.Point(10, 15);
    this.lblCopyFromRole.Text = "Copy from role:";
    
    this.cmbCopyFromRole.Location = new System.Drawing.Point(100, 12);
    this.cmbCopyFromRole.Size = new System.Drawing.Size(150, 20);
    
    this.btnCopyFromRole.Location = new System.Drawing.Point(260, 10);
    this.btnCopyFromRole.Size = new System.Drawing.Size(75, 23);
    this.btnCopyFromRole.Text = "Copy";
    
    this.btnResetPermissions.Location = new System.Drawing.Point(345, 10);
    this.btnResetPermissions.Size = new System.Drawing.Size(75, 23);
    this.btnResetPermissions.Text = "Reset All";
    
    // Add tab control to form
    this.Controls.Add(this.tabControlRole);
}
```

## Integration Points

### 1. Form Discovery Integration
When new forms are added via FormDiscoveryService, they should automatically appear in the permissions grid.

### 2. Permission Cache Integration
After saving role permissions, clear the permission cache to ensure changes take effect immediately.

### 3. Validation
- Ensure at least one role has full permissions (prevent lockout)
- Validate that Administrator role maintains system access

## Acceptance Criteria

- [ ] Tab control added with Role Details and Permissions tabs
- [ ] Permissions grid shows all forms with checkbox editing
- [ ] Copy permissions from another role functionality
- [ ] Reset all permissions functionality
- [ ] Integration with existing role save/load logic
- [ ] Unsaved changes detection and prompting
- [ ] Permission cache clearing after saves
- [ ] Form validation to prevent system lockout
- [ ] Proper error handling and user feedback

## Dependencies
- Task 02: Permission Data Models Creation
- Task 06: Core Permission Service Logic
- Task 09: Permission Management Form (3-Tab UI)

## Next Tasks
This task enables:
- Task 11: User Master Form Permission Integration
- Task 13: MainFrame Ribbon Permission Filtering
