# Task 13: MainFrame Ribbon Permission Filtering with MenuRibbon UC Integration

## Objective
Integrate the RBAC permission system with the MainFrame ribbon interface and coordinate with MenuRibbon UC centralized permission control. Filter ribbon buttons and menu items based on user permissions, ensuring users only see forms and functions they have access to, while maintaining consistency with MenuRibbon UC behavior.

## Priority
**INTEGRATION** - Depends on Tasks 01-12

## Estimated Time
2 hours

## Dependencies
- Task 06: Core Permission Service Logic
- Task 03: Forms Configuration Setup
- Task 05: Form Discovery Service Implementation

## Files to Modify
- `Forms/MainFrame.cs` (enhance existing)
- `Modules/Services/RibbonPermissionService.cs` (create new)
- `Forms/ReusableForms/MenuRibbon.cs` (coordinate with centralized permission control)

## Integration Overview

### Current MainFrame Structure
Based on ProManage patterns, the MainFrame likely has:
- DevExpress Ribbon Control
- Ribbon pages for different modules
- Ribbon groups with buttons for forms
- MDI container for child forms

### Enhanced Structure
Add permission-based filtering with MenuRibbon UC coordination:
- Hide/show ribbon buttons based on read permissions
- Disable buttons based on specific permissions
- Dynamic ribbon updates when permissions change
- User permission status display
- **Coordination with MenuRibbon UC**: Ensure MainFrame ribbon and MenuRibbon UC have consistent permission behavior
- **Centralized Permission Updates**: Changes in one location affect both MainFrame and all forms using MenuRibbon UC

## Implementation

### RibbonPermissionService.cs
```csharp
using System;
using System.Collections.Generic;
using System.Linq;
using DevExpress.XtraBars;
using DevExpress.XtraBars.Ribbon;
using ProManage.Modules.Services;
using ProManage.Modules.Models;

namespace ProManage.Modules.Services
{
    public class RibbonPermissionService
    {
        private readonly Dictionary<string, BarItem> _formButtonMapping;
        private readonly Dictionary<string, string> _buttonFormMapping;
        private int _currentUserId;
        
        public RibbonPermissionService()
        {
            _formButtonMapping = new Dictionary<string, BarItem>();
            _buttonFormMapping = new Dictionary<string, string>();
        }
        
        #region Button Registration
        
        /// <summary>
        /// Register a ribbon button with its corresponding form
        /// </summary>
        public void RegisterFormButton(string formName, BarItem button)
        {
            if (string.IsNullOrEmpty(formName) || button == null) return;
            
            _formButtonMapping[formName] = button;
            _buttonFormMapping[button.Name] = formName;
            
            // Set initial permission state
            if (_currentUserId > 0)
            {
                UpdateButtonPermission(formName, button);
            }
        }
        
        /// <summary>
        /// Register multiple form buttons at once
        /// </summary>
        public void RegisterFormButtons(Dictionary<string, BarItem> formButtonMap)
        {
            foreach (var kvp in formButtonMap)
            {
                RegisterFormButton(kvp.Key, kvp.Value);
            }
        }
        
        /// <summary>
        /// Unregister a form button
        /// </summary>
        public void UnregisterFormButton(string formName)
        {
            if (_formButtonMapping.TryGetValue(formName, out BarItem button))
            {
                _formButtonMapping.Remove(formName);
                _buttonFormMapping.Remove(button.Name);
            }
        }
        
        #endregion
        
        #region Permission Application
        
        /// <summary>
        /// Apply permissions for a specific user
        /// </summary>
        public void ApplyUserPermissions(int userId)
        {
            _currentUserId = userId;
            
            foreach (var kvp in _formButtonMapping)
            {
                UpdateButtonPermission(kvp.Key, kvp.Value);
            }
        }
        
        /// <summary>
        /// Update permission for a specific button
        /// </summary>
        private void UpdateButtonPermission(string formName, BarItem button)
        {
            try
            {
                // Check read permission (determines visibility)
                var canRead = PermissionService.HasPermission(_currentUserId, formName, PermissionType.Read);
                
                // Set visibility
                button.Visibility = canRead ? BarItemVisibility.Always : BarItemVisibility.Never;
                
                // If visible, check other permissions for tooltip/styling
                if (canRead)
                {
                    var canNew = PermissionService.HasPermission(_currentUserId, formName, PermissionType.New);
                    var canEdit = PermissionService.HasPermission(_currentUserId, formName, PermissionType.Edit);
                    var canDelete = PermissionService.HasPermission(_currentUserId, formName, PermissionType.Delete);
                    
                    // Update tooltip with permission info
                    UpdateButtonTooltip(button, formName, canNew, canEdit, canDelete);
                    
                    // Update button styling based on permissions
                    UpdateButtonStyling(button, canNew, canEdit, canDelete);
                }
            }
            catch (Exception ex)
            {
                // Log error and hide button for security
                System.Diagnostics.Debug.WriteLine($"Error applying permission for {formName}: {ex.Message}");
                button.Visibility = BarItemVisibility.Never;
            }
        }
        
        /// <summary>
        /// Update button tooltip with permission information
        /// </summary>
        private void UpdateButtonTooltip(BarItem button, string formName, bool canNew, bool canEdit, bool canDelete)
        {
            var displayName = FormsConfigurationService.GetFormDisplayName(formName);
            var permissions = new List<string>();
            
            if (canNew) permissions.Add("Create");
            if (canEdit) permissions.Add("Edit");
            if (canDelete) permissions.Add("Delete");
            
            var permissionText = permissions.Count > 0 
                ? $"Permissions: {string.Join(", ", permissions)}"
                : "Read-only access";
            
            button.SuperTip = CreateSuperTip(displayName, permissionText);
        }
        
        /// <summary>
        /// Update button styling based on permissions
        /// </summary>
        private void UpdateButtonStyling(BarItem button, bool canNew, bool canEdit, bool canDelete)
        {
            // Add visual indicators for limited permissions
            if (!canNew && !canEdit && !canDelete)
            {
                // Read-only - add indicator
                button.Caption = button.Caption.TrimEnd('*', '°') + "°"; // Add read-only indicator
            }
            else if (!canEdit && !canDelete)
            {
                // Limited access - add indicator
                button.Caption = button.Caption.TrimEnd('*', '°') + "*"; // Add limited indicator
            }
            else
            {
                // Full access - remove indicators
                button.Caption = button.Caption.TrimEnd('*', '°');
            }
        }
        
        /// <summary>
        /// Create super tip for button
        /// </summary>
        private SuperToolTip CreateSuperTip(string title, string content)
        {
            var superTip = new SuperToolTip();
            superTip.Items.Add(new ToolTipTitleItem { Text = title });
            superTip.Items.Add(new ToolTipItem { Text = content });
            return superTip;
        }
        
        #endregion
        
        #region Ribbon Management
        
        /// <summary>
        /// Hide empty ribbon groups (groups with no visible buttons)
        /// </summary>
        public void HideEmptyRibbonGroups(RibbonControl ribbon)
        {
            foreach (RibbonPage page in ribbon.Pages)
            {
                foreach (RibbonPageGroup group in page.Groups)
                {
                    var hasVisibleItems = group.ItemLinks.Any(link => 
                        link.Item.Visibility == BarItemVisibility.Always);
                    
                    group.Visible = hasVisibleItems;
                }
            }
        }
        
        /// <summary>
        /// Hide empty ribbon pages (pages with no visible groups)
        /// </summary>
        public void HideEmptyRibbonPages(RibbonControl ribbon)
        {
            foreach (RibbonPage page in ribbon.Pages)
            {
                var hasVisibleGroups = page.Groups.Any(group => group.Visible);
                page.Visible = hasVisibleGroups;
            }
        }
        
        /// <summary>
        /// Refresh all ribbon permissions
        /// </summary>
        public void RefreshRibbonPermissions(RibbonControl ribbon)
        {
            if (_currentUserId <= 0) return;
            
            ApplyUserPermissions(_currentUserId);
            HideEmptyRibbonGroups(ribbon);
            HideEmptyRibbonPages(ribbon);
        }
        
        #endregion
        
        #region Permission Checking
        
        /// <summary>
        /// Check if user can access a form via button click
        /// </summary>
        public bool CanAccessForm(string buttonName)
        {
            if (!_buttonFormMapping.TryGetValue(buttonName, out string formName))
                return false;
            
            return PermissionService.HasPermission(_currentUserId, formName, PermissionType.Read);
        }
        
        /// <summary>
        /// Get form name from button
        /// </summary>
        public string GetFormNameFromButton(string buttonName)
        {
            _buttonFormMapping.TryGetValue(buttonName, out string formName);
            return formName;
        }
        
        /// <summary>
        /// Get visible forms for current user
        /// </summary>
        public List<string> GetVisibleForms()
        {
            if (_currentUserId <= 0) return new List<string>();
            
            return PermissionService.GetVisibleForms(_currentUserId);
        }
        
        #endregion
    }
}
```

### Enhanced MainFrame.cs

Add these methods to the existing MainFrame class:

```csharp
using ProManage.Modules.Services;

public partial class MainFrame : DevExpress.XtraBars.Ribbon.RibbonForm
{
    private RibbonPermissionService _ribbonPermissionService;
    private int _currentUserId;
    
    // Add to constructor or form load
    private void InitializePermissionSystem()
    {
        _ribbonPermissionService = new RibbonPermissionService();
        RegisterFormButtons();
        
        // Apply permissions for current user
        var currentUser = GetCurrentUser(); // Implement based on existing user management
        if (currentUser != null)
        {
            ApplyUserPermissions(currentUser.UserId);
        }
    }
    
    #region Button Registration
    
    /// <summary>
    /// Register all form buttons with the permission service
    /// </summary>
    private void RegisterFormButtons()
    {
        // Register buttons with their corresponding forms
        var buttonMappings = new Dictionary<string, BarItem>
        {
            // System forms
            { "DatabaseForm", btnDatabase },
            { "ParametersForm", btnParameters },
            { "SQLQueryForm", btnSQLQuery },
            
            // Security forms
            { "UserMasterForm", btnUserMaster },
            { "UserManagementListForm", btnUserList },
            { "RoleMasterForm", btnRoleMaster },
            { "PermissionManagementForm", btnPermissionManagement },
            
            // Add other form buttons as needed
        };
        
        _ribbonPermissionService.RegisterFormButtons(buttonMappings);
    }
    
    #endregion
    
    #region Permission Application
    
    /// <summary>
    /// Apply permissions for a user
    /// </summary>
    public void ApplyUserPermissions(int userId)
    {
        _currentUserId = userId;
        _ribbonPermissionService.ApplyUserPermissions(userId);
        _ribbonPermissionService.RefreshRibbonPermissions(ribbonControl);
        
        // Update status bar or user info
        UpdateUserPermissionStatus(userId);
    }
    
    /// <summary>
    /// Update user permission status display
    /// </summary>
    private void UpdateUserPermissionStatus(int userId)
    {
        try
        {
            var visibleForms = _ribbonPermissionService.GetVisibleForms();
            var totalForms = FormsConfigurationService.GetAllForms().Count;
            
            // Update status bar or info panel
            if (statusStrip != null) // Assuming status strip exists
            {
                var statusText = $"Access: {visibleForms.Count}/{totalForms} forms";
                // Update status strip label
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Error updating permission status: {ex.Message}");
        }
    }
    
    #endregion
    
    #region Form Opening with Permission Checks
    
    /// <summary>
    /// Override or enhance existing form opening methods
    /// </summary>
    private void OpenFormWithPermissionCheck(string formName, Type formType)
    {
        try
        {
            // Check permission before opening
            if (!PermissionService.HasPermission(_currentUserId, formName, PermissionType.Read))
            {
                MessageBox.Show($"You do not have permission to access {FormsConfigurationService.GetFormDisplayName(formName)}.", 
                    "Access Denied", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }
            
            // Open the form
            OpenMdiChildForm(formType);
        }
        catch (Exception ex)
        {
            MessageBox.Show($"Error opening form: {ex.Message}", "Error", 
                MessageBoxButtons.OK, MessageBoxIcon.Error);
        }
    }
    
    /// <summary>
    /// Generic MDI child form opener
    /// </summary>
    private void OpenMdiChildForm(Type formType)
    {
        // Check if form is already open
        foreach (Form childForm in this.MdiChildren)
        {
            if (childForm.GetType() == formType)
            {
                childForm.Activate();
                return;
            }
        }
        
        // Create and show new form
        var form = (Form)Activator.CreateInstance(formType);
        form.MdiParent = this;
        form.Show();
    }
    
    #endregion
    
    #region Button Event Handlers
    
    // Update existing button click handlers to use permission checks
    private void btnUserMaster_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
    {
        OpenFormWithPermissionCheck("UserMasterForm", typeof(UserMasterForm));
    }
    
    private void btnDatabase_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
    {
        OpenFormWithPermissionCheck("DatabaseForm", typeof(DatabaseForm));
    }
    
    private void btnParameters_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
    {
        OpenFormWithPermissionCheck("ParametersForm", typeof(ParametersForm));
    }
    
    private void btnPermissionManagement_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
    {
        OpenFormWithPermissionCheck("PermissionManagementForm", typeof(PermissionManagementForm));
    }
    
    // Add similar handlers for other buttons
    
    #endregion
    
    #region User Session Management
    
    /// <summary>
    /// Handle user login/logout
    /// </summary>
    public void OnUserLogin(int userId)
    {
        ApplyUserPermissions(userId);
        
        // Sync forms with database (in case new forms were added)
        try
        {
            var syncResult = FormDiscoveryService.SyncFormsWithDatabase();
            if (syncResult.HasChanges)
            {
                // Re-register buttons if forms changed
                RegisterFormButtons();
                ApplyUserPermissions(userId);
            }
        }
        catch (Exception ex)
        {
            System.Diagnostics.Debug.WriteLine($"Error syncing forms on login: {ex.Message}");
        }
    }
    
    /// <summary>
    /// Handle user logout
    /// </summary>
    public void OnUserLogout()
    {
        _currentUserId = 0;
        
        // Hide all form buttons
        foreach (RibbonPage page in ribbonControl.Pages)
        {
            foreach (RibbonPageGroup group in page.Groups)
            {
                foreach (var link in group.ItemLinks)
                {
                    link.Item.Visibility = BarItemVisibility.Never;
                }
            }
        }
        
        // Close all MDI children
        foreach (Form child in this.MdiChildren)
        {
            child.Close();
        }
    }
    
    /// <summary>
    /// Refresh permissions (call when permissions change)
    /// </summary>
    public void RefreshUserPermissions()
    {
        if (_currentUserId > 0)
        {
            // Clear permission cache
            PermissionService.ClearUserPermissionCache(_currentUserId);
            
            // Reapply permissions
            ApplyUserPermissions(_currentUserId);
        }
    }
    
    #endregion
    
    #region Helper Methods
    
    private User GetCurrentUser()
    {
        // Implement based on existing user session management
        // Return current logged-in user
        return null; // TODO: Implement
    }
    
    #endregion
}
```

### Integration Points

#### 1. Application Startup
```csharp
// In Program.cs or application startup
private void Application_Startup()
{
    // Initialize permission system
    var mainFrame = new MainFrame();
    
    // Show login form
    var loginForm = new LoginForm();
    if (loginForm.ShowDialog() == DialogResult.OK)
    {
        var userId = loginForm.LoggedInUserId;
        mainFrame.OnUserLogin(userId);
        mainFrame.Show();
    }
}
```

#### 2. Permission Change Notifications
```csharp
// After permission changes in PermissionManagementForm
private void OnPermissionsSaved()
{
    // Notify MainFrame to refresh permissions
    var mainFrame = Application.OpenForms.OfType<MainFrame>().FirstOrDefault();
    mainFrame?.RefreshUserPermissions();
}
```

## Acceptance Criteria

- [ ] Ribbon buttons filtered based on read permissions
- [ ] Hidden buttons for forms user cannot access
- [ ] Visual indicators for limited permissions (read-only, etc.)
- [ ] Tooltips showing available permissions
- [ ] Empty ribbon groups and pages hidden automatically
- [ ] Permission checks before opening forms
- [ ] User permission status display
- [ ] Integration with user login/logout
- [ ] Automatic form discovery sync on login
- [ ] Permission refresh capability

## Dependencies
- Task 06: Core Permission Service Logic
- Task 03: Forms Configuration Setup
- Task 05: Form Discovery Service Implementation

## Next Tasks
This task enables:
- Task 14: Individual Form Permission Checks
- Task 15: Global Permission Implementation
- Task 16: Testing and Validation Suite
