# Global Permission System Architecture

## Overview

The Global Permission System provides centralized control over MenuRibbon UserControl functionality across all forms in ProManage. It implements a 2-level permission hierarchy where global permissions act as first-level filters before form-specific permissions are evaluated.

## Architecture Components

### 1. GlobalPermissionService
**Location:** `Modules/Services/GlobalPermissionService.cs`

**Purpose:** Centralized service for managing global permissions that control MenuRibbon UC across all forms.

**Key Features:**
- Global permission checking (first-level filter)
- Event-driven notification system for permission changes
- Integration with existing PermissionService
- Effective permission calculation (global + form-specific)

### 2. PermissionRepository
**Location:** `Modules/Data/Permissions/PermissionRepository.cs`

**Purpose:** Repository pattern wrapper for permission database operations.

**Key Features:**
- Clean interface for GlobalPermissionService
- Conversion between service models and database models
- Error handling and logging

### 3. MenuRibbon UserControl Enhancement
**Location:** `Forms/ReusableForms/MenuRibbon.cs`

**Purpose:** Enhanced MenuRibbon UC with global permission integration.

**Key Features:**
- Global permission checking before button state updates
- Event subscription for real-time permission changes
- Effective permission calculation
- Proper cleanup and resource management

## Permission Hierarchy

### Level 1: Global Permissions (First-Level Filter)
Global permissions control basic operations across ALL forms:

- **Global Read Permission:** Controls form visibility in ribbon menu
- **Global New Permission:** Controls New button across all forms
- **Global Edit Permission:** Controls Edit/Save buttons across all forms
- **Global Delete Permission:** Controls Delete button across all forms
- **Global Print Permission:** Controls Print buttons across all forms

### Level 2: Form-Specific Permissions (Second-Level Filter)
Form-specific permissions provide granular control per form:

- **Role Permissions:** Default permissions assigned to roles
- **User Permission Overrides:** User-specific overrides (NULL = inherit from role)

### Permission Resolution Logic
```
1. Check Global Permission (first-level filter)
   - If Global Permission = FALSE → DENY ACCESS
   - If Global Permission = TRUE → Continue to step 2

2. Check Form-Specific Permission (second-level filter)
   - Check User Permission Override (if not NULL, use it)
   - Else use Role Permission
   
3. Final Access = Global Permission AND Form-Specific Permission
```

## Database Schema

### global_permissions Table
```sql
CREATE TABLE global_permissions (
    permission_id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(user_id) ON DELETE CASCADE UNIQUE,
    global_read_permission BOOLEAN DEFAULT false,
    global_new_permission BOOLEAN DEFAULT false,
    global_edit_permission BOOLEAN DEFAULT false,
    global_delete_permission BOOLEAN DEFAULT false,
    global_print_permission BOOLEAN DEFAULT false
);
```

**Note:** Current implementation uses different column names in UI:
- `chkCanCreateUsers` → `global_new_permission`
- `chkCanEditUsers` → `global_edit_permission`
- `chkCanDeleteUsers` → `global_delete_permission`
- `chkCanPrintUsers` → `global_print_permission`

## Event-Driven Architecture

### Global Permission Change Notification
When global permissions are updated:

1. **GlobalPermissionService.UpdateGlobalPermissions()** is called
2. Database is updated via **PermissionRepository**
3. **GlobalPermissionsChanged** event is fired
4. All **MenuRibbon UC** instances receive the event
5. Each **MenuRibbon UC** refreshes its button states if affected

### Event Flow Diagram
```
PermissionManagementForm
    ↓ (User changes checkbox)
GlobalPermissionService.UpdateGlobalPermissions()
    ↓ (Database update)
PermissionRepository.UpdateGlobalPermissions()
    ↓ (Fire event)
GlobalPermissionsChanged Event
    ↓ (Notify all subscribers)
MenuRibbon UC instances
    ↓ (Refresh button states)
UpdateButtonStates()
```

## Implementation Details

### MenuRibbon UC Integration

#### Effective Permission Checking
```csharp
private bool HasEffectivePermission(string permissionType)
{
    // First check global permission (first-level filter)
    bool hasGlobal = GlobalPermissionService.HasGlobalPermission(_currentUserId, permissionType);
    if (!hasGlobal) return false;

    // Then check form-specific permission (second-level filter)
    if (_currentPermissions == null) return true; // If no form restrictions, allow
    
    // Return form-specific permission result
    return _currentPermissions.GetPermission(permissionType);
}
```

#### Button State Updates
```csharp
private void UpdateButtonStates()
{
    // Get effective permissions (global + form-specific)
    bool canRead = HasEffectivePermission("read");
    bool canCreate = HasEffectivePermission("new");
    bool canEdit = HasEffectivePermission("edit");
    bool canDelete = HasEffectivePermission("delete");
    bool canPrint = HasEffectivePermission("print");

    // Update button states based on effective permissions
    BarButtonItemNew.Enabled = canCreate && !_isEditMode;
    BarButtonItemEdit.Enabled = canEdit && !_isEditMode;
    // ... etc
}
```

### Global Permission Management

#### Checkbox Functionality
In **PermissionManagementForm**, global permission checkboxes:

1. **Load:** Retrieve current global permissions from **GlobalPermissionService**
2. **Change:** Mark form as having unsaved changes
3. **Save:** Update global permissions and notify all forms

#### Real-Time Updates
When global permissions change:

1. All open forms with **MenuRibbon UC** are notified immediately
2. Button states are refreshed without requiring form reload
3. Users see permission changes take effect instantly

## Usage Examples

### Checking Effective Permission
```csharp
// Check if user can edit in EstimateForm
bool canEdit = GlobalPermissionService.HasEffectivePermission(userId, "EstimateForm", "edit");
```

### Updating Global Permissions
```csharp
var permissions = new GlobalPermissionModel
{
    UserId = userId,
    CanCreateUsers = true,
    CanEditUsers = true,
    CanDeleteUsers = false,
    CanPrintUsers = true
};

bool success = GlobalPermissionService.UpdateGlobalPermissions(userId, permissions);
```

### MenuRibbon UC Setup
```csharp
// In form constructor
menuRibbon.FormName = "EstimateForm";
menuRibbon.CurrentUserId = currentUserId;
menuRibbon.RefreshAll(); // Load permissions and update button states
```

## Benefits

1. **Centralized Control:** Global permissions provide system-wide access control
2. **Real-Time Updates:** Changes take effect immediately across all forms
3. **Hierarchical Security:** Two-level filtering provides flexible security model
4. **Performance:** Event-driven updates minimize database queries
5. **Consistency:** All forms use the same permission checking logic
6. **Maintainability:** Clean separation of concerns and modular design

## Future Enhancements

1. **Permission Groups:** Group related permissions for easier management
2. **Time-Based Permissions:** Temporary permission grants with expiration
3. **Audit Trail:** Log all permission changes for compliance
4. **Role Templates:** Pre-defined permission sets for common roles
5. **Bulk Operations:** Update permissions for multiple users simultaneously
