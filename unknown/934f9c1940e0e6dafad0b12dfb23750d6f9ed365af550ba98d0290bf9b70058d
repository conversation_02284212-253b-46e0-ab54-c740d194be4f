using System;
using System.Collections.Generic;
using System.Linq;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using ProManage.Modules.Connections;
using ProManage.Modules.Services;
using ProManage.Modules.Models;

namespace ProManage.Tests
{
    /// <summary>
    /// Comprehensive integration tests for the permission system
    /// Tests database connectivity, data retrieval, and form integration
    /// </summary>
    [TestClass]
    public class PermissionSystemIntegrationTests
    {
        private static bool _schemaVerified = false;

        [ClassInitialize]
        public static void ClassInitialize(TestContext context)
        {
            // Verify database schema before running tests
            _schemaVerified = PermissionDatabaseService.VerifyAndCreateSchema();
            Assert.IsTrue(_schemaVerified, "Failed to verify/create database schema");
        }

        [TestMethod]
        public void TestDatabaseConnection()
        {
            // Test basic database connectivity
            try
            {
                using (var connection = DatabaseConnectionManager.Instance.CreateNewConnection())
                {
                    connection.Open();
                    Assert.IsTrue(connection.State == System.Data.ConnectionState.Open, 
                        "Database connection should be open");
                }
            }
            catch (Exception ex)
            {
                Assert.Fail($"Database connection failed: {ex.Message}");
            }
        }

        [TestMethod]
        public void TestGetAllRoles_ShouldReturnActiveRoles()
        {
            // Test that GetAllRoles returns data
            var roles = PermissionService.GetAllRoles();
            
            Assert.IsNotNull(roles, "Roles list should not be null");
            Assert.IsTrue(roles.Count > 0, "Should have at least one active role");
            
            // Verify all returned roles are active
            foreach (var role in roles)
            {
                Assert.IsTrue(role.IsActive, $"Role {role.RoleName} should be active");
                Assert.IsFalse(string.IsNullOrEmpty(role.RoleName), "Role name should not be empty");
            }
            
            Console.WriteLine($"Found {roles.Count} active roles");
            foreach (var role in roles.Take(5)) // Show first 5 roles
            {
                Console.WriteLine($"- {role.RoleId}: {role.RoleName}");
            }
        }

        [TestMethod]
        public void TestGetAllUsers_ShouldReturnActiveUsers()
        {
            // Test that GetAllUsers returns data
            var users = PermissionService.GetAllUsers();
            
            Assert.IsNotNull(users, "Users list should not be null");
            Assert.IsTrue(users.Count > 0, "Should have at least one active user");
            
            // Verify all returned users are active
            foreach (var user in users)
            {
                Assert.IsTrue(user.IsActive, $"User {user.Username} should be active");
                Assert.IsFalse(string.IsNullOrEmpty(user.Username), "Username should not be empty");
            }
            
            Console.WriteLine($"Found {users.Count} active users");
            foreach (var user in users)
            {
                Console.WriteLine($"- {user.UserId}: {user.Username} ({user.FullName}) - Role: {user.RoleName}");
            }
        }

        [TestMethod]
        public void TestRolePermissions_ShouldLoadCorrectly()
        {
            // Get first active role
            var roles = PermissionService.GetAllRoles();
            Assert.IsTrue(roles.Count > 0, "Need at least one role for testing");
            
            var firstRole = roles.First();
            var permissions = PermissionDatabaseService.GetRolePermissions(firstRole.RoleId);
            
            Assert.IsNotNull(permissions, "Role permissions should not be null");
            Console.WriteLine($"Role '{firstRole.RoleName}' has {permissions.Count} permission entries");
            
            // Verify permission structure
            foreach (var permission in permissions.Take(3)) // Show first 3 permissions
            {
                Assert.AreEqual(firstRole.RoleId, permission.RoleId, "Permission should belong to correct role");
                Assert.IsFalse(string.IsNullOrEmpty(permission.FormName), "Form name should not be empty");
                Console.WriteLine($"- {permission.FormName}: R:{permission.ReadPermission} N:{permission.NewPermission} E:{permission.EditPermission} D:{permission.DeletePermission} P:{permission.PrintPermission}");
            }
        }

        [TestMethod]
        public void TestUserPermissions_ShouldHandleEmptyOverrides()
        {
            // Get first active user
            var users = PermissionService.GetAllUsers();
            Assert.IsTrue(users.Count > 0, "Need at least one user for testing");
            
            var firstUser = users.First();
            var userPermissions = PermissionDatabaseService.GetUserPermissions(firstUser.UserId);
            
            Assert.IsNotNull(userPermissions, "User permissions should not be null (even if empty)");
            Console.WriteLine($"User '{firstUser.Username}' has {userPermissions.Count} permission overrides");
            
            // User permissions can be empty (no overrides)
            if (userPermissions.Count > 0)
            {
                foreach (var permission in userPermissions.Take(3))
                {
                    Assert.AreEqual(firstUser.UserId, permission.UserId, "Permission should belong to correct user");
                    Console.WriteLine($"- Override for {permission.FormName}");
                }
            }
        }

        [TestMethod]
        public void TestGlobalPermissions_ShouldLoadCorrectly()
        {
            // Get first active user
            var users = PermissionService.GetAllUsers();
            Assert.IsTrue(users.Count > 0, "Need at least one user for testing");
            
            var firstUser = users.First();
            
            try
            {
                var globalPermissions = PermissionDatabaseService.GetGlobalPermissions(firstUser.UserId);
                
                // Global permissions can be null if not set for user
                if (globalPermissions != null)
                {
                    Assert.AreEqual(firstUser.UserId, globalPermissions.UserId, "Global permissions should belong to correct user");
                    Console.WriteLine($"User '{firstUser.Username}' global permissions:");
                    Console.WriteLine($"- Can Create Users: {globalPermissions.CanCreateUsers}");
                    Console.WriteLine($"- Can Edit Users: {globalPermissions.CanEditUsers}");
                    Console.WriteLine($"- Can Delete Users: {globalPermissions.CanDeleteUsers}");
                    Console.WriteLine($"- Can Print Users: {globalPermissions.CanPrintUsers}");
                }
                else
                {
                    Console.WriteLine($"User '{firstUser.Username}' has no global permissions set");
                }
            }
            catch (Exception ex)
            {
                Assert.Fail($"Error loading global permissions: {ex.Message}");
            }
        }

        [TestMethod]
        public void TestPermissionServiceIntegration()
        {
            // Test the full integration through PermissionService
            try
            {
                var roles = PermissionService.GetAllRoles();
                var users = PermissionService.GetAllUsers();
                
                Assert.IsTrue(roles.Count > 0, "Should have roles");
                Assert.IsTrue(users.Count > 0, "Should have users");
                
                // Test that we can get permissions for a user
                var firstUser = users.First();
                var userWithRole = PermissionDatabaseService.GetUserWithRole(firstUser.UserId);
                
                Assert.IsNotNull(userWithRole, "Should be able to get user with role info");
                Assert.AreEqual(firstUser.UserId, userWithRole.UserId, "User ID should match");
                
                Console.WriteLine($"Integration test successful:");
                Console.WriteLine($"- Found {roles.Count} roles");
                Console.WriteLine($"- Found {users.Count} users");
                Console.WriteLine($"- User '{firstUser.Username}' has role '{userWithRole.RoleName}'");
            }
            catch (Exception ex)
            {
                Assert.Fail($"Permission service integration failed: {ex.Message}");
            }
        }

        [TestMethod]
        public void TestDatabaseSchemaConsistency()
        {
            // Verify that all expected tables exist and have correct structure
            try
            {
                // Test each table by running a simple query
                var roles = PermissionDatabaseService.GetAllRoles();
                var users = PermissionDatabaseService.GetAllUsers();
                
                if (roles.Count > 0)
                {
                    var rolePermissions = PermissionDatabaseService.GetRolePermissions(roles.First().RoleId);
                    Assert.IsNotNull(rolePermissions, "Role permissions query should work");
                }
                
                if (users.Count > 0)
                {
                    var userPermissions = PermissionDatabaseService.GetUserPermissions(users.First().UserId);
                    Assert.IsNotNull(userPermissions, "User permissions query should work");
                    
                    var globalPermissions = PermissionDatabaseService.GetGlobalPermissions(users.First().UserId);
                    // Global permissions can be null, but query should not fail
                }
                
                Console.WriteLine("Database schema consistency check passed");
            }
            catch (Exception ex)
            {
                Assert.Fail($"Database schema consistency check failed: {ex.Message}");
            }
        }
    }
}
