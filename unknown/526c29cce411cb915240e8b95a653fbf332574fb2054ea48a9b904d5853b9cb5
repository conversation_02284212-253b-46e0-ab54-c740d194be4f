<?xml version="1.0"?>
<doc>
    <assembly>
        <name>TelerikData</name>
    </assembly>
    <members>
        <member name="T:Telerik.WinControls.UI.Export.CSV.CSVCellFormattingEventArgs">
            <summary>
            Provides event arguments for the CSVCellFormatting event
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.CSV.CSVCellFormattingEventArgs.GridColumnIndex">
            <summary>
            Get source grid's column index
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.CSV.CSVCellFormattingEventArgs.GridRowIndex">
            <summary>
            Get source grid's row index
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.CSV.CSVCellFormattingEventArgs.GridRowInfoType">
            <summary>
            Get source grid's row type
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.CSV.CSVCellFormattingEventArgs.GridCellInfo">
            <summary>
            Get a reference to the source grid's cell.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.CSV.CSVCellFormattingEventArgs.CSVCellElement">
            <summary>
            Get csv cell element object for further formatting
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.CSV.CSVCellFormattingEventArgs.InnerCellValue">
            <summary>
            Get the inner cell value which reference the original grid's value
            </summary>
        </member>
        <member name="T:Telerik.WinControls.UI.Export.CSV.CSVTableCreatedEventArgs">
            <summary>
            Provides event arguments for the ExcelTableCreated event
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.CSV.CSVTableCreatedEventArgs.CSVTableElement">
            <summary>
            Get excel table element
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.CSV.RowElement.RowDelimiter">
            <summary>
            Delimeter is added before the row. 
            For the first row of the CSV file the delimeter must be set to null.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.CSV.RowElement.InnerElements">
            <summary>
            This element cannot have inner elements.
            Use Cells property 
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.CSV.TableElement.InnerElements">
            <summary>
            This element cannot have inner elements.
            Use Rows property 
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Export.ExportToCSV.#ctor(Telerik.WinControls.UI.RadGridView)">
            <summary>
            Create instance of exporter class with specified RadGridView to export 
            </summary>
            <param name="radGridView">RadGridView to export</param>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.ExportToCSV.RowDelimiter">
            <summary>
            Gets or sets the value that will be used for a row delimiter.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.ExportToCSV.ColumnDelimiter">
            <summary>
            Gets or sets the value that will be used for a column delimiter.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.ExportToCSV.ExportHierarchy">
            <summary>
            Set if child rows should be exported.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.ExportToCSV.HiddenColumnOption">
            <summary>
            Set if hidden column would be exported.
            Note that <see cref="F:Telerik.WinControls.UI.Export.HiddenOption.ExportAsHidden"/> is not relevant for CSV export.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.ExportToCSV.HiddenRowOption">
            <summary>
            Set if hidden rows would be exported.
            Note that <see cref="F:Telerik.WinControls.UI.Export.HiddenOption.ExportAsHidden"/> is not relevant for CSV export.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.ExportToCSV.SummariesExportOption">
            <summary>
            Set how to export summary rows.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.ExportToCSV.PagingExportOption">
            <summary>
            Sets a value indicating how the export behavies when paging is enabled.
            </summary>
            <value>
            The paging export option.
            </value>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.ExportToCSV.ChildViewExportMode">
            <summary>
            Gets or sets the child view export mode.
            </summary>
            <value>
            The child view export mode.
            </value>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.ExportToCSV.FileExtension">
            <summary>
            Set file extension. Default value is <b>csv</b>.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.ExportToCSV.RadGridViewToExport">
            <summary>
            Set RadGridView that would be exported.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.ExportToCSV.EncloseDataWithQuotes">
            <summary>
            Gets or sets a value indicating whether values are enclosed in quotes in the exported file.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.ExportToCSV.Encoding">
            <summary>
            Gets or sets the encoding used for the exported file. Default encoding is UTF8.
            </summary>
        </member>
        <member name="E:Telerik.WinControls.UI.Export.ExportToCSV.ChildViewExporting">
            <summary>
            Occurs when a child view will be exported.
            </summary>
        </member>
        <member name="E:Telerik.WinControls.UI.Export.ExportToCSV.CSVCellFormatting">
            <summary>
            Fires when an csv cell was created and needs to be formatted.
            </summary>
        </member>
        <member name="E:Telerik.WinControls.UI.Export.ExportToCSV.CSVTableCreated">
            <summary>
            Fires when the csv table element was created.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Export.ExportToCSV.RunExport(System.String)">
            <summary>
            Creates file in csv format.
            </summary>
            <param name="fileName">Export file name</param>
        </member>
        <member name="M:Telerik.WinControls.UI.Export.ExportToCSV.FileWriteElement(System.IO.Stream,Telerik.WinControls.UI.Export.CSV.ElementBase)">
            <summary>
            Write element to file and specify how many simbols to cut from the end
            </summary>
            <param name="stream">File stream to use</param>
            <param name="csvElement">element to write</param>
        </member>
        <member name="M:Telerik.WinControls.UI.Export.ExportToCSV.GetBytes(System.String)">
            <summary>
            Converts string to byte array
            </summary>
            <param name="s">string to convert</param>
            <returns>byte array</returns>
        </member>
        <member name="M:Telerik.WinControls.UI.Export.ExportToCSV.AddCustomCSVRow(Telerik.WinControls.UI.Export.CSV.TableElement,System.String[])">
            <summary>
            Add custom row in the provided csv table element
            </summary>
            <param name="csvTableElement">CSV table element where the new row will be added</param>
            <param name="cellValues">Values for the every csv cell object in the custom row</param>
        </member>
        <member name="T:Telerik.WinControls.UI.Export.ExcelML.ExcelCellFormattingEventArgs">
            <summary>
            Provides event arguments for the ExcelCellFormatting event
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.ExcelML.ExcelCellFormattingEventArgs.GridColumnIndex">
            <summary>
            Get source grid's column index
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.ExcelML.ExcelCellFormattingEventArgs.GridRowIndex">
            <summary>
            Get source grid's row index
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.ExcelML.ExcelCellFormattingEventArgs.GridRowInfoType">
            <summary>
            Get source grid's row type
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.ExcelML.ExcelCellFormattingEventArgs.GridCellInfo">
            <summary>
            Get a reference to the source grid's cell.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.ExcelML.ExcelCellFormattingEventArgs.ExcelCellElement">
            <summary>
            Get excel cell element for further formatting
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.ExcelML.ExcelCellFormattingEventArgs.ExcelStyleElement">
            <summary>
            Get excel style element for further formatting
            </summary>
        </member>
        <member name="T:Telerik.WinControls.UI.Export.ExcelML.ExcelRowFormattingEventArgs">
            <summary>
            Provides event arguments for the ExcelRowFormatting event
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.ExcelML.ExcelRowFormattingEventArgs.GridViewRowInfo">
            <summary>
            Get a reference to the source grid's row.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.ExcelML.ExcelRowFormattingEventArgs.ExcelRowElement">
            <summary>
            Get excel row element for further formatting
            </summary>
        </member>
        <member name="T:Telerik.WinControls.UI.Export.ExcelML.ExcelTableCreatedEventArgs">
            <summary>
            Provides event arguments for the ExcelTableCreated event
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.ExcelML.ExcelTableCreatedEventArgs.ExcelTableElement">
            <summary>
            Get excel table element
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.ExcelML.ExcelTableCreatedEventArgs.SheetIndex">
            <summary>
            Get sheet's index, which contains excel table element 
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.ExcelML.BorderStyles.Weight">
            <summary>
            Max value 3
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.ExcelML.BorderStyles.PositionType">
            <summary>
            This is required
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.ExcelML.CellElement.Data">
            <summary>
            Get data element (use to change and format value). 
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.ExcelML.CellElement.StyleValue">
            <summary>
            Set or Get style ID for this cell element.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.ExcelML.CellElement.ColumnName">
            <summary>
            Set or Get column name.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.ExcelML.DataElement.DataType">
            <summary>
            Get or Set data type.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.ExcelML.DataElement.DataItem">
            <summary>
            Get or Set data item (use to specify the cell value).
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.ExcelML.DataElement.EscapeStrings">
            <summary>
            Gets or sets a value indicating whether strings will be escaped.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.ExcelML.DataElement.InnerElements">
            <summary>
            Gets the inner elements.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.ExcelML.DataElement.StartTag">
            <summary>
            Gets the start tag for this element.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.ExcelML.DataElement.EndTag">
            <summary>
            Gets the end tag for this element.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.ExcelML.FontStyleElement.Underline">
            <summary> 
            Get or set font underline.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.ExcelML.FontStyleElement.Size">
            <summary>
            Get or Set font size.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.ExcelML.FontStyleElement.Italic">
            <summary>
            Get or Set font italic
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.ExcelML.FontStyleElement.FontName">
            <summary>
            Get or Set font name
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.ExcelML.FontStyleElement.Color">
            <summary>
            Get or Set font color
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.ExcelML.FontStyleElement.Bold">
            <summary>
            Get or Set font bold
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.ExcelML.InteriorStyleElement.Pattern">
            <summary>
            Get or Set color pattern
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.ExcelML.InteriorStyleElement.Color">
            <summary>
            Get or Set back color
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.ExcelML.RowElement.InnerElements">
            <summary>
            This element cannot have inner elements.
            Use Cells property 
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.ExcelML.SingleStyleElement.AlignmentElement">
            <summary>
            Get text alignment element (use to set text alignments).
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.ExcelML.SingleStyleElement.Borders">
            <summary>
            Get border elements collection (use to add borders).
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.ExcelML.SingleStyleElement.NumberFormat">
            <summary>
            Get number formatting element (use to set format).
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.ExcelML.SingleStyleElement.InteriorStyle">
            <summary>
            Get interior style element (use to change back color and color pattern).
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.ExcelML.SingleStyleElement.FontStyle">
            <summary>
            Get font style settings (use to change font settings).
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.ExcelML.SingleStyleElement.Id">
            <summary>
            Get or Set unique style identificator.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Export.ExcelML.SingleStyleElement.#ctor(System.String)">
            <summary>
            Creates new style element with specified ID.
            </summary>
            <param name="id">Unique style identificator.</param>
        </member>
        <member name="M:Telerik.WinControls.UI.Export.ExcelML.SingleStyleElement.#ctor">
            <summary>
            Creates new style element.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Export.ExcelML.SingleStyleElement.Equals(System.Object)">
            <summary>
            Compare with other style element.
            </summary>
            <param name="obj">SingleStyleElement to compare with.</param>
            <returns>True if they are equal</returns>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.ExcelML.StylesElement.Attributes">
            <summary>
            This element cannot have attributes
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.ExportToExcelML.ExportHierarchy">
            <summary>
            Set if child rows should be exported.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.ExportToExcelML.ExportVisualSettings">
            <summary>
            Sets a value indicating whether the visual settings should be exported.
            </summary>
            <value>
            <c>true</c> if visual settings are exported; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.ExportToExcelML.SheetName">
            <summary>
            Sets the name of the sheet.
            </summary>
            <value>
            The name of the sheet.
            </value>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.ExportToExcelML.SheetMaxRows">
            <summary>
            Sets the sheet max rows.
            </summary>
            <value>
            The sheet max rows.
            </value>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.ExportToExcelML.SummariesExportOption">
            <summary>
            Sets a value indicating how summary rows are exported.
            </summary>
            <value>
            The summaries export option.
            </value>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.ExportToExcelML.HiddenColumnOption">
            <summary>
            Sets a value indicating how hidden columns are exported.
            </summary>
            <value>
            The hidden column option.
            </value>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.ExportToExcelML.HiddenRowOption">
            <summary>
            Sets a value indicating how hidden rows are exported.
            </summary>
            <value>
            The hidden row option.
            </value>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.ExportToExcelML.PagingExportOption">
            <summary>
            Sets a value indicating how the export behavies when paging is enabled.
            </summary>
            <value>
            The paging export option.
            </value>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.ExportToExcelML.ChildViewExportMode">
            <summary>
            Gets or sets the child view export mode.
            </summary>
            <value>
            The child view export mode.
            </value>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.ExportToExcelML.RadGridViewToExport">
            <summary>
            Sets the RadGridView to export.
            </summary>
            <value>
            The RadGridView to export.
            </value>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.ExportToExcelML.FileExtension">
            <summary>
            Sets the file extension of the exported file.
            </summary>
            <value>
            The file extension.
            </value>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.ExportToExcelML.Encoding">
            <summary>
            Gets or sets the encoding used for the exported file. Default encoding is UTF8.
            </summary>
        </member>
        <member name="E:Telerik.WinControls.UI.Export.ExportToExcelML.ChildViewExporting">
            <summary>
            Occurs when a child view will be exported.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Export.ExportToExcelML.OnChildViewExporting(Telerik.WinControls.UI.Export.ChildViewExportingEventArgs)">
            <summary>
            Raises the <see cref="E:ChildViewExporting" /> event.
            </summary>
            <param name="e">The <see cref="T:Telerik.WinControls.UI.Export.ChildViewExportingEventArgs"/> instance containing the event data.</param>
        </member>
        <member name="E:Telerik.WinControls.UI.Export.ExportToExcelML.ExcelCellFormatting">
            <summary>
            Occurs for every cell that is being exported.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Export.ExportToExcelML.OnExcelCellFormatting(Telerik.WinControls.UI.Export.ExcelML.ExcelCellFormattingEventArgs)">
            <summary>
            Raises the <see cref="E:ExcelCellFormatting" /> event.
            </summary>
            <param name="e">The <see cref="T:Telerik.WinControls.UI.Export.ExcelML.ExcelCellFormattingEventArgs"/> instance containing the event data.</param>
        </member>
        <member name="E:Telerik.WinControls.UI.Export.ExportToExcelML.ExcelRowFormatting">
            <summary>
            Occurs for every row that is being exported.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Export.ExportToExcelML.OnExcelCellFormatting(Telerik.WinControls.UI.Export.ExcelML.ExcelRowFormattingEventArgs)">
            <summary>
            Raises the <see cref="E:ExcelCellFormatting" /> event.
            </summary>
            <param name="e">The <see cref="T:Telerik.WinControls.UI.Export.ExcelML.ExcelRowFormattingEventArgs"/> instance containing the event data.</param>
        </member>
        <member name="E:Telerik.WinControls.UI.Export.ExportToExcelML.ExcelTableCreated">
            <summary>
            Occurs when the excel table is created.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Export.ExportToExcelML.OnExcelTableCreated(Telerik.WinControls.UI.Export.ExcelML.ExcelTableCreatedEventArgs)">
            <summary>
            Raises the <see cref="E:ExcelTableCreated" /> event.
            </summary>
            <param name="e">The <see cref="T:Telerik.WinControls.UI.Export.ExcelML.ExcelTableCreatedEventArgs"/> instance containing the event data.</param>
        </member>
        <member name="M:Telerik.WinControls.UI.Export.ExportToExcelML.FileWriteBytes(System.String,System.Byte[],System.Int64)">
            <summary>
            Write bytes to the file
            </summary>
            <param name="fileName">File name to write into</param>
            <param name="bytes">bytes to write</param>
            <param name="position">position where to begin to write</param>
        </member>
        <member name="M:Telerik.WinControls.UI.Export.ExportToExcelML.FileTranspose(System.String,System.Int64,System.Int64,System.Int64,System.Int32)">
            <summary>
            XML file transponse. Used to prepare file for inserting text into it.
            </summary>
            <param name="fileName">File name to transponce</param>
            <param name="sourcePos"></param>
            <param name="destPos"></param>
            <param name="length"></param>
            <param name="bfrSz"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Export.ExportToExcelML.FileSetLength(System.String,System.Int64)">
            <summary>
            Wrapper for FileStream.SetLength().
            </summary>
            <remarks>
            When lengthening a file, this method appends null characters to it which 
            does NOT leave it in an XML-parseable state. After all your transpositions, 
            ALWAYS come back and truncate the file unless you've overwritten the 
            appended space with valid characters.
            </remarks>
            <param name="fileName">Name of file to resize</param>
            <param name="length">New size of file</param>
        </member>
        <member name="M:Telerik.WinControls.UI.Export.ExportToExcelML.FileInsertString(System.String,System.String)">
            <summary>
            Insert string to the text file
            </summary>
            <param name="fileName">File name to insert into</param>
            <param name="stringToInsert">Text to insert</param>
        </member>
        <member name="M:Telerik.WinControls.UI.Export.ExportToExcelML.FinishXmlFile(System.String,Telerik.WinControls.UI.Export.ExcelML.WorkBookElement)">
            <summary>
            Finish the excelML file
            </summary>
            <param name="fileName">File name to finish</param>
            <param name="workbook">Workbook element</param>
        </member>
        <member name="M:Telerik.WinControls.UI.Export.ExportToExcelML.AddStyleElement(Telerik.WinControls.UI.Export.ExcelML.WorkBookElement,Telerik.WinControls.UI.Export.ExcelML.SingleStyleElement)">
            <summary>
            Add style element to the workbook's styles collection 
            </summary>
            <param name="workbook">Workbook where to add</param>
            <param name="excelStyleElement">Style to add</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.WinControls.UI.Export.ExportToExcelML.SetDataTypeForCellElement(Telerik.WinControls.UI.Export.ExcelML.CellElement,Telerik.WinControls.Export.DataType)">
            <summary>
            Set data type for cell formatting to already created excel cell element
            </summary>
            <param name="cellElement">Excel cell element</param>
            <param name="dataType">Data type</param>
        </member>
        <member name="M:Telerik.WinControls.UI.Export.ExportToExcelML.SetDataTypeForCellElement(Telerik.WinControls.UI.Export.ExcelML.CellElement,Telerik.WinControls.UI.Export.DisplayFormatType)">
            <summary>
            Set data type for cell formatting to already created excel cell element according to DisplayFormatType
            </summary>
            <param name="cellElement">Excel cell element</param>
            <param name="displayFormatType"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Export.ExportToExcelML.ApplyTextAlignment(Telerik.WinControls.UI.Export.ExcelML.SingleStyleElement,System.Drawing.ContentAlignment)">
            <summary>
            Convert and apply content text alignment to style element
            </summary>
            <param name="styleElement">Style element</param>
            <param name="contentAlignment">Content alignment to apply</param>
        </member>
        <member name="M:Telerik.WinControls.UI.Export.ExportToExcelML.CreateStyleElement(Telerik.WinControls.UI.LightVisualElement)">
            <summary>
            Create excel StyleElement
            </summary>
            <param name="gridVisualElement">Grid's visual element</param>
            <returns>Excel style element</returns>
        </member>
        <member name="M:Telerik.WinControls.UI.Export.ExportToExcelML.CreateCellElement(Telerik.WinControls.UI.GridViewCellInfo)">
            <summary>
            Create excel cell element.
            </summary>
            <param name="gridCell">grid's cell info</param>
            <returns>excel cell element</returns>
        </member>
        <member name="M:Telerik.WinControls.UI.Export.ExportToExcelML.CreateRowElement(Telerik.WinControls.UI.Export.ExcelML.WorkBookElement,Telerik.WinControls.UI.GridViewRowInfo)">
            <summary>
            Create excel row element with a style element and add this style to excel workbook
            </summary>
            <param name="workbook">Excel workbook object</param>
            <param name="gridViewRowInfo">Grid's row info</param>
            <returns>Excel row element</returns>
        </member>
        <member name="M:Telerik.WinControls.UI.Export.ExportToExcelML.SetExcelRowElementHeight(Telerik.WinControls.UI.Export.ExcelML.RowElement,System.Int32)">
            <summary>
            Set excel row element height
            </summary>
            <param name="excelRowElement">RowInfo to set</param>
            <param name="height">Desired height in pixels, if 0 than remains default height.</param>
        </member>
        <member name="M:Telerik.WinControls.UI.Export.ExportToExcelML.AddWorkSheet(Telerik.WinControls.UI.Export.ExcelML.WorkBookElement,System.String,System.Int32@)">
            <summary>
            Create and add excel worksheet to the workbook
            </summary>
            <param name="workbook">Excel workbook</param>
            <param name="sheetName"></param>
            <param name="currentSheet"></param>
            <returns>Excel table element</returns>
        </member>
        <member name="M:Telerik.WinControls.UI.Export.ExportToExcelML.AddColumnElements(Telerik.WinControls.UI.RadGridView,Telerik.WinControls.UI.Export.ExcelML.TableElement)">
            <summary>
            add column eelemnts to the excel table element
            </summary>
            <param name="grid">source RadGridView</param>
            <param name="excelTable">Excel table element</param>
        </member>
        <member name="M:Telerik.WinControls.UI.Export.ExportToExcelML.CreateColumnElement(Telerik.WinControls.UI.GridViewColumn)">
            <summary>
            Create excel column element
            </summary>
            <param name="gridColumn">RadGridView's column</param>
            <returns>Excel ColumnElement</returns>
        </member>
        <member name="M:Telerik.WinControls.UI.Export.ExportToExcelML.FileWriteElement(System.IO.FileStream,Telerik.WinControls.UI.Export.ExcelML.ElementBase)">
            <summary>
            Write element to file.
            </summary>
            <param name="fileStream">File stream to use</param>
            <param name="excelElement">element to write</param>
        </member>
        <member name="M:Telerik.WinControls.UI.Export.ExportToExcelML.FileWriteElement(System.IO.FileStream,Telerik.WinControls.UI.Export.ExcelML.ElementBase,System.Int32)">
            <summary>
            Write element to file and specify how many simbols to cut from the end
            </summary>
            <param name="fileStream">File stream to use</param>
            <param name="excelElement">element to write</param>
            <param name="cutLastSymbols">count how many symbols to cut at the end</param>
        </member>
        <member name="M:Telerik.WinControls.UI.Export.ExportToExcelML.GetBytes(System.String)">
            <summary>
            Converts string to byte array
            </summary>
            <param name="s">string to convert</param>
            <returns>byte array</returns>
        </member>
        <member name="M:Telerik.WinControls.UI.Export.ExportToExcelML.RunExport(System.String)">
            <summary>
            Creates ExcelML file
            </summary>
            <param name="fileName">Export file name</param>
        </member>
        <member name="M:Telerik.WinControls.UI.Export.ExportToExcelML.AddCustomExcelRow(Telerik.WinControls.UI.Export.ExcelML.TableElement,System.Int32,System.Object[])">
            <summary>
            Add custom row in the provided excel table element
            </summary>
            <param name="excelTableElement">Excel table element where the new row will be added</param>
            <param name="rowHeight">Custom row height in pixels. If 0 than the height has default value</param>
            <param name="cellValues">Values for the every excel cell in the custom row</param>
            <returns>New row's style element for further formatting </returns>
        </member>
        <member name="T:Telerik.WinControls.UI.Export.CellWhiteSpace">
            <summary>
            Defines how white space is handeled in the exported cells.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Export.CellWhiteSpace.Normal">
            <summary>
            Sequences of whitespace will collapse into a single whitespace. Text will wrap when necessary.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Export.CellWhiteSpace.NoWrap">
            <summary>
            Sequences of whitespace will collapse into a single whitespace. Text will never wrap to the 
            next line.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Export.CellWhiteSpace.Pre">
            <summary>
            Whitespace is preserved. Text will only wrap on line breaks.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Export.CellWhiteSpace.PreLine">
            <summary>
            Sequences of whitespace will collapse into a single whitespace. Text will wrap when necessary, and on line breaks.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Export.CellWhiteSpace.PreWrap">
            <summary>
            Whitespace is preserved. Text will wrap when necessary, and on line breaks.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.ExportSettings.PDF.PageWidth">
            <summary>
            Set or get pdf page width in milimeters
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.ExportSettings.PDF.PageHeight">
            <summary>
            Set or get pdf page height in milimeters
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Export.ExportSettings.PDF.AddKeyword(System.String)">
            <summary>
            Add pdf keyword
            </summary>
            <param name="keyword">keyword to add</param>
        </member>
        <member name="T:Telerik.WinControls.UI.Export.HTML.HTMLCellFormattingEventArgs">
            <summary>
            Provides event arguments for the HTMLCellFormatting event
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.HTML.HTMLCellFormattingEventArgs.GridColumnIndex">
            <summary>
            Get source grid's column index
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.HTML.HTMLCellFormattingEventArgs.GridRowIndex">
            <summary>
            Get source grid's row index
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.HTML.HTMLCellFormattingEventArgs.GridRowInfoType">
            <summary>
            Get source grid's row type
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.HTML.HTMLCellFormattingEventArgs.GridCellInfo">
            <summary>
            Get a reference to the source grid's cell.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.HTML.HTMLCellFormattingEventArgs.HTMLCellElement">
            <summary>
            Get html cell element for further formatting
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.HTML.HTMLCellFormattingEventArgs.CancelConditionalFormatting">
            <summary>
            Get or Set escaping for conditional formatting for this cell.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.HTML.HTMLCellFormattingEventArgs.InnerCellValue">
            <summary>
            Get the inner cell value which reference the original grid's value
            </summary>
        </member>
        <member name="T:Telerik.WinControls.UI.Export.HTML.HTMLTableCaptionFormattingEventArgs">
            <summary>
            Provides event erguments for HTMLTableCaptionFormatting event
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.HTML.HTMLTableCaptionFormattingEventArgs.TableCaptionElement">
            <summary>
            Get html table CaptionElement for further formatting
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.HTML.HTMLTableCaptionFormattingEventArgs.CaptionText">
            <summary>
            Get or set caption text
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.HTML.CellElement.AlwaysApplyAlign">
            <summary>
            Add align attributes even if they are equal to default ones (Middle|Left)
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.HTML.CellElement.CellWhiteSpace">
            <summary>
            Gets or sets a value indicating how the white spaces in the cell will be handled.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.HTML.CellElement.EscapeHTMLChars">
            <summary>
            Gets or sets a value indicating if special HTML characters should be escaped.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.HTML.HtmlExportElement.Styles">
            <summary>
            This element cannot have styles collection
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.HTML.StyleElement.Styles">
            <summary>
            This element cannot have styles collection
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.ExportToHTML.AutoSizeColumns">
            <summary>
            Gets or sets a value indicating whether the columns width should be calculated 
            based on the content of the cells or should be fixed.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.ExportToHTML.ExportHierarchy">
            <summary>
            Set if child rows should be exported.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.ExportToHTML.FitWidthSize">
            <summary>
            Set the width in milimeters to fit the table in.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.ExportToHTML.SummariesExportOption">
            <summary>
            Set how to export summary rows.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.ExportToHTML.ChildViewExportMode">
            <summary>
            Gets or sets the child view export mode.
            </summary>
            <value>
            The child view export mode.
            </value>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.ExportToHTML.HiddenColumnOption">
            <summary>
            Set if to export hidden column.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.ExportToHTML.HiddenRowOption">
            <summary>
            Set if to export hidden rows.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.ExportToHTML.PagingExportOption">
            <summary>
            Sets a value indicating how the export behavies when paging is enabled.
            </summary>
            <value>
            The paging export option.
            </value>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.ExportToHTML.RadGridViewToExport">
            <summary>
            Set RadGridView that would be exported.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.ExportToHTML.FileExtension">
            <summary>
            Set the file extension. Default value is <b>htm</b>.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.ExportToHTML.TableCaption">
            <summary>
            Set html table caption.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.ExportToHTML.ExportVisualSettings">
            <summary>
            Set to export visual settings e.g. background colors, font, alignment,
            conditional formatting etc. Default value: <b>false</b>.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.ExportToHTML.TableBorderThickness">
            <summary>
            Set html table border thickness. Default value is <b>0</b>
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.ExportToHTML.Scale">
            <summary>
            Set the table size scaling. Default value is <b>1</b>. For example, scale of 1.2 means 20% size increase.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.ExportToHTML.Font">
            <summary>
            Set the html table general Font.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.ExportToHTML.Encoding">
            <summary>
            Gets or sets the encoding used for the exported file. Default encoding is UTF8.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.ExportToHTML.ExportImages">
            <summary>
            Gets or sets a value indicating whether to export images.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.ExportToHTML.ScaleImages">
            <summary>
            Gets or sets a value indicating whether to scale exported images.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Export.ExportToHTML.#ctor(Telerik.WinControls.UI.RadGridView)">
            <summary>
            Create instance of exporter class with specified RadGridView to export 
            </summary>
            <param name="gridToExport">RadGridView to export</param>
        </member>
        <member name="E:Telerik.WinControls.UI.Export.ExportToHTML.ChildViewExporting">
            <summary>
            Occurs when a child view will be exported.
            </summary>
        </member>
        <member name="E:Telerik.WinControls.UI.Export.ExportToHTML.HTMLCellFormatting">
            <summary>
            Fires when an html cell object was created and needs to be formatted.
            </summary>
        </member>
        <member name="E:Telerik.WinControls.UI.Export.ExportToHTML.HTMLTableCaptionFormatting">
            <summary>
            Occurs when html table was created and provides access to its caption element
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Export.ExportToHTML.OnHTMLTableCaptionFormatting(Telerik.WinControls.UI.Export.HTML.HTMLTableCaptionFormattingEventArgs)">
            <summary>
            Fires when html table was created and provides access to its caption element
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Export.ExportToHTML.RunExport(System.String)">
            <summary>
            Creates file in html format.
            </summary>
            <param name="fileName">Export file name</param>
        </member>
        <member name="M:Telerik.WinControls.UI.Export.ExportToHTML.GetAllColumns(Telerik.WinControls.UI.GridViewTemplate,System.Int32,System.Collections.Generic.List{Telerik.WinControls.UI.GridViewColumn}@)">
            <summary>
            Fills list with all required columns. Includes columns from child templates, which exceed the main template column count.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Export.ExportToHTML.CreateRowElement(Telerik.WinControls.UI.GridViewRowInfo)">
            <summary>
            Create html row element with a styles
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Export.ExportToHTML.SetHtmlRowElementHeight(Telerik.WinControls.UI.Export.HTML.RowElement,System.Int32)">
            <summary>
            Set html row element height
            </summary>
            <param name="htmlRowElement">RowElement to set</param>
            <param name="height">Desired height in pixels, if 0 than remains default height.</param>
        </member>
        <member name="M:Telerik.WinControls.UI.Export.ExportToHTML.CreateCellElement(Telerik.WinControls.UI.GridViewCellInfo)">
            <summary>
            Create html cell element
            </summary>
            <param name="gridCell">grid's cell info</param>
            <returns>html cell element</returns>
        </member>
        <member name="M:Telerik.WinControls.UI.Export.ExportToHTML.ApplyConditionalFormat(Telerik.WinControls.UI.GridViewRowInfo,System.Int32,Telerik.WinControls.UI.Export.HTML.RowElement@,Telerik.WinControls.UI.Export.HTML.CellElement@)">
            <summary>
            Apply conditional formatting to row element
            </summary>
            <returns>true if formatting has been applyed to the entire row</returns>        
        </member>
        <member name="M:Telerik.WinControls.UI.Export.ExportToHTML.ApplyBaseCellFormatting(System.Int32,Telerik.WinControls.UI.Export.HTML.CellElement,System.Drawing.Color)">
            <summary>
            Apply base cell formating on single html cell element
            </summary>
            <param name="gridColumnIndex"></param>
            <param name="htmlCellElement"></param>
            <param name="alternatingColor"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Export.ExportToHTML.AddSummaryRows(Telerik.WinControls.UI.GridViewSummaryRowInfo[],System.IO.FileStream,Telerik.WinControls.UI.Export.HTML.TableElement)">
            <summary>
            Add summary row
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Export.ExportToHTML.SearchForNeighbourElement(System.Collections.Generic.List{Telerik.WinControls.RadElement},System.Int32)">
            <summary>
            Search for a neighbour RadElement im a RadElement list
            </summary>
            <param name="radElementList">List of RadElements</param>
            <param name="index">Index for element which neighbour going to search</param>
            <returns>First founded neighbour element</returns>
        </member>
        <member name="M:Telerik.WinControls.UI.Export.ExportToHTML.FileWriteElement(System.IO.FileStream,Telerik.WinControls.UI.Export.HTML.ElementBase,System.Int32)">
            <summary>
            Write element to file and specify how many simbols to cut from the end
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Export.ExportToHTML.GetBytes(System.String)">
            <summary>
            Converts string to byte array
            </summary>
            <param name="s">string to convert</param>
            <returns>byte array</returns>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.ExportToPDF.ExportHierarchy">
            <summary>
            Set if child rows should be exported.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.ExportToPDF.FitToPageWidth">
            <summary>
            Set table to fit to pdf page width
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.ExportToPDF.PdfExportSettings">
            <summary>
            Get or set pdf export settings class
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.ExportToPDF.SummariesExportOption">
            <summary>
            Set how to export summary rows.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.ExportToPDF.HiddenColumnOption">
            <summary>
            Set how to export hidden column.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.ExportToPDF.HiddenRowOption">
            <summary>
            Set how to export hidden rows.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.ExportToPDF.PagingExportOption">
            <summary>
            Sets a value indicating how the export behavies when paging is enabled.
            </summary>
            <value>
            The paging export option.
            </value>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.ExportToPDF.ChildViewExportMode">
            <summary>
            Gets or sets the child view export mode.
            </summary>
            <value>
            The child view export mode.
            </value>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.ExportToPDF.RadGridViewToExport">
            <summary>
            Set RadGridView that would be exported.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.ExportToPDF.FileExtension">
            <summary>
            Set the file extension. Default value is <b>pdf</b>.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.ExportToPDF.PageTitle">
            <summary>
            Set pdf page title.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.ExportToPDF.ExportVisualSettings">
            <summary>
            Set to export visual settings e.g. background colors, font, alignment,
            conditional formatting etc. Default value: <b>false</b>.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.ExportToPDF.TableBorderThickness">
            <summary>
            Set pdf table border thickness. Default value is <b>0</b>
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.ExportToPDF.Scale">
            <summary>
            Set the table size scaling. Default value is <b>1</b>. For example, scale of 1.2f means 20% size increase.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.ExportToPDF.Font">
            <summary>
            Set the table Font.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Export.ExportToPDF.Encoding">
            <summary>
            Gets or sets the encoding used for the exported file. Default encoding is UTF8.
            </summary>
        </member>
        <member name="E:Telerik.WinControls.UI.Export.ExportToPDF.ChildViewExporting">
            <summary>
            Occurs when a child view will be exported.
            </summary>
        </member>
        <member name="E:Telerik.WinControls.UI.Export.ExportToPDF.HTMLCellFormatting">
            <summary>
            Fires when an html cell object was created and needs to be formatted. 
            Note that pdf export firstly renders as html and this allows to apply custom format to any cell.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Export.ExportToPDF.RunExport(System.String)">
            <summary>
            Creates PDF file
            </summary>
            <param name="fileName">Export file name</param>
        </member>
        <member name="M:Telerik.WinControls.UI.Export.Utils.ConvertContentAligmentH(System.Drawing.ContentAlignment)">
            <summary>
            Convert ContentAlignment to HorizontalAlignmentType
            </summary>
            <param name="alignment">System.Drawing.ContentAlignment</param>
            <returns>Telerik.WinControls.UI.Export.HorizontalAligmentType</returns>
        </member>
        <member name="M:Telerik.WinControls.UI.Export.Utils.ConvertContentAligmentV(System.Drawing.ContentAlignment)">
            <summary>
            Convert ContentAignment to VerticalAlignmentType
            </summary>
            <param name="alignment">System.Drawing.ContentAlignment</param>
            <returns>Telerik.WinControls.UI.Export.VerticalAlignmentType</returns>
        </member>
        <member name="M:Telerik.WinControls.UI.Export.Utils.GetBackColor(Telerik.WinControls.UI.LightVisualElement)">
            <summary>
            Search for real visible fill and mix its colors
            </summary>
            <param name="element"></param>
            <returns>Mixed collor, ready for export</returns>
        </member>
        <member name="M:Telerik.WinControls.UI.Export.Utils.ColorToHex(System.Drawing.Color)">
            <summary>
            Converts a color to a hexidecimal value for use within HTML
            </summary>
            <param name="color">The color to convert</param>
            <returns>A hex value, including '#'</returns>
        </member>
        <member name="M:Telerik.WinControls.UI.Export.Utils.CheckSourceOfVisualSettings(Telerik.WinControls.RadElement)">
            <summary>
            Check if visual style settings comes from theme.
            </summary>
            <param name="element">Element to check</param>
            <returns>True if visual settings come from theme</returns>
        </member>
        <member name="M:Telerik.WinControls.UI.Export.Utils.BaseCell.InitializeBaseCell(Telerik.WinControls.UI.RadGridView)">
            <summary>
            Search for base grid's data cell element
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Export.Utils.BaseCell.PrepareGridView(Telerik.WinControls.UI.RadGridView)">
            <summary>
            Prepare/Reset the RadGridView for exporting
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Export.Utils.BaseCell.InitializeNewGridCellElement(Telerik.WinControls.UI.GridDataCellElement)">
            <summary>
            Initialize new grid data cell element and change its base settings according to transperancy of its fill
            </summary>
            <param name="gridDataCellElement">Base grid cell element</param>
            <returns>Transformed cell element</returns>
        </member>
        <member name="T:Telerik.Apoc.ApocDriver">
            <summary>
                ApocDriver provides the client with a single interface to invoking Apoc XSL-FO.
            </summary>
            <remarks>
                The examples belows demonstrate several ways of invoking Apoc XSL-FO.  The 
                methodology is the same regardless of how Apoc is embedded in your 
                system (ASP.NET, WinForm, Web Service, etc).
            </remarks>
            <example>
            <code lang="csharp">
            // This example demonstrates rendering an XSL-FO file to a PDF file.
            ApocDriver driver = ApocDriver.Make();
            driver.Render(
                new FileStream("readme.fo", FileMode.Open), 
                new FileStream("readme.pdf", FileMode.Create));
            </code>
            <code lang="vb">
            // This example demonstrates rendering an XSL-FO file to a PDF file.
            Dim driver As ApocDriver = ApocDriver.Make
            driver.Render( _
                New FileStream("readme.fo", FileMode.Open), _
                New FileStream("readme.pdf", FileMode.Create))
            </code>
            <code lang="csharp">
            // This example demonstrates rendering the result of an XSLT transformation 
            // into a PDF file.
            ApocDriver driver = ApocDriver.Make();
            driver.Render(
                XslTransformer.Transform("readme.xml", "readme.xsl"),
                new FileStream("readme.pdf", FileMode.Create));
            </code>
            <code lang="vb">
            // This example demonstrates rendering the result of an XSLT transformation 
            // into a PDF file.
            Dim driver As ApocDriver = ApocDriver.Make
            driver.Render( _
                XslTransformer.Transform("readme.xml", "readme.xsl"), _
                New FileStream("readme.pdf", FileMode.Create))
            </code>
            <code lang="csharp">
            // This example demonstrates using an XmlDocument as the source of the 
            // XSL-FO tree.  The XmlDocument could easily be dynamically generated.
            XmlDocument doc = new XmlDocument()
            doc.Load("reader.fo");
                
            ApocDriver driver = ApocDriver.Make();
            driver.Render(doc, new FileStream("readme.pdf", FileMode.Create));
            </code>
            <code lang="vb">
            // This example demonstrates using an XmlDocument as the source of the 
            // XSL-FO tree.  The XmlDocument could easily be dynamically generated.
            Dim doc As XmlDocument = New XmlDocument()
            doc.Load("reader.fo")
                
            Dim driver As ApocDriver = ApocDriver.Make
            driver.Render(doc, New FileStream("readme.pdf", FileMode.Create))
            </code>
            </example>
        </member>
        <member name="F:Telerik.Apoc.ApocDriver.renderEngine">
            <summary>
                Controls the output format of the renderer.
            </summary>
            <remarks>
                Defaults to PDF.
            </remarks>
        </member>
        <member name="F:Telerik.Apoc.ApocDriver.closeOnExit">
            <summary>
                Determines if the output stream passed to the render
                should be closed upon completion or if a fatal exception occurs.
            </summary>
        </member>
        <member name="F:Telerik.Apoc.ApocDriver.renderOptions">
            <summary>
                Options to supply to the renderer.
            </summary>
        </member>
        <member name="F:Telerik.Apoc.ApocDriver.credentials">
            <summary>
                Maps a set of credentials to an internet resource
            </summary>
        </member>
        <member name="F:Telerik.Apoc.ApocDriver.rm">
            <summary>
                The ResourceManager embedded in the core dll.
            </summary>
        </member>
        <member name="F:Telerik.Apoc.ApocDriver.activeDriver">
            <summary>
                The active driver.
            </summary>
        </member>
        <member name="F:Telerik.Apoc.ApocDriver.productKey">
            <summary>
                Permits the product key to be specified using code, rather than
                the flakey licenses.licx method.
            </summary>
        </member>
        <member name="T:Telerik.Apoc.ApocDriver.ApocEventHandler">
            <summary>
                The delegate subscribers must implement to receive Apoc events.
            </summary>
            <remarks>
                The <paramref name="driver"/> parameter will be a reference to 
                the  active ApocDriver.  The <paramref name="e"/> parameter will 
                contain a human-readable error message.
            </remarks>
            <param name="driver">A reference to the active ApocDriver</param>
            <param name="e">Encapsulates a human readable error message</param>
        </member>
        <member name="T:Telerik.Apoc.ApocDriver.ApocImageHandler">
            <summary>
                The delegat subscribers must implement to handle the loading 
                of image data in response to external-graphic formatting objects.
            </summary>
        </member>
        <member name="E:Telerik.Apoc.ApocDriver.OnError">
            <summary>
                A multicast delegate.  The error event Apoc publishes.
            </summary>
            <remarks>
                The method signature for this event handler should match 
                the following:
                <pre class="code"><span class="lang">
                void ApocError(object driver, ApocEventArgs e);
                </span></pre>
                The first parameter <i>driver</i> will be a reference to the 
                active ApocDriver instance.
            </remarks>
            <example>Subscribing to the 'error' event
                <pre class="code"><span class="lang">[C#]</span><br/>
                {
                ApocDriver driver = ApocDriver.Make();
                driver.OnError += new ApocDriver.ApocEventHandler(ApocError);
                ...
                }
                </pre>
            </example>
        </member>
        <member name="E:Telerik.Apoc.ApocDriver.OnWarning">
            <summary>
                A multicast delegate.  The warning event Apoc publishes.
            </summary>
            <remarks>
                The method signature for this event handler should match 
                the following:
                <pre class="code"><span class="lang">
                void ApocWarning(object driver, ApocEventArgs e);
                </span></pre>
                The first parameter <i>driver</i> will be a reference to the 
                active ApocDriver instance.
            </remarks>
        </member>
        <member name="E:Telerik.Apoc.ApocDriver.OnInfo">
            <summary>
                A multicast delegate.  The info event Apoc publishes.
            </summary>
            <remarks>
                The method signature for this event handler should match 
                the following:
                <pre class="code"><span class="lang">
                void ApocInfo(object driver, ApocEventArgs e);
                </span></pre>
                The first parameter <i>driver</i> will be a reference to the 
                active ApocDriver instance.
            </remarks>
        </member>
        <member name="M:Telerik.Apoc.ApocDriver.Make">
            <summary>
                Constructs a new ApocDriver and registers the newly created 
                driver as the active driver.
            </summary>
            <returns>An instance of ApocDriver</returns>
        </member>
        <member name="M:Telerik.Apoc.ApocDriver.#ctor">
            <summary>
                Sets the the 'baseDir' property in the Configuration class using 
                the value returned by Directory.GetCurrentDirectory().
            </summary>
        </member>
        <member name="P:Telerik.Apoc.ApocDriver.CloseOnExit">
            <summary>
                Determines if the output stream should be automatically closed 
                upon completion of the render process.
            </summary>
        </member>
        <member name="P:Telerik.Apoc.ApocDriver.ActiveDriver">
            <summary>
                Gets or sets the active <see cref="T:Telerik.Apoc.ApocDriver"/>.
            </summary>
            <value>
                An instance of <see cref="T:Telerik.Apoc.ApocDriver"/> created via the factory method 
                <see cref="M:Telerik.Apoc.ApocDriver.Make"/>.
            </value>
        </member>
        <member name="P:Telerik.Apoc.ApocDriver.Renderer">
            <summary>
                Determines which rendering engine to use.
            </summary>
            <value>
                A value from the <see cref="T:Telerik.Apoc.Render.RendererEngine"/> enumeration.
            </value>
            <remarks>
                The default value is 
                <see cref="F:Telerik.Apoc.Render.RendererEngine.PDF"/>.
            </remarks>
        </member>
        <member name="P:Telerik.Apoc.ApocDriver.BaseDirectory">
            <summary>
                Gets or sets the base directory used to locate external 
                resourcs such as images.
            </summary>
            <value>
                Defaults to the current working directory.
            </value>
        </member>
        <member name="F:Telerik.Apoc.ApocDriver.imageHandler">
            <summary>
                An optional image handler that can be registered to load image
                data for external graphic formatting objects.
            </summary>
        </member>
        <member name="P:Telerik.Apoc.ApocDriver.ImageHandler">
            <summary>
                Gets or sets the handler that is responsible for loading the image
                data for external graphics.
            </summary>
            <remarks>
                If null is returned from the image handler, then Apoc will perform 
                normal processing.
            </remarks>
        </member>
        <member name="P:Telerik.Apoc.ApocDriver.Timeout">
            <summary>
                Gets or sets the time in milliseconds until an HTTP image request 
                times out.
            </summary>
            <remarks>
                The default value is 100000 milliseconds.
            </remarks>
            <value>
                The timeout value in milliseconds
            </value>
        </member>
        <member name="P:Telerik.Apoc.ApocDriver.Credentials">
            <summary>
                Gets a reference to a <see cref="T:System.Net.CredentialCache"/> object 
                that manages credentials for multiple Internet resources.
                <seealso cref="T:System.Net.CredentialCache"/>
            </summary>
            <remarks>
                The purpose of this property is to associate a set of credentials against 
                an Internet resource.  These credentials are then used by Apoc when 
                fetching images from one of the listed resources.
            </remarks>
            <example>
                ApocDriver driver = ApocDriver.Make();
                
                NetworkCredential nc1 = new NetworkCredential("foo", "password");
                driver.Credentials.Add(new Uri("http://www.chive.com"), "Basic", nc1);
                
                NetworkCredential nc2 = new NetworkCredential("john", "password", "UK");
                driver.Credentials.Add(new Uri("http://www.xyz.com"), "Digest", nc2);
            </example>
        </member>
        <member name="P:Telerik.Apoc.ApocDriver.ProductKey">
            <summary>
                Write only property that can be used to bypass licenses.licx
                and set a product key directly.
            </summary>
        </member>
        <member name="P:Telerik.Apoc.ApocDriver.InternalProductKey">
            <summary>
                Returns the product key.
            </summary>
        </member>
        <member name="P:Telerik.Apoc.ApocDriver.Options">
            <summary>
                Options that are passed to the rendering engine.
            </summary>
            <value>
                An object that implements the <see cref="T:Telerik.Apoc.Render.IRendererOptions"/> marker interface.
                The default value is null, in which case all default options will be used.
            </value>
            <remarks>
                An instance of <see cref="T:Telerik.Apoc.Render.Pdf.PdfRendererOptions"/>
                is typically passed to this property.
            </remarks>
        </member>
        <member name="M:Telerik.Apoc.ApocDriver.Render(System.Xml.XmlDocument,System.IO.Stream)">
            <summary>
                Executes the conversion reading the source tree from the supplied 
                XmlDocument, converting it to a format dictated by the renderer 
                and writing it to the supplied output stream.
            </summary>
            <param name="doc">
                An in-memory representation of an XML document (DOM).
            </param>
            <param name="outputStream">
                Any subclass of the Stream class.
            </param>
            <remarks>
                Any exceptions that occur during the render process are arranged 
                into three categories: information, warning and error.  You may 
                intercept any or all of theses exceptional states by registering 
                an event listener.  See <see cref="E:Telerik.Apoc.ApocDriver.OnError"/> for an 
                example of registering an event listener.  If there are no 
                registered listeners, the exceptions are dumped to standard out - 
                except for the error event which is wrapped in a 
                <see cref="T:System.SystemException"/>.
            </remarks>
        </member>
        <member name="M:Telerik.Apoc.ApocDriver.Render(System.IO.TextReader,System.IO.Stream)">
            <summary>
                Executes the conversion reading the source tree from the input 
                reader, converting it to a format dictated by the renderer and 
                writing it to the supplied output stream.
            </summary>
            <param name="inputReader">A character orientated stream</param>
            <param name="outputStream">Any subclass of the Stream class</param>
        </member>
        <member name="M:Telerik.Apoc.ApocDriver.Render(System.String,System.String)">
            <summary>
                Executes the conversion reading the source tree from the file 
                <i>inputFile</i>, converting it to a format dictated by the 
                renderer and writing it to the file identified by <i>outputFile</i>.
            </summary>
            <remarks>
                If the file <i>outputFile</i> does not exist, it will created 
                otherwise it will be overwritten.  Creating a file may 
                generate a variety of exceptions.  See <see cref="T:System.IO.FileStream"/>
                for a complete list.<br/>
            </remarks>
            <param name="inputFile">Path to an XSL-FO file</param>
            <param name="outputFile">Path to a file</param>
        </member>
        <member name="M:Telerik.Apoc.ApocDriver.Render(System.String,System.IO.Stream)">
            <summary>
                Executes the conversion reading the source tree from the file 
                <i>inputFile</i>, converting it to a format dictated by the 
                renderer and writing it to the supplied output stream.
            </summary>
            <param name="inputFile">Path to an XSL-FO file</param>
            <param name="outputStream">
                Any subclass of the Stream class, e.g. FileStream
            </param>
        </member>
        <member name="M:Telerik.Apoc.ApocDriver.Render(System.IO.Stream,System.IO.Stream)">
            <summary>
                Executes the conversion reading the source tree from the input 
                stream, converting it to a format dictated by the render and 
                writing it to the supplied output stream.
            </summary>
            <param name="inputStream">Any subclass of the Stream class, e.g. FileStream</param>
            <param name="outputStream">Any subclass of the Stream class, e.g. FileStream</param>
        </member>
        <member name="M:Telerik.Apoc.ApocDriver.Render(System.Xml.XmlReader,System.IO.Stream)">
            <summary>
                Executes the conversion reading the source tree from the input 
                reader, converting it to a format dictated by the render and 
                writing it to the supplied output stream.
            </summary>
            <remarks>
                The evaluation copy of this class will output an evaluation
                banner to standard out
            </remarks>
            <param name="inputReader">
                Reader that provides fast, non-cached, forward-only access 
                to XML data
            </param>
            <param name="outputStream">
                Any subclass of the Stream class, e.g. FileStream
            </param>
        </member>
        <member name="P:Telerik.Apoc.ApocDriver.IsEvaluation">
            <summary>
                True if the current license is an evaluation license.
            </summary>
        </member>
        <member name="M:Telerik.Apoc.ApocDriver.GetString(System.String)">
            <summary>
                Retrieves the string resource with the specific key using the 
                default culture
            </summary>
            <param name="key">A resource key</param>
            <returns>
                The resource string identified by <code>key</code> from the 
                current culture's setting
            </returns>
            <exception cref="T:System.ArgumentNullException">
                The <i>key</i> parameter is a null reference</exception>
            <exception cref="T:System.InvalidOperationException">
                The value of the specified resource is not a string</exception>
            <exception cref="T:System.Resources.MissingManifestResourceException">
                No usable set of resources has been found, and there are no 
                neutral culture resources
            </exception>
        </member>
        <member name="M:Telerik.Apoc.ApocDriver.FireApocError(System.String)">
            <summary>
                Sends an 'error' event to all registered listeners.
            </summary>
            <remarks>
                If there are no listeners, a <see cref="T:System.SystemException"/> is 
                thrown immediately halting execution
            </remarks>
            <param name="message">Any error message, which may be null</param>
            <exception cref="T:System.SystemException">
                If no listener is registered for this event, a SystemException
                will be thrown
            </exception>
        </member>
        <member name="M:Telerik.Apoc.ApocDriver.FireApocWarning(System.String)">
            <summary>
                Sends a 'warning' event to all registered listeners
            </summary>
            <remarks>
                If there are no listeners, <i>message</i> is written out 
                to the console instead
            </remarks>
            <param name="message">Any warning message, which may be null</param>
        </member>
        <member name="M:Telerik.Apoc.ApocDriver.FireApocInfo(System.String)">
            <summary>
                Sends an 'info' event to all registered lisetners
            </summary>
            <remarks>
                If there are no listeners, <i>message</i> is written out 
                to the console instead
            </remarks>
            <param name="message">An info message, which may be null</param>
        </member>
        <member name="M:Telerik.Apoc.ApocDriver.CreateXmlTextReader(System.String)">
            <summary>
                Utility method that creates an <see cref="T:System.Xml.XmlTextReader"/>
                for the supplied file
            </summary>
            <remarks>
                The returned <see cref="T:System.Xml.XmlReader"/> interprets all whitespace
            </remarks>
        </member>
        <member name="M:Telerik.Apoc.ApocDriver.CreateXmlTextReader(System.IO.Stream)">
            <summary>
                Utility method that creates an <see cref="T:System.Xml.XmlTextReader"/>
                for the supplied file
            </summary>
            <remarks>
                The returned <see cref="T:System.Xml.XmlReader"/> interprets all whitespace
            </remarks>
        </member>
        <member name="M:Telerik.Apoc.ApocDriver.CreateXmlTextReader(System.IO.TextReader)">
            <summary>
                Utility method that creates an <see cref="T:System.Xml.XmlTextReader"/>
                for the supplied file
            </summary>
            <remarks>
                The returned <see cref="T:System.Xml.XmlReader"/> interprets all whitespace
            </remarks>
        </member>
        <member name="T:Telerik.Apoc.ApocEventArgs">
            <summary>
                A class containing event data for the Error, Warning and Info 
                events defined in <see cref="T:Telerik.Apoc.ApocDriver"/>.
            </summary>
        </member>
        <member name="M:Telerik.Apoc.ApocEventArgs.#ctor(System.String)">
            <summary>
                Initialises a new instance of the <i>ApocEventArgs</i> class.
            </summary>
            <param name="message">The text of the event message.</param>
        </member>
        <member name="M:Telerik.Apoc.ApocEventArgs.GetMessage">
            <summary>
                Retrieves the event message.
            </summary>
            <returns>A string which may be null.</returns>
        </member>
        <member name="M:Telerik.Apoc.ApocEventArgs.ToString">
            <summary>
                Converts this <i>ApocEventArgs</i> to a string.
            </summary>
            <returns>
                A string representation of this class which is identical 
                to <see cref="M:Telerik.Apoc.ApocEventArgs.GetMessage"/>.
            </returns>
        </member>
        <member name="T:Telerik.Apoc.ApocException">
            <summary>
                This exception is thrown by Apoc when an error occurs.
            </summary>
        </member>
        <member name="M:Telerik.Apoc.ApocException.#ctor(System.Exception)">
            <summary>
                Initialises a new instance of the ApocException class.
            </summary>
            <remarks>
                The <see cref="P:System.Exception.Message"/> property will be initialised 
                to <i>innerException.Message</i>
            </remarks>
            <param name="innerException">
                The exception that is the cause of the current exception
            </param>
        </member>
        <member name="M:Telerik.Apoc.ApocException.#ctor(System.String)">
            <summary>
                Initialises a new instance of the ApocException class.
            </summary>
            <param name="message">
                The error message that explains the reason for this exception
            </param>
        </member>
        <member name="M:Telerik.Apoc.ApocException.#ctor(System.String,System.Exception)">
            <summary>
                Initialises a new instance of the ApocException class.
            </summary>
            <param name="message">
                The error message that explains the reason for this exception
            </param>
            <param name="innerException">
                The exception that is the cause of the current exception
            </param>
        </member>
        <member name="T:Telerik.Apoc.DataTypes.AutoLength">
            <summary>
            A length quantity in XSL which is specified as "auto".
            </summary>
        </member>
        <member name="T:Telerik.Apoc.DataTypes.ColorType">
            a colour quantity in XSL
        </member>
        <member name="F:Telerik.Apoc.DataTypes.ColorType._red">
            the red component
        </member>
        <member name="F:Telerik.Apoc.DataTypes.ColorType._green">
            the green component
        </member>
        <member name="F:Telerik.Apoc.DataTypes.ColorType._blue">
            the blue component
        </member>
        <member name="F:Telerik.Apoc.DataTypes.ColorType._alpha">
            the alpha component
        </member>
        <member name="M:Telerik.Apoc.DataTypes.ColorType.#ctor(System.String)">
            set the colour given a particular String specifying either a
            colour name or #RGB or #RRGGBB
        </member>
        <member name="T:Telerik.Apoc.DataTypes.CondLength">
            a space quantity in XSL (space-before, space-after)
        </member>
        <member name="T:Telerik.Apoc.DataTypes.FixedLength">
            a length quantity in XSL
        </member>
        <member name="M:Telerik.Apoc.DataTypes.FixedLength.#ctor(System.Double,System.Int32)">
            Set the length given a number of relative units and the current
            font size in base units.
        </member>
        <member name="M:Telerik.Apoc.DataTypes.FixedLength.#ctor(System.Double,System.String)">
            Set the length given a number of units and a unit name.
        </member>
        <member name="M:Telerik.Apoc.DataTypes.FixedLength.#ctor(System.Int32)">
            set the length as a number of base units
        </member>
        <member name="M:Telerik.Apoc.DataTypes.FixedLength.Convert(System.Double,System.String)">
            Convert the given length to a dimensionless integer representing
            a whole number of base units (milli-points).
        </member>
        <member name="M:Telerik.Apoc.DataTypes.IDNode.#ctor(System.String)">
             Constructor for IDNode
            
             @param idValue The value of the id for this node
        </member>
        <member name="M:Telerik.Apoc.DataTypes.IDNode.SetPageNumber(System.Int32)">
             Sets the page number for this node
            
             @param number page number of node
        </member>
        <member name="M:Telerik.Apoc.DataTypes.IDNode.GetPageNumber">
             Returns the page number of this node
            
             @return page number of this node
        </member>
        <member name="M:Telerik.Apoc.DataTypes.IDNode.CreateInternalLinkGoTo(Telerik.Pdf.PdfObjectId)">
             creates a new GoTo object for an internal link
            
             @param objectNumber
             the number to be assigned to the new object
        </member>
        <member name="M:Telerik.Apoc.DataTypes.IDNode.SetInternalLinkGoToPageReference(Telerik.Pdf.PdfObjectReference)">
             sets the page reference for the internal link's GoTo.  The GoTo will jump to this page reference.
            
             @param pageReference
             the page reference to which the internal link GoTo should jump
             ex. 23 0 R
        </member>
        <member name="M:Telerik.Apoc.DataTypes.IDNode.GetInternalLinkGoToReference">
             Returns the reference to the Internal Link's GoTo object
            
             @return GoTo object reference
        </member>
        <member name="M:Telerik.Apoc.DataTypes.IDNode.GetIDValue">
             Returns the id value of this node
            
             @return this node's id value
        </member>
        <member name="M:Telerik.Apoc.DataTypes.IDNode.GetInternalLinkGoTo">
             Returns the PDFGoTo object associated with the internal link
            
             @return PDFGoTo object
        </member>
        <member name="M:Telerik.Apoc.DataTypes.IDNode.IsThereInternalLinkGoTo">
             Determines whether there is an internal link GoTo for this node
            
             @return true if internal link GoTo for this node is set, false otherwise
        </member>
        <member name="M:Telerik.Apoc.DataTypes.IDNode.SetPosition(System.Int32,System.Int32)">
             Sets the position of this node
            
             @param x      the x position
             @param y      the y position
        </member>
        <member name="M:Telerik.Apoc.DataTypes.IDReferences.#ctor">
            Constructor for IDReferences
        </member>
        <member name="M:Telerik.Apoc.DataTypes.IDReferences.InitializeID(System.String,Telerik.Apoc.Layout.Area)">
             Creates and configures the specified id.
            
             @param id     The id to initialize
             @param area   The area where this id was encountered
             @exception ApocException
        </member>
        <member name="M:Telerik.Apoc.DataTypes.IDReferences.CreateID(System.String)">
             Creates id entry
            
             @param id     The id to create
             @param area   The area where this id was encountered
             @exception ApocException
        </member>
        <member name="M:Telerik.Apoc.DataTypes.IDReferences.CreateUnvalidatedID(System.String)">
             Creates id entry that hasn't been validated
            
             @param id     The id to create
             @exception ApocException
        </member>
        <member name="M:Telerik.Apoc.DataTypes.IDReferences.AddToUnvalidatedIdList(System.String)">
             Adds created id list of unvalidated ids that have already
             been created. This should be used if it is unsure whether
             the id is valid but it must be anyhow.
            
             @param id     The id to create
        </member>
        <member name="M:Telerik.Apoc.DataTypes.IDReferences.RemoveFromUnvalidatedIDList(System.String)">
             Removes id from list of unvalidated ids.
             This should be used if the id has been determined
             to be valid.
            
             @param id     The id to remove
        </member>
        <member name="M:Telerik.Apoc.DataTypes.IDReferences.DoesUnvalidatedIDExist(System.String)">
             Determines whether specified id already exists in
             idUnvalidated
            
             @param id     The id to search for
             @return true if ID was found, false otherwise
        </member>
        <member name="M:Telerik.Apoc.DataTypes.IDReferences.ConfigureID(System.String,Telerik.Apoc.Layout.Area)">
             Configures this id
            
             @param id     The id to configure
             @param area   The area where the id was encountered
        </member>
        <member name="M:Telerik.Apoc.DataTypes.IDReferences.AddToIdValidationList(System.String)">
             Adds id to validation list to be validated .  This should be used if it is unsure whether the id is valid
            
             @param id     id to be added
        </member>
        <member name="M:Telerik.Apoc.DataTypes.IDReferences.RemoveFromIdValidationList(System.String)">
             Removes id from validation list. This should be used if the id has been determined to be valid
            
             @param id     the id to remove
        </member>
        <member name="M:Telerik.Apoc.DataTypes.IDReferences.RemoveID(System.String)">
             Removes id from IDReferences
            
             @param id     The id to remove
             @exception ApocException
        </member>
        <member name="M:Telerik.Apoc.DataTypes.IDReferences.IsEveryIdValid">
             Determines whether all id's are valid
            
             @return true if all id's are valid, false otherwise
        </member>
        <member name="M:Telerik.Apoc.DataTypes.IDReferences.GetInvalidIds">
             Returns all invalid id's still remaining in the validation list
            
             @return invalid ids from validation list
        </member>
        <member name="M:Telerik.Apoc.DataTypes.IDReferences.doesIDExist(System.String)">
             Determines whether specified id already exists in IDReferences
            
             @param id     the id to search for
             @return true if ID was found, false otherwise
        </member>
        <member name="M:Telerik.Apoc.DataTypes.IDReferences.doesGoToReferenceExist(System.String)">
             Determines whether the GoTo reference for the specified id is defined
            
             @param id     the id to search for
             @return true if GoTo reference is defined, false otherwise
        </member>
        <member name="M:Telerik.Apoc.DataTypes.IDReferences.getInternalLinkGoTo(System.String)">
             Returns the reference to the GoTo object used for the internal link
            
             @param id     the id whose reference to use
             @return reference to GoTo object
        </member>
        <member name="M:Telerik.Apoc.DataTypes.IDReferences.createInternalLinkGoTo(System.String,Telerik.Pdf.PdfObjectId)">
            <summary>
                Creates an PdfGoto object that will 'goto' the passed Id.
            </summary>
            <param name="id">The ID of the link's target.</param>
            <param name="objectId">The PDF object id to use for the GoTo object.</param>
            <remarks>
                This method is a bit 'wrong'.  Passing in an objectId seems a bit
                dirty and I don't see why an IDNode should be responsible for
                keeping track of the GoTo object that points to it.  These decisions
                only seem to pollute this class with PDF specific code.
            </remarks>
        </member>
        <member name="M:Telerik.Apoc.DataTypes.IDReferences.createNewId(System.String)">
             Adds an id to IDReferences
            
             @param id     the id to add
        </member>
        <member name="M:Telerik.Apoc.DataTypes.IDReferences.getPDFGoTo(System.String)">
             Returns the PDFGoTo object for the specified id
            
             @param id     the id for which the PDFGoTo to be retrieved is associated
             @return the PdfGoTo object associated with the specified id
        </member>
        <member name="M:Telerik.Apoc.DataTypes.IDReferences.setInternalGoToPageReference(System.String,Telerik.Pdf.PdfObjectReference)">
             sets the page reference for the internal link's GoTo.  The GoTo will jump to this page reference.
            
             @param pageReference
             the page reference to which the internal link GoTo should jump
             ex. 23 0 R
        </member>
        <member name="M:Telerik.Apoc.DataTypes.IDReferences.setPageNumber(System.String,System.Int32)">
             Sets the page number for the specified id
            
             @param id     The id whose page number is being set
             @param pageNumber The page number of the specified id
        </member>
        <member name="M:Telerik.Apoc.DataTypes.IDReferences.getPageNumber(System.String)">
             Returns the page number where the specified id is found
            
             @param id     The id whose page number to return
             @return the page number of the id, or null if the id does not exist
        </member>
        <member name="M:Telerik.Apoc.DataTypes.IDReferences.setPosition(System.String,System.Int32,System.Int32)">
             Sets the x and y position of specified id
            
             @param id     the id whose position is to be set
             @param x      x position of id
             @param y      y position of id
        </member>
        <member name="T:Telerik.Apoc.DataTypes.Keep">
            XSL FO Keep Property datatype (keep-together, etc)
        </member>
        <member name="M:Telerik.Apoc.DataTypes.Keep.ToString">
            What to do here? There isn't really a meaningful single value.
        </member>
        <member name="T:Telerik.Apoc.DataTypes.KeepValue">
            Keep Value
            Stores the different types of keeps in a single convenient format.
        </member>
        <member name="F:Telerik.Apoc.DataTypes.LengthBase.parentFO">
            FO parent of the FO for which this property is to be calculated.
        </member>
        <member name="F:Telerik.Apoc.DataTypes.LengthBase.propertyList">
            PropertyList for the FO where this property is calculated.
        </member>
        <member name="F:Telerik.Apoc.DataTypes.LengthBase.iBaseType">
            One of the defined types of LengthBase
        </member>
        <member name="M:Telerik.Apoc.DataTypes.LengthBase.GetParentFO">
            Accessor for parentFO object from subclasses which define
            custom kinds of LengthBase calculations.
        </member>
        <member name="M:Telerik.Apoc.DataTypes.LengthBase.getPropertyList">
            Accessor for propertyList object from subclasses which define
            custom kinds of LengthBase calculations.
        </member>
        <member name="T:Telerik.Apoc.DataTypes.LengthPair">
            This datatype hold a pair of lengths, specifiying the dimensions in
            both inline and block-progression-directions.
            It is currently only used to specify border-separation in tables.
        </member>
        <member name="T:Telerik.Apoc.DataTypes.LengthRange">
            a "progression-dimension" quantity
            ex. block-progression-dimension, inline-progression-dimension
            corresponds to the triplet min-height, height, max-height (or width)
        </member>
        <member name="M:Telerik.Apoc.DataTypes.LengthRange.SetMinimum(Telerik.Apoc.Fo.Property,System.Boolean)">
            Set minimum value to min.
            @param min A Length value specifying the minimum value for this
            LengthRange.
            @param bIsDefault If true, this is set as a "default" value
            and not a user-specified explicit value.
        </member>
        <member name="M:Telerik.Apoc.DataTypes.LengthRange.SetMaximum(Telerik.Apoc.Fo.Property,System.Boolean)">
            Set maximum value to max if it is >= optimum or optimum isn't set.
            @param max A Length value specifying the maximum value for this
            @param bIsDefault If true, this is set as a "default" value
            and not a user-specified explicit value.
        </member>
        <member name="M:Telerik.Apoc.DataTypes.LengthRange.SetOptimum(Telerik.Apoc.Fo.Property,System.Boolean)">
            Set the optimum value.
            @param opt A Length value specifying the optimum value for this
            @param bIsDefault If true, this is set as a "default" value
            and not a user-specified explicit value.
        </member>
        <member name="M:Telerik.Apoc.DataTypes.LinearCombinationLength.ComputeValue">
            Return the computed value in millipoints.
        </member>
        <member name="T:Telerik.Apoc.DataTypes.MixedLength">
            A length quantity in XSL which is specified with a mixture
            of absolute and relative and/or percent components.
            The actual value may not be computable before layout is done.
        </member>
        <member name="T:Telerik.Apoc.DataTypes.PercentLength">
            a percent specified length quantity in XSL
        </member>
        <member name="M:Telerik.Apoc.DataTypes.PercentLength.#ctor(System.Double)">
            construct an object based on a factor (the percent, as a
            a factor) and an object which has a method to return the
            Length which provides the "base" for this calculation.
        </member>
        <member name="M:Telerik.Apoc.DataTypes.PercentLength.ComputeValue">
            Return the computed value in millipoints. This assumes that the
            base length has been resolved to an absolute length value.
        </member>
        <member name="T:Telerik.Apoc.DataTypes.Space">
            <summary>
                A space quantity in XSL (space-before, space-after)
            </summary>
        </member>
        <member name="T:Telerik.Apoc.DataTypes.TableColLength">
            A table-column width specification, possibly including some
            number of proportional "column-units". The absolute size of a
            column-unit depends on the fixed and proportional sizes of all
            columns in the table, and on the overall size of the table.
            It can't be calculated until all columns have been specified and until
            the actual width of the table is known. Since this can be specified
            as a percent of its parent containing width, the calculation is done
            during layout.
            NOTE: this is only supposed to be allowed if table-layout=fixed.
        </member>
        <member name="F:Telerik.Apoc.DataTypes.TableColLength.tcolUnits">
            Number of table-column proportional units
        </member>
        <member name="M:Telerik.Apoc.DataTypes.TableColLength.#ctor(System.Double)">
            Construct an object with tcolUnits of proportional measure.
        </member>
        <member name="M:Telerik.Apoc.DataTypes.TableColLength.GetTableUnits">
            Override the method in Length to return the number of specified
            proportional table-column units.
        </member>
        <member name="M:Telerik.Apoc.DataTypes.TableColLength.ResolveTableUnit(System.Double)">
            Calculate the number of millipoints and set it.
        </member>
        <member name="T:Telerik.Apoc.Extensions.ExtensionObj">
            base class for extension objects
        </member>
        <member name="M:Telerik.Apoc.Extensions.ExtensionObj.#ctor(Telerik.Apoc.Fo.FObj,Telerik.Apoc.Fo.PropertyList)">
            
             @param parent the parent formatting object
             @param propertyList the explicit properties of this object
        </member>
        <member name="M:Telerik.Apoc.Extensions.ExtensionObj.Layout(Telerik.Apoc.Layout.Area)">
             Called for extensions within a page sequence or flow. These extensions
             are allowed to generate visible areas within the layout.
            
            
             @param area
        </member>
        <member name="M:Telerik.Apoc.Extensions.ExtensionObj.Format(Telerik.Apoc.Layout.AreaTree)">
             Called for root extensions. Root extensions aren't allowed to generate
             any visible areas. They are used for extra items that don't show up in
             the page layout itself. For example: pdf outlines
            
             @param areaTree
        </member>
        <member name="F:Telerik.Apoc.Extensions.Outline._parentOutline">
            The parent outline object if it exists
        </member>
        <member name="F:Telerik.Apoc.Extensions.Outline._rendererObject">
            an opaque renderer context object, e.g. PDFOutline for PDFRenderer
        </member>
        <member name="T:Telerik.Apoc.Fo.ColorProfile">
            The fo:root formatting object. Contains page masters, root extensions,
            page-sequences.
        </member>
        <member name="M:Telerik.Apoc.Fo.EnumProperty.Maker.CheckEnumValues(System.String)">
            Called by subclass if no match found.
        </member>
        <member name="T:Telerik.Apoc.Fo.Expr.ApocPropValFunction">
            Return the specified or initial value of the property on this object.
        </member>
        <member name="M:Telerik.Apoc.Fo.Expr.FunctionBase.GetPercentBase">
            By default, functions have no percent-based arguments.
        </member>
        <member name="M:Telerik.Apoc.Fo.Expr.NCnameProperty.GetString">
            Return the name as a String (should be specified with quotes!)
        </member>
        <member name="M:Telerik.Apoc.Fo.Expr.Numeric.#ctor(System.Decimal)">
            Construct a Numeric object from a Number.
            @param num The number.
        </member>
        <member name="M:Telerik.Apoc.Fo.Expr.Numeric.#ctor(Telerik.Apoc.DataTypes.FixedLength)">
            Construct a Numeric object from a Length.
            @param l The Length.
        </member>
        <member name="M:Telerik.Apoc.Fo.Expr.Numeric.#ctor(Telerik.Apoc.DataTypes.PercentLength)">
            Construct a Numeric object from a PercentLength.
            @param pclen The PercentLength.
        </member>
        <member name="M:Telerik.Apoc.Fo.Expr.Numeric.#ctor(Telerik.Apoc.DataTypes.TableColLength)">
            v         * Construct a Numeric object from a TableColLength.
                     * @param tclen The TableColLength.
        </member>
        <member name="M:Telerik.Apoc.Fo.Expr.Numeric.asLength">
            Return the current value as a Length if possible. This constructs
            a new Length or Length subclass based on the current value type
            of the Numeric.
            If the stored value has a unit dimension other than 1, null
            is returned.
        </member>
        <member name="M:Telerik.Apoc.Fo.Expr.Numeric.asNumber">
            Return the current value as a Number if possible.
            Calls asDouble().
        </member>
        <member name="M:Telerik.Apoc.Fo.Expr.Numeric.isMixedType">
            Return a boolean value indiciating whether the currently stored
            value consists of different "types" of values (absolute, percent,
            and/or table-unit.)
        </member>
        <member name="M:Telerik.Apoc.Fo.Expr.Numeric.subtract(Telerik.Apoc.Fo.Expr.Numeric)">
            Subtract the operand from the current value and return a new Numeric
            representing the result.
            @param op The value to subtract.
            @return A Numeric representing the result.
            @throws PropertyException If the dimension of the operand is different
            from the dimension of this Numeric.
        </member>
        <member name="M:Telerik.Apoc.Fo.Expr.Numeric.add(Telerik.Apoc.Fo.Expr.Numeric)">
            Add the operand from the current value and return a new Numeric
            representing the result.
            @param op The value to add.
            @return A Numeric representing the result.
            @throws PropertyException If the dimension of the operand is different
            from the dimension of this Numeric.
        </member>
        <member name="M:Telerik.Apoc.Fo.Expr.Numeric.multiply(Telerik.Apoc.Fo.Expr.Numeric)">
            Multiply the the current value by the operand and return a new Numeric
            representing the result.
            @param op The multiplier.
            @return A Numeric representing the result.
            @throws PropertyException If both Numerics have "mixed" type.
        </member>
        <member name="M:Telerik.Apoc.Fo.Expr.Numeric.divide(Telerik.Apoc.Fo.Expr.Numeric)">
            Divide the the current value by the operand and return a new Numeric
            representing the result.
            @param op The divisor.
            @return A Numeric representing the result.
            @throws PropertyException If both Numerics have "mixed" type.
        </member>
        <member name="M:Telerik.Apoc.Fo.Expr.Numeric.abs">
            Return the absolute value of this Numeric.
            @return A new Numeric object representing the absolute value.
        </member>
        <member name="M:Telerik.Apoc.Fo.Expr.Numeric.max(Telerik.Apoc.Fo.Expr.Numeric)">
            Return a Numeric which is the maximum of the current value and the
            operand.
            @throws PropertyException If the dimensions or value types of the
            object and the operand are different.
        </member>
        <member name="M:Telerik.Apoc.Fo.Expr.Numeric.min(Telerik.Apoc.Fo.Expr.Numeric)">
            Return a Numeric which is the minimum of the current value and the
            operand.
            @throws PropertyException If the dimensions or value types of the
            object and the operand are different.
        </member>
        <member name="T:Telerik.Apoc.Fo.Expr.PropertyInfo">
            This class holds context information needed during property expression
            evaluation.
            It holds the Maker object for the property, the PropertyList being
            built, and the FObj parent of the FObj for which the property is being set.
        </member>
        <member name="M:Telerik.Apoc.Fo.Expr.PropertyInfo.inheritsSpecified">
            Return whether this property inherits specified values.
            Propagates to the Maker.
            @return true if the property inherits specified values, false if it
            inherits computed values.
        </member>
        <member name="M:Telerik.Apoc.Fo.Expr.PropertyInfo.GetPercentBase">
            Return the PercentBase object used to calculate the absolute value from
            a percent specification.
            Propagates to the Maker.
            @return The PercentBase object or null if percentLengthOK()=false.
        </member>
        <member name="M:Telerik.Apoc.Fo.Expr.PropertyInfo.currentFontSize">
            Return the current font-size value as base units (milli-points).
        </member>
        <member name="M:Telerik.Apoc.Fo.Expr.PropertyParser.parse(System.String,Telerik.Apoc.Fo.Expr.PropertyInfo)">
            Public entrypoint to the Property expression parser.
            @param expr The specified value (attribute on the xml element).
            @param propInfo A PropertyInfo object representing the context in
            which the property expression is to be evaluated.
            @return A Property object holding the parsed result.
            @throws PropertyException If the "expr" cannot be parsed as a Property.
        </member>
        <member name="M:Telerik.Apoc.Fo.Expr.PropertyParser.#ctor(System.String,Telerik.Apoc.Fo.Expr.PropertyInfo)">
            Private constructor. Called by the static parse() method.
            @param propExpr The specified value (attribute on the xml element).
            @param propInfo A PropertyInfo object representing the context in
            which the property expression is to be evaluated.
        </member>
        <member name="M:Telerik.Apoc.Fo.Expr.PropertyParser.parseProperty">
            Parse the property expression described in the instance variables.
            Note: If the property expression String is empty, a StringProperty
            object holding an empty String is returned.
            @return A Property object holding the parsed result.
            @throws PropertyException If the "expr" cannot be parsed as a Property.
        </member>
        <member name="M:Telerik.Apoc.Fo.Expr.PropertyParser.parseAdditiveExpr">
            Try to parse an addition or subtraction expression and return the
            resulting Property.
        </member>
        <member name="M:Telerik.Apoc.Fo.Expr.PropertyParser.parseMultiplicativeExpr">
            Try to parse a multiply, divide or modulo expression and return
            the resulting Property.
        </member>
        <member name="M:Telerik.Apoc.Fo.Expr.PropertyParser.parseUnaryExpr">
            Try to parse a unary minus expression and return the
            resulting Property.
        </member>
        <member name="M:Telerik.Apoc.Fo.Expr.PropertyParser.expectRpar">
            Checks that the current token is a right parenthesis
            and throws an exception if this isn't the case.
        </member>
        <member name="M:Telerik.Apoc.Fo.Expr.PropertyParser.parsePrimaryExpr">
            Try to parse a primary expression and return the
            resulting Property.
            A primary expression is either a parenthesized expression or an
            expression representing a primitive Property datatype, such as a
            string literal, an NCname, a number or a unit expression, or a
            function call expression.
        </member>
        <member name="M:Telerik.Apoc.Fo.Expr.PropertyParser.parseArgs(System.Int32)">
            Parse a comma separated list of function arguments. Each argument
            may itself be an expression. This method consumes the closing right
            parenthesis of the argument list.
            @param nbArgs The number of arguments expected by the function.
            @return An array of Property objects representing the arguments
            found.
            @throws PropertyException If the number of arguments found isn't equal
            to the number expected.
        </member>
        <member name="M:Telerik.Apoc.Fo.Expr.PropertyParser.evalAddition(Telerik.Apoc.Fo.Expr.Numeric,Telerik.Apoc.Fo.Expr.Numeric)">
            Evaluate an addition operation. If either of the arguments is null,
            this means that it wasn't convertible to a Numeric value.
            @param op1 A Numeric object (Number or Length-type object)
            @param op2 A Numeric object (Number or Length-type object)
            @return A new NumericProperty object holding an object which represents
            the sum of the two operands.
            @throws PropertyException If either operand is null.
        </member>
        <member name="M:Telerik.Apoc.Fo.Expr.PropertyParser.evalSubtraction(Telerik.Apoc.Fo.Expr.Numeric,Telerik.Apoc.Fo.Expr.Numeric)">
            Evaluate a subtraction operation. If either of the arguments is null,
            this means that it wasn't convertible to a Numeric value.
            @param op1 A Numeric object (Number or Length-type object)
            @param op2 A Numeric object (Number or Length-type object)
            @return A new NumericProperty object holding an object which represents
            the difference of the two operands.
            @throws PropertyException If either operand is null.
        </member>
        <member name="M:Telerik.Apoc.Fo.Expr.PropertyParser.evalNegate(Telerik.Apoc.Fo.Expr.Numeric)">
            Evaluate a unary minus operation. If the argument is null,
            this means that it wasn't convertible to a Numeric value.
            @param op A Numeric object (Number or Length-type object)
            @return A new NumericProperty object holding an object which represents
            the negative of the operand (multiplication by *1).
            @throws PropertyException If the operand is null.
        </member>
        <member name="M:Telerik.Apoc.Fo.Expr.PropertyParser.evalMultiply(Telerik.Apoc.Fo.Expr.Numeric,Telerik.Apoc.Fo.Expr.Numeric)">
            Evaluate a multiplication operation. If either of the arguments is null,
            this means that it wasn't convertible to a Numeric value.
            @param op1 A Numeric object (Number or Length-type object)
            @param op2 A Numeric object (Number or Length-type object)
            @return A new NumericProperty object holding an object which represents
            the product of the two operands.
            @throws PropertyException If either operand is null.
        </member>
        <member name="M:Telerik.Apoc.Fo.Expr.PropertyParser.evalDivide(Telerik.Apoc.Fo.Expr.Numeric,Telerik.Apoc.Fo.Expr.Numeric)">
            Evaluate a division operation. If either of the arguments is null,
            this means that it wasn't convertible to a Numeric value.
            @param op1 A Numeric object (Number or Length-type object)
            @param op2 A Numeric object (Number or Length-type object)
            @return A new NumericProperty object holding an object which represents
            op1 divided by op2.
            @throws PropertyException If either operand is null.
        </member>
        <member name="M:Telerik.Apoc.Fo.Expr.PropertyParser.evalModulo(Telerik.Apoc.DataTypes.Number,Telerik.Apoc.DataTypes.Number)">
            Evaluate a modulo operation. If either of the arguments is null,
            this means that it wasn't convertible to a Number value.
            @param op1 A Number object
            @param op2 A Number object
            @return A new NumberProperty object holding an object which represents
            op1 mod op2.
            @throws PropertyException If either operand is null.
        </member>
        <member name="M:Telerik.Apoc.Fo.Expr.PropertyParser.ParseDouble(System.String)">
            <summary>
                Parses a double value using a culture insensitive locale.
            </summary>
            <param name="s">The double value as a string.</param>
            <returns>The double value parsed.</returns>
        </member>
        <member name="M:Telerik.Apoc.Fo.Expr.PropertyTokenizer.#ctor(System.String)">
            Construct a new PropertyTokenizer object to tokenize the passed
            string.
            @param s The Property expressio to tokenize.
        </member>
        <member name="M:Telerik.Apoc.Fo.Expr.PropertyTokenizer.next">
            Return the next token in the expression string.
            This sets the following package visible variables:
            currentToken  An enumerated value identifying the recognized token
            currentTokenValue  A string containing the token contents
            currentUnitLength  If currentToken = TOK_NUMERIC, the number of
            characters in the unit name.
            @throws PropertyException If un unrecognized token is encountered.
        </member>
        <member name="M:Telerik.Apoc.Fo.Expr.PropertyTokenizer.scanName">
            Attempt to recognize a valid NAME token in the input expression.
        </member>
        <member name="M:Telerik.Apoc.Fo.Expr.PropertyTokenizer.scanDigits">
            Attempt to recognize a valid sequence of decimal digits in the
            input expression.
        </member>
        <member name="M:Telerik.Apoc.Fo.Expr.PropertyTokenizer.scanHexDigits">
            Attempt to recognize a valid sequence of hexadecimal digits in the
            input expression.
        </member>
        <member name="M:Telerik.Apoc.Fo.Expr.PropertyTokenizer.followingParen">
            Return a bool value indicating whether the following non-whitespace
            character is an opening parenthesis.
        </member>
        <member name="M:Telerik.Apoc.Fo.Expr.PropertyTokenizer.isDigit(System.Char)">
            Return a bool value indicating whether the argument is a
            decimal digit (0-9).
            @param c The character to check
        </member>
        <member name="M:Telerik.Apoc.Fo.Expr.PropertyTokenizer.isHexDigit(System.Char)">
            Return a bool value indicating whether the argument is a
            hexadecimal digit (0-9, A-F, a-f).
            @param c The character to check
        </member>
        <member name="M:Telerik.Apoc.Fo.Expr.PropertyTokenizer.isSpace(System.Char)">
            Return a bool value indicating whether the argument is whitespace
            as defined by XSL (space, newline, CR, tab).
            @param c The character to check
        </member>
        <member name="M:Telerik.Apoc.Fo.Expr.PropertyTokenizer.isNameStartChar(System.Char)">
            Return a  bool value indicating whether the argument is a valid name
            start character, ie. can start a NAME as defined by XSL.
            @param c The character to check
        </member>
        <member name="M:Telerik.Apoc.Fo.Expr.PropertyTokenizer.isNameChar(System.Char)">
            Return a  bool value indicating whether the argument is a valid name
            character, ie. can occur in a NAME as defined by XSL.
            @param c The character to check
        </member>
        <member name="M:Telerik.Apoc.Fo.Expr.RGBColorFunction.GetPercentBase">
            Return an object which implements the PercentBase interface.
            Percents in arguments to this function are interpreted relative
            to 255.
        </member>
        <member name="M:Telerik.Apoc.Fo.Flow.AbstractTableBody.startsAC(Telerik.Apoc.Layout.Area)">
            Return true if the passed area is on the left edge of its nearest
            absolute AreaContainer (generally a page column).
        </member>
        <member name="M:Telerik.Apoc.Fo.Flow.Block.GetContentWidth">
            Return the content width of the boxes generated by this FO.
        </member>
        <member name="M:Telerik.Apoc.Fo.Flow.BlockContainer.GetContentWidth">
            Return the content width of the boxes generated by this block
            container FO.
        </member>
        <member name="T:Telerik.Apoc.Fo.Flow.Character">
             this class represents the flow object 'fo:character'. Its use is defined by
             the spec: "The fo:character flow object represents a character that is mapped to
             a glyph for presentation. It is an atomic unit to the formatter.
             When the result tree is interpreted as a tree of formatting objects,
             a character in the result tree is treated as if it were an empty
             element of type fo:character with a character attribute
             equal to the Unicode representation of the character.
             The semantics of an "auto" value for character properties, which is
             typically their initial value,  are based on the Unicode codepoint.
             Overrides may be specified in an implementation-specific manner." (6.6.3)
            
        </member>
        <member name="F:Telerik.Apoc.Fo.Flow.Flow.pageSequence">
            PageSequence container
        </member>
        <member name="F:Telerik.Apoc.Fo.Flow.Flow.markerSnapshot">
            Vector to store snapshot
        </member>
        <member name="F:Telerik.Apoc.Fo.Flow.Flow._flowName">
            flow-name attribute
        </member>
        <member name="F:Telerik.Apoc.Fo.Flow.Flow.contentWidth">
            Content-width of current column area during layout
        </member>
        <member name="M:Telerik.Apoc.Fo.Flow.Flow.GetContentWidth">
            Return the content width of this flow (really of the region
            in which it is flowing).
        </member>
        <member name="T:Telerik.Apoc.Fo.Flow.InstreamForeignObject.Maker">
            inner class for making SVG objects.
        </member>
        <member name="M:Telerik.Apoc.Fo.Flow.InstreamForeignObject.Maker.Make(Telerik.Apoc.Fo.FObj,Telerik.Apoc.Fo.PropertyList)">
             make an SVG object.
            
             @param parent the parent formatting object
             @param propertyList the explicit properties of this object
            
             @return the SVG object
        </member>
        <member name="M:Telerik.Apoc.Fo.Flow.InstreamForeignObject.GetMaker">
             returns the maker for this object.
            
             @return the maker for SVG objects
        </member>
        <member name="M:Telerik.Apoc.Fo.Flow.InstreamForeignObject.#ctor(Telerik.Apoc.Fo.FObj,Telerik.Apoc.Fo.PropertyList)">
             constructs an instream-foreign-object object (called by Maker).
            
             @param parent the parent formatting object
             @param propertyList the explicit properties of this object
        </member>
        <member name="M:Telerik.Apoc.Fo.Flow.InstreamForeignObject.Layout(Telerik.Apoc.Layout.Area)">
             layout this formatting object.
            
             @param area the area to layout the object into
            
             @return the status of the layout
        </member>
        <member name="T:Telerik.Apoc.Fo.Flow.Leader">
            Implements fo:leader; main property of leader leader-pattern.
            The following patterns are treated: rule, space, dots.
            The pattern use-content is ignored, i.e. it still must be implemented.
        </member>
        <member name="M:Telerik.Apoc.Fo.Flow.Leader.AddLeader(Telerik.Apoc.Layout.BlockArea,Telerik.Apoc.Layout.FontState,System.Single,System.Single,System.Single,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32)">
             adds a leader to current line area of containing block area
             the actual leader area is created in the line area
            
             @return int +1 for success and -1 for none
        </member>
        <member name="M:Telerik.Apoc.Fo.Flow.ListItem.GetContentWidth">
            Return the content width of the boxes generated by this FO.
        </member>
        <member name="M:Telerik.Apoc.Fo.Flow.Marker.releaseRegistryArea">
            <summary>
                The page the marker was registered is put into the renderer 
                queue. The marker is transferred to it's own marker list,
                release the area for GC. We also know now whether the area is
                first/last.
            </summary>
        </member>
        <member name="M:Telerik.Apoc.Fo.Flow.Marker.resetMarker">
            <summary>
                This has actually nothing to do with resseting this marker,
                but the 'marker' from FONode, marking layout status.
                Called in case layout is to be rolled back. Unregister this
                marker from the page, it isn't laid aout anyway.
            </summary>
        </member>
        <member name="M:Telerik.Apoc.Fo.Flow.Marker.resetMarkerContent">
            <summary>
                More hackery: reset layout status marker. Called before the
                content is laid out from RetrieveMarker.
            </summary>
        </member>
        <member name="T:Telerik.Apoc.Fo.Flow.PageNumberCitation">
             6.6.11 fo:page-number-citation
            
             Common Usage:
             The fo:page-number-citation is used to reference the page-number for the page containing the first normal area returned by
             the cited formatting object.
            
             NOTE:
             It may be used to provide the page-numbers in the table of contents, cross-references, and index entries.
            
             Areas:
             The fo:page-number-citation formatting object generates and returns a single normal inline-area.
             Constraints:
            
             The cited page-number is the number of the page containing, as a descendant, the first normal area returned by the
             formatting object with an id trait matching the ref-id trait of the fo:page-number-citation (the referenced formatting
             object).
            
             The cited page-number string is obtained by converting the cited page-number in accordance with the number to string
             conversion properties specified on the ancestor fo:page-sequence of the referenced formatting object.
            
             The child areas of the generated inline-area are the same as the result of formatting a result-tree fragment consisting of
             fo:character flow objects; one for each character in the cited page-number string and with only the "character" property
             specified.
            
             Contents:
            
             EMPTY
            
             The following properties apply to this formatting object:
            
             [7.3 Common Accessibility Properties]
             [7.5 Common Aural Properties]
             [7.6 Common Border, Padding, and Background Properties]
             [7.7 Common Font Properties]
             [7.10 Common Margin Properties-Inline]
             [7.11.1 "alignment-adjust"]
             [7.11.2 "baseline-identifier"]
             [7.11.3 "baseline-shift"]
             [7.11.5 "dominant-baseline"]
             [7.36.2 "id"]
             [7.17.4 "keep-with-next"]
             [7.17.5 "keep-with-previous"]
             [7.14.2 "letter-spacing"]
             [7.13.4 "line-height"]
             [7.13.5 "line-height-shift-adjustment"]
             [7.36.5 "ref-id"]
             [7.18.4 "relative-position"]
             [7.36.6 "score-spaces"]
             [7.14.4 "text-decoration"]
             [7.14.5 "text-shadow"]
             [7.14.6 "text-transform"]
             [7.14.8 "word-spacing"]
        </member>
        <member name="M:Telerik.Apoc.Fo.Flow.RowSpanMgr.SpanInfo.heightRemaining">
            Return the height remaining in the span.
        </member>
        <member name="M:Telerik.Apoc.Fo.Flow.RowSpanMgr.HasUnfinishedSpans">
            Return true if any column has an unfinished vertical span.
        </member>
        <member name="M:Telerik.Apoc.Fo.Flow.RowSpanMgr.FinishRow(System.Int32)">
            Done with a row.
            Any spans with only one row left are done
            This means that we can now set the total height for this cell box
            Loop over all cells with spans and find number of rows remaining
            if rows remaining  = 1, set the height on the cell area and
            then remove the cell from the list of spanned cells. For other
            spans, add the rowHeight to the spanHeight.
        </member>
        <member name="M:Telerik.Apoc.Fo.Flow.RowSpanMgr.GetRemainingHeight(System.Int32)">
            If the cell in this column is in the last row of its vertical
            span, return the height left. If it's not in the last row, or if
            the content height &lt;= the content height of the previous rows
            of the span, return 0.
        </member>
        <member name="M:Telerik.Apoc.Fo.Flow.RowSpanMgr.SetIgnoreKeeps(System.Boolean)">
            helper method to prevent infinite loops if
            keeps or spans are not fitting on a page
            @param <code>true</code> if keeps and spans should be ignored
        </member>
        <member name="M:Telerik.Apoc.Fo.Flow.RowSpanMgr.IgnoreKeeps">
            helper method (i.e. hack ;-) to prevent infinite loops if
            keeps or spans are not fitting on a page
            @return true if keeps or spans should be ignored
        </member>
        <member name="F:Telerik.Apoc.Fo.Flow.Table.optIPD">
            Optimum inline-progression-dimension 
        </member>
        <member name="F:Telerik.Apoc.Fo.Flow.Table.minIPD">
            Minimum inline-progression-dimension 
        </member>
        <member name="F:Telerik.Apoc.Fo.Flow.Table.maxIPD">
            Maximum inline-progression-dimension 
        </member>
        <member name="M:Telerik.Apoc.Fo.Flow.Table.GetContentWidth">
            Return the content width of the boxes generated by this table FO.
        </member>
        <member name="M:Telerik.Apoc.Fo.Flow.Table.SetIPD(System.Boolean,System.Int32)">
            Initialize table inline-progression-properties values
        </member>
        <member name="F:Telerik.Apoc.Fo.Flow.TableCell.startOffset">
            Offset of content rectangle in inline-progression-direction,
            relative to table.
        </member>
        <member name="F:Telerik.Apoc.Fo.Flow.TableCell.width">
            Dimension of allocation rectangle in inline-progression-direction,
            determined by the width of the column(s) occupied by the cell
        </member>
        <member name="F:Telerik.Apoc.Fo.Flow.TableCell.beforeOffset">
            Offset of content rectangle, in block-progression-direction,
            relative to the row.
        </member>
        <member name="F:Telerik.Apoc.Fo.Flow.TableCell.startAdjust">
            Offset of content rectangle, in inline-progression-direction,
            relative to the column start edge.
        </member>
        <member name="F:Telerik.Apoc.Fo.Flow.TableCell.widthAdjust">
            Adjust to theoretical column width to obtain content width
            relative to the column start edge.
        </member>
        <member name="F:Telerik.Apoc.Fo.Flow.TableCell.minCellHeight">
            Minimum ontent height of cell.
        </member>
        <member name="F:Telerik.Apoc.Fo.Flow.TableCell.bDone">
            Set to true if all content completely laid out.
        </member>
        <member name="F:Telerik.Apoc.Fo.Flow.TableCell.m_borderSeparation">
            Border separation value in the block-progression dimension.
            Used in calculating cells height.
        </member>
        <member name="M:Telerik.Apoc.Fo.Flow.TableCell.GetHeight">
            Return the allocation height of the cell area.
            Note: called by TableRow.
            We adjust the actual allocation height of the area by the value
            of border separation (for separate borders) or border height
            adjustment for collapse style (because current scheme makes cell
            overestimate the allocation height).
        </member>
        <member name="M:Telerik.Apoc.Fo.Flow.TableCell.SetRowHeight(System.Int32)">
            Set the final size of cell content rectangles to the actual row height
            and to vertically align the actual content within the cell rectangle.
            @param h Height of this row in the grid  which is based on
            the allocation height of all the cells in the row, including any
            border separation values.
        </member>
        <member name="M:Telerik.Apoc.Fo.Flow.TableCell.CalcBorders(Telerik.Apoc.Layout.BorderAndPadding)">
            Calculate cell border and padding, including offset of content
            rectangle from the theoretical grid position.
        </member>
        <member name="M:Telerik.Apoc.Fo.Flow.TableColumn.SetColumnWidth(System.Int32)">
            Set the column width value in base units which overrides the
            value from the column-width Property.
        </member>
        <member name="M:Telerik.Apoc.Fo.Flow.TableRow.CellArray.GetNextFreeCell(System.Int32)">
            Return column which doesn't already contain a span or a cell
            If past the end or no free cells after colNum, return -1
            Otherwise return value >= input value.
        </member>
        <member name="M:Telerik.Apoc.Fo.Flow.TableRow.CellArray.GetCellType(System.Int32)">
            Return type of cell in colNum (1 based)
        </member>
        <member name="M:Telerik.Apoc.Fo.Flow.TableRow.CellArray.GetCell(System.Int32)">
            Return cell in colNum (1 based)
        </member>
        <member name="M:Telerik.Apoc.Fo.Flow.TableRow.CellArray.StoreCell(Telerik.Apoc.Fo.Flow.TableCell,System.Int32,System.Int32)">
            Store cell starting at cellColNum (1 based) and spanning numCols
            If any of the columns is already occupied, return false, else true
        </member>
        <member name="M:Telerik.Apoc.Fo.Flow.TableRow.SetRowSpanMgr(Telerik.Apoc.Fo.Flow.RowSpanMgr)">
            Called by parent FO to initialize information about
            cells started in previous rows which span into this row.
            The layout operation modifies rowSpanMgr
        </member>
        <member name="M:Telerik.Apoc.Fo.Flow.TableRow.InitCellArray">
            Before starting layout for the first time, initialize information
            about spanning rows, empty cells and spanning columns.
        </member>
        <member name="T:Telerik.Apoc.Fo.Flow.Wrapper">
             Implementation for fo:wrapper formatting object.
             The wrapper object serves as
             a property holder for it's children objects.
            
             Content: (#PCDATA|%inline;|%block;)*
             Properties: id
        </member>
        <member name="T:Telerik.Apoc.Fo.FObj">
            base class for representation of formatting objects and their processing
        </member>
        <member name="M:Telerik.Apoc.Fo.FObj.AddCharacters(System.Char[],System.Int32,System.Int32)">
            adds characters (does nothing here)
            @param data text
            @param start start position
            @param length length of the text
        </member>
        <member name="M:Telerik.Apoc.Fo.FObj.Layout(Telerik.Apoc.Layout.Area)">
             generates the area or areas for this formatting object
             and adds these to the area. This method should always be
             overridden by all sub classes
            
             @param area
        </member>
        <member name="M:Telerik.Apoc.Fo.FObj.GetName">
            returns the name of the formatting object
            @return the name of this formatting objects
        </member>
        <member name="M:Telerik.Apoc.Fo.FObj.Start">
            
        </member>
        <member name="M:Telerik.Apoc.Fo.FObj.End">
            
        </member>
        <member name="M:Telerik.Apoc.Fo.FObj.GetProperty(System.String)">
            lets outside sources access the property list
            first used by PageNumberCitation to find the "id" property
            @param name - the name of the desired property to obtain
            @return the property
        </member>
        <member name="M:Telerik.Apoc.Fo.FObj.GetContentWidth">
            Return the "content width" of the areas generated by this FO.
            This is used by percent-based properties to get the dimension of
            the containing block.
            If an FO has a property with a percentage value, that value
            is usually calculated on the basis of the corresponding dimension
            of the area which contains areas generated by the FO.
            NOTE: subclasses of FObj should implement this to return a reasonable
            value!
        </member>
        <member name="M:Telerik.Apoc.Fo.FObj.RemoveID(Telerik.Apoc.DataTypes.IDReferences)">
            removes property id
            @param idReferences the id to remove
        </member>
        <member name="M:Telerik.Apoc.Fo.FObj.SetWritingMode">
            Set writing mode for this FO.
            Find nearest ancestor, including self, which generates
            reference areas and use the value of its writing-mode property.
            If no such ancestor is found, use the value on the root FO.
        </member>
        <member name="T:Telerik.Apoc.Fo.FObjMixed">
            base class for representation of mixed content formatting objects
            and their processing
        </member>
        <member name="F:Telerik.Apoc.Fo.FONode.MarkerStart">
            <summary>
                Value of marker before layout begins
            </summary>
        </member>
        <member name="F:Telerik.Apoc.Fo.FONode.MarkerBreakAfter">
            value of marker after break-after
        </member>
        <member name="F:Telerik.Apoc.Fo.FONode.marker">
            where the layout was up to.
            for FObjs it is the child number
            for FOText it is the character number
        </member>
        <member name="M:Telerik.Apoc.Fo.FONode.GetProperty(System.String)">
            lets outside sources access the property list
            first used by PageNumberCitation to find the "id" property
            returns null by default, overide this function when there is a property list
            @param name - the name of the desired property to obtain
            @returns the property
        </member>
        <member name="M:Telerik.Apoc.Fo.FONode.getMarkerSnapshot(System.Collections.ArrayList)">
            At the start of a new span area layout may be partway through a
            nested FO, and balancing requires rollback to this known point.
            The snapshot records exactly where layout is at.
            @param snapshot a Vector of markers (Integer)
            @returns the updated Vector of markers (Integers)
        </member>
        <member name="M:Telerik.Apoc.Fo.FONode.Rollback(System.Collections.ArrayList)">
            When balancing occurs, the flow layout() method restarts at the
            point specified by the current marker snapshot, which is retrieved
            and restored using this method.
            @param snapshot the Vector of saved markers (Integers)
        </member>
        <member name="T:Telerik.Apoc.Fo.FOTreeBuilder">
            <summary>
                Builds the formatting object tree.
            </summary>
        </member>
        <member name="F:Telerik.Apoc.Fo.FOTreeBuilder.fobjTable">
            <summary>
                Table mapping element names to the makers of objects
                representing formatting objects.
            </summary>
        </member>
        <member name="F:Telerik.Apoc.Fo.FOTreeBuilder.propertylistTable">
            <summary>
                Class that builds a property list for each formatting object.
            </summary>
        </member>
        <member name="F:Telerik.Apoc.Fo.FOTreeBuilder.currentFObj">
            <summary>
                Current formatting object being handled.
            </summary>
        </member>
        <member name="F:Telerik.Apoc.Fo.FOTreeBuilder.rootFObj">
            <summary>
                The root of the formatting object tree.
            </summary>
        </member>
        <member name="F:Telerik.Apoc.Fo.FOTreeBuilder.unknownFOs">
            <summary>
                Set of names of formatting objects encountered but unknown.
            </summary>
        </member>
        <member name="F:Telerik.Apoc.Fo.FOTreeBuilder.streamRenderer">
            <summary>
                The class that handles formatting and rendering to a stream.
            </summary>
        </member>
        <member name="M:Telerik.Apoc.Fo.FOTreeBuilder.SetStreamRenderer(Telerik.Apoc.StreamRenderer)">
            <summary>
                Sets the stream renderer that will be used as output.
            </summary>
        </member>
        <member name="M:Telerik.Apoc.Fo.FOTreeBuilder.AddElementMapping(System.String,System.Collections.Hashtable)">
            <summary>
                Add a mapping from element name to maker.
            </summary>
        </member>
        <member name="M:Telerik.Apoc.Fo.FOTreeBuilder.AddPropertyMapping(System.String,System.Collections.Hashtable)">
            <summary>
                Add a mapping from property name to maker.
            </summary>
        </member>
        <member name="F:Telerik.Apoc.Fo.LengthProperty.length">
            This object may be also be a subclass of Length, such
            as PercentLength, TableColLength.
        </member>
        <member name="M:Telerik.Apoc.Fo.NumberProperty.GetObject">
            public Double getDouble() {
            return new Double(this.number.doubleValue());
            }
            public Integer getInteger() {
            return new Integer(this.number.intValue());
            }
        </member>
        <member name="M:Telerik.Apoc.Fo.Pagination.ConditionalPageMasterReference.GetMasterName">
            Returns the "master-reference" attribute of this page master reference
        </member>
        <member name="M:Telerik.Apoc.Fo.Pagination.LayoutMasterSet.regionNameExists(System.String)">
            Checks whether or not a region name exists in this master set
            @returns true when the region name specified has a region in this LayoutMasterSet
        </member>
        <member name="T:Telerik.Apoc.Fo.Pagination.PageMasterReference">
            Base PageMasterReference class. Provides implementation for handling the
            master-reference attribute and containment within a PageSequenceMaster
        </member>
        <member name="P:Telerik.Apoc.Fo.Pagination.PageMasterReference.MasterName">
            Returns the "master-reference" attribute of this page master reference
        </member>
        <member name="M:Telerik.Apoc.Fo.Pagination.PageMasterReference.GetElementName">
             Gets the formating object name for this object. Subclasses must provide this.
            
             @return the element name of this reference. e.g. fo:repeatable-page-master-reference
        </member>
        <member name="M:Telerik.Apoc.Fo.Pagination.PageMasterReference.validateParent(Telerik.Apoc.Fo.FObj)">
            Checks that the parent is the right element. The default implementation
            checks for fo:page-sequence-master
        </member>
        <member name="T:Telerik.Apoc.Fo.Pagination.PageNumberGenerator">
            This class uses the 'format', 'groupingSeparator', 'groupingSize',
            and 'letterValue' properties on fo:page-sequence to return a string
            corresponding to the supplied integer page number.
        </member>
        <member name="T:Telerik.Apoc.Fo.Pagination.PageSequence">
            This provides pagination of flows onto pages. Much of the logic for paginating
            flows is contained in this class. The main entry point is the format method.
        </member>
        <member name="F:Telerik.Apoc.Fo.Pagination.PageSequence.root">
            The parent root object
        </member>
        <member name="F:Telerik.Apoc.Fo.Pagination.PageSequence.layoutMasterSet">
            the set of layout masters (provided by the root object)
        </member>
        <member name="F:Telerik.Apoc.Fo.Pagination.PageSequence._flowMap">
            Map of flows to their flow name (flow-name, Flow)
        </member>
        <member name="F:Telerik.Apoc.Fo.Pagination.PageSequence.masterName">
            the "master-reference" attribute,
            which specifies the name of the page-sequence-master or
            page-master to be used to create pages in the sequence
        </member>
        <member name="F:Telerik.Apoc.Fo.Pagination.PageSequence.pageNumberType">
            specifies page numbering type (auto|auto-even|auto-odd|explicit)
        </member>
        <member name="F:Telerik.Apoc.Fo.Pagination.PageSequence.thisIsFirstPage">
            used to determine whether to calculate auto, auto-even, auto-odd
        </member>
        <member name="F:Telerik.Apoc.Fo.Pagination.PageSequence.currentSubsequence">
            the current subsequence while formatting a given page sequence
        </member>
        <member name="F:Telerik.Apoc.Fo.Pagination.PageSequence.currentSubsequenceNumber">
            the current index in the subsequence list
        </member>
        <member name="F:Telerik.Apoc.Fo.Pagination.PageSequence.currentPageMasterName">
            the name of the current page master
        </member>
        <member name="M:Telerik.Apoc.Fo.Pagination.PageSequence.Format(Telerik.Apoc.Layout.AreaTree)">
            Runs the formatting of this page sequence into the given area tree
        </member>
        <member name="M:Telerik.Apoc.Fo.Pagination.PageSequence.MakePage(Telerik.Apoc.Layout.AreaTree,System.Int32,System.Boolean,System.Boolean)">
            Creates a new page area for the given parameters
            @param areaTree the area tree the page should be contained in
            @param firstAvailPageNumber the page number for this page
            @param isFirstPage true when this is the first page in the sequence
            @param isEmptyPage true if this page will be empty (e.g. forced even or odd break)
            @return a Page layout object based on the page master selected from the params
        </member>
        <member name="M:Telerik.Apoc.Fo.Pagination.PageSequence.FormatStaticContent(Telerik.Apoc.Layout.AreaTree)">
            Formats the static content of the current page
        </member>
        <member name="M:Telerik.Apoc.Fo.Pagination.PageSequence.GetNextSubsequence(Telerik.Apoc.Fo.Pagination.PageSequenceMaster)">
            Returns the next SubSequenceSpecifier for the given page sequence master. The result
            is bassed on the current state of this page sequence.
        </member>
        <member name="M:Telerik.Apoc.Fo.Pagination.PageSequence.GetNextSimplePageMaster(Telerik.Apoc.Fo.Pagination.PageSequenceMaster,System.Int32,System.Boolean,System.Boolean)">
            Returns the next simple page master for the given sequence master, page number and
            other state information
        </member>
        <member name="M:Telerik.Apoc.Fo.Pagination.PageSequence.FlowsAreIncomplete">
            Returns true when there is more flow elements left to lay out.
        </member>
        <member name="M:Telerik.Apoc.Fo.Pagination.PageSequence.GetCurrentFlow(System.String)">
            Returns the flow that maps to the given region class for the current
            page master.
        </member>
        <member name="T:Telerik.Apoc.Fo.Pagination.Region">
            This is an abstract base class for pagination regions
        </member>
        <member name="M:Telerik.Apoc.Fo.Pagination.Region.MakeRegionArea(System.Int32,System.Int32,System.Int32,System.Int32)">
            Creates a Region layout object for this pagination Region.
        </member>
        <member name="M:Telerik.Apoc.Fo.Pagination.Region.GetDefaultRegionName">
            Returns the default region name (xsl-region-before, xsl-region-start,
            etc.)
        </member>
        <member name="M:Telerik.Apoc.Fo.Pagination.Region.GetElementName">
            Returns the element name ("fo:region-body", "fo:region-start",
            etc.)
        </member>
        <member name="M:Telerik.Apoc.Fo.Pagination.Region.getRegionName">
            Returns the name of this region
        </member>
        <member name="M:Telerik.Apoc.Fo.Pagination.Region.isReserved(System.String)">
             Checks to see if a given region name is one of the reserved names
            
             @param name a region name to check
             @return true if the name parameter is a reserved region name
        </member>
        <member name="F:Telerik.Apoc.Fo.Pagination.RepeatablePageMasterAlternatives.maximumRepeats">
            Max times this page master can be repeated.
            INFINITE is used for the unbounded case
        </member>
        <member name="T:Telerik.Apoc.Fo.Pagination.Root">
            The fo:root formatting object. Contains page masters, root extensions,
            page-sequences.
        </member>
        <member name="F:Telerik.Apoc.Fo.Pagination.Root.runningPageNumberCounter">
            keeps count of page number from over PageSequence instances
        </member>
        <member name="M:Telerik.Apoc.Fo.Pagination.Root.getSucceedingPageSequence(Telerik.Apoc.Fo.Pagination.PageSequence)">
            Some properties, such as 'force-page-count', require a
            page-sequence to know about some properties of the next.
            @returns succeeding PageSequence; null if none
        </member>
        <member name="F:Telerik.Apoc.Fo.Pagination.SimplePageMaster._regions">
            Page regions (regionClass, Region)
        </member>
        <member name="T:Telerik.Apoc.Fo.Pagination.SubSequenceSpecifier">
            Classes that implement this interface can be added to a PageSequenceMaster,
            and are capable of looking up an appropriate PageMaster.
        </member>
        <member name="M:Telerik.Apoc.Fo.Pagination.SubSequenceSpecifier.Reset">
            Called before a new page sequence is rendered so subsequences can reset
            any state they keep during the formatting process.
        </member>
        <member name="M:Telerik.Apoc.Fo.Properties.BlockProgressionDimensionMaker.SP_MinimumMaker.GetPercentBase(Telerik.Apoc.Fo.FObj,Telerik.Apoc.Fo.PropertyList)">
            Return object used to calculate base Length
            for percent specifications.
        </member>
        <member name="M:Telerik.Apoc.Fo.Properties.BlockProgressionDimensionMaker.SP_OptimumMaker.GetPercentBase(Telerik.Apoc.Fo.FObj,Telerik.Apoc.Fo.PropertyList)">
            Return object used to calculate base Length
            for percent specifications.
        </member>
        <member name="M:Telerik.Apoc.Fo.Properties.BlockProgressionDimensionMaker.SP_MaximumMaker.GetPercentBase(Telerik.Apoc.Fo.FObj,Telerik.Apoc.Fo.PropertyList)">
            Return object used to calculate base Length
            for percent specifications.
        </member>
        <member name="M:Telerik.Apoc.Fo.Properties.BlockProgressionDimensionMaker.ConvertProperty(Telerik.Apoc.Fo.Property,Telerik.Apoc.Fo.PropertyList,Telerik.Apoc.Fo.FObj)">
            Set the appropriate components when the "base" property is set. 
        </member>
        <member name="M:Telerik.Apoc.Fo.Properties.BorderSeparationMaker.ConvertProperty(Telerik.Apoc.Fo.Property,Telerik.Apoc.Fo.PropertyList,Telerik.Apoc.Fo.FObj)">
            Set the appropriate components when the "base" property is set. 
        </member>
        <member name="M:Telerik.Apoc.Fo.Properties.FontSizeMaker.GetPercentBase(Telerik.Apoc.Fo.FObj,Telerik.Apoc.Fo.PropertyList)">
            Return object used to calculate base Length
            for percent specifications.
        </member>
        <member name="M:Telerik.Apoc.Fo.Properties.GenericCondBorderWidth.ConvertProperty(Telerik.Apoc.Fo.Property,Telerik.Apoc.Fo.PropertyList,Telerik.Apoc.Fo.FObj)">
            Set the appropriate components when the "base" property is set. 
        </member>
        <member name="M:Telerik.Apoc.Fo.Properties.GenericCondLength.ConvertProperty(Telerik.Apoc.Fo.Property,Telerik.Apoc.Fo.PropertyList,Telerik.Apoc.Fo.FObj)">
            Set the appropriate components when the "base" property is set. 
        </member>
        <member name="M:Telerik.Apoc.Fo.Properties.GenericKeep.ConvertProperty(Telerik.Apoc.Fo.Property,Telerik.Apoc.Fo.PropertyList,Telerik.Apoc.Fo.FObj)">
            Set the appropriate components when the "base" property is set. 
        </member>
        <member name="M:Telerik.Apoc.Fo.Properties.GenericSpace.ConvertProperty(Telerik.Apoc.Fo.Property,Telerik.Apoc.Fo.PropertyList,Telerik.Apoc.Fo.FObj)">
            Set the appropriate components when the "base" property is set. 
        </member>
        <member name="M:Telerik.Apoc.Fo.Properties.InlineProgressionDimensionMaker.SP_MinimumMaker.GetPercentBase(Telerik.Apoc.Fo.FObj,Telerik.Apoc.Fo.PropertyList)">
            Return object used to calculate base Length
            for percent specifications.
        </member>
        <member name="M:Telerik.Apoc.Fo.Properties.InlineProgressionDimensionMaker.SP_OptimumMaker.GetPercentBase(Telerik.Apoc.Fo.FObj,Telerik.Apoc.Fo.PropertyList)">
            Return object used to calculate base Length
            for percent specifications.
        </member>
        <member name="M:Telerik.Apoc.Fo.Properties.InlineProgressionDimensionMaker.SP_MaximumMaker.GetPercentBase(Telerik.Apoc.Fo.FObj,Telerik.Apoc.Fo.PropertyList)">
            Return object used to calculate base Length
            for percent specifications.
        </member>
        <member name="M:Telerik.Apoc.Fo.Properties.InlineProgressionDimensionMaker.ConvertProperty(Telerik.Apoc.Fo.Property,Telerik.Apoc.Fo.PropertyList,Telerik.Apoc.Fo.FObj)">
            Set the appropriate components when the "base" property is set. 
        </member>
        <member name="M:Telerik.Apoc.Fo.Properties.LeaderLengthMaker.SP_MinimumMaker.GetPercentBase(Telerik.Apoc.Fo.FObj,Telerik.Apoc.Fo.PropertyList)">
            Return object used to calculate base Length
            for percent specifications.
        </member>
        <member name="M:Telerik.Apoc.Fo.Properties.LeaderLengthMaker.SP_OptimumMaker.GetPercentBase(Telerik.Apoc.Fo.FObj,Telerik.Apoc.Fo.PropertyList)">
            Return object used to calculate base Length
            for percent specifications.
        </member>
        <member name="M:Telerik.Apoc.Fo.Properties.LeaderLengthMaker.SP_MaximumMaker.GetPercentBase(Telerik.Apoc.Fo.FObj,Telerik.Apoc.Fo.PropertyList)">
            Return object used to calculate base Length
            for percent specifications.
        </member>
        <member name="M:Telerik.Apoc.Fo.Properties.LeaderLengthMaker.ConvertProperty(Telerik.Apoc.Fo.Property,Telerik.Apoc.Fo.PropertyList,Telerik.Apoc.Fo.FObj)">
            Set the appropriate components when the "base" property is set. 
        </member>
        <member name="M:Telerik.Apoc.Fo.Properties.LeaderLengthMaker.GetPercentBase(Telerik.Apoc.Fo.FObj,Telerik.Apoc.Fo.PropertyList)">
            Return object used to calculate base Length
            for percent specifications.
        </member>
        <member name="M:Telerik.Apoc.Fo.Properties.LeaderPatternWidthMaker.GetPercentBase(Telerik.Apoc.Fo.FObj,Telerik.Apoc.Fo.PropertyList)">
            Return object used to calculate base Length
            for percent specifications.
        </member>
        <member name="M:Telerik.Apoc.Fo.Properties.LineHeightMaker.GetPercentBase(Telerik.Apoc.Fo.FObj,Telerik.Apoc.Fo.PropertyList)">
            Return object used to calculate base Length
            for percent specifications.
        </member>
        <member name="M:Telerik.Apoc.Fo.Properties.WidthMaker.GetPercentBase(Telerik.Apoc.Fo.FObj,Telerik.Apoc.Fo.PropertyList)">
            Return object used to calculate base Length
            for percent specifications.
        </member>
        <member name="F:Telerik.Apoc.Fo.Property.specVal">
            The original specified value for properties which inherit
            specified values.
        </member>
        <member name="P:Telerik.Apoc.Fo.Property.SpecifiedValue">
            <summary>
                Gets or setd the original value specified for the property attribute.
            </summary>
        </member>
        <member name="M:Telerik.Apoc.Fo.Property.GetLength">
            Accessor functions for all possible Property datatypes
        </member>
        <member name="M:Telerik.Apoc.Fo.PropertyList.GetExplicitOrShorthandProperty(System.String)">
            Return the value explicitly specified on this FO.
            @param propertyName The name of the property whose value is desired.
            It may be a compound name, such as space-before.optimum.
            @return The value if the property is explicitly set or set by
            a shorthand property, otherwise null.
        </member>
        <member name="M:Telerik.Apoc.Fo.PropertyList.GetExplicitProperty(System.String)">
            Return the value explicitly specified on this FO.
            @param propertyName The name of the property whose value is desired.
            It may be a compound name, such as space-before.optimum.
            @return The value if the property is explicitly set, otherwise null.
        </member>
        <member name="M:Telerik.Apoc.Fo.PropertyList.GetExplicitBaseProperty(System.String)">
            Return the value explicitly specified on this FO.
            @param propertyName The name of the base property whose value is desired.
            @return The value if the property is explicitly set, otherwise null.
        </member>
        <member name="M:Telerik.Apoc.Fo.PropertyList.GetInheritedProperty(System.String)">
            Return the value of this property inherited by this FO.
            Implements the inherited-property-value function.
            The property must be inheritable!
            @param propertyName The name of the property whose value is desired.
            @return The inherited value, otherwise null.
        </member>
        <member name="M:Telerik.Apoc.Fo.PropertyList.GetSpecifiedProperty(System.String)">
            Return the property on the current FlowObject if it is specified, or if a
            corresponding property is specified. If neither is specified, it returns null.
        </member>
        <member name="M:Telerik.Apoc.Fo.PropertyList.GetProperty(System.String)">
            Return the property on the current FlowObject. If it isn't set explicitly,
            this will try to compute it based on other properties, or if it is
            inheritable, to return the inherited value. If all else fails, it returns
            the default value.
        </member>
        <member name="M:Telerik.Apoc.Fo.PropertyList.GetProperty(System.String,System.Boolean,System.Boolean)">
            Return the property on the current FlowObject. Depending on the passed flags,
            this will try to compute it based on other properties, or if it is
            inheritable, to return the inherited value. If all else fails, it returns
            the default value.
        </member>
        <member name="M:Telerik.Apoc.Fo.PropertyList.GetNearestSpecifiedProperty(System.String)">
            Return the "nearest" specified value for the given property.
            Implements the from-nearest-specified-value function.
            @param propertyName The name of the property whose value is desired.
            @return The computed value if the property is explicitly set on some
            ancestor of the current FO, else the initial value.
        </member>
        <member name="M:Telerik.Apoc.Fo.PropertyList.GetFromParentProperty(System.String)">
            Return the value of this property on the parent of this FO.
            Implements the from-parent function.
            @param propertyName The name of the property whose value is desired.
            @return The computed value on the parent or the initial value if this
            FO is the root or is in a different namespace from its parent.
        </member>
        <member name="M:Telerik.Apoc.Fo.PropertyList.wmAbsToRel(System.Int32)">
            Given an absolute direction (top, bottom, left, right),
            return the corresponding writing model relative direction name
            for the flow object. Uses the stored writingMode.
        </member>
        <member name="M:Telerik.Apoc.Fo.PropertyList.wmRelToAbs(System.Int32)">
            Given a writing mode relative direction (start, end, before, after)
            return the corresponding absolute direction name
            for the flow object. Uses the stored writingMode.
        </member>
        <member name="M:Telerik.Apoc.Fo.PropertyList.SetWritingMode(System.Int32)">
            Set the writing mode traits for the FO with this property list.
        </member>
        <member name="F:Telerik.Apoc.Fo.PropertyListBuilder.FONTSIZEATTR">
            Name of font-size property attribute to set first.
        </member>
        <member name="M:Telerik.Apoc.Fo.PropertyListBuilder.MakeProperty(Telerik.Apoc.Fo.PropertyList,System.String)">
            <summary>
                This seems to be just a helper method that looks up a property maker and
                creates the property.
            </summary>
        </member>
        <member name="M:Telerik.Apoc.Fo.PropertyListBuilder.FindMaker(System.String)">
            <summary>
                Convenience function to return the Maker for a given property.
            </summary>
        </member>
        <member name="P:Telerik.Apoc.Fo.PropertyMaker.PropName">
            <summary>
                Return the name of the property whose value is being set.
            </summary>
        </member>
        <member name="M:Telerik.Apoc.Fo.PropertyMaker.#ctor">
            <summary>
                Construct an instance of a PropertyMaker.
            </summary>
            <remarks>
                The property name is set to "UNKNOWN".
            </remarks>
        </member>
        <member name="M:Telerik.Apoc.Fo.PropertyMaker.#ctor(System.String)">
            <summary>
                Construct an instance of a PropertyMaker for the given property.
            </summary>
            <param name="propName">The name of the property to be made.</param>
        </member>
        <member name="M:Telerik.Apoc.Fo.PropertyMaker.IsInherited">
            <summary>
                Default implementation of isInherited.
            </summary>
            <returns>A boolean indicating whether this property is inherited.</returns>
        </member>
        <member name="M:Telerik.Apoc.Fo.PropertyMaker.InheritsSpecified">
            <summary>
                Return a boolean indicating whether this property inherits the
                "specified" value rather than the "computed" value. The default is 
                to inherit the "computed" value.
            </summary>
            <returns>If true, property inherits the value specified.</returns>
        </member>
        <member name="M:Telerik.Apoc.Fo.PropertyMaker.GetPercentBase(Telerik.Apoc.Fo.FObj,Telerik.Apoc.Fo.PropertyList)">
            <summary>
                Return an object implementing the PercentBase interface.  This is 
                used to handle properties specified as a percentage of some "base 
                length", such as the content width of their containing box.  
                Overridden by subclasses which allow percent specifications. See
                the documentation on properties.xsl for details.
            </summary>
            <param name="fo"></param>
            <param name="pl"></param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Apoc.Fo.PropertyMaker.GetSubpropMaker(System.String)">
            <summary>
                Return a Maker object which is used to set the values on components 
                of compound property types, such as "space".  Overridden by property 
                maker subclasses which handle compound properties.
            </summary>
            <param name="subprop">
                The name of the component for which a Maker is to returned, for 
                example "optimum", if the FO attribute is space.optimum='10pt'.
            </param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Apoc.Fo.PropertyMaker.GetSubpropValue(Telerik.Apoc.Fo.Property,System.String)">
            <summary>
                Return a property value for the given component of a compound 
                property.
            </summary>
            <remarks>
                NOTE: this is only to ease porting when calls are made to 
                PropertyList.get() using a component name of a compound property,
                such as get("space.optimum"). 
                The recommended technique is: get("space").getOptimum().
                Overridden by property maker subclasses which handle compound properties.
            </remarks>
            <param name="p">A property value for a compound property type such as SpaceProperty.</param>
            <param name="subprop">The name of the component whose value is to be returned.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Apoc.Fo.PropertyMaker.Make(Telerik.Apoc.Fo.Property,System.String,Telerik.Apoc.Fo.PropertyList,System.String,Telerik.Apoc.Fo.FObj)">
            <summary>
                Return a property value for a compound property. If the property
                value is already partially initialized, this method will modify it.
            </summary>
            <param name="baseProp">
                The Property object representing the compound property, such as 
                SpaceProperty.
            </param>
            <param name="partName">The name of the component whose value is specified.</param>
            <param name="propertyList">The propertyList being built.</param>
            <param name="value"></param>
            <param name="fo">The FO whose properties are being set.</param>
            <returns>A compound property object.</returns>
        </member>
        <member name="M:Telerik.Apoc.Fo.PropertyMaker.SetSubprop(Telerik.Apoc.Fo.Property,System.String,Telerik.Apoc.Fo.Property)">
            <summary>
                Set a component in a compound property and return the modified
                compound property object.  This default implementation returns 
                the original base property without modifying it.  It is overridden 
                by property maker subclasses which handle compound properties.
            </summary>
            <param name="baseProp">
                The Property object representing the compound property, such as SpaceProperty.
            </param>
            <param name="partName">The name of the component whose value is specified.</param>
            <param name="subProp">
                A Property object holding the specified value of the component to be set.
            </param>
            <returns>The modified compound property object.</returns>
        </member>
        <member name="M:Telerik.Apoc.Fo.PropertyMaker.Make(Telerik.Apoc.Fo.PropertyList,System.String,Telerik.Apoc.Fo.FObj)">
            <summary>
                Create a Property object from an attribute specification.
            </summary>
            <param name="propertyList">The PropertyList object being built for this FO.</param>
            <param name="value">The attribute value.</param>
            <param name="fo">The current FO whose properties are being set.</param>
            <returns>The initialized Property object.</returns>
        </member>
        <member name="M:Telerik.Apoc.Fo.PropertyMaker.CheckValueKeywords(System.String)">
            <summary>
                Return a String to be parsed if the passed value corresponds to
                a keyword which can be parsed and used to initialize the property.
                For example, the border-width family of properties can have the
                initializers "thin", "medium", or "thick". The foproperties.xml
                file specifies a length value equivalent for these keywords,
                such as "0.5pt" for "thin". These values are considered parseable,
                since the Length object is no longer responsible for parsing
                unit expresssions.
            </summary>
            <param name="value">The string value of property attribute.</param>
            <returns>
                A string containging a parseable equivalent or null if the passed 
                value isn't a keyword initializer for this Property.
            </returns>
        </member>
        <member name="M:Telerik.Apoc.Fo.PropertyMaker.ConvertProperty(Telerik.Apoc.Fo.Property,Telerik.Apoc.Fo.PropertyList,Telerik.Apoc.Fo.FObj)">
            <summary>
                Return a Property object based on the passed Property object.
                This method is called if the Property object built by the parser
                isn't the right type for this property.
                It is overridden by subclasses when the property specification in
                foproperties.xml specifies conversion rules.
            </summary>
            <param name="p">The Property object return by the expression parser</param>
            <param name="propertyList">The PropertyList object being built for this FO.</param>
            <param name="fo">The current FO whose properties are being set.</param>
            <returns>
                A Property of the correct type or null if the parsed value
                can't be converted to the correct type.
            </returns>
        </member>
        <member name="M:Telerik.Apoc.Fo.PropertyMaker.Make(Telerik.Apoc.Fo.PropertyList)">
            <summary>
                Return a Property object representing the initial value.
            </summary>
            <param name="propertyList">The PropertyList object being built for this FO.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Apoc.Fo.PropertyMaker.MakeCompound(Telerik.Apoc.Fo.PropertyList,Telerik.Apoc.Fo.FObj)">
            <summary>
                Return a Property object representing the initial value.
            </summary>
            <param name="propertyList">The PropertyList object being built for this FO.</param>
            <param name="parentFO">The parent FO for the FO whose property is being made.</param>
            <returns>
                A Property subclass object holding a "compound" property object
                initialized to the default values for each component.
            </returns>
        </member>
        <member name="M:Telerik.Apoc.Fo.PropertyMaker.Compute(Telerik.Apoc.Fo.PropertyList)">
            <summary>
                Return a Property object representing the value of this property,
                based on other property values for this FO.
                A special case is properties which inherit the specified value,
                rather than the computed value.
            </summary>
            <param name="propertyList">The PropertyList for the FO.</param>
            <returns>
                Property A computed Property value or null if no rules are 
                specified (in foproperties.xml) to compute the value.
            </returns>
        </member>
        <member name="T:Telerik.Apoc.Fo.Status">
            classes representating the status of laying out a formatting object
        </member>
        <member name="T:Telerik.Apoc.Fo.Unknown">
            This represents an unknown element.
            For example with unsupported namespaces.
            This prevents any further problems arising from the unknown
            data.
        </member>
        <member name="T:Telerik.Apoc.Fo.UnknownXMLObj.Maker">
            inner class for making unknown xml objects.
        </member>
        <member name="M:Telerik.Apoc.Fo.UnknownXMLObj.Maker.Make(Telerik.Apoc.Fo.FObj,Telerik.Apoc.Fo.PropertyList)">
             make an unknown xml object.
            
             @param parent the parent formatting object
             @param propertyList the explicit properties of this object
            
             @return the unknown xml object
        </member>
        <member name="M:Telerik.Apoc.Fo.UnknownXMLObj.GetMaker(System.String,System.String)">
             returns the maker for this object.
            
             @return the maker for an unknown xml object
        </member>
        <member name="M:Telerik.Apoc.Fo.UnknownXMLObj.#ctor(Telerik.Apoc.Fo.FObj,Telerik.Apoc.Fo.PropertyList,System.String,System.String)">
             constructs an unknown xml object (called by Maker).
            
             @param parent the parent formatting object
             @param propertyList the explicit properties of this object
        </member>
        <member name="T:Telerik.Apoc.IDriver">
            <summary>
                This interface is implemented by the ApocDriver class to permit usage 
                from COM applications.  This is the recommended method of supporting 
                invocation from COM application as it permits interface versioning.
            </summary>
        </member>
        <member name="T:Telerik.Apoc.Image.ApocImage">
            <summary>
                A bitmap image that will be referenced by fo:external-graphic.
            </summary>
            <remarks>
                This class and the associated ColorSpace class are PDF specific ideally 
                will be moved to the PDF library project at some point in the future.  
                Internally, Apoc should handle images using the standard framework 
                Bitmap class.
            </remarks>
        </member>
        <member name="F:Telerik.Apoc.Image.ApocImage.filter">
            <summary>
                Filter that will be applied to image data
            </summary>
        </member>
        <member name="M:Telerik.Apoc.Image.ApocImage.#ctor(System.String,System.Byte[])">
            <summary>
                Constructs a new ApocImage using the supplied bitmap.
            </summary>
            <remarks>
                Does not hold a reference to the passed bitmap.  Instead the
                image data is extracted from <b>bitmap</b> on construction.
            </remarks>
            <param name="href">The location of <i>bitmap</i></param>
            <param name="imageData">The image data</param>
        </member>
        <member name="P:Telerik.Apoc.Image.ApocImage.Uri">
            <summary>
                Return the image URL.
            </summary>
            <returns>the image URL (as a string)</returns>
        </member>
        <member name="P:Telerik.Apoc.Image.ApocImage.Width">
            <summary>
                Return the image width. 
            </summary>
            <returns>the image width</returns>
        </member>
        <member name="P:Telerik.Apoc.Image.ApocImage.Height">
            <summary>
                Return the image height. 
            </summary>
            <returns>the image height</returns>
        </member>
        <member name="P:Telerik.Apoc.Image.ApocImage.BitsPerPixel">
            <summary>
                Return the number of bits per pixel. 
            </summary>
            <returns>number of bits per pixel</returns>
        </member>
        <member name="P:Telerik.Apoc.Image.ApocImage.BitmapsSize">
            <summary>
                Return the image data size
            </summary>
            <returns>The image data size</returns>
        </member>
        <member name="P:Telerik.Apoc.Image.ApocImage.Bitmaps">
            <summary>
                Return the image data (uncompressed). 
            </summary>
            <returns>the image data</returns>
        </member>
        <member name="P:Telerik.Apoc.Image.ApocImage.ColorSpace">
            <summary>
                Return the image color space. 
            </summary>
            <returns>the image color space (Apoc.Datatypes.ColorSpace)</returns>
        </member>
        <member name="P:Telerik.Apoc.Image.ApocImage.Filter">
            <summary>
                Returns the <see cref="T:Telerik.Pdf.Filter.IFilter"/> implementation 
                that should be applied to the bitmap data.
            </summary>
        </member>
        <member name="M:Telerik.Apoc.Image.ApocImage.ExtractImage(System.Drawing.Bitmap)">
            <summary>
                Extracts the raw data from the image into a byte array suitable
                for including in the PDF document.  The image is always extracted
                as a 24-bit RGB image, regardless of it's original colour space
                and colour depth.
            </summary>
            <param name="bitmap">The <see cref="T:System.Drawing.Bitmap"/> from which the data is extracted</param>
            <returns>A byte array containing the raw 24-bit RGB data</returns>
        </member>
        <member name="T:Telerik.Apoc.Image.JpegParser">
            <summary>
                Parses the contents of a JPEG image header to infer the colour 
                space and bits per pixel.
            </summary>
        </member>
        <member name="F:Telerik.Apoc.Image.JpegParser.ms">
            <summary>
                JPEG image data
            </summary>
        </member>
        <member name="F:Telerik.Apoc.Image.JpegParser.headerInfo">
            <summary>
                Contains number of bitplanes, color space and optional ICC Profile
            </summary>
        </member>
        <member name="F:Telerik.Apoc.Image.JpegParser.iccProfileData">
            <summary>
                Raw ICC Profile
            </summary>
        </member>
        <member name="M:Telerik.Apoc.Image.JpegParser.#ctor(System.Byte[])">
            <summary>
                Class constructor.
            </summary>
            <param name="data"></param>
        </member>
        <member name="M:Telerik.Apoc.Image.JpegParser.ReadHeader">
            <summary>
                
            </summary>
        </member>
        <member name="M:Telerik.Apoc.Image.JpegParser.ReadInt">
            <summary>
                Reads a 16-bit integer from the underlying stream
            </summary>
            <returns></returns>
        </member>
        <member name="M:Telerik.Apoc.Image.JpegParser.ReadByte">
            <summary>
                Reads a 32-bit integer from the underlying stream
            </summary>
            <returns></returns>
        </member>
        <member name="M:Telerik.Apoc.Image.JpegParser.ReadString(System.Int32)">
            <summary>
                Reads the specified number of bytes from theunderlying stream 
                and converts them to a string using the ASCII encoding.
            </summary>
            <param name="numBytes"></param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Apoc.Image.JpegParser.ReadFirstMarker">
            <summary>
                Reads the initial marker which should be SOI.
            </summary>
            <remarks>
                After invoking this method the stream will point to the location 
                immediately after the fiorst marker.
            </remarks>
            <returns></returns>
        </member>
        <member name="M:Telerik.Apoc.Image.JpegParser.ReadNextMarker">
            <summary>
                Reads the next JPEG marker and returns its marker code.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Telerik.Apoc.Image.JpegParser.SkipVariable">
            <summary>
                Skips over the parameters for any marker we don't want to process.
            </summary>
        </member>
        <member name="T:Telerik.Apoc.Image.UriSpecificationParser">
            <summary>
                Parses a &lt;uri-specification&gt; as defined by 
                section 5.11 of the XSL specification.
            </summary>
            <remarks>
                This class may be better expressed as a datatype residing in 
                Telerik.Apoc.DataTypes.
            </remarks>
        </member>
        <member name="T:Telerik.Apoc.Layout.AbsolutePositionProps">
            Store all hyphenation related properties on an FO.
            Public "structure" allows direct member access.
        </member>
        <member name="T:Telerik.Apoc.Layout.AccessibilityProps">
            Store all hyphenation related properties on an FO.
            Public "structure" allows direct member access.
        </member>
        <member name="F:Telerik.Apoc.Layout.Area.currentHeight">
            Total height of content of this area.
        </member>
        <member name="M:Telerik.Apoc.Layout.Area.#ctor(Telerik.Apoc.Layout.FontState,System.Int32,System.Int32)">
             Creates a new <code>Area</code> instance.
            
             @param fontState a <code>FontState</code> value
             @param allocationWidth the inline-progression dimension of the content
             rectangle of the Area
             @param maxHeight the maximum block-progression dimension available
             for this Area (its allocation rectangle)
        </member>
        <member name="M:Telerik.Apoc.Layout.Area.setAllocationWidth(System.Int32)">
            Set the allocation width.
            @param w The new allocation width.
            This sets content width to the same value.
            Currently only called during layout of Table to set the width
            to the total width of all the columns. Note that this assumes the
            column widths are explicitly specified.
        </member>
        <member name="M:Telerik.Apoc.Layout.Area.hasNonSpaceChildren">
            <summary>
                Tell whether this area contains any children which are not 
                DisplaySpace. This is used in determining whether to honour keeps.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Telerik.Apoc.Layout.Area.getContentHeight">
             Returns content height of the area.
            
             @return Content height in millipoints
        </member>
        <member name="M:Telerik.Apoc.Layout.Area.GetHeight">
             Returns allocation height of this area.
             The allocation height is the sum of the content height plus border
             and padding in the vertical direction.
            
             @return allocation height in millipoints
        </member>
        <member name="M:Telerik.Apoc.Layout.Area.getAbsoluteHeight">
            <summary>
                Return absolute Y position of the current bottom of this area,
                not counting any bottom padding or border.
            </summary>
            <remarks>
                This is used to set positions for link hotspots.
                In fact, the position is not really absolute, but is relative
                to the Ypos of the column-level AreaContainer, even when the
                area is in a page header or footer!
            </remarks>
        </member>
        <member name="M:Telerik.Apoc.Layout.Area.setAbsoluteHeight(System.Int32)">
            <summary>
                Set "absolute" Y position of the top of this area.
            </summary>
            <remarks>
                In fact, the position is not really absolute, but relative to 
                the Ypos of the column-level AreaContainer, even when the area 
                is in a page header or footer! 
                It is set from the value of getAbsoluteHeight() on the parent 
                area, just before adding this area. 
            </remarks>
        </member>
        <member name="M:Telerik.Apoc.Layout.Area.spaceLeft">
            Return space remaining in the vertical direction (height).
            This returns maximum available space - current content height
            Note: content height should be based on allocation height of content!
            @return space remaining in base units (millipoints)
        </member>
        <member name="M:Telerik.Apoc.Layout.Area.SetHeight(System.Int32)">
             Set the content height to the passed value if that value is
             larger than current content height. If the new content height
             is greater than the maximum available height, set the content height
             to the max. available (!!!)
            
             @param height allocation height of content in millipoints
        </member>
        <member name="F:Telerik.Apoc.Layout.AreaTree.fontInfo">
            object containing information on available fonts, including
            metrics
        </member>
        <member name="F:Telerik.Apoc.Layout.AreaTree.rootExtensions">
            List of root extension objects
        </member>
        <member name="M:Telerik.Apoc.Layout.AreaTree.GetDocumentMarkers">
            <summary>
                Auxillary function for retrieving markers.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Telerik.Apoc.Layout.AreaTree.GetCurrentPageSequence">
            <summary>
                Auxillary function for retrieving markers.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Telerik.Apoc.Layout.AreaTree.GetCurrentPageSequenceMarkers">
            <summary>
                Auxillary function for retrieving markers.
            </summary>
            <returns></returns>
        </member>
        <member name="T:Telerik.Apoc.Layout.AuralProps">
            Store all hyphenation related properties on an FO.
            Public "structure" allows direct member access.
        </member>
        <member name="T:Telerik.Apoc.Layout.BlockArea">
             This class represents a Block Area.
             A block area is made up of a sequence of Line Areas.
            
             This class is used to organise the sequence of line areas as
             inline areas are added to this block it creates and ands line areas
             to hold the inline areas.
             This uses the line-height and line-stacking-strategy to work
             out how to stack the lines.
        </member>
        <member name="M:Telerik.Apoc.Layout.BlockArea.addLineArea(Telerik.Apoc.Layout.LineArea)">
             Add a Line Area to this block area.
             Used internally to add a completed line area to this block area
             when either a new line area is created or this block area is
             completed.
            
             @param la the LineArea to add
        </member>
        <member name="M:Telerik.Apoc.Layout.BlockArea.getCurrentLineArea">
             Get the current line area in this block area.
             This is used to get the current line area for adding
             inline objects to.
             This will return null if there is not enough room left
             in the block area to accomodate the line area.
            
             @return the line area to be used to add inlie objects
        </member>
        <member name="M:Telerik.Apoc.Layout.BlockArea.createNextLineArea">
             Create a new line area to add inline objects.
             This should be called after getting the current line area
             and discovering that the inline object will not fit inside the current
             line. This method will create a new line area to place the inline
             object into.
             This will return null if the new line cannot fit into the block area.
            
             @return the new current line area, which will be empty.
        </member>
        <member name="M:Telerik.Apoc.Layout.BlockArea.end">
            Notify this block that the area has completed layout.
            Indicates the the block has been fully laid out, this will
            add (if any) the current line area.
        </member>
        <member name="M:Telerik.Apoc.Layout.BlockArea.spaceLeft">
            Return the maximum space remaining for this area's content in
            the block-progression-dimension.
            Remove top and bottom padding and spacing since these reduce
            available space for content and they are not yet accounted for
            in the positioning of the object.
        </member>
        <member name="M:Telerik.Apoc.Layout.BodyAreaContainer.getNextArea(Telerik.Apoc.Fo.FObj)">
            Depending on the column-count of the next FO, determine whether
            a new span area needs to be constructed or not, and return the
            appropriate ColumnArea.
            The next cut of this method should also inspect the FO to see
            whether the area to be returned ought not to be the footnote
            or before-float reference area.
            @param fo The next formatting object
            @returns the next column area (possibly the current one)
        </member>
        <member name="M:Telerik.Apoc.Layout.BodyAreaContainer.addSpanArea(System.Int32)">
            Add a new span area with specified number of column areas.
            @param numColumns The number of column areas
            @returns AreaContainer The next column area
        </member>
        <member name="M:Telerik.Apoc.Layout.BodyAreaContainer.isBalancingRequired(Telerik.Apoc.Fo.FObj)">
            This almost does what getNewArea() does, without actually
            returning an area. These 2 methods can be reworked.
            @param fo The next formatting object
            @returns bool True if we need to balance.
        </member>
        <member name="M:Telerik.Apoc.Layout.BodyAreaContainer.resetSpanArea">
            This is where the balancing algorithm lives, or gets called.
            Right now it's primitive: get the total content height in all
            columns, divide by the column count, and add a heuristic
            safety factor.
            Then the previous (unbalanced) span area is removed, and a new
            one added with the computed max height.
        </member>
        <member name="M:Telerik.Apoc.Layout.BodyAreaContainer.GetRemainingHeight">
            Determine remaining height for new span area. Needs to be
            modified for footnote and before-float reference areas when
            those are supported.
            @returns int The remaining available height in millipoints.
        </member>
        <member name="M:Telerik.Apoc.Layout.BodyAreaContainer.resetHeights">
            Used by resetSpanArea() and addSpanArea() to adjust the main
            reference area height before creating a new span.
        </member>
        <member name="M:Telerik.Apoc.Layout.BodyAreaContainer.isLastColumn">
            Used in Flow when layout returns incomplete.
            @returns bool Is this the last column in this span?
        </member>
        <member name="M:Telerik.Apoc.Layout.BodyAreaContainer.isNewSpanArea">
            This variable is unset by getNextArea(), is set by addSpanArea(),
            and <i>may</i> be set by resetSpanArea().
            @returns bool Is the span area new or not?
        </member>
        <member name="M:Telerik.Apoc.Layout.BorderAndPadding.Clone">
            Return a full copy of the BorderAndPadding information. This clones all
            padding and border information.
            @return The copy.
        </member>
        <member name="M:Telerik.Apoc.Layout.FontInfo.CreateFontKey(System.String,System.String,System.String)">
            Creates a key from the given strings
        </member>
        <member name="M:Telerik.Apoc.Layout.FontState.#ctor(Telerik.Apoc.Layout.FontInfo,System.String,System.String,System.String,System.Int32,System.Int32)">
            <summary>
                Class constructor
            </summary>
            <remarks>
                Defaults the letter spacing to 0 millipoints.
            </remarks>
        </member>
        <member name="M:Telerik.Apoc.Layout.FontState.GetWidth(System.Int32)">
            <summary>
                Gets width of given character identifier plus <see cref="F:Telerik.Apoc.Layout.FontState.letterSpacing"/> 
                in millipoints (1/1000ths of a point).
            </summary>
            <param name="charId"></param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Apoc.Layout.FontState.MapCharacter(System.Char)">
            <summary>
                Map a Unicode character to a code point
            </summary>
            <param name="c">Any Unicode character.</param>
            <returns></returns>
        </member>
        <member name="T:Telerik.Apoc.Layout.HyphenationProps">
            Store all hyphenation related properties on an FO.
            Public "structure" allows direct member access.
        </member>
        <member name="T:Telerik.Apoc.Layout.IFontDescriptor">
            <summary>
                A font descriptor specifies metrics and other attributes of a 
                font, as distinct from the metrics of individual glyphs.
            </summary>
            <remarks>
                See page 355 of PDF 1.4 specification for more information.
            </remarks>
        </member>
        <member name="P:Telerik.Apoc.Layout.IFontDescriptor.Flags">
            <summary>
                Gets a collection of flags providing various font characteristics.
            </summary>
        </member>
        <member name="P:Telerik.Apoc.Layout.IFontDescriptor.FontBBox">
            <summary>
                Gets the smallest rectangle that will encompass the shape that 
                would result if all glyhs of the font were placed with their 
                origins coincident.
            </summary>
        </member>
        <member name="P:Telerik.Apoc.Layout.IFontDescriptor.ItalicAngle">
            <summary>
                Gets the main italic angle of the font expressed in tenths of 
                a degree counterclockwise from the vertical.
            </summary>
        </member>
        <member name="P:Telerik.Apoc.Layout.IFontDescriptor.StemV">
            <summary>
                TODO: The thickness, measured horizontally, of the dominant vertical 
                stems of the glyphs in the font.
            </summary>
        </member>
        <member name="P:Telerik.Apoc.Layout.IFontDescriptor.HasKerningInfo">
            <summary>
                Gets a value that indicates whether this font has kerning support.
            </summary>
            <returns></returns>
        </member>
        <member name="P:Telerik.Apoc.Layout.IFontDescriptor.IsEmbeddable">
            <summary>
                Gets a value that indicates whether this font program may be legally 
                embedded within a document.
            </summary>
            <returns></returns>
        </member>
        <member name="P:Telerik.Apoc.Layout.IFontDescriptor.IsSubsettable">
            <summary>
                Gets a value that indicates whether this font program my be subsetted.
            </summary>
            <returns></returns>
        </member>
        <member name="P:Telerik.Apoc.Layout.IFontDescriptor.FontData">
            <summary>
                Gets a byte array representing a font program to be embedded 
                in a document.
            </summary>
            <remarks>
                If <see cref="P:Telerik.Apoc.Layout.IFontDescriptor.IsEmbeddable"/> is <b>false</b> it is acceptable 
                for this method to return null.
            </remarks>
        </member>
        <member name="P:Telerik.Apoc.Layout.IFontDescriptor.KerningInfo">
            <summary>
                Gets kerning information for this font.
            </summary>
            <remarks>
                If <see cref="P:Telerik.Apoc.Layout.IFontDescriptor.HasKerningInfo"/> is <b>false</b> it is acceptable 
                for this method to return null.
            </remarks>
        </member>
        <member name="T:Telerik.Apoc.Layout.IFontMetric">
            <summary>
                Interface for font metric classes
            </summary>
        </member>
        <member name="P:Telerik.Apoc.Layout.IFontMetric.Ascender">
            <summary>
                Specifies the maximum distance characters in this font extend 
                above the base line. This is the typographic ascent for the font. 
            </summary>
        </member>
        <member name="P:Telerik.Apoc.Layout.IFontMetric.Descender">
            <summary>
                Specifies the maximum distance characters in this font extend 
                below the base line. This is the typographic descent for the font. 
            </summary>
        </member>
        <member name="P:Telerik.Apoc.Layout.IFontMetric.CapHeight">
            <summary>
                Gets the vertical coordinate of the top of flat captial letters.
            </summary>
        </member>
        <member name="P:Telerik.Apoc.Layout.IFontMetric.FirstChar">
            <summary>
                Gets the value of the first character used in the font
            </summary>
        </member>
        <member name="P:Telerik.Apoc.Layout.IFontMetric.LastChar">
            <summary>
                Gets the value of the last character used in the font
            </summary>
        </member>
        <member name="P:Telerik.Apoc.Layout.IFontMetric.Descriptor">
            <summary>
                Gets a reference to a font descriptor.  A descriptor is akin to 
                the PDF FontDescriptor object (see page 355 of PDF 1.4 spec).
            </summary>
        </member>
        <member name="M:Telerik.Apoc.Layout.IFontMetric.GetWidth(System.Int32)">
            <summary>
                Gets the width of a character in 1/1000ths of a point size 
                located at the supplied codepoint.
            </summary>
            <remarks>
                For a type 1 font a code point is an octal code obtained from a 
                character encoding scheme (WinAnsiEncoding, MacRomaonEncoding, etc).
                For example, the code point for the space character is 040 (octal).
                For a type 0 font a code point represents a GID (Glyph index).
            </remarks>
            <param name="charIndex">A character code point.</param>
            <returns></returns>
        </member>
        <member name="P:Telerik.Apoc.Layout.IFontMetric.Widths">
            <summary>
                Gets the widths of all characters in 1/1000ths of a point size.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Telerik.Apoc.Layout.Inline.ForeignObjectArea.getContentWidth">
            This is NOT the content width of the instream-foreign-object.
            This is the content width for a Box.
        </member>
        <member name="M:Telerik.Apoc.Layout.Inline.ForeignObjectArea.GetHeight">
            This is NOT the content height of the instream-foreign-object.
            This is the content height for a Box.
        </member>
        <member name="F:Telerik.Apoc.Layout.Inline.InlineArea.xOffset">
            amount of space added since the original layout - needed by links
        </member>
        <member name="M:Telerik.Apoc.Layout.Inline.InlineSpace.setUnderlined(System.Boolean)">
            @param ul true if text should be underlined
        </member>
        <member name="M:Telerik.Apoc.Layout.Inline.InlineSpace.setEatable(System.Boolean)">
            And eatable InlineSpace is discarded if it occurs
            as the first pending element in a LineArea
        </member>
        <member name="M:Telerik.Apoc.Layout.LineArea.addText(System.Char[],System.Int32,System.Int32,Telerik.Apoc.Layout.LinkSet,Telerik.Apoc.Layout.TextState)">
             adds text to line area
            
             @return int character position
        </member>
        <member name="M:Telerik.Apoc.Layout.LineArea.AddLeader(System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32)">
            adds a Leader; actually the method receives the leader properties
            and creates a leader area or an inline area which is appended to
            the children of the containing line area.
            leader pattern use-content is not implemented.
        </member>
        <member name="M:Telerik.Apoc.Layout.LineArea.addPending">
            adds pending inline areas to the line area
            normally done, when the line area is filled and
            added as child to the parent block area
        </member>
        <member name="M:Telerik.Apoc.Layout.LineArea.align(System.Int32)">
             aligns line area
            
        </member>
        <member name="M:Telerik.Apoc.Layout.LineArea.verticalAlign">
            Balance (vertically) the inline areas within this line.
        </member>
        <member name="M:Telerik.Apoc.Layout.LineArea.changeHyphenation(Telerik.Apoc.Layout.HyphenationProps)">
            sets hyphenation related traits: language, country, hyphenate, hyphenation-character
            and minimum number of character to remain one the previous line and to be on the
            next line.
        </member>
        <member name="M:Telerik.Apoc.Layout.LineArea.buildSimpleLeader(System.Char,System.Int32)">
            creates a leader as String out of the given char and the leader length
            and wraps it in an InlineArea which is returned
        </member>
        <member name="M:Telerik.Apoc.Layout.LineArea.getLeaderAlignIndent(System.Int32,System.Int32)">
             calculates the width of space which has to be inserted before the
             start of the leader, so that all leader characters are aligned.
             is used if property leader-align is set. At the moment only the value
             for leader-align="reference-area" is supported.
            
        </member>
        <member name="M:Telerik.Apoc.Layout.LineArea.getCurrentXPosition">
            calculates the used space in this line area
        </member>
        <member name="M:Telerik.Apoc.Layout.LineArea.getHyphenationWord(System.Char[],System.Int32)">
            extracts a complete word from the character data
        </member>
        <member name="M:Telerik.Apoc.Layout.LineArea.getWordWidth(System.String)">
            Calculates the wordWidth using the actual fontstate
        </member>
        <member name="M:Telerik.Apoc.Layout.LineArea.addCharacter(System.Char,Telerik.Apoc.Layout.LinkSet,System.Boolean)">
            adds a single character to the line area tree
        </member>
        <member name="M:Telerik.Apoc.Layout.LineArea.addMapWord(System.Char,System.Text.StringBuilder)">
            Same as addWord except that characters in wordBuf is mapped
            to the current fontstate's encoding
        </member>
        <member name="M:Telerik.Apoc.Layout.LineArea.addWord(System.Char,System.Text.StringBuilder)">
            adds a InlineArea containing the String startChar+wordBuf to the line area children.
        </member>
        <member name="M:Telerik.Apoc.Layout.LineArea.canBreakMidWord">
            Checks if it's legal to break a word in the middle
            based on the current language property.
            @return true if legal to break word in the middle
        </member>
        <member name="M:Telerik.Apoc.Layout.LineArea.getCharWidth(System.Char)">
            Helper method for getting the width of a unicode char
            from the current fontstate.
            This also performs some guessing on widths on various
            versions of space that might not exists in the font.
        </member>
        <member name="M:Telerik.Apoc.Layout.LineArea.isSpace(System.Char)">
            Helper method to determine if the character is a
            space with normal behaviour. Normal behaviour means that
            it's not non-breaking
        </member>
        <member name="M:Telerik.Apoc.Layout.LineArea.isNBSP(System.Char)">
            Method to determine if the character is a nonbreaking
            space.
        </member>
        <member name="M:Telerik.Apoc.Layout.LineArea.isAnySpace(System.Char)">
            @return true if the character represents any kind of space
        </member>
        <member name="M:Telerik.Apoc.Layout.LineArea.addSpacedWord(System.String,Telerik.Apoc.Layout.LinkSet,System.Int32,System.Int32,Telerik.Apoc.Layout.TextState,System.Boolean)">
            Add a word that might contain non-breaking spaces.
            Split the word into WordArea and InlineSpace and add it.
            If addToPending is true, add to pending areas.
        </member>
        <member name="T:Telerik.Apoc.Layout.LinkedRectangle">
            an object that stores a rectangle that is linked, and the LineArea
            that it is logically associated with
            <AUTHOR> Sandstrom
            <AUTHOR> Tauber
        </member>
        <member name="F:Telerik.Apoc.Layout.LinkedRectangle.link">
            the linked Rectangle
        </member>
        <member name="F:Telerik.Apoc.Layout.LinkedRectangle.lineArea">
            the associated LineArea
        </member>
        <member name="F:Telerik.Apoc.Layout.LinkedRectangle.inlineArea">
            the associated InlineArea
        </member>
        <member name="T:Telerik.Apoc.Layout.LinkSet">
            a set of rectangles on a page that are linked to a common
            destination
        </member>
        <member name="F:Telerik.Apoc.Layout.LinkSet.destination">
            the destination of the links
        </member>
        <member name="F:Telerik.Apoc.Layout.LinkSet.rects">
            the set of rectangles
        </member>
        <member name="T:Telerik.Apoc.Layout.MarginInlineProps">
            Store all hyphenation related properties on an FO.
            Public "structure" allows direct member access.
        </member>
        <member name="T:Telerik.Apoc.Layout.MarginProps">
            Store all hyphenation related properties on an FO.
            Public "structure" allows direct member access.
        </member>
        <member name="M:Telerik.Apoc.Layout.Page.addBody(Telerik.Apoc.Layout.BodyAreaContainer)">
            Ensure that page is set not only on B.A.C. but also on the
            three top-level reference areas.
            @param area The region-body area container (special)
        </member>
        <member name="T:Telerik.Apoc.Layout.RelativePositionProps">
            Store all hyphenation related properties on an FO.
            Public "structure" allows direct member access.
        </member>
        <member name="T:Telerik.Apoc.Layout.TextState">
             This class holds information about text-decoration
            
        </member>
        <member name="M:Telerik.Apoc.Layout.TextState.getUnderlined">
            @return true if text should be underlined
        </member>
        <member name="M:Telerik.Apoc.Layout.TextState.setUnderlined(System.Boolean)">
            set text as underlined
        </member>
        <member name="M:Telerik.Apoc.Layout.TextState.getOverlined">
            @return true if text should be overlined
        </member>
        <member name="T:Telerik.Apoc.Pdf.FontLicenseException">
            <summary>
                Thrown during creation of PDF font object if the font's license
                is violated, e.g. attempting to subset a font that does not permit 
                subsetting.
            </summary>
        </member>
        <member name="T:Telerik.Apoc.Pdf.PdfCreator">
            <remarks>
                Was originally called PdfDocument, but this name is now in
                use by the Telerik.Pdf library. Eventually all code in this 
                class should either be moved to either the Telerik.Pdf library, 
                or to the PdfRenderer.
            </remarks>
        </member>
        <member name="M:Telerik.Apoc.Pdf.PdfCreator.getOutlineRoot">
            Get the root Outlines object. This method does not write
            the outline to the Pdf document, it simply creates a
            reference for later.
        </member>
        <member name="M:Telerik.Apoc.Pdf.PdfCreator.makeOutline(Telerik.Pdf.PdfOutline,System.String,System.String)">
            Make an outline object and add it to the given outline
            @param parent parent PdfOutline object
            @param label the title for the new outline object
            @param action the PdfAction to reference
        </member>
        <member name="M:Telerik.Apoc.Pdf.PdfCreator.getResources">
             get the /Resources object for the document
            
             @return the /Resources object
        </member>
        <member name="T:Telerik.Apoc.Pdf.PdfFontCreator">
            <summary>
                Creates all the necessary PDF objects required to represent 
                a font object in a PDF document.
            </summary>
        </member>
        <member name="F:Telerik.Apoc.Pdf.PdfFontCreator.creator">
            <summary>
                Generates object id's.
            </summary>
        </member>
        <member name="M:Telerik.Apoc.Pdf.PdfFontCreator.#ctor(Telerik.Apoc.Pdf.PdfCreator)">
            <summary>
                
            </summary>
            <param name="creator"></param>
        </member>
        <member name="M:Telerik.Apoc.Pdf.PdfFontCreator.MakeFont(System.String,Telerik.Apoc.Render.Pdf.Fonts.Font)">
            <summary>
                Returns a subclass of the PdfFont class that may be one of
                PdfType0Font, PdfType1Font or PdfTrueTypeFont.  The type of 
                subclass returned is determined by the type of the <i>font</i>
                parameter.
            </summary>
            <param name="pdfFontID">The PDF font identifier, e.g. F15</param>
            <param name="font">Underlying font object.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Apoc.Pdf.PdfFontCreator.CreateCIDFont(System.String,Telerik.Apoc.Render.Pdf.Fonts.Font,Telerik.Apoc.Render.Pdf.Fonts.CIDFont)">
            <summary>
                Creates a character indexed font from <i>cidFont</i>
            </summary>
            <remarks>
                The <i>font</i> and <i>cidFont</i> will be different object 
                references since the <i>font</i> parameter will most likely 
                be a <see cref="T:Telerik.Apoc.Render.Pdf.Fonts.ProxyFont"/>.
            </remarks>
            <param name="pdfFontID">The Pdf font identifier, e.g. F15</param>
            <param name="font">Required to access the font descriptor.</param>
            <param name="cidFont">The underlying CID font.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Apoc.Pdf.PdfFontCreator.NextObjectId">
            <summary>
                Returns the next available Pdf object identifier.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Telerik.Apoc.Pdf.PdfFontCreator.CreateBase14Font(System.String,Telerik.Apoc.Render.Pdf.Fonts.Base14Font)">
            <summary>
                Creates an instance of the <see cref="T:Telerik.Pdf.PdfType1Font"/> class
            </summary>
            <param name="pdfFontID">The Pdf font identifier, e.g. F15</param>
            <param name="base14"></param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Apoc.Pdf.PdfFontCreator.CreateTrueTypeFont(System.String,Telerik.Apoc.Render.Pdf.Fonts.Font,Telerik.Apoc.Render.Pdf.Fonts.TrueTypeFont)">
            <summary>
                Creates an instance of the <see cref="T:Telerik.Pdf.PdfTrueTypeFont"/> class
                that defaults the font encoding to WinAnsiEncoding.
            </summary>
            <param name="pdfFontID"></param>
            <param name="font"></param>
            <param name="ttf"></param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Apoc.Pdf.PdfFontCreator.GetFontMetrics(Telerik.Apoc.Render.Pdf.Fonts.Font)">
            <remarks>
                A ProxyFont must first be resolved before getting the 
                IFontMetircs implementation of the underlying font.
            </remarks>
            <param name="font"></param>
        </member>
        <member name="T:Telerik.Apoc.Pdf.FontSetup">
            <summary>
                Sets up the PDF fonts.
            </summary>
            <remarks>
                Assigns the font (with metrics) to internal names like "F1" and
                assigns family-style-weight triplets to the fonts.
            </remarks>
        </member>
        <member name="F:Telerik.Apoc.Pdf.FontSetup.startIndex">
            <summary>
                First 16 indices are used by base 14 and generic fonts
            </summary>
        </member>
        <member name="F:Telerik.Apoc.Pdf.FontSetup.fontInfo">
            <summary>
                Handles mapping font triplets to a IFontMetric implementor
            </summary>
        </member>
        <member name="M:Telerik.Apoc.Pdf.FontSetup.AddSystemFonts(Telerik.Apoc.Render.Pdf.FontType)">
            <summary>
                Adds all the system fonts to the FontInfo object.
            </summary>
            <remarks>
                Adds metrics for basic fonts and useful family-style-weight
                triplets for lookup.
            </remarks>
            <param name="fontType">Determines what type of font to instantiate.</param>
        </member>
        <member name="M:Telerik.Apoc.Pdf.FontSetup.IsBase14FontName(System.String)">
            <summary>
                Returns <b>true</b> is <i>familyName</i> represents one of the 
                base 14 fonts; otherwise <b>false</b>.
            </summary>
            <param name="familyName"></param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Apoc.Pdf.FontSetup.GetNextAvailableName">
            <summary>
                Gets the next available font name.  A font name is defined as an 
                integer prefixed by the letter 'F'.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Telerik.Apoc.Pdf.FontSetup.AddToResources(Telerik.Apoc.Pdf.PdfFontCreator,Telerik.Pdf.PdfResources)">
            <summary>
                Add the fonts in the font info to the PDF document.
            </summary>
            <param name="fontCreator">Object that creates PdfFont objects.</param>
            <param name="resources">Resources object to add fonts too.</param>
        </member>
        <member name="T:Telerik.Apoc.Render.IRendererOptions">
            <summary>
                A marker interface to indicate an object can be passed to
                the <see cref="P:Telerik.Apoc.ApocDriver.Options"/> property.
            </summary>
            <remarks>
                <seealso cref="T:Telerik.Apoc.Render.Pdf.PdfRendererOptions"/>
            </remarks>
        </member>
        <member name="T:Telerik.Apoc.Render.Pdf.Fonts.Base14Font">
            <summary>
                Base class for the standard 14 fonts as defined in the PDF spec.
            </summary>
        </member>
        <member name="M:Telerik.Apoc.Render.Pdf.Fonts.Base14Font.#ctor(System.String,System.String,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32[],Telerik.Apoc.Render.Pdf.CodePointMapping)">
            <summary>
                Class constructor.
            </summary>
        </member>
        <member name="P:Telerik.Apoc.Render.Pdf.Fonts.Base14Font.Descriptor">
            <summary>
                Will always return null since the standard 14 fonts do not 
                have a FontDescriptor.
            </summary>
            <remarks>
                It is possible to override the default metrics, but the 
                current version of Apoc does not support this feature.
            </remarks>
        </member>
        <member name="T:Telerik.Apoc.Render.Pdf.Fonts.CIDFont">
            <summary>
                Base class for a CID (Character Indexed) font.
            </summary>
            <remarks>
                There are two types of CIDFont: Type 0 and Type 2.  A Type 0 CIDFont
                contains glyph description based on Adobe Type 1 font format; a 
                Type 2 CIDFont contains glyph descriptions based on the TrueType 
                font format.
                See page 338 of the Adode PDF 1.4 specification for futher details.
            </remarks>
        </member>
        <member name="P:Telerik.Apoc.Render.Pdf.Fonts.CIDFont.CidBaseFont">
            <summary>
                Gets the PostScript name of the font.
            </summary>
        </member>
        <member name="P:Telerik.Apoc.Render.Pdf.Fonts.CIDFont.CMapEntries">
            <summary>
                Gets a dictionary mapping character codes to unicode values
            </summary>
        </member>
        <member name="P:Telerik.Apoc.Render.Pdf.Fonts.CIDFont.Type">
            <summary>
                Returns <see cref="F:Telerik.Pdf.PdfFontTypeEnum.CIDFont"/>.
            </summary>
        </member>
        <member name="P:Telerik.Apoc.Render.Pdf.Fonts.CIDFont.Registry">
            <summary>
                Gets a string identifying the issuer of the character collections.
            </summary>
            <remarks>
                The default implementation returns <see cref="F:Telerik.Pdf.PdfCIDSystemInfo.DefaultRegistry"/>.
            </remarks>
        </member>
        <member name="P:Telerik.Apoc.Render.Pdf.Fonts.CIDFont.Ordering">
            <summary>
                Gets a string that uniquely names the character collection.
            </summary>
            <remarks>
                The default implementation returns <see cref="F:Telerik.Pdf.PdfCIDSystemInfo.DefaultOrdering"/>.
            </remarks>
        </member>
        <member name="P:Telerik.Apoc.Render.Pdf.Fonts.CIDFont.Supplement">
            <summary>
                Gets the supplement number of the character collection.
            </summary>
            <remarks>
                The default implementation returns <see cref="F:Telerik.Pdf.PdfCIDSystemInfo.DefaultSupplement"/>.
            </remarks>
        </member>
        <member name="P:Telerik.Apoc.Render.Pdf.Fonts.CIDFont.DefaultWidth">
            <summary>
                Gets the default width for all glyphs.
            </summary>
            <remarks>
                The default implementation returns <see cref="F:Telerik.Apoc.Render.Pdf.Fonts.CIDFont.DefaultWidthConst"/>
            </remarks>
        </member>
        <member name="T:Telerik.Apoc.Render.Pdf.Fonts.Font">
            <summary>
                Base class for PDF font classes
            </summary>
        </member>
        <member name="P:Telerik.Apoc.Render.Pdf.Fonts.Font.Encoding">
            <summary>
                Get the encoding of the font.
            </summary>
            <remarks>
                A font encoding defines a mapping between a character code 
                and a code point.  
            </remarks>
        </member>
        <member name="P:Telerik.Apoc.Render.Pdf.Fonts.Font.FontName">
            <summary>
                Gets the base font name.
            </summary>
            <returns></returns>
        </member>
        <member name="P:Telerik.Apoc.Render.Pdf.Fonts.Font.Type">
            <summary>
                Gets the type of font, e.g. Type 0, Type 1, etc.
            </summary>
            <returns></returns>
        </member>
        <member name="P:Telerik.Apoc.Render.Pdf.Fonts.Font.SubType">
            <summary>
                Gets the font subtype.
            </summary>
            <returns></returns>
        </member>
        <member name="P:Telerik.Apoc.Render.Pdf.Fonts.Font.Descriptor">
            <summary>
                Gets a reference to a FontDescriptor
            </summary>
        </member>
        <member name="P:Telerik.Apoc.Render.Pdf.Fonts.Font.MultiByteFont">
            <summary>
                Gets a boolean value indicating whether this font supports 
                multi-byte characters
            </summary>
        </member>
        <member name="M:Telerik.Apoc.Render.Pdf.Fonts.Font.MapCharacter(System.Char)">
            <summary>
                Maps a Unicode character to a character index.
            </summary>
            <param name="c">A Unicode character.</param>
            <returns></returns>
        </member>
        <member name="P:Telerik.Apoc.Render.Pdf.Fonts.Font.Ascender">
            <summary>
                See <see cref="P:Telerik.Apoc.Layout.IFontMetric.Ascender"/>
            </summary>
        </member>
        <member name="P:Telerik.Apoc.Render.Pdf.Fonts.Font.Descender">
            <summary>
                See <see cref="P:Telerik.Apoc.Layout.IFontMetric.Descender"/>
            </summary>
        </member>
        <member name="P:Telerik.Apoc.Render.Pdf.Fonts.Font.CapHeight">
            <summary>
                See <see cref="P:Telerik.Apoc.Layout.IFontMetric.CapHeight"/>
            </summary>
        </member>
        <member name="P:Telerik.Apoc.Render.Pdf.Fonts.Font.FirstChar">
            <summary>
                See <see cref="P:Telerik.Apoc.Layout.IFontMetric.FirstChar"/>
            </summary>
        </member>
        <member name="P:Telerik.Apoc.Render.Pdf.Fonts.Font.LastChar">
            <summary>
                See <see cref="P:Telerik.Apoc.Layout.IFontMetric.LastChar"/>
            </summary>
        </member>
        <member name="M:Telerik.Apoc.Render.Pdf.Fonts.Font.GetWidth(System.Int32)">
            <summary>
                See <see cref="M:Telerik.Apoc.Layout.IFontMetric.GetWidth(System.Int32)"/>
            </summary>
        </member>
        <member name="P:Telerik.Apoc.Render.Pdf.Fonts.Font.Widths">
            <summary>
                See <see cref="P:Telerik.Apoc.Layout.IFontMetric.Widths"/>
            </summary>
        </member>
        <member name="T:Telerik.Apoc.Render.Pdf.Fonts.FontDescriptorFlags">
            <summary>
                Represents a collection of font descriptor flags specifying 
                various characterisitics of a font.
            </summary>
            <remarks>
                The following lists the bit positions and associated flags:
                1  - FixedPitch
                2  - Serif
                3  - Symbolic
                4  - Script
                6  - Nonsymbolic
                7  - Italic
                17 - AllCap
                18 - SmallCap
                19 - ForceBold
            </remarks>
        </member>
        <member name="T:Telerik.Apoc.Render.Pdf.Fonts.FontDescriptorFlags.FontDescriptorFlagsEnum">
            <summary>
                Handy enumeration used to reference individual bit positions
                in the BitVector32.
            </summary>
        </member>
        <member name="M:Telerik.Apoc.Render.Pdf.Fonts.FontDescriptorFlags.#ctor">
            <summary>
                Default class constructor.
            </summary>
        </member>
        <member name="M:Telerik.Apoc.Render.Pdf.Fonts.FontDescriptorFlags.#ctor(System.Int32)">
            <summary>
                Class constructor.  Initialises the flags BitVector with the 
                supplied integer.
            </summary>
        </member>
        <member name="P:Telerik.Apoc.Render.Pdf.Fonts.FontDescriptorFlags.Flags">
            <summary>
                Gets the font descriptor flags as a 32-bit signed integer.
            </summary>
        </member>
        <member name="T:Telerik.Apoc.Render.Pdf.Fonts.FontProperties">
            <summary>
                Collection of font properties such as face name and whether the 
                a font is bold and/or italic.
            </summary>
        </member>
        <member name="M:Telerik.Apoc.Render.Pdf.Fonts.FontProperties.#ctor(System.String,System.Boolean,System.Boolean)">
            <summary>
                Class constructor.
            </summary>
            <remarks>
                Regular    : bold=false, italic=false
                Bold       : bold=true,  italic=false
                Italic     : bold=false, italic=true
                BoldItalic : bold=true,  italic=true
            </remarks>
            <param name="faceName">Font face name, e.g. Arial.</param>
            <param name="bold">Bold flag.</param>
            <param name="italic">Italic flag.</param>
        </member>
        <member name="T:Telerik.Apoc.Render.Pdf.Fonts.ProxyFont">
            <summary>
                A proxy object that delegates all operations to a concrete 
                subclass of the Font class.
            </summary>
        </member>
        <member name="F:Telerik.Apoc.Render.Pdf.Fonts.ProxyFont.fontLoaded">
            <summary>
                Flag that indicates whether the underlying font has been loaded.
            </summary>
        </member>
        <member name="F:Telerik.Apoc.Render.Pdf.Fonts.ProxyFont.properties">
            <summary>
                Font details such as face name, bold and italic flags
            </summary>
        </member>
        <member name="F:Telerik.Apoc.Render.Pdf.Fonts.ProxyFont.realFont">
            <summary>
                The font that does all the work.
            </summary>
        </member>
        <member name="F:Telerik.Apoc.Render.Pdf.Fonts.ProxyFont.fontType">
            <summary>
                Determines what type of "real" font to instantiate.
            </summary>
        </member>
        <member name="M:Telerik.Apoc.Render.Pdf.Fonts.ProxyFont.#ctor(Telerik.Apoc.Render.Pdf.Fonts.FontProperties,Telerik.Apoc.Render.Pdf.FontType)">
            <summary>
                Class constructor.
            </summary>
            <param name="properties"></param>
            <param name="fontType"></param>
        </member>
        <member name="M:Telerik.Apoc.Render.Pdf.Fonts.ProxyFont.LoadIfNecessary">
            <summary>
                Loads the underlying font.
            </summary>
        </member>
        <member name="P:Telerik.Apoc.Render.Pdf.Fonts.ProxyFont.RealFont">
            <summary>
                Gets the underlying font.
            </summary>
        </member>
        <member name="T:Telerik.Apoc.Render.Pdf.Fonts.TrueTypeFont">
            <summary>
                Represents a TrueType font program.
            </summary>
        </member>
        <member name="F:Telerik.Apoc.Render.Pdf.Fonts.TrueTypeFont.dc">
            <summary>
                Wrapper around a Win32 HDC.
            </summary>
        </member>
        <member name="F:Telerik.Apoc.Render.Pdf.Fonts.TrueTypeFont.metrics">
            <summary>
                Provides font metrics using the Win32 Api.
            </summary>
        </member>
        <member name="F:Telerik.Apoc.Render.Pdf.Fonts.TrueTypeFont.kerning">
            <summary>
                List of kerning pairs.
            </summary>
        </member>
        <member name="F:Telerik.Apoc.Render.Pdf.Fonts.TrueTypeFont.widths">
            <summary>
                Maps a glyph index to a PDF width
            </summary>
        </member>
        <member name="F:Telerik.Apoc.Render.Pdf.Fonts.TrueTypeFont.properties">
            <summary>
                
            </summary>
        </member>
        <member name="M:Telerik.Apoc.Render.Pdf.Fonts.TrueTypeFont.#ctor(Telerik.Apoc.Render.Pdf.Fonts.FontProperties)">
            <summary>
                Class constructor
            </summary>
            <param name="properties"></param>
        </member>
        <member name="M:Telerik.Apoc.Render.Pdf.Fonts.TrueTypeFont.ObtainFontMetrics">
            <summary>
                Creates a <see cref="T:Telerik.Pdf.Gdi.GdiFontMetrics"/> object from <b>baseFontName</b>
            </summary>
        </member>
        <member name="P:Telerik.Apoc.Render.Pdf.Fonts.TrueTypeFont.SubType">
            <summary>
                Returns <see cref="F:Telerik.Pdf.PdfFontSubTypeEnum.TrueType"/>.
            </summary>
        </member>
        <member name="M:Telerik.Apoc.Render.Pdf.Fonts.TrueTypeFont.GetWidth(System.Int32)">
            <summary>
                See <see cref="M:Telerik.Apoc.Render.Pdf.Fonts.Font.GetWidth(System.Int32)"/>
            </summary>
            <param name="charIndex">A WinAnsi codepoint.</param>
            <returns></returns>
        </member>
        <member name="T:Telerik.Apoc.Render.Pdf.Fonts.Type2CIDFont">
            <summary>
                A Type 2 CIDFont is a font whose glyph descriptions are based on the 
                TrueType font format.
            </summary>
            <remarks>
                TODO: Support font subsetting
            </remarks>
        </member>
        <member name="F:Telerik.Apoc.Render.Pdf.Fonts.Type2CIDFont.dc">
            <summary>
                Wrapper around a Win32 HDC.
            </summary>
        </member>
        <member name="F:Telerik.Apoc.Render.Pdf.Fonts.Type2CIDFont.metrics">
            <summary>
                Provides font metrics using the Win32 Api.
            </summary>
        </member>
        <member name="F:Telerik.Apoc.Render.Pdf.Fonts.Type2CIDFont.kerning">
            <summary>
                List of kerning pairs.
            </summary>
        </member>
        <member name="F:Telerik.Apoc.Render.Pdf.Fonts.Type2CIDFont.widths">
            <summary>
                Maps a glyph index to a PDF width
            </summary>
        </member>
        <member name="F:Telerik.Apoc.Render.Pdf.Fonts.Type2CIDFont.baseFontName">
            <summary>
                Windows font name, e.g. 'Arial Bold'
            </summary>
        </member>
        <member name="F:Telerik.Apoc.Render.Pdf.Fonts.Type2CIDFont.properties">
            <summary>
                
            </summary>
        </member>
        <member name="F:Telerik.Apoc.Render.Pdf.Fonts.Type2CIDFont.usedGlyphs">
            <summary>
                Maps a glyph index to a character code.
            </summary>
        </member>
        <member name="F:Telerik.Apoc.Render.Pdf.Fonts.Type2CIDFont.unicodeRanges">
            <summary>
                Maps character code to glyph index.  The array is based on the 
                value of <see cref="P:Telerik.Apoc.Render.Pdf.Fonts.Type2CIDFont.FirstChar"/>.
            </summary>
        </member>
        <member name="M:Telerik.Apoc.Render.Pdf.Fonts.Type2CIDFont.#ctor(Telerik.Apoc.Render.Pdf.Fonts.FontProperties)">
            <summary>
                Class constructor.
            </summary>
            <param name="properties"></param>
        </member>
        <member name="M:Telerik.Apoc.Render.Pdf.Fonts.Type2CIDFont.ObtainFontMetrics">
            <summary>
                Creates a <see cref="T:Telerik.Pdf.Gdi.GdiFontMetrics"/> object from <b>baseFontName</b>
            </summary>
        </member>
        <member name="M:Telerik.Apoc.Render.Pdf.Fonts.Type2CIDFont.Finalize">
            <summary>
                Class destructor.
            </summary>
        </member>
        <member name="P:Telerik.Apoc.Render.Pdf.Fonts.Type2CIDFont.SubType">
            <summary>
                Returns <see cref="F:Telerik.Pdf.PdfFontSubTypeEnum.CIDFontType2"/>.
            </summary>
        </member>
        <member name="T:Telerik.Apoc.Render.Pdf.Fonts.Type2CIDSubsetFont">
            <summary>
                A subclass of Type2CIDFont that generates a subset of a 
                TrueType font.
            </summary>
        </member>
        <member name="F:Telerik.Apoc.Render.Pdf.Fonts.Type2CIDSubsetFont.indexMappings">
            <summary>
                Maps a glyph index to a subset index.
            </summary>
        </member>
        <member name="F:Telerik.Apoc.Render.Pdf.Fonts.Type2CIDSubsetFont.namePrefix">
            <summary>
                Quasi-unique six character name prefix.
            </summary>
        </member>
        <member name="M:Telerik.Apoc.Render.Pdf.Fonts.Type2CIDSubsetFont.#ctor(Telerik.Apoc.Render.Pdf.Fonts.FontProperties)">
            <summary>
                Class constructor.
            </summary>
            <param name="properties"></param>
        </member>
        <member name="M:Telerik.Apoc.Render.Pdf.Fonts.Type2CIDSubsetFont.InsertNotdefGlyphs">
            <summary>
                Creates the index mappings list and adds the .notedef glyphs
            </summary>
        </member>
        <member name="T:Telerik.Apoc.Render.Pdf.FontType">
            <summary>
                Enumeration that dictates how Apoc should treat fonts when 
                producing a PDF document.
            </summary>
            <remarks>
                <p>Each of the three alernatives has particular advantages and 
                disadvantages, which will be explained here.</p>
                <p>The <see cref="F:Telerik.Apoc.Render.Pdf.FontType.Link"/> member specifies that all fonts 
                should be linked.  This option will produce the smallest PDF 
                document because the font program required to render individual 
                glyphs is not embedded in the PDF document.  However, this 
                option does possess two distinct disadvantages:
                <ol>
                  <li>Only characters in the WinAnsi character encoding are 
                  supported (i.e. Latin)</li>
                  <li>The PDF document will not render correctly if the linked 
                  font is not installed.</li>
                </ol>///     </p>
                <p>The <see cref="F:Telerik.Apoc.Render.Pdf.FontType.Embed"/> option will copy the contents of 
                the entire font program into the PDF document.  This will guarantee 
                correct rendering of the document on any system, however certain 
                fonts - especially CJK fonts - are extremely large.  The MS Gothic 
                TrueType collection, for example, is 8MB.  Embedding this font file 
                would produce a ridicuously large PDF.</p>
                <p>Finally, the <see cref="F:Telerik.Apoc.Render.Pdf.FontType.Subset"/> option will only copy the required 
                glyphs required to render a PDF document.  This option will ensure that 
                a PDF document is rendered correctly on any system, but does incur a 
                slight processing overhead to subset the font.</p>
            </remarks>
        </member>
        <member name="F:Telerik.Apoc.Render.Pdf.FontType.Link">
            <summary>
                Fonts are linked.
            </summary>
        </member>
        <member name="F:Telerik.Apoc.Render.Pdf.FontType.Embed">
            <summary>
                The entire font program is embedded.
            </summary>
        </member>
        <member name="F:Telerik.Apoc.Render.Pdf.FontType.Subset">
            <summary>
                The font program is subsetted and embedded.
            </summary>
        </member>
        <member name="F:Telerik.Apoc.Render.Pdf.PdfRenderer.currentYPosition">
            <summary>
                The current vertical position in millipoints from bottom.
            </summary>
        </member>
        <member name="F:Telerik.Apoc.Render.Pdf.PdfRenderer.currentXPosition">
            <summary>
                The current horizontal position in millipoints from left.
            </summary>
        </member>
        <member name="F:Telerik.Apoc.Render.Pdf.PdfRenderer.currentAreaContainerXPosition">
            <summary>
                The horizontal position of the current area container.
            </summary>
        </member>
        <member name="F:Telerik.Apoc.Render.Pdf.PdfRenderer.pdfDoc">
            <summary>
                The PDF Document being created.
            </summary>
        </member>
        <member name="F:Telerik.Apoc.Render.Pdf.PdfRenderer.pdfResources">
            <summary>
                The /Resources object of the PDF document being created.
            </summary>
        </member>
        <member name="F:Telerik.Apoc.Render.Pdf.PdfRenderer.currentStream">
            <summary>
                The current stream to add PDF commands to.
            </summary>
        </member>
        <member name="F:Telerik.Apoc.Render.Pdf.PdfRenderer.currentAnnotList">
            <summary>
                The current annotation list to add annotations to.
            </summary>
        </member>
        <member name="F:Telerik.Apoc.Render.Pdf.PdfRenderer.currentPage">
            <summary>
                The current page to add annotations to.
            </summary>
        </member>
        <member name="F:Telerik.Apoc.Render.Pdf.PdfRenderer.textOpen">
            <summary>
                True if a TJ command is left to be written.
            </summary>
        </member>
        <member name="F:Telerik.Apoc.Render.Pdf.PdfRenderer.prevWordY">
            <summary>
                The previous Y coordinate of the last word written.
            </summary>
            <remarks>
                Used to decide if we can draw the next word on the same line.
            </remarks>
        </member>
        <member name="F:Telerik.Apoc.Render.Pdf.PdfRenderer.prevWordX">
            <summary>
                The previous X coordinate of the last word written.
            </summary>
            <remarks>
                Used to calculate how much space between two words.
            </remarks>
        </member>
        <member name="F:Telerik.Apoc.Render.Pdf.PdfRenderer.prevWordWidth">
            <summary>
            The  width of the previous word.
            </summary>
            <remarks>
                Used to calculate space between.
            </remarks>
        </member>
        <member name="F:Telerik.Apoc.Render.Pdf.PdfRenderer._wordAreaPDF">
            <summary>
                Reusable word area string buffer to reduce memory usage.
            </summary>
            <remarks>
                TODO: remove use of this.
            </remarks>
        </member>
        <member name="F:Telerik.Apoc.Render.Pdf.PdfRenderer.options">
            <summary>
                User specified rendering options.
            </summary>
        </member>
        <member name="F:Telerik.Apoc.Render.Pdf.PdfRenderer.currentFontName">
            <summary>
                The current (internal) font name.
            </summary>
        </member>
        <member name="F:Telerik.Apoc.Render.Pdf.PdfRenderer.currentFontSize">
            <summary>
                The current font size in millipoints.
            </summary>
        </member>
        <member name="F:Telerik.Apoc.Render.Pdf.PdfRenderer.currentFill">
            <summary>
                The current color/gradient to fill shapes with.
            </summary>
        </member>
        <member name="F:Telerik.Apoc.Render.Pdf.PdfRenderer.prevUnderlineXEndPos">
            <summary>
                Previous values used for text-decoration drawing.
            </summary>
        </member>
        <member name="F:Telerik.Apoc.Render.Pdf.PdfRenderer.prevUnderlineYEndPos">
            <summary>
                Previous values used for text-decoration drawing.
            </summary>
        </member>
        <member name="F:Telerik.Apoc.Render.Pdf.PdfRenderer.prevUnderlineSize">
            <summary>
                Previous values used for text-decoration drawing.
            </summary>
        </member>
        <member name="F:Telerik.Apoc.Render.Pdf.PdfRenderer.prevUnderlineColor">
            <summary>
                Previous values used for text-decoration drawing.
            </summary>
        </member>
        <member name="F:Telerik.Apoc.Render.Pdf.PdfRenderer.prevOverlineXEndPos">
            <summary>
                Previous values used for text-decoration drawing.
            </summary>
        </member>
        <member name="F:Telerik.Apoc.Render.Pdf.PdfRenderer.prevOverlineYEndPos">
            <summary>
                Previous values used for text-decoration drawing.
            </summary>
        </member>
        <member name="F:Telerik.Apoc.Render.Pdf.PdfRenderer.prevOverlineSize">
            <summary>
                Previous values used for text-decoration drawing.
            </summary>
        </member>
        <member name="F:Telerik.Apoc.Render.Pdf.PdfRenderer.prevOverlineColor">
            <summary>
                Previous values used for text-decoration drawing.
            </summary>
        </member>
        <member name="F:Telerik.Apoc.Render.Pdf.PdfRenderer.prevLineThroughXEndPos">
            <summary>
                Previous values used for text-decoration drawing.
            </summary>
        </member>
        <member name="F:Telerik.Apoc.Render.Pdf.PdfRenderer.prevLineThroughYEndPos">
            <summary>
                Previous values used for text-decoration drawing.
            </summary>
        </member>
        <member name="F:Telerik.Apoc.Render.Pdf.PdfRenderer.prevLineThroughSize">
            <summary>
                Previous values used for text-decoration drawing.
            </summary>
        </member>
        <member name="F:Telerik.Apoc.Render.Pdf.PdfRenderer.prevLineThroughColor">
            <summary>
                Previous values used for text-decoration drawing.
            </summary>
        </member>
        <member name="F:Telerik.Apoc.Render.Pdf.PdfRenderer.fontInfo">
            <summary>
                Provides triplet to font resolution.
            </summary>
        </member>
        <member name="F:Telerik.Apoc.Render.Pdf.PdfRenderer.fontSetup">
            <summary>
                Handles adding base 14 and all system fonts.
            </summary>
        </member>
        <member name="F:Telerik.Apoc.Render.Pdf.PdfRenderer.idReferences">
            <summary>
                The IDReferences for this document.
            </summary>
        </member>
        <member name="M:Telerik.Apoc.Render.Pdf.PdfRenderer.#ctor(System.IO.Stream)">
            <summary>
                Create the PDF renderer.
            </summary>
        </member>
        <member name="P:Telerik.Apoc.Render.Pdf.PdfRenderer.Options">
            <summary>
                Assigns renderer options to this PdfRenderer
            </summary>
            <remarks>
                This property will only accept an instance of the PdfRendererOptions class
            </remarks>
            <exception cref="T:System.ArgumentException">
                If <i>value</i> is not an instance of PdfRendererOptions
            </exception>
        </member>
        <member name="M:Telerik.Apoc.Render.Pdf.PdfRenderer.SetupFontInfo(Telerik.Apoc.Layout.FontInfo)">
            <summary>
            </summary>
            <param name="fontInfo"></param>
        </member>
        <member name="M:Telerik.Apoc.Render.Pdf.PdfRenderer.AddLine(System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,Telerik.Apoc.Render.Pdf.PdfColor)">
             add a line to the current stream
            
             @param x1 the start x location in millipoints
             @param y1 the start y location in millipoints
             @param x2 the end x location in millipoints
             @param y2 the end y location in millipoints
             @param th the thickness in millipoints
             @param r the red component
             @param g the green component
             @param b the blue component
        </member>
        <member name="M:Telerik.Apoc.Render.Pdf.PdfRenderer.AddLine(System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,Telerik.Apoc.Render.Pdf.PdfColor)">
             add a line to the current stream
            
             @param x1 the start x location in millipoints
             @param y1 the start y location in millipoints
             @param x2 the end x location in millipoints
             @param y2 the end y location in millipoints
             @param th the thickness in millipoints
             @param rs the rule style
             @param r the red component
             @param g the green component
             @param b the blue component
        </member>
        <member name="M:Telerik.Apoc.Render.Pdf.PdfRenderer.AddRect(System.Int32,System.Int32,System.Int32,System.Int32,Telerik.Apoc.Render.Pdf.PdfColor)">
             add a rectangle to the current stream
            
             @param x the x position of left edge in millipoints
             @param y the y position of top edge in millipoints
             @param w the width in millipoints
             @param h the height in millipoints
             @param stroke the stroke color/gradient
        </member>
        <member name="M:Telerik.Apoc.Render.Pdf.PdfRenderer.AddRect(System.Int32,System.Int32,System.Int32,System.Int32,Telerik.Apoc.Render.Pdf.PdfColor,Telerik.Apoc.Render.Pdf.PdfColor)">
             add a filled rectangle to the current stream
            
             @param x the x position of left edge in millipoints
             @param y the y position of top edge in millipoints
             @param w the width in millipoints
             @param h the height in millipoints
             @param fill the fill color/gradient
             @param stroke the stroke color/gradient
        </member>
        <member name="M:Telerik.Apoc.Render.Pdf.PdfRenderer.AddFilledRect(System.Int32,System.Int32,System.Int32,System.Int32,Telerik.Apoc.Render.Pdf.PdfColor)">
             add a filled rectangle to the current stream
            
             @param x the x position of left edge in millipoints
             @param y the y position of top edge in millipoints
             @param w the width in millipoints
             @param h the height in millipoints
             @param fill the fill color/gradient
        </member>
        <member name="M:Telerik.Apoc.Render.Pdf.PdfRenderer.RenderImageArea(Telerik.Apoc.Image.ImageArea)">
             render image area to PDF
            
             @param area the image area to render
        </member>
        <member name="M:Telerik.Apoc.Render.Pdf.PdfRenderer.RenderForeignObjectArea(Telerik.Apoc.Layout.Inline.ForeignObjectArea)">
            render a foreign object area
        </member>
        <member name="M:Telerik.Apoc.Render.Pdf.PdfRenderer.RenderWordArea(Telerik.Apoc.Layout.Inline.WordArea)">
             render inline area to PDF
            
             @param area inline area to render
        </member>
        <member name="M:Telerik.Apoc.Render.Pdf.PdfRenderer.GetUnicodeString(System.Int32)">
            Convert a char to a multibyte hex representation
        </member>
        <member name="M:Telerik.Apoc.Render.Pdf.PdfRenderer.CloseText">
            Checks to see if we have some text rendering commands open
            still and writes out the TJ command to the stream if we do
        </member>
        <member name="M:Telerik.Apoc.Render.Pdf.PdfRenderer.RenderPage(Telerik.Apoc.Layout.Page)">
             render page into PDF
            
             @param page page to render
        </member>
        <member name="M:Telerik.Apoc.Render.Pdf.PdfRenderer.SetRuleStylePattern(System.Int32)">
            defines a string containing dashArray and dashPhase for the rule style
        </member>
        <member name="M:Telerik.Apoc.Render.Pdf.PdfRenderer.DoBackground(Telerik.Apoc.Layout.Area,System.Int32,System.Int32,System.Int32,System.Int32)">
            <summary>
                Renders an area's background.
            </summary>
            <param name="area">The area whose background is to be rendered.</param>
            <param name="x">The x position of the left edge in millipoints.</param>
            <param name="y">The y position of top edge in millipoints.</param>
            <param name="w">The width in millipoints.</param>
            <param name="h">The height in millipoints.</param>
        </member>
        <member name="M:Telerik.Apoc.Render.Pdf.PdfRenderer.DrawImage(System.Int32,System.Int32,Telerik.Apoc.Image.ApocImage)">
            <summary>
                Renders an image, rendered at the image's intrinsic size.
                This by default calls drawImageScaled() with the image's
                intrinsic width and height, but implementations may
                override this method if it can provide a more efficient solution.
            </summary>
            <param name="x">The x position of left edge in millipoints.</param>
            <param name="y">The y position of top edge in millipoints.</param>
            <param name="image">The image to be rendered.</param>
        </member>
        <member name="M:Telerik.Apoc.Render.Pdf.PdfRenderer.DrawImageScaled(System.Int32,System.Int32,System.Int32,System.Int32,Telerik.Apoc.Image.ApocImage)">
            <summary>
                Renders an image, scaling it to the given width and height.
                If the scaled width and height is the same intrinsic size 
                of the image, the image is not scaled
            </summary>
            <param name="x">The x position of left edge in millipoints.</param>
            <param name="y">The y position of top edge in millipoints.</param>
            <param name="w">The width in millipoints.</param>
            <param name="h">The height in millipoints.</param>
            <param name="image">The image to be rendered.</param>
        </member>
        <member name="M:Telerik.Apoc.Render.Pdf.PdfRenderer.DrawImageClipped(System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,System.Int32,Telerik.Apoc.Image.ApocImage)">
            <summary>
                Renders an image, clipping it as specified.
            </summary>
            <param name="x">The x position of left edge in millipoints.</param>
            <param name="y">The y position of top edge in millipoints.</param>
            <param name="clipX">The left edge of the clip in millipoints.</param>
            <param name="clipY">The top edge of the clip in millipoints.</param>
            <param name="clipW">The clip width in millipoints.</param>
            <param name="clipH">The clip height in millipoints.</param>
            <param name="image">The image to be rendered.</param>
        </member>
        <member name="M:Telerik.Apoc.Render.Pdf.PdfRenderer.RenderDisplaySpace(Telerik.Apoc.Layout.DisplaySpace)">
             render display space
            
             @param space the display space to render
        </member>
        <member name="M:Telerik.Apoc.Render.Pdf.PdfRenderer.RenderInlineSpace(Telerik.Apoc.Layout.Inline.InlineSpace)">
             render inline space
            
             @param space space to render
        </member>
        <member name="M:Telerik.Apoc.Render.Pdf.PdfRenderer.RenderLeaderArea(Telerik.Apoc.Layout.Inline.LeaderArea)">
             render leader area
            
             @param area area to render
        </member>
        <member name="T:Telerik.Apoc.Render.Pdf.PdfRendererOptions">
            <summary>
                This class can be used to control various properties of PDF files
                created by Apoc XSL-FO.
            </summary>
            <remarks>
                Can be used to control certain values in the generated PDF's information
                dictionary.  These values are typically displayed in a document summary 
                dialog of PDF viewer applications.
                This class also allows security settings to be specified that will 
                cause generated PDF files to be encrypted and optionally password protected.
            </remarks>
        </member>
        <member name="F:Telerik.Apoc.Render.Pdf.PdfRendererOptions.permissions">
            <remarks>
                The given initial value zero's out first two bits.
                The PDF specification dictates that these entries must be 0.
            </remarks>
        </member>
        <member name="P:Telerik.Apoc.Render.Pdf.PdfRendererOptions.Title">
            <summary>
                Specifies the Title of the PDF document.
            </summary>
            <value>
                The default value is null.
            </value>
            <remarks>
                This value will be embedded in the PDF information dictionary.
            </remarks>
        </member>
        <member name="P:Telerik.Apoc.Render.Pdf.PdfRendererOptions.Subject">
            <summary>
                Specifies the Subject of the PDF document.
            </summary>
            <value>
                The default value is null.
            </value>
            <remarks>
                This value will be embedded in the PDF information dictionary.
            </remarks>
        </member>
        <member name="P:Telerik.Apoc.Render.Pdf.PdfRendererOptions.Author">
            <summary>
                Specifies the Author of the PDF document.
            </summary>
            <value>
                The default value is null.
            </value>
            <remarks>
                This value will be embedded in the PDF information dictionary.
            </remarks>
        </member>
        <member name="P:Telerik.Apoc.Render.Pdf.PdfRendererOptions.Creator">
            <summary>
                Returns the Creator of the PDF document.
            </summary>
            <value>
                This method will always return "XSL-FO http://www.w3.org/1999/XSL/Format".
            </value>
        </member>
        <member name="P:Telerik.Apoc.Render.Pdf.PdfRendererOptions.Producer">
            <summary>
                Returns the Producer of the PDF document.
            </summary>
            <value>
                This method will return the assembly name and version of Apoc.
            </value>
        </member>
        <member name="P:Telerik.Apoc.Render.Pdf.PdfRendererOptions.Keywords">
            <summary>
                Returns a list of keywords as a comma-separated string
            </summary>
            <value>
                If no keywords exist the empty string <see cref="F:System.String.Empty"/> is returned
            </value>
        </member>
        <member name="M:Telerik.Apoc.Render.Pdf.PdfRendererOptions.AddKeyword(System.String)">
            <summary>
                Adds a keyword to the PDF document.
            </summary>
            <remarks>
                Keywords are embedded in the PDF information dictionary.
            </remarks>
            <param name="keyword">The keyword to be added.</param>
        </member>
        <member name="P:Telerik.Apoc.Render.Pdf.PdfRendererOptions.OwnerPassword">
            <summary>
                Specifies the owner password that will protect full access to any generated PDF documents.
            </summary>
            <remarks>
                If either the owner or the user password is specified, 
                then the document will be encrypted.
            </remarks>
            <value>
                The default value is null.
            </value>
        </member>
        <member name="P:Telerik.Apoc.Render.Pdf.PdfRendererOptions.UserPassword">
            <summary>
                Specifies the user password that will protect access to any generated PDF documents.
            </summary>
            <remarks>
                If either the owner or the user password is specified, 
                then the document will be encrypted.
            </remarks>
            <value>
                The default value is null.
            </value>
        </member>
        <member name="P:Telerik.Apoc.Render.Pdf.PdfRendererOptions.HasPermissions">
            <summary>
                Returns true if any permissions have been set.
            </summary>
        </member>
        <member name="P:Telerik.Apoc.Render.Pdf.PdfRendererOptions.Permissions">
            <summary>
                Returns the PDF permissions encoded as an 32-bit integer.
            </summary>
        </member>
        <member name="P:Telerik.Apoc.Render.Pdf.PdfRendererOptions.EnablePrinting">
            <summary>
                Enables or disables printing.
            </summary>
            <value>
                The default value is true.
            </value>
        </member>
        <member name="P:Telerik.Apoc.Render.Pdf.PdfRendererOptions.EnableModify">
            <summary>
                Enables or disables modifying document contents (other than text annotations and 
                interactive form fields).
            </summary>
            <value>
                The default value is true.
            </value>
        </member>
        <member name="P:Telerik.Apoc.Render.Pdf.PdfRendererOptions.EnableCopy">
            <summary>
                Enables or disables copying of text and graphics.
            </summary>
            <value>
                The default value is true.
            </value>
        </member>
        <member name="P:Telerik.Apoc.Render.Pdf.PdfRendererOptions.EnableAdd">
            <summary>
                Enables or disables adding or modifying text annotations and interactive
                form fields.
            </summary>
            <value>
                The default value is true.
            </value>
        </member>
        <member name="P:Telerik.Apoc.Render.Pdf.PdfRendererOptions.FontType">
            <summary>
                Specifies how Apoc should treat fonts.
            </summary>
            <value>
                The default value is FontType.Link
            </value>
        </member>
        <member name="P:Telerik.Apoc.Render.Pdf.PdfRendererOptions.Kerning">
            <summary>
                Gets or sets a value that indicates whether to enable kerning.
            </summary>
            <value>
                The default value is <b>false</b>
            </value>
        </member>
        <member name="M:Telerik.Apoc.Render.Pdf.PdfRendererOptions.AddPrivateFont(System.IO.FileInfo)">
            <summary>
                Adds <i>fileInfo</i> to the private font collection.
            </summary>
            <param name="fileInfo">
                Absolute path to a TrueType font or collection.
            </param>
            <exception cref="T:System.ArgumentNullException">
                If <i>fileInfo</i> is null.
            </exception>
            <exception cref="T:System.IO.FileNotFoundException">
                If <i>fileInfo</i> does not exist.
            </exception>
            <exception cref="T:System.ArgumentException">
                If <i>fileInfo</i> has already been added.
            </exception>
            <exception cref="T:System.ArgumentException">
                If <i>fileInfo</i> cannot be added to the system font collection.
            </exception>
        </member>
        <member name="T:Telerik.Apoc.Render.RendererEngine">
            <summary>
                Specifies the output format that Apoc XSL-FO should render to.
            </summary>
            <remarks>
                Currently the only useful format supported is PDF.  The
                XML format is intended for informational/debugging purposes
                only.
                <seealso cref="P:Telerik.Apoc.ApocDriver.Renderer"/>
            </remarks>
        </member>
        <member name="F:Telerik.Apoc.Render.RendererEngine.XML">
            <summary>
                Instructs Apoc to output an XML representation.
            </summary>
            <remarks>
                This format is useful only for informational/debugging purposes.
            </remarks>
        </member>
        <member name="F:Telerik.Apoc.Render.RendererEngine.PDF">
            <summary>
                Instructs Apoc to output PDF.
            </summary>
        </member>
        <member name="T:Telerik.Apoc.Render.Xml.XmlRendererOptions">
            <summary>
                This class can be used to control various properties of PDF files
                created by the XML tree renderer.
            </summary>
        </member>
        <member name="F:Telerik.Apoc.Render.Xml.XmlRendererOptions.Default">
            <summary>
                Default XML renderer properties
            </summary>
        </member>
        <member name="P:Telerik.Apoc.Render.Xml.XmlRendererOptions.FineDetail">
            <summary>
                Determines if the XMLRenderer should use verbose output
            </summary>
        </member>
        <member name="T:Telerik.Apoc.StreamRenderer">
            <summary>
                This class acts as a bridge between the XML:FO parser and the 
                formatting/rendering classes. It will queue PageSequences up until 
                all the IDs required by them are satisfied, at which time it will 
                render the pages.
                StreamRenderer is created by Driver and called from FOTreeBuilder 
                when a PageSequence is created, and AreaTree when a Page is formatted.
            </summary>
        </member>
        <member name="F:Telerik.Apoc.StreamRenderer.pageCount">
            <summary>
                Keep track of the number of pages rendered.
            </summary>
        </member>
        <member name="F:Telerik.Apoc.StreamRenderer.renderer">
            <summary>
                The renderer being used.
            </summary>
        </member>
        <member name="F:Telerik.Apoc.StreamRenderer.results">
            <summary>
                The formatting results to be handed back to the caller.
            </summary>
        </member>
        <member name="F:Telerik.Apoc.StreamRenderer.fontInfo">
            <summary>
                The FontInfo for this renderer.
            </summary>
        </member>
        <member name="F:Telerik.Apoc.StreamRenderer.renderQueue">
            <summary>
                The list of pages waiting to be renderered.
            </summary>
        </member>
        <member name="F:Telerik.Apoc.StreamRenderer.idReferences">
            <summary>
                The current set of IDReferences, passed to the areatrees 
                and pages. This is used by the AreaTree as a single map of 
                all IDs.
            </summary>
        </member>
        <member name="F:Telerik.Apoc.StreamRenderer.extensions">
            <summary>
                The list of extensions.
            </summary>
        </member>
        <member name="F:Telerik.Apoc.StreamRenderer.documentMarkers">
            <summary>
                The list of markers.
            </summary>
        </member>
        <member name="M:Telerik.Apoc.StreamRenderer.Render(Telerik.Apoc.Fo.Pagination.PageSequence)">
            <summary>
                Format the PageSequence. The PageSequence formats Pages and adds 
                them to the AreaTree, which subsequently calls the StreamRenderer
                instance (this) again to render the page.  At this time the page 
                might be printed or it might be queued. A page might not be 
                renderable immediately if the IDReferences are not all valid. In 
                this case we defer the rendering until they are all valid.
            </summary>
            <param name="pageSequence"></param>
        </member>
        <member name="M:Telerik.Apoc.StreamRenderer.ProcessQueue(System.Boolean)">
            <summary>
                Try to process the queue from the first entry forward.  If an 
                entry can't be processed, then the queue can't move forward, 
                so return.
            </summary>
            <param name="force"></param>
        </member>
        <member name="T:Telerik.Apoc.StreamRenderer.RenderQueueEntry">
            <summary>
                A RenderQueueEntry consists of the Page to be queued, plus a list 
                of outstanding ID references that need to be resolved before the 
                Page can be renderered.
            </summary>
        </member>
        <member name="F:Telerik.Apoc.StreamRenderer.RenderQueueEntry.page">
            <summary>
                The Page that has outstanding ID references.
            </summary>
        </member>
        <member name="F:Telerik.Apoc.StreamRenderer.RenderQueueEntry.outer">
            <summary>
                MG - to replace the outer this Java nonsense */
            </summary>
        </member>
        <member name="F:Telerik.Apoc.StreamRenderer.RenderQueueEntry.unresolvedIdReferences">
            <summary>
                A list of ID references (names).
            </summary>
        </member>
        <member name="M:Telerik.Apoc.StreamRenderer.RenderQueueEntry.isResolved">
            <summary>
                See if the outstanding references are resolved in the current 
                copy of IDReferences.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Telerik.Apoc.StreamRenderer.GetDocumentMarkers">
            <summary>
                Auxillary function for retrieving markers.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Telerik.Apoc.StreamRenderer.GetCurrentPageSequence">
            <summary>
                Auxillary function for retrieving markers.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Telerik.Apoc.StreamRenderer.GetCurrentPageSequenceMarkers">
            <summary>
                Auxillary function for retrieving markers.
            </summary>
            <returns></returns>
        </member>
        <member name="T:Telerik.Apoc.TempFileStream">
            <summary>
                A subclass of FileStream that automatically deletes the file supplied 
                to the class constructor when Close() is invoked
            </summary>
        </member>
        <member name="M:Telerik.Apoc.TempFileStream.#ctor(System.String)">
            <summary>
                Default class constuctor that opens the supplied file <i>filename</i>
                using the FileMode.Open parameter.<br/>
                <see cref="T:System.IO.FileMode"/>
            </summary>
        </member>
        <member name="M:Telerik.Apoc.TempFileStream.Close">
            <summary>
                Overriden.  Attempts to delete the filename supplied to the 
                class constructor using <see cref="M:System.IO.File.Delete(System.String)"/>.  If an 
                exception is thrown, it is caught and ignored
            </summary>
        </member>
        <member name="F:Telerik.Apoc.Util.StringTokenizer.maxDelimChar">
            <summary>
                maxDelimChar stores the value of the delimiter character with 
                the highest value. It is used to optimize the detection of 
                delimiter characters.
            </summary>
        </member>
        <member name="M:Telerik.Apoc.Util.StringTokenizer.SetMaxDelimChar">
            <summary>
                Set maxDelimChar to the highest char in the delimiter set.
            </summary>
        </member>
        <member name="M:Telerik.Apoc.Util.StringTokenizer.#ctor(System.String,System.String,System.Boolean)">
            <summary>
                Constructs a string tokenizer for the specified string. All 
                characters in the <i>delim</i> argument are the delimiters 
                for separating tokens.<br/>
                If the <i>returnDelims</i> flag is <i>true</i>, then 
                the delimiter characters are also returned as tokens. Each delimiter 
                is returned as a string of length one. If the flag is 
                <i>false</i>, the delimiter characters are skipped and only 
                serve as separators between tokens. 
            </summary>
            <param name="str"></param>
            <param name="delim"></param>
            <param name="returnDelims"></param>
        </member>
        <member name="M:Telerik.Apoc.Util.StringTokenizer.#ctor(System.String,System.String)">
            <summary>
                Constructs a string tokenizer for the specified string. The 
                characters in the <code>delim</code> argument are the delimiters 
                for separating tokens. Delimiter characters themselves will not 
                be treated as tokens.
            </summary>
            <param name="str">a string to be parsed.</param>
            <param name="delim">the delimiters.</param>
        </member>
        <member name="M:Telerik.Apoc.Util.StringTokenizer.#ctor(System.String)">
            <summary>
                Constructs a string tokenizer for the specified string. The 
                tokenizer uses the default delimiter set, which is the space 
                character, the tab character, the newline character, the 
                carriage-return character, and the form-feed character. 
                Delimiter characters themselves will not be treated as tokens.
            </summary>
            <param name="str">a string to be parsed</param>
        </member>
        <member name="M:Telerik.Apoc.Util.StringTokenizer.SkipDelimiters(System.Int32)">
            <summary>
                Skips delimiters starting from the specified position. If 
                retDelims is false, returns the index of the first non-delimiter 
                character at or after startPos. If retDelims is true, startPos 
                is returned.
            </summary>
            <param name="startPos"></param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Apoc.Util.StringTokenizer.ScanToken(System.Int32)">
            <summary>
                Skips ahead from startPos and returns the index of the next 
                delimiter character encountered, or maxPosition if no such 
                delimiter is found.
            </summary>
            <param name="startPos"></param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Apoc.Util.StringTokenizer.NextToken">
            <summary>
                Returns the next token from this string tokenizer.
            </summary>
            <returns>the next token from this string tokenizer.</returns>
        </member>
        <member name="M:Telerik.Apoc.Util.StringTokenizer.MoveNext">
            <summary>
                Returns the same value as the <code>hasMoreTokens</code> method. 
                It exists so that this class can implement the 
                <i>Enumeration</i> interface. 
            </summary>
            <returns>
            <i>true</i> if there are more tokens; <i>false</i> 
            otherwise.</returns>
        </member>
        <member name="M:Telerik.Apoc.Util.StringTokenizer.HasMoreTokens">
            <summary>
            Tests if there are more tokens available from this tokenizer's 
            string.  If this method returns <tt>true</tt>, then a subsequent 
            call to <tt>nextToken</tt> with no argument will successfully 
            return a token.
            </summary>
            <returns>
            <code>true</code> if and only if there is at least one token in 
            the string after the current position; <code>false</code> otherwise.
            </returns>
        </member>
        <member name="P:Telerik.Apoc.Util.StringTokenizer.Current">
            <summary>
            Returns the same value as the <code>nextToken</code> method, except 
            that its declared return value is <code>Object</code> rather than 
            <code>String</code>. It exists so that this class can implement the
            <code>Enumeration</code> interface. 
            </summary>
        </member>
        <member name="M:Telerik.Apoc.Util.StringTokenizer.CountTokens">
            <summary>
            Calculates the number of times that this tokenizer's 
            <code>nextToken</code> method can be called before it generates an 
            exception. The current position is not advanced.
            </summary>
            <returns>
            the number of tokens remaining in the string using the current 
            delimiter set.</returns>
        </member>
        <member name="T:Telerik.Apoc.XslTransformer">
            <summary>
                Provides a static method that applies an 
                XSL stylesheet to an XML document
            </summary>
        </member>
        <member name="M:Telerik.Apoc.XslTransformer.#ctor">
            <summary>
                Private constructor to prevent instantiation
            </summary>
        </member>
        <member name="M:Telerik.Apoc.XslTransformer.Transform(System.String,System.String)">
            <summary>
                Applies the style sheet <i>xslFile</i> to the XML document 
                identified by <i>xmlFile</i>.    
            </summary>
            <param name="xmlFile">Path to an XML document</param>
            <param name="xslFile">Path to an XSL stylesheet</param>
            <returns>A Stream representing a sequence of XSL:FO elements</returns>
            <exception cref="T:Telerik.Apoc.ApocException">
                The files <i>xmlFile</i> and <i>xslFile</i> do not exist or are 
                inaccessible.  The XSL file cannot be compiled
            </exception>
            <remarks>
                This method will create a temporary filename in the system's 
                temporary directory, which is automatically deleted when the 
                returned stream is closed.
                <seealso cref="T:System.Xml.Xsl.XslTransform"/>
            </remarks>
        </member>
        <member name="T:Telerik.Pdf.BfEntryList">
            <summary>
                A collection of <see cref="T:Telerik.Pdf.BfEntry"/> instances.
            </summary>
        </member>
        <member name="M:Telerik.Pdf.BfEntryList.Add(Telerik.Pdf.BfEntry)">
            <summary>
                Adds the supplied <see cref="T:Telerik.Pdf.BfEntry"/> to the end of the collection.
            </summary>
            <param name="entry"></param>
        </member>
        <member name="P:Telerik.Pdf.BfEntryList.Item(System.Int32)">
            <summary>
                Gets the <see cref="T:Telerik.Pdf.BfEntry"/> at <i>index</i>.
            </summary>
        </member>
        <member name="P:Telerik.Pdf.BfEntryList.Count">
            <summary>
                Gets the number of <see cref="T:Telerik.Pdf.BfEntry"/> objects contained by this 
                <see cref="T:Telerik.Pdf.BfEntryList"/>
            </summary>
        </member>
        <member name="P:Telerik.Pdf.BfEntryList.NumRanges">
            <summary>
                Returns the number of <see cref="T:Telerik.Pdf.BfEntry"/> instances that 
                represent bfrange's
            </summary>
            <returns></returns>
        </member>
        <member name="P:Telerik.Pdf.BfEntryList.Ranges">
            <summary>
                
            </summary>
        </member>
        <member name="P:Telerik.Pdf.BfEntryList.NumChars">
            <summary>
                Returns the number of <see cref="T:Telerik.Pdf.BfEntry"/> instances that 
                represent bfchar's
            </summary>
            <returns></returns>
        </member>
        <member name="P:Telerik.Pdf.BfEntryList.Chars">
            <summary>
                
            </summary>
        </member>
        <member name="M:Telerik.Pdf.BfEntryList.GetEnumerator">
            <summary>
                Returns an ArrayList enumerator that references a read-only version
                of the BfEntry list.
            </summary>
            <returns></returns>
        </member>
        <member name="T:Telerik.Pdf.BfEntry">
            <summary>
                A <see cref="T:Telerik.Pdf.BfEntry"/> class can represent either a bfrange 
                or bfchar.
            </summary>
        </member>
        <member name="M:Telerik.Pdf.BfEntry.#ctor(System.Int32,System.Int32)">
            <summary>
                Class cosntructor.
            </summary>
            <param name="startIndex"></param>
            <param name="unicodeValue"></param>
        </member>
        <member name="M:Telerik.Pdf.BfEntry.IncrementEndIndex">
            <summary>
                Increments the end index by one.
            </summary>
            <remarks>
                Incrementing the end index turns this BfEntry into a bfrange.
            </remarks>
        </member>
        <member name="P:Telerik.Pdf.BfEntry.IsRange">
            <summary>
                Returns <b>true</b> if this BfEntry represents a glyph range, i.e.
                the start index is not equal to the end index.
            </summary>
        </member>
        <member name="P:Telerik.Pdf.BfEntry.IsChar">
            <summary>
                Returns <b>true</b> if this BfEntry represents a bfchar entry, i.e.
                the start index is equal to the end index.
            </summary>
        </member>
        <member name="T:Telerik.Pdf.FileIdentifier">
            <summary>
                A File Identifier is described in section 8.3 of the PDF specification.
                The first string is a permanent identifier based on the contents of the file 
                at the time it was originally created, and does not change as the file is 
                incrementally updated.  The second string is a changing identifier based 
                on the file's contents the last time it was updated.
            </summary>
            <remarks>
                If this class were being use to update a PDF's file identifier, we'd need 
                to add a method to parse an existing file identifier.
            </remarks>
        </member>
        <member name="M:Telerik.Pdf.FileIdentifier.#ctor">
            <summary>
                Initialises the CreatedPart and ModifiedPart to a randomly generated GUID.
            </summary>
        </member>
        <member name="M:Telerik.Pdf.FileIdentifier.#ctor(System.Byte[])">
            <summary>
                Initialises the CreatedPart and ModifiedPart to the passed string.
            </summary>
        </member>
        <member name="P:Telerik.Pdf.FileIdentifier.CreatedPart">
            <summary>
                Returns the CreatedPart as a byte array.
            </summary>
        </member>
        <member name="P:Telerik.Pdf.FileIdentifier.ModifiedPart">
            <summary>
                Returns the ModifiedPart as a byte array.
            </summary>
        </member>
        <member name="T:Telerik.Pdf.Gdi.Font.DirectoryEntry">
            <summary>
                Represents an entry in the directory table
            </summary>
        </member>
        <member name="P:Telerik.Pdf.Gdi.Font.DirectoryEntry.TableName">
            <summary>
                Returns the table tag as a string
            </summary>
            <returns></returns>
        </member>
        <member name="P:Telerik.Pdf.Gdi.Font.DirectoryEntry.Tag">
            <summary>
                Gets the table tag encoded as an unsigned 32-bite integer.
            </summary>
        </member>
        <member name="P:Telerik.Pdf.Gdi.Font.DirectoryEntry.Offset">
            <summary>
                Gets or sets a value that represents a <see cref="T:Telerik.Pdf.Gdi.Font.FontTable"/> 
                offset, i.e. the number of bytes from the beginning of the file.
            </summary>
        </member>
        <member name="P:Telerik.Pdf.Gdi.Font.DirectoryEntry.Length">
            <summary>
                Gets or sets a value representing the number number of bytes
                a <see cref="T:Telerik.Pdf.Gdi.Font.FontTable"/> object occupies in a stream.
            </summary>
        </member>
        <member name="P:Telerik.Pdf.Gdi.Font.DirectoryEntry.CheckSum">
            <summary>
                Gets or sets value that represents a checksum of a <see cref="T:Telerik.Pdf.Gdi.Font.FontTable"/>.
            </summary>
        </member>
        <member name="M:Telerik.Pdf.Gdi.Font.DirectoryEntry.MakeTable(Telerik.Pdf.Gdi.Font.FontFileReader)">
            <summary>
                Gets an instance of an <see cref="T:Telerik.Pdf.Gdi.Font.FontTable"/> implementation that is 
                capable of parsing the table identified by <b>tab</b>.
            </summary>
            <returns></returns>
        </member>
        <member name="T:Telerik.Pdf.Gdi.Font.FontFileReader">
            <summary>
                Class designed to parse a TrueType font file.
            </summary>
        </member>
        <member name="F:Telerik.Pdf.Gdi.Font.FontFileReader.stream">
            <summary>
                A Big Endian stream.
            </summary>
        </member>
        <member name="F:Telerik.Pdf.Gdi.Font.FontFileReader.fontName">
            <summary>
                Used to identity a font within a TrueType collection.
            </summary>
        </member>
        <member name="F:Telerik.Pdf.Gdi.Font.FontFileReader.header">
            <summary>
                Maps a table name (4-character string) to a <see cref="T:Telerik.Pdf.Gdi.Font.DirectoryEntry"/>
            </summary>
        </member>
        <member name="F:Telerik.Pdf.Gdi.Font.FontFileReader.tableCache">
            <summary>
                A dictionary of cached <see cref="T:Telerik.Pdf.Gdi.Font.FontTable"/> instances.  
                The index is the table name.
            </summary>
        </member>
        <member name="F:Telerik.Pdf.Gdi.Font.FontFileReader.mappings">
            <summary>
                Maps a glyph index to a subset index.
            </summary>
        </member>
        <member name="M:Telerik.Pdf.Gdi.Font.FontFileReader.#ctor(System.IO.MemoryStream)">
            <summary>
                Class constructor.
            </summary>
            <param name="stream">Font data stream.</param>
        </member>
        <member name="M:Telerik.Pdf.Gdi.Font.FontFileReader.#ctor(System.IO.MemoryStream,System.String)">
            <summary>
                Class constructor.
            </summary>
            <param name="stream">Font data stream.</param>
            <param name="fontName">Name of a font in a TrueType collection.</param>
        </member>
        <member name="P:Telerik.Pdf.Gdi.Font.FontFileReader.IndexMappings">
            <summary>
                Gets or sets a dictionary containing glyph index to subset 
                index mappings.
            </summary>
        </member>
        <member name="P:Telerik.Pdf.Gdi.Font.FontFileReader.Stream">
            <summary>
                Gets the underlying <see cref="T:Telerik.Pdf.Gdi.Font.FontFileStream"/>.
            </summary>
        </member>
        <member name="P:Telerik.Pdf.Gdi.Font.FontFileReader.TableCount">
            <summary>
                Gets the number tables.
            </summary>
        </member>
        <member name="M:Telerik.Pdf.Gdi.Font.FontFileReader.ContainsTable(System.String)">
            <summary>
                Gets a value indicating whether or not this font contains the 
                supplied table.
            </summary>
            <param name="tableName">A table name.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Pdf.Gdi.Font.FontFileReader.GetTable(System.String)">
            <summary>
                Gets a reference to the table structure identified by <i>tableName</i>
            </summary>
            <remarks>
                Only the following tables are supported: 
                <see cref="F:Telerik.Pdf.Gdi.Font.TableNames.Head"/> - Font header,
                <see cref="F:Telerik.Pdf.Gdi.Font.TableNames.Hhea"/> - Horizontal header,
                <see cref="F:Telerik.Pdf.Gdi.Font.TableNames.Hmtx"/> - Horizontal metrics,
                <see cref="F:Telerik.Pdf.Gdi.Font.TableNames.Maxp"/> - Maximum profile,
                <see cref="F:Telerik.Pdf.Gdi.Font.TableNames.Loca"/> - Index to location, 
                <see cref="F:Telerik.Pdf.Gdi.Font.TableNames.Glyf"/> - Glyf data,
                <see cref="F:Telerik.Pdf.Gdi.Font.TableNames.Cvt"/> - Control value,
                <see cref="F:Telerik.Pdf.Gdi.Font.TableNames.Prep"/> - Control value program,
                <see cref="F:Telerik.Pdf.Gdi.Font.TableNames.Fpgm"/> - Font program
            </remarks>
            <param name="tableName">A 4-character code identifying a table.</param>
            <exception cref="T:System.ArgumentException">
                If <b>tableName</b> does not represent a table in this font.
            </exception>
        </member>
        <member name="M:Telerik.Pdf.Gdi.Font.FontFileReader.GetDictionaryEntry(System.String)">
            <summary>
                Gets a <see cref="T:Telerik.Pdf.Gdi.Font.DirectoryEntry"/> object for the supplied table.
            </summary>
            <param name="tableName">A 4-character code identifying a table.</param>
            <returns>
                A <see cref="T:Telerik.Pdf.Gdi.Font.DirectoryEntry"/> object or null if the table cannot 
                be located.
            </returns>
            <exception cref="T:System.ArgumentException">
                If <b>tag</b> does not represent a table in this font.
            </exception>
        </member>
        <member name="M:Telerik.Pdf.Gdi.Font.FontFileReader.ReadTableHeaders">
            <summary>
                Reads the Offset and Directory tables.  If the FontFileStream represents 
                a TrueType collection, this method will look for the aforementioned 
                tables belonging to <i>fontName</i>.
            </summary>
            <remarks>
                This method can handle a TrueType collection.
            </remarks>
        </member>
        <member name="M:Telerik.Pdf.Gdi.Font.FontFileReader.ReadRequiredTables">
            <summary>
                Caches the following tables: 'head', 'hhea', 'maxp', 'loca'
            </summary>
        </member>
        <member name="M:Telerik.Pdf.Gdi.Font.FontFileReader.OffsetStream(Telerik.Pdf.Gdi.Font.DirectoryEntry)">
            <summary>
                Sets the stream position to the offset in the supplied directory
                entry. Also ensures that the FontFileStream has enough bytes 
                available to read a font table.  Throws an exception if this 
                condition is not met.
            </summary>
            <param name="entry"></param>
            <exception cref="T:System.ArgumentException">
                If the supplied stream does not contain enough data.
            </exception>
        </member>
        <member name="T:Telerik.Pdf.Gdi.Font.FontFileStream">
            <summary>
                Class designed to read and write primitive datatypes from/to a 
                TrueType font file.
            </summary>
            <remarks>
                <p>All OpenType fonts use Motorola-style byte ordering (Big Endian).</p>
                <p>The following table lists the primitives and their definition. 
                Note the difference between the .NET CLR definition of certain 
                types and the TrueType definition.</p>
                <p>
                BYTE         8-bit unsigned integer. 
                CHAR         8-bit signed integer. 
                USHORT       16-bit unsigned integer. 
                SHORT        16-bit signed integer. 
                ULONG        32-bit unsigned integer. 
                LONG         32-bit signed integer. 
                Fixed        32-bit signed fixed-point number (16.16) 
                FWORD        16-bit signed integer (SHORT) that describes a 
                             quantity in FUnits. 
                UFWORD       16-bit unsigned integer (USHORT) that describes a 
                             quantity in FUnits. 
                F2DOT14      16-bit signed fixed number with the low 14 bits of 
                             fraction (2.14). 
                LONGDATETIME Date represented in number of seconds since 12:00 
                             midnight, January 1, 1904. The value is represented 
                             as a signed 64-bit integer. 
                Tag          Array of four uint8s (length = 32 bits) used to identify 
                             a script, language system, feature, or baseline 
                GlyphID      Glyph index number, same as uint16(length = 16 bits) 
                Offset       Offset to a table, same as uint16 (length = 16 bits), 
                             NULL offset = 0x0000 
                </p>
            </remarks>
        </member>
        <member name="M:Telerik.Pdf.Gdi.Font.FontFileStream.#ctor(System.Byte[])">
            <summary>
                Initialises a new instance of the <see cref="T:Telerik.Pdf.Gdi.Font.FontFileStream"/> 
                class using the supplied byte array as the underlying buffer.
            </summary>
            <param name="data">The font data encoded in a byte array.</param>
            <exception cref="T:System.ArgumentNullException">
                <i>data</i> is a null reference.
            </exception>
            <exception cref="T:System.ArgumentException">
                <i>data</i> is a zero-length array.
            </exception>
        </member>
        <member name="M:Telerik.Pdf.Gdi.Font.FontFileStream.#ctor(System.IO.Stream)">
            <summary>
                Initialises a new instance of the <see cref="T:Telerik.Pdf.Gdi.Font.FontFileStream"/>
                class using the supplied stream as the underlying buffer.
            </summary>
            <param name="stream">Reference to an existing stream.</param>
            <exception cref="T:System.ArgumentNullException">
                <i>stream</i> is a null reference.
            </exception>
        </member>
        <member name="M:Telerik.Pdf.Gdi.Font.FontFileStream.ReadByte">
            <summary>
                Reads an unsigned byte from the font file.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Telerik.Pdf.Gdi.Font.FontFileStream.WriteByte(System.Byte)">
            <summary>
                Writes an unsigned byte from the font file.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Telerik.Pdf.Gdi.Font.FontFileStream.ReadChar">
            <summary>
                Reads an signed byte from the font file.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Telerik.Pdf.Gdi.Font.FontFileStream.WriteChar(System.SByte)">
            <summary>
                Writes a signed byte from the font file.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Telerik.Pdf.Gdi.Font.FontFileStream.ReadShort">
            <summary>
                Reads a short (16-bit signed integer) from the font file.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Telerik.Pdf.Gdi.Font.FontFileStream.WriteShort(System.Int32)">
            <summary>
                Writes a short (16-bit signed integer) to the font file.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Telerik.Pdf.Gdi.Font.FontFileStream.ReadFWord">
            <summary>
                Reads a short (16-bit signed integer) from the font file.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Telerik.Pdf.Gdi.Font.FontFileStream.WriteFWord(System.Int32)">
            <summary>
                Writes a short (16-bit signed integer) to the font file.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Telerik.Pdf.Gdi.Font.FontFileStream.ReadUShort">
            <summary>
                Reads a int (16-bit unsigned integer) from the font file.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Telerik.Pdf.Gdi.Font.FontFileStream.WriteUShort(System.Int32)">
            <summary>
                Writes a int (16-bit unsigned integer) to the font file.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Telerik.Pdf.Gdi.Font.FontFileStream.ReadUFWord">
            <summary>
                Reads a int (16-bit unsigned integer) from the font file.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Telerik.Pdf.Gdi.Font.FontFileStream.WriteUFWord(System.Int32)">
            <summary>
                Writes a int (16-bit unsigned integer) to the font file.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Telerik.Pdf.Gdi.Font.FontFileStream.ReadLong">
            <summary>
                Reads an int (32-bit signed integer) from the font file.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Telerik.Pdf.Gdi.Font.FontFileStream.WriteLong(System.Int32)">
            <summary>
                Writes an int (32-bit signed integer) to the font file.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Telerik.Pdf.Gdi.Font.FontFileStream.ReadULong">
            <summary>
                Reads a int (32-bit unsigned integer) from the font file.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Telerik.Pdf.Gdi.Font.FontFileStream.WriteULong(System.Int64)">
            <summary>
                Writes a int (32-bit unsigned integer) to the font file.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Telerik.Pdf.Gdi.Font.FontFileStream.ReadFixed">
            <summary>
                Reads an int (32-bit signed integer) from the font file.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Telerik.Pdf.Gdi.Font.FontFileStream.WriteFixed(System.Int32)">
            <summary>
                Writes an int (32-bit unsigned integer) to the font file.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Telerik.Pdf.Gdi.Font.FontFileStream.ReadLongDateTime">
            <summary>
                Reads a long (64-bit signed integer) from the font file.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Telerik.Pdf.Gdi.Font.FontFileStream.WriteDateTime(System.Int64)">
            <summary>
                Writes a long (64-bit signed integer) to the font file.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Telerik.Pdf.Gdi.Font.FontFileStream.ReadTag">
            <summary>
                Reads a tag (array of four bytes) from the font stream.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Telerik.Pdf.Gdi.Font.FontFileStream.WriteTag(System.Byte[])">
            <summary>
                Writes a tab (array of four bytes) to the font file.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Telerik.Pdf.Gdi.Font.FontFileStream.Pad">
            <summary>
                Ensures the stream is padded on a 4-byte boundary.
            </summary>
            <remarks>
                This method will output between 0 and 3 bytes to the stream.
            </remarks>
            <returns>
                A value between 0 and 3 (inclusive).
            </returns>
        </member>
        <member name="M:Telerik.Pdf.Gdi.Font.FontFileStream.Write(System.Byte[],System.Int32,System.Int32)">
            <summary>
                Writes a sequence of bytes to the underlying stream.
            </summary>
            <param name="buffer"></param>
            <param name="offset"></param>
            <param name="count"></param>
        </member>
        <member name="M:Telerik.Pdf.Gdi.Font.FontFileStream.Read(System.Byte[],System.Int32,System.Int32)">
            <summary>
                Reads a block of bytes from the current stream and writes 
                the data to buffer.
            </summary>
            <param name="buffer">A byte buffer big enough to store <i>count</i> bytes.</param>
            <param name="offset">The byte offset in buffer to begin reading.</param>
            <param name="count">Number of bytes to read.</param>
        </member>
        <member name="P:Telerik.Pdf.Gdi.Font.FontFileStream.Position">
            <summary>
                Gets or sets the current position of the font stream.
            </summary>
        </member>
        <member name="P:Telerik.Pdf.Gdi.Font.FontFileStream.Length">
            <summary>
                Gets the length of the font stream in bytes.
            </summary>
        </member>
        <member name="M:Telerik.Pdf.Gdi.Font.FontFileStream.Skip(System.Int64)">
            <summary>
                Offsets the stream position by the supplied number of bytes.
            </summary>
            <param name="offset"></param>
        </member>
        <member name="M:Telerik.Pdf.Gdi.Font.FontFileStream.SetRestorePoint">
            <summary>
                Saves the current stream position onto a marker stack.
            </summary>
            <returns>
                Returns the current stream position.
            </returns>
        </member>
        <member name="M:Telerik.Pdf.Gdi.Font.FontFileStream.Restore">
            <summary>
                Sets the stream <see cref="P:Telerik.Pdf.Gdi.Font.FontFileStream.Position"/> using the marker at the 
                head of the marker stack.
            </summary>
            <returns>
                Returns the stream position before it was reset.
            </returns>
            <exception cref="T:System.InvalidOperationException">
                If the markers stack is empty.
            </exception>
        </member>
        <member name="T:Telerik.Pdf.Gdi.Font.FontFileWriter">
            <summary>
                A specialised stream writer for creating OpenType fonts.
            </summary>
        </member>
        <member name="F:Telerik.Pdf.Gdi.Font.FontFileWriter.OffsetTableSize">
            <summary>
                Size of the offset table in bytes.
            </summary>
        </member>
        <member name="F:Telerik.Pdf.Gdi.Font.FontFileWriter.stream">
            <summary>
                The underlying stream.
            </summary>
        </member>
        <member name="F:Telerik.Pdf.Gdi.Font.FontFileWriter.tables">
            <summary>
                List of font tables to write.
            </summary>
        </member>
        <member name="M:Telerik.Pdf.Gdi.Font.FontFileWriter.#ctor(System.IO.Stream)">
            <summary>
                Creates a new instance of the <see cref="T:Telerik.Pdf.Gdi.Font.FontFileWriter"/> class
                using <i>stream</i> as the underlying stream object.
            </summary>
            <param name="stream"></param>
            <exception cref="T:System.ArgumentException">
                If <i>stream</i> is not writable.
            </exception>
            <exception cref="T:System.ArgumentNullException">
                If <i>streamm</i> is a null reference.
            </exception>
        </member>
        <member name="P:Telerik.Pdf.Gdi.Font.FontFileWriter.Stream">
            <summary>
                Gets the underlying <see cref="T:Telerik.Pdf.Gdi.Font.FontFileStream"/>.
            </summary>
        </member>
        <member name="M:Telerik.Pdf.Gdi.Font.FontFileWriter.Write(Telerik.Pdf.Gdi.Font.FontTable)">
            <summary>
                Queues the supplied <see cref="T:Telerik.Pdf.Gdi.Font.FontTable"/> for writing 
                to the underlying stream.
            </summary>
            <remarks>
                The method will not immediately write the supplied font 
                table to the underlying stream.  Instead it queues the 
                font table since the offset table must be written out 
                before any tables.
            </remarks>
            <param name="table"></param>
        </member>
        <member name="M:Telerik.Pdf.Gdi.Font.FontFileWriter.Close">
            <summary>
                Writes the header and font tables to the underlying stream.
            </summary>
        </member>
        <member name="M:Telerik.Pdf.Gdi.Font.FontFileWriter.WriteChecksumAdjustment">
            <summary>
                Updates the checkSumAdjustment field in the head table.
            </summary>
        </member>
        <member name="M:Telerik.Pdf.Gdi.Font.FontFileWriter.WriteTables">
            <summary>
                Writes out each table to the font stream.
            </summary>
        </member>
        <member name="M:Telerik.Pdf.Gdi.Font.FontFileWriter.WriteOffsetTable">
            <summary>
                Writes the offset table that appears at the beginning of 
                every TrueType/OpenType font.
            </summary>
        </member>
        <member name="M:Telerik.Pdf.Gdi.Font.FontFileWriter.SkipTableDirectory">
            <summary>
                Does not actually write the table directory - simply "allocates"
                space for it in the stream.
            </summary>
        </member>
        <member name="M:Telerik.Pdf.Gdi.Font.FontFileWriter.MaxPow2(System.Int32)">
            <summary>
                Returns the maximum power of 2 &lt;= max
            </summary>
            <param name="max"></param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Pdf.Gdi.Font.FontFileWriter.CalculateCheckSumAdjustment">
            <summary>
                Calculates the checksum of the entire font.
            </summary>
            <remarks>
                The underlying <see cref="T:Telerik.Pdf.Gdi.Font.FontFileStream"/> must be aligned on
                a 4-byte boundary.
            </remarks>
            <returns></returns>
        </member>
        <member name="M:Telerik.Pdf.Gdi.Font.FontFileWriter.CalculateCheckSum(System.Int64)">
            <summary>
                Calculates the checksum of a <see cref="T:Telerik.Pdf.Gdi.Font.FontTable"/>.
            </summary>
            <remarks>
                The supplied <i>stream</i> must be positioned at the beginning of 
                the table.
            </remarks>
            <param name="length"></param>
            <returns></returns>
        </member>
        <member name="T:Telerik.Pdf.Gdi.Font.FontSubset">
            <summary>
                Generates a subset from a TrueType font.
            </summary>
        </member>
        <member name="M:Telerik.Pdf.Gdi.Font.FontSubset.#ctor(Telerik.Pdf.Gdi.Font.FontFileReader)">
            <summary>
                Creates a new instance of the FontSubset class.
            </summary>
            <param name="reader">TrueType font parser.</param>
        </member>
        <member name="M:Telerik.Pdf.Gdi.Font.FontSubset.Generate(System.IO.MemoryStream)">
            <summary>
                Writes the font subset to the supplied output stream.
            </summary>
        </member>
        <member name="M:Telerik.Pdf.Gdi.Font.GlyphReader.ReadGlyph(System.Int32)">
            <summary>
                Reads a glyph description from the specified offset.
            </summary>
        </member>
        <member name="M:Telerik.Pdf.Gdi.Font.GlyphReader.ReadCompositeGlyph(Telerik.Pdf.Gdi.Font.FontFileStream,Telerik.Pdf.Gdi.Font.Glyph)">
            <summary>
                Populate the <i>composites</i>IList containing all child glyphs 
                that this glyph uses.
            </summary>
            <remarks>
                The <i>stream</i> parameter must be positioned 10 bytes from 
                the beginning of the glyph description, i.e. the flags field.
            </remarks>
            <param name="stream"></param>
            <param name="glyph"></param>
        </member>
        <member name="M:Telerik.Pdf.Gdi.Font.GlyphReader.GetGlyphLength(System.Int32)">
            <summary>
                Gets the length of the glyph description in bytes at 
                index <i>index</i>.
            </summary>
            <param name="index"></param>
            <returns></returns>
        </member>
        <member name="T:Telerik.Pdf.Gdi.Font.BitMasks">
            <summary>
                Bit masks of the flags field in a composite glyph.
            </summary>
        </member>
        <member name="T:Telerik.Pdf.Gdi.Font.IndexMappings">
            <summary>
                Utility class that stores a list of glyph indices and their 
                asociated subset indices.
            </summary>
        </member>
        <member name="F:Telerik.Pdf.Gdi.Font.IndexMappings.glyphToSubset">
            <summary>
                Maps a glyph index to a subset index.
            </summary>
        </member>
        <member name="F:Telerik.Pdf.Gdi.Font.IndexMappings.subsetToGlyph">
            <summary>
                Maps a subset index to glyph index.
            </summary>
        </member>
        <member name="M:Telerik.Pdf.Gdi.Font.IndexMappings.#ctor">
            <summary>
                Class constructor.
            </summary>
        </member>
        <member name="P:Telerik.Pdf.Gdi.Font.IndexMappings.Count">
            <summary>
                Gets the number of glyph to subset index mappings.
            </summary>
        </member>
        <member name="M:Telerik.Pdf.Gdi.Font.IndexMappings.HasMapping(System.Int32)">
            <summary>
                Determines whether a mapping exists for the supplied glyph index.
            </summary>
            <param name="glyphIndex"></param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Pdf.Gdi.Font.IndexMappings.Map(System.Int32)">
            <summary>
                Returns the subset index for <i>glyphIndex</i>.  If a subset 
                index does not exist for <i>glyphIndex</i> one is generated.
            </summary>
            <param name="glyphIndex"></param>
            <returns>A subset index.</returns>
        </member>
        <member name="M:Telerik.Pdf.Gdi.Font.IndexMappings.Add(System.Int32[])">
            <summary>
                Adds the list of supplied glyph indices to the index mappings using 
                the next available subset index for each glyph index.
            </summary>
            <param name="glyphIndices"></param>
        </member>
        <member name="M:Telerik.Pdf.Gdi.Font.IndexMappings.GetSubsetIndex(System.Int32)">
            <summary>
                Gets the subset index of <i>glyphIndex</i>.
            </summary>
            <param name="glyphIndex"></param>
            <returns>
                A glyph index or <b>-1</b> if a glyph to subset mapping does not exist.
            </returns>
        </member>
        <member name="M:Telerik.Pdf.Gdi.Font.IndexMappings.GetGlyphIndex(System.Int32)">
            <summary>
                Gets the glyph index of <i>subsetIndex</i>.
            </summary>
            <param name="subsetIndex"></param>
            <returns>
                A subset index or <b>-1</b> if a subset to glyph mapping does not exist.
            </returns>
        </member>
        <member name="P:Telerik.Pdf.Gdi.Font.IndexMappings.GlyphIndices">
            <summary>
                Gets a list of glyph indices sorted in ascending order.
            </summary>
        </member>
        <member name="P:Telerik.Pdf.Gdi.Font.IndexMappings.SubsetIndices">
            <summary>
                Gets a list of subset indices sorted in ascending order.
            </summary>
        </member>
        <member name="F:Telerik.Pdf.Gdi.Font.KerningPairs.pairs">
            <summary>
                Key - Kerning pair identifier
                Value - Kerning amount
            </summary>
        </member>
        <member name="M:Telerik.Pdf.Gdi.Font.KerningPairs.#ctor">
            <summary>
                Creates an instance of KerningPairs allocating space for 
                100 kerning pairs.
            </summary>
        </member>
        <member name="M:Telerik.Pdf.Gdi.Font.KerningPairs.#ctor(System.Int32)">
            <summary>
                Creates an instance of KerningPairs allocating space for 
                <i>numPairs</i> kerning pairs.
            </summary>
            <param name="numPairs"></param>
        </member>
        <member name="M:Telerik.Pdf.Gdi.Font.KerningPairs.HasKerning(System.Int32,System.Int32)">
            <summary>
                Returns true if a kerning value exists for the supplied 
                glyph index pair.
            </summary>
            <param name="left">Glyph index for left-hand glyph.</param>
            <param name="right">Glyph index for right-hand glyph.</param>
            <returns></returns>
        </member>
        <member name="P:Telerik.Pdf.Gdi.Font.KerningPairs.Item(System.Int32,System.Int32)">
            <summary>
                Gets the kerning amount for the supplied glyph index pair.
            </summary>
        </member>
        <member name="P:Telerik.Pdf.Gdi.Font.KerningPairs.Length">
            <summary>
                Gets the number of kernings pairs.
            </summary>
        </member>
        <member name="M:Telerik.Pdf.Gdi.Font.KerningPairs.Add(System.Int32,System.Int32,System.Int32)">
            <summary>
                Creates a new kerning pair.
            </summary>
            <remarks>
                This method will ignore duplicates.
            </remarks>
            <param name="left">The glyph index for the left-hand glyph in the kerning pair.</param>
            <param name="right">The glyph index for the right-hand glyph in the kerning pair. </param>
            <param name="value">The kerning value for the supplied pair.</param>
        </member>
        <member name="M:Telerik.Pdf.Gdi.Font.KerningPairs.GetIndex(System.Int32,System.Int32)">
            <summary>
                Returns a kerning pair identifier.
            </summary>
            <param name="left"></param>
            <param name="right"></param>
            <returns></returns>
        </member>
        <member name="T:Telerik.Pdf.Gdi.Font.PrimitiveSizes">
            <summary>
                A helper designed that provides the size of each TrueType primitives.
            </summary>
        </member>
        <member name="T:Telerik.Pdf.Gdi.Font.TableNames">
            <summary>
                List of all TrueType and OpenType tables
            </summary>
        </member>
        <member name="M:Telerik.Pdf.Gdi.Font.TableNames.ToUint(System.String)">
            <summary>
                Converts one of the predefined table names to an unsigned integer.
            </summary>
            <param name="tableName"></param>
            <returns></returns>
        </member>
        <member name="T:Telerik.Pdf.Gdi.Font.ControlValueProgramTable">
            <summary>
                Class that represents the Control Value Program table ('prep').
            </summary>
        </member>
        <member name="F:Telerik.Pdf.Gdi.Font.ControlValueProgramTable.instructions">
            <summary>
                Set of instructions executed whenever point size or font 
                or transformation change.
            </summary>
        </member>
        <member name="M:Telerik.Pdf.Gdi.Font.ControlValueProgramTable.#ctor(Telerik.Pdf.Gdi.Font.DirectoryEntry)">
            <summary>
                Creates an instance of the <see cref="T:Telerik.Pdf.Gdi.Font.ControlValueProgramTable"/> class.
            </summary>
            <param name="entry"></param>
        </member>
        <member name="M:Telerik.Pdf.Gdi.Font.ControlValueProgramTable.Read(Telerik.Pdf.Gdi.Font.FontFileReader)">
            <summary>
                Reads the contents of the "prep" table from the current position 
                in the supplied stream.
            </summary>
            <param name="reader"></param>
        </member>
        <member name="M:Telerik.Pdf.Gdi.Font.ControlValueProgramTable.Write(Telerik.Pdf.Gdi.Font.FontFileWriter)">
            <summary>
                Writes out the array of instructions to the supplied stream.
            </summary>
            <param name="writer"></param>
        </member>
        <member name="T:Telerik.Pdf.Gdi.Font.ControlValueTable">
            <summary>
                Class that represents the Control Value table ('cvt').
            </summary>
        </member>
        <member name="F:Telerik.Pdf.Gdi.Font.ControlValueTable.values">
            <summary>
                List of N values referenceable by instructions. 
            </summary>
        </member>
        <member name="M:Telerik.Pdf.Gdi.Font.ControlValueTable.#ctor(Telerik.Pdf.Gdi.Font.DirectoryEntry)">
            <summary>
                Creates an instance of the <see cref="T:Telerik.Pdf.Gdi.Font.ControlValueTable"/> class.
            </summary>
            <param name="entry"></param>
        </member>
        <member name="P:Telerik.Pdf.Gdi.Font.ControlValueTable.Count">
            <summary>
                Gets the value representing the number of values that can 
                be referenced by instructions.
            </summary>
        </member>
        <member name="M:Telerik.Pdf.Gdi.Font.ControlValueTable.Read(Telerik.Pdf.Gdi.Font.FontFileReader)">
            <summary>
                Reads the contents of the "cvt" table from the current position 
                in the supplied stream.
            </summary>
            <param name="reader"></param>
        </member>
        <member name="M:Telerik.Pdf.Gdi.Font.ControlValueTable.Write(Telerik.Pdf.Gdi.Font.FontFileWriter)">
            <summary>
                Writes out the array of values to the supplied stream.
            </summary>
            <param name="writer"></param>
        </member>
        <member name="T:Telerik.Pdf.Gdi.Font.FontProgramTable">
            <summary>
                Class that represents the Font Program table ('fpgm').
            </summary>
        </member>
        <member name="F:Telerik.Pdf.Gdi.Font.FontProgramTable.instructions">
            <summary>
                List of N instructions. 
            </summary>
        </member>
        <member name="M:Telerik.Pdf.Gdi.Font.FontProgramTable.#ctor(Telerik.Pdf.Gdi.Font.DirectoryEntry)">
            <summary>
                Creates an instance of the <see cref="T:Telerik.Pdf.Gdi.Font.FontProgramTable"/> class.
            </summary>
            <param name="entry"></param>
        </member>
        <member name="P:Telerik.Pdf.Gdi.Font.FontProgramTable.Count">
            <summary>
                Gets the value representing the number of instructions 
                in the font program.
            </summary>
        </member>
        <member name="M:Telerik.Pdf.Gdi.Font.FontProgramTable.Read(Telerik.Pdf.Gdi.Font.FontFileReader)">
            <summary>
                Reads the contents of the "fpgm" table from the current position 
                in the supplied stream.
            </summary>
            <param name="reader"></param>
        </member>
        <member name="M:Telerik.Pdf.Gdi.Font.FontProgramTable.Write(Telerik.Pdf.Gdi.Font.FontFileWriter)">
            <summary>
                Writes out the array of instructions to the supplied stream.
            </summary>
            <param name="writer"></param>
        </member>
        <member name="T:Telerik.Pdf.Gdi.Font.FontTable">
            <summary>
                Class derived by all TrueType table classes.
            </summary>
        </member>
        <member name="F:Telerik.Pdf.Gdi.Font.FontTable.directoryEntry">
            <summary>
                The dictionary entry for this table.
            </summary>
        </member>
        <member name="M:Telerik.Pdf.Gdi.Font.FontTable.#ctor(System.String,Telerik.Pdf.Gdi.Font.DirectoryEntry)">
            <summary>
                Class constructor
            </summary>
            <param name="tableName">The table name.</param>
            <param name="entry">Table directory entry.</param>
        </member>
        <member name="P:Telerik.Pdf.Gdi.Font.FontTable.Entry">
            <summary>
                Gets or sets a directory entry for this table.
            </summary>
        </member>
        <member name="M:Telerik.Pdf.Gdi.Font.FontTable.Read(Telerik.Pdf.Gdi.Font.FontFileReader)">
            <summary>
                Reads the contents of a table from the current position in 
                the supplied stream.
            </summary>
            <param name="reader"></param>
            <exception cref="T:System.ArgumentException">
                If the supplied stream does not contain enough data.
            </exception>
        </member>
        <member name="M:Telerik.Pdf.Gdi.Font.FontTable.Write(Telerik.Pdf.Gdi.Font.FontFileWriter)">
            <summary>
                Writes the contents of a table to the supplied writer.
            </summary>
            <remarks>
                This method should not be concerned with aligning the 
                table output on the 4-byte boundary.
            </remarks>
            <param name="writer"></param>
        </member>
        <member name="P:Telerik.Pdf.Gdi.Font.FontTable.Name">
            <summary>
                Gets the unique name of this table as a 4-character string.
            </summary>
            <remarks>
                Note that some TrueType tables are only 3 characters long 
                (e.g. 'cvt').  In this case the returned string will be padded 
                with a extra space at the end of the string.
            </remarks>
        </member>
        <member name="P:Telerik.Pdf.Gdi.Font.FontTable.Tag">
            <summary>
                Gets the table name encoded as a 32-bit unsigned integer.
            </summary>
        </member>
        <member name="T:Telerik.Pdf.Gdi.Font.FontTableFactory">
            <summary>
                Instantiates a font table from a table tag.
            </summary>
        </member>
        <member name="M:Telerik.Pdf.Gdi.Font.FontTableFactory.#ctor">
            <summary>
                Prevent instantiation since this is a factory class.
            </summary>
        </member>
        <member name="M:Telerik.Pdf.Gdi.Font.FontTableFactory.Make(System.String,Telerik.Pdf.Gdi.Font.FontFileReader)">
            <summary>
                Creates an instance of a class that implements the FontTable interface.
            </summary>
            <param name="reader"></param>
            <param name="tableName">
                One of the pre-defined TrueType tables from the <see cref="T:Telerik.Pdf.Gdi.Font.TableNames"/> class.
            </param>
            <returns>
                A subclass of <see cref="T:Telerik.Pdf.Gdi.Font.FontTable"/> that is capable of parsing 
                a TrueType table.
            </returns>
            <exception cref="T:System.ArgumentException">
                If a class capable of parsing <i>tableName</i> is not available.
            </exception>
        </member>
        <member name="T:Telerik.Pdf.Gdi.Font.GlyfDataTable">
            <summary>
                Class that represents the Glyf Data table ('glyf').
            </summary>
            <remarks>
                http://www.microsoft.com/typography/otspec/glyf.htm
            </remarks>
        </member>
        <member name="F:Telerik.Pdf.Gdi.Font.GlyfDataTable.glyphDescriptions">
            <summary>
                Maps a glyph index to a <see cref="T:Telerik.Pdf.Gdi.Font.Glyph"/> object.
            </summary>
        </member>
        <member name="M:Telerik.Pdf.Gdi.Font.GlyfDataTable.#ctor(Telerik.Pdf.Gdi.Font.DirectoryEntry)">
            <summary>
                Creates an instance of the <see cref="T:Telerik.Pdf.Gdi.Font.GlyfDataTable"/> class.
            </summary>
            <param name="entry"></param>
        </member>
        <member name="P:Telerik.Pdf.Gdi.Font.GlyfDataTable.Item(System.Int32)">
            <summary>
                Gets the <see cref="T:Telerik.Pdf.Gdi.Font.Glyph"/> instance located at <i>glyphIndex</i>
            </summary>
        </member>
        <member name="P:Telerik.Pdf.Gdi.Font.GlyfDataTable.Count">
            <summary>
                Gets the number of glyphs.
            </summary>
        </member>
        <member name="M:Telerik.Pdf.Gdi.Font.GlyfDataTable.Read(Telerik.Pdf.Gdi.Font.FontFileReader)">
            <summary>
                Reads the contents of the "glyf" table from the current position 
                in the supplied stream.
            </summary>
            <param name="reader"></param>
        </member>
        <member name="M:Telerik.Pdf.Gdi.Font.GlyfDataTable.Write(Telerik.Pdf.Gdi.Font.FontFileWriter)">
            <summary>
                Writes the contents of the glyf table to the supplied stream.
            </summary>
            <param name="writer"></param>
        </member>
        <member name="T:Telerik.Pdf.Gdi.Font.Glyph">
            <summary>
                Represents either a simple or composite glyph description from
                the 'glyf' table.
            </summary>
            <remarks>
                This class is nothing more than a wrapper around 
                a byte array.
            </remarks>
        </member>
        <member name="F:Telerik.Pdf.Gdi.Font.Glyph.glyphIndex">
            <summary>
                The index of this glyph as obtained from the 'loca' table.
            </summary>
        </member>
        <member name="F:Telerik.Pdf.Gdi.Font.Glyph.glyphData">
            <summary>
                Contains glyph description as raw data.
            </summary>
        </member>
        <member name="F:Telerik.Pdf.Gdi.Font.Glyph.children">
            <summary>
                List of composite glyph indices.
            </summary>
        </member>
        <member name="M:Telerik.Pdf.Gdi.Font.Glyph.#ctor(System.Int32)">
            <summary>
                Class constructor.
            </summary>
        </member>
        <member name="M:Telerik.Pdf.Gdi.Font.Glyph.SetGlyphData(System.Byte[])">
            <summary>
                Sets the glyph data (duh!).
            </summary>
            <param name="glyphData"></param>
        </member>
        <member name="P:Telerik.Pdf.Gdi.Font.Glyph.Index">
            <summary>
                Gets or sets the index of this glyph.
            </summary>
        </member>
        <member name="P:Telerik.Pdf.Gdi.Font.Glyph.Length">
            <summary>
                Gets the length of the glyph data buffer.
            </summary>
        </member>
        <member name="M:Telerik.Pdf.Gdi.Font.Glyph.AddChild(System.Int32)">
            <summary>
                Add the supplied glyph index to list of children.
            </summary>
            <param name="glyphIndex"></param>
        </member>
        <member name="P:Telerik.Pdf.Gdi.Font.Glyph.Children">
            <summary>
                Gets a ilst of child glyph indices.
            </summary>
        </member>
        <member name="P:Telerik.Pdf.Gdi.Font.Glyph.IsComposite">
            <summary>
                Gets a value indicating whether or not this glyph represents 
                a composite glyph.
            </summary>
        </member>
        <member name="M:Telerik.Pdf.Gdi.Font.Glyph.Write(Telerik.Pdf.Gdi.Font.FontFileStream)">
            <summary>
                Writes a glyph description to the supplied stream.
            </summary>
            <param name="stream"></param>
        </member>
        <member name="T:Telerik.Pdf.Gdi.Font.HeaderTable">
            <summary>
                Class that represents the Font Header table.
            </summary>
            <remarks>
                http://www.microsoft.com/typography/otspec/head.htm
            </remarks>
        </member>
        <member name="M:Telerik.Pdf.Gdi.Font.HeaderTable.#ctor(Telerik.Pdf.Gdi.Font.DirectoryEntry)">
            <summary>
                Class constructor.
            </summary>
            <param name="entry"></param>
        </member>
        <member name="P:Telerik.Pdf.Gdi.Font.HeaderTable.IsShortFormat">
            <summary>
                Gets a value that indicates whether glyph offsets in the 
                loca table are stored as a int or ulong.
            </summary>
        </member>
        <member name="M:Telerik.Pdf.Gdi.Font.HeaderTable.Read(Telerik.Pdf.Gdi.Font.FontFileReader)">
            <summary>
                Reads the contents of the "head" table from the current position 
                in the supplied stream.
            </summary>
            <param name="reader"></param>
        </member>
        <member name="M:Telerik.Pdf.Gdi.Font.HeaderTable.GetDate(System.Int64)">
            <summary>
                Returns a DateTime instance which is the result of adding <i>seconds</i>
                to BaseDate.  If an exception occurs, BaseDate is returned.
            </summary>
            <param name="seconds"></param>
        </member>
        <member name="M:Telerik.Pdf.Gdi.Font.HeaderTable.Write(Telerik.Pdf.Gdi.Font.FontFileWriter)">
            <summary>
                Writes the contents of the head table to the supplied stream.
            </summary>
            <param name="writer"></param>
        </member>
        <member name="T:Telerik.Pdf.Gdi.Font.HorizontalHeaderTable">
            <summary>
                Class that represents the Horizontal Header table.
            </summary>
            <remarks>
                http://www.microsoft.com/typography/otspec/hhea.htm
            </remarks>
        </member>
        <member name="F:Telerik.Pdf.Gdi.Font.HorizontalHeaderTable.versionNo">
            <summary>
                Table version number 0x00010000 for version 1.0. 
            </summary>
        </member>
        <member name="F:Telerik.Pdf.Gdi.Font.HorizontalHeaderTable.ascender">
            <summary>
                Typographic ascent. (Distance from baseline of highest ascender).
            </summary>
        </member>
        <member name="F:Telerik.Pdf.Gdi.Font.HorizontalHeaderTable.decender">
            <summary>
                Typographic descent. (Distance from baseline of lowest descender).
            </summary>
        </member>
        <member name="F:Telerik.Pdf.Gdi.Font.HorizontalHeaderTable.lineGap">
            <summary>
                Typographic line gap.  Negative LineGap values are treated as zero 
                in Windows 3.1, System 6, and System 7. 
            </summary>
        </member>
        <member name="F:Telerik.Pdf.Gdi.Font.HorizontalHeaderTable.advanceWidthMax">
            <summary>
                Maximum advance width value in 'hmtx' table. 
            </summary>
        </member>
        <member name="F:Telerik.Pdf.Gdi.Font.HorizontalHeaderTable.minLeftSideBearing">
            <summary>
                Minimum left sidebearing value in 'hmtx' table.
            </summary>
        </member>
        <member name="F:Telerik.Pdf.Gdi.Font.HorizontalHeaderTable.minRightSideBearing">
            <summary>
                Minimum right sidebearing value.
            </summary>
        </member>
        <member name="F:Telerik.Pdf.Gdi.Font.HorizontalHeaderTable.xMaxExtent">
            <summary>
                Max(lsb + (xMax - xMin)).
            </summary>
        </member>
        <member name="F:Telerik.Pdf.Gdi.Font.HorizontalHeaderTable.caretSlopeRise">
            <summary>
                Used to calculate the slope of the cursor (rise/run); 1 for vertical.
            </summary>
        </member>
        <member name="F:Telerik.Pdf.Gdi.Font.HorizontalHeaderTable.caretSlopeRun">
            <summary>
                0 for vertical.
            </summary>
        </member>
        <member name="F:Telerik.Pdf.Gdi.Font.HorizontalHeaderTable.caretOffset">
            <summary>
                The amount by which a slanted highlight on a glyph needs to be 
                shifted to produce the best appearance. Set to 0 for non-slanted fonts.
            </summary>
        </member>
        <member name="F:Telerik.Pdf.Gdi.Font.HorizontalHeaderTable.metricDataFormat">
            <summary>
                0 for current format.
            </summary>
        </member>
        <member name="F:Telerik.Pdf.Gdi.Font.HorizontalHeaderTable.numberOfHMetrics">
            <summary>
                Number of hMetric entries in 'hmtx' table.
            </summary>
        </member>
        <member name="M:Telerik.Pdf.Gdi.Font.HorizontalHeaderTable.#ctor(Telerik.Pdf.Gdi.Font.DirectoryEntry)">
            <summary>
                Class constructor.
            </summary>
            <param name="entry"></param>
        </member>
        <member name="P:Telerik.Pdf.Gdi.Font.HorizontalHeaderTable.HMetricCount">
            <summary>
                Gets the number of horiztonal metrics.
            </summary>
        </member>
        <member name="M:Telerik.Pdf.Gdi.Font.HorizontalHeaderTable.Read(Telerik.Pdf.Gdi.Font.FontFileReader)">
            <summary>
                Reads the contents of the "hhea" table from the current position 
                in the supplied stream.
            </summary>
            <param name="reader"></param>
        </member>
        <member name="T:Telerik.Pdf.Gdi.Font.HorizontalMetric">
            <summary>
                Summary description for HorizontalMetric.
            </summary>
        </member>
        <member name="T:Telerik.Pdf.Gdi.Font.HorizontalMetricsTable">
            <summary>
                Class that represents the Horizontal Metrics ('hmtx') table.
            </summary>
            <remarks>
                http://www.microsoft.com/typography/otspec/hmtx.htm
            </remarks>
        </member>
        <member name="M:Telerik.Pdf.Gdi.Font.HorizontalMetricsTable.#ctor(Telerik.Pdf.Gdi.Font.DirectoryEntry)">
            <summary>
                Initialises a new instance of the 
                <see cref="T:Telerik.Pdf.Gdi.Font.HorizontalMetricsTable"/> class.
            </summary>
            <param name="entry"></param>
        </member>
        <member name="M:Telerik.Pdf.Gdi.Font.HorizontalMetricsTable.#ctor(Telerik.Pdf.Gdi.Font.DirectoryEntry,System.Int32)">
            <summary>
                Initialises a new instance of the HorizontalMetricsTable class.
            </summary>
            <param name="entry"></param>
            <param name="numMetrics"></param>
        </member>
        <member name="P:Telerik.Pdf.Gdi.Font.HorizontalMetricsTable.Count">
            <summary>
                Returns the number of horizontal metrics stored in the 
                hmtx table.
            </summary>
        </member>
        <member name="P:Telerik.Pdf.Gdi.Font.HorizontalMetricsTable.Item(System.Int32)">
            <summary>
                Gets the <see cref="T:Telerik.Pdf.Gdi.Font.HorizontalMetric"/> located at <i>index</i>.
            </summary>
        </member>
        <member name="M:Telerik.Pdf.Gdi.Font.HorizontalMetricsTable.Read(Telerik.Pdf.Gdi.Font.FontFileReader)">
            <summary>
                Reads the contents of the "hmtx" table from the supplied stream 
                at the current position.
            </summary>
            <param name="reader"></param>
        </member>
        <member name="T:Telerik.Pdf.Gdi.Font.IndexToLocationTable">
            <summary>
                Class that represents the Index To Location ('loca') table.
            </summary>
            <remarks>
                http://www.microsoft.com/typography/otspec/loca.htm
            </remarks>
        </member>
        <member name="M:Telerik.Pdf.Gdi.Font.IndexToLocationTable.#ctor(Telerik.Pdf.Gdi.Font.DirectoryEntry)">
            <summary>
                Initialises a new instance of the 
                <see cref="T:Telerik.Pdf.Gdi.Font.IndexToLocationTable"/> class.
            </summary>
            <param name="entry"></param>
        </member>
        <member name="M:Telerik.Pdf.Gdi.Font.IndexToLocationTable.#ctor(Telerik.Pdf.Gdi.Font.DirectoryEntry,System.Int32)">
            <summary>
                Initialises a new instance of the IndexToLocationTable class.
            </summary>
            <param name="entry"></param>
            <param name="numOffsets"></param>
        </member>
        <member name="M:Telerik.Pdf.Gdi.Font.IndexToLocationTable.Read(Telerik.Pdf.Gdi.Font.FontFileReader)">
            <summary>
                Reads the contents of the "loca" table from the supplied stream 
                at the current position.
            </summary>
            <param name="reader"></param>
        </member>
        <member name="M:Telerik.Pdf.Gdi.Font.IndexToLocationTable.Clear">
            <summary>
                Removes all offsets.
            </summary>
        </member>
        <member name="M:Telerik.Pdf.Gdi.Font.IndexToLocationTable.AddOffset(System.Int32)">
            <summary>
                Includes the supplied offset.
            </summary>
            <param name="offset"></param>
        </member>
        <member name="P:Telerik.Pdf.Gdi.Font.IndexToLocationTable.Count">
            <summary>
                Gets the number of glyph offsets.
            </summary>
        </member>
        <member name="P:Telerik.Pdf.Gdi.Font.IndexToLocationTable.Item(System.Int32)">
            <summary>
                Gets or sets the glyph offset at index <i>index</i>.
            </summary>
            <param name="index">A glyph index.</param>
            <returns></returns>
        </member>
        <member name="T:Telerik.Pdf.Gdi.Font.KerningTable">
            <summary>
                Class that represents the Kerning table.
            </summary>
            <remarks>
                http://www.microsoft.com/typography/otspec/kern.htm
            </remarks>
        </member>
        <member name="M:Telerik.Pdf.Gdi.Font.KerningTable.#ctor(Telerik.Pdf.Gdi.Font.DirectoryEntry)">
            <summary>
                Class constructor.
            </summary>
            <param name="entry"></param>
        </member>
        <member name="P:Telerik.Pdf.Gdi.Font.KerningTable.HasKerningInfo">
            <summary>
                Gets a boolean value that indicates this font contains format 0
                kerning information.
            </summary>
        </member>
        <member name="P:Telerik.Pdf.Gdi.Font.KerningTable.KerningPairs">
            <summary>
                Returns a collection of kerning pairs.
            </summary>
            <remarks>
                If <i>HasKerningInfo</i> returns <b>false</b>, this method will 
                always return null.
            </remarks>
        </member>
        <member name="M:Telerik.Pdf.Gdi.Font.KerningTable.Read(Telerik.Pdf.Gdi.Font.FontFileReader)">
            <summary>
                Reads the contents of the "kern" table from the current position 
                in the supplied stream.
            </summary>
            <param name="reader"></param>
        </member>
        <member name="M:Telerik.Pdf.Gdi.Font.KerningTable.Write(Telerik.Pdf.Gdi.Font.FontFileWriter)">
            <summary>
                No supported.
            </summary>
            <param name="writer"></param>
        </member>
        <member name="T:Telerik.Pdf.Gdi.Font.MaximumProfileTable">
            <summary>
                Class that represents the Horizontal Metrics ('maxp') table.
            </summary>
            <remarks>
                http://www.microsoft.com/typography/otspec/maxp.htm
            </remarks>
        </member>
        <member name="F:Telerik.Pdf.Gdi.Font.MaximumProfileTable.versionNo">
            <summary>
                Table version number
            </summary>
        </member>
        <member name="F:Telerik.Pdf.Gdi.Font.MaximumProfileTable.numGlyphs">
            <summary>
                The number of glyphs in the font.
            </summary>
        </member>
        <member name="F:Telerik.Pdf.Gdi.Font.MaximumProfileTable.maxPoints">
            <summary>
                Maximum points in a non-composite glyph. 
            </summary>
        </member>
        <member name="F:Telerik.Pdf.Gdi.Font.MaximumProfileTable.maxContours">
            <summary>
                Maximum contours in a non-composite glyph.  Only set if 
                <i>versionNo</i> is 1.0.
            </summary>
        </member>
        <member name="F:Telerik.Pdf.Gdi.Font.MaximumProfileTable.maxCompositePoints">
            <summary>
                Maximum points in a composite glyph.  Only set if 
                <i>versionNo</i> is 1.0.
            </summary>
        </member>
        <member name="F:Telerik.Pdf.Gdi.Font.MaximumProfileTable.maxCompositeContours">
            <summary>
                Maximum contours in a composite glyph.  Only set if 
                <i>versionNo</i> is 1.0.
            </summary>
        </member>
        <member name="F:Telerik.Pdf.Gdi.Font.MaximumProfileTable.maxZones">
            <summary>
                1 if instructions do not use the twilight zone (Z0), or 
                2 if instructions do use Z0; should be set to 2 in most 
                cases.  Only set if <i>versionNo</i> is 1.0.
            </summary>
        </member>
        <member name="F:Telerik.Pdf.Gdi.Font.MaximumProfileTable.maxTwilightPoints">
            <summary>
                Maximum points used in Z0.   Only set if 
                <i>versionNo</i> is 1.0.
            </summary>
        </member>
        <member name="F:Telerik.Pdf.Gdi.Font.MaximumProfileTable.maxStorage">
            <summary>
                Number of Storage Area locations.  Only set if 
                <i>versionNo</i> is 1.0.
            </summary>
        </member>
        <member name="F:Telerik.Pdf.Gdi.Font.MaximumProfileTable.maxFunctionDefs">
            <summary>
                Number of FDEFs.   Only set if <i>versionNo</i> is 1.0.
            </summary>
        </member>
        <member name="F:Telerik.Pdf.Gdi.Font.MaximumProfileTable.maxInstructionDefs">
            <summary>
                Number of IDEFs.   Only set if <i>versionNo</i> is 1.0.
            </summary>
        </member>
        <member name="F:Telerik.Pdf.Gdi.Font.MaximumProfileTable.maxStackElements">
            <summary>
                Maximum stack depth2.  Only set if <i>versionNo</i> is 1.0.
            </summary>
        </member>
        <member name="F:Telerik.Pdf.Gdi.Font.MaximumProfileTable.maxSizeOfInstructions">
            <summary>
                Maximum byte count for glyph instructions.  Only set 
                if <i>versionNo</i> is 1.0.
            </summary>
        </member>
        <member name="F:Telerik.Pdf.Gdi.Font.MaximumProfileTable.maxComponentElements">
            <summary>
                Maximum number of components referenced at "top level" 
                for any composite glyph.   Only set if 
                <i>versionNo</i> is 1.0.
            </summary>
        </member>
        <member name="F:Telerik.Pdf.Gdi.Font.MaximumProfileTable.maxComponentDepth">
            <summary>
                Maximum levels of recursion; 1 for simple components. 
                Only set if <i>versionNo</i> is 1.0.
            </summary>
        </member>
        <member name="M:Telerik.Pdf.Gdi.Font.MaximumProfileTable.#ctor(Telerik.Pdf.Gdi.Font.DirectoryEntry)">
            <summary>
                Initialises a new instance of the <see cref="T:Telerik.Pdf.Gdi.Font.MaximumProfileTable"/>
                class.
            </summary>
            <param name="entry"></param>
        </member>
        <member name="P:Telerik.Pdf.Gdi.Font.MaximumProfileTable.GlyphCount">
            <summary>
                Gets the number of glyphs
            </summary>
        </member>
        <member name="M:Telerik.Pdf.Gdi.Font.MaximumProfileTable.Read(Telerik.Pdf.Gdi.Font.FontFileReader)">
            <summary>
                Reads the contents of the "maxp" table from the supplied stream 
                at the current position.
            </summary>
            <param name="reader"></param>
        </member>
        <member name="T:Telerik.Pdf.Gdi.Font.NameTable">
            <summary>
                Class that represents the Naming ('name') table
            </summary>
            <remarks>
                http://www.microsoft.com/typography/otspec/name.htm
            </remarks>
        </member>
        <member name="F:Telerik.Pdf.Gdi.Font.NameTable.storageOffset">
            <summary>
                Offset to start of string storage (from start of table).
            </summary>
        </member>
        <member name="P:Telerik.Pdf.Gdi.Font.NameTable.FamilyName">
            <summary>
                Get the font family name.
            </summary>
        </member>
        <member name="P:Telerik.Pdf.Gdi.Font.NameTable.FullName">
            <summary>
                Gets the font full name composed of the family name and the 
                subfamily name.
            </summary>
        </member>
        <member name="M:Telerik.Pdf.Gdi.Font.NameTable.Read(Telerik.Pdf.Gdi.Font.FontFileReader)">
            <summary>
                Reads the contents of the "name" table from the supplied stream 
                at the current position.
            </summary>
            <param name="reader"></param>
        </member>
        <member name="M:Telerik.Pdf.Gdi.Font.NameTable.ReadString(Telerik.Pdf.Gdi.Font.FontFileStream,System.Int32,System.Int32)">
            <summary>
                Reads a string from the storage area beginning at <i>offset</i>
                consisting of <i>length</i> bytes.  The returned string will be 
                converted using the Unicode encoding.
            </summary>
            <param name="stream">Big-endian font stream.</param>
            <param name="stringOffset">
                The offset in bytes from the beginning of the string storage area.
             </param>
            <param name="length">The length of the string in bytes.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Pdf.Gdi.Font.NameTable.Write(Telerik.Pdf.Gdi.Font.FontFileWriter)">
            <summary>
                Not supported.
            </summary>
            <param name="writer"></param>
        </member>
        <member name="T:Telerik.Pdf.Gdi.Font.OS2Table">
            <summary>
                Class that represents the OS/2 ('OS/2') table
            </summary>
            <remarks>
                <p>For detailed information on the OS/2 table, visit the following link:
                http://www.microsoft.com/typography/otspec/os2.htm</p>
                <p>For more details on the Panose classification metrics, visit the following URL:
                http://www.panose.com/hardware/pan2.asp</p>
            </remarks>
        </member>
        <member name="P:Telerik.Pdf.Gdi.Font.OS2Table.IsItalic">
            <summary>
                Gets a boolean value that indicates whether this font contains 
                italic characters.
            </summary>
        </member>
        <member name="P:Telerik.Pdf.Gdi.Font.OS2Table.IsRegular">
            <summary>
                Gets a boolean value that indicates whether characters are 
                in the standard weight/style.
            </summary>
        </member>
        <member name="P:Telerik.Pdf.Gdi.Font.OS2Table.IsBold">
            <summary>
                Gets a boolean value that indicates whether characters possess
                a weight greater than or equal to 700.
            </summary>
        </member>
        <member name="P:Telerik.Pdf.Gdi.Font.OS2Table.IsMonospaced">
            <summary>
                Gets a boolean value that indicates whether this font contains 
                characters that all have the same width.
            </summary>
        </member>
        <member name="P:Telerik.Pdf.Gdi.Font.OS2Table.IsSymbolic">
            <summary>
                Gets a boolean value that indicates whether this font contains 
                special characters such as dingbats, icons, etc.
            </summary>
        </member>
        <member name="P:Telerik.Pdf.Gdi.Font.OS2Table.IsSerif">
            <summary>
                Gets a boolean value that indicates whether characters  
                do possess serifs
            </summary>
        </member>
        <member name="P:Telerik.Pdf.Gdi.Font.OS2Table.IsScript">
            <summary>
                Gets a boolean value that indicates whether characters 
                are designed to simulate hand writing.
            </summary>
        </member>
        <member name="P:Telerik.Pdf.Gdi.Font.OS2Table.IsSansSerif">
            <summary>
                Gets a boolean value that indicates whether characters  
                do not possess serifs
            </summary>
        </member>
        <member name="P:Telerik.Pdf.Gdi.Font.OS2Table.IsEmbeddable">
            <summary>
                Gets a boolean value that indicates whether this font may be 
                legally embedded.
            </summary>
        </member>
        <member name="P:Telerik.Pdf.Gdi.Font.OS2Table.IsSubsettable">
            <summary>
                Gets a boolean value that indicates whether this font may be 
                subsetted.
            </summary>
        </member>
        <member name="M:Telerik.Pdf.Gdi.Font.OS2Table.Read(Telerik.Pdf.Gdi.Font.FontFileReader)">
            <summary>
                Reads the contents of the "os/2" table from the supplied stream 
                at the current position.
            </summary>
            <param name="reader"></param>
        </member>
        <member name="T:Telerik.Pdf.Gdi.Font.PostTable">
            <summary>
                Class that represents the PostScript ('post') table
            </summary>
            <remarks>
                http://www.microsoft.com/typography/otspec/post.htm
            </remarks>
        </member>
        <member name="F:Telerik.Pdf.Gdi.Font.PostTable.version">
            <summary>
                0x00010000 for version 1.0 
                0x00020000 for version 2.0 
                0x00025000 for version 2.5 (deprecated) 
                0x00030000 for version 3.0 
            </summary>
        </member>
        <member name="F:Telerik.Pdf.Gdi.Font.PostTable.italicAngle">
            <summary>
                Italic angle in counter-clockwise degrees from the vertical. 
                Zero for upright text, negative for text that leans to the 
                right (forward). 
            </summary>
        </member>
        <member name="F:Telerik.Pdf.Gdi.Font.PostTable.underlinePosition">
            <summary>
                This is the suggested distance of the top of the underline from 
                the baseline (negative values indicate below baseline). 
            </summary>
        </member>
        <member name="F:Telerik.Pdf.Gdi.Font.PostTable.underlineThickness">
            <summary>
                Suggested values for the underline thickness. 
            </summary>
        </member>
        <member name="F:Telerik.Pdf.Gdi.Font.PostTable.fixedPitch">
            <summary>
                Set to 0 if the font is proportionally spaced, non-zero if the 
                font is not proportionally spaced (i.e. monospaced). 
            </summary>
        </member>
        <member name="F:Telerik.Pdf.Gdi.Font.PostTable.minMemType42">
            <summary>
                Minimum memory usage when an OpenType font is downloaded. 
            </summary>
        </member>
        <member name="F:Telerik.Pdf.Gdi.Font.PostTable.maxMemType42">
            <summary>
                Maximum memory usage when an OpenType font is downloaded. 
            </summary>
        </member>
        <member name="F:Telerik.Pdf.Gdi.Font.PostTable.minMemType1">
            <summary>
                Minimum memory usage when an OpenType font is downloaded 
                as a Type 1 font. 
            </summary>
        </member>
        <member name="F:Telerik.Pdf.Gdi.Font.PostTable.maxMemType1">
            <summary>
                Maximum memory usage when an OpenType font is downloaded 
                as a Type 1 font. 
            </summary>
        </member>
        <member name="M:Telerik.Pdf.Gdi.Font.PostTable.#ctor(Telerik.Pdf.Gdi.Font.DirectoryEntry)">
            <summary>
                Class constructor.
            </summary>
            <param name="entry"></param>
        </member>
        <member name="P:Telerik.Pdf.Gdi.Font.PostTable.IsFixedPitch">
            <summary>
                Gets a boolean value that indicates whether this font is 
                proportionally spaced (fixed pitch) or not.
            </summary>
        </member>
        <member name="M:Telerik.Pdf.Gdi.Font.PostTable.Read(Telerik.Pdf.Gdi.Font.FontFileReader)">
            <summary>
                Reads the contents of the "post" table from the supplied stream 
                at the current position.
            </summary>
            <param name="reader"></param>
        </member>
        <member name="T:Telerik.Pdf.Gdi.Font.TrueTypeHeader">
            <summary>
                Class that represents the Offset and Directory tables.
            </summary>
            <remarks>
                http://www.microsoft.com/typography/otspec/otff.htm
            </remarks>
        </member>
        <member name="M:Telerik.Pdf.Gdi.Font.TrueTypeHeader.Contains(System.String)">
            <summary>
                Gets a value indicating whether or not this font contains the 
                supplied table.
            </summary>
            <param name="tableName">A table name.</param>
            <returns></returns>
        </member>
        <member name="P:Telerik.Pdf.Gdi.Font.TrueTypeHeader.Item(System.String)">
            <summary>
                Gets a DirectoryEntry object for the supplied table.
            </summary>
            <param name="tableName">A 4-character code identifying a table.</param>
            <returns>
                A DirectoryEntry object or null if the table cannot be located.
            </returns>
            <exception cref="T:System.ArgumentException">
                If <b>tableName</b> does not represent a table in this font.
            </exception>
        </member>
        <member name="P:Telerik.Pdf.Gdi.Font.TrueTypeHeader.Count">
            <summary>
                Gets the number tables.
            </summary>
        </member>
        <member name="T:Telerik.Pdf.Gdi.GdiDeviceContent">
            <summary>
                A very lightweight wrapper around a Win32 device context
            </summary>
        </member>
        <member name="F:Telerik.Pdf.Gdi.GdiDeviceContent.hDC">
            <summary>
                Pointer to device context created by ::CreateDC()
            </summary>
        </member>
        <member name="M:Telerik.Pdf.Gdi.GdiDeviceContent.#ctor">
            <summary>
                Creates a new device context that matches the desktop display surface
            </summary>
        </member>
        <member name="M:Telerik.Pdf.Gdi.GdiDeviceContent.Finalize">
            <summary>
            </summary>
        </member>
        <member name="M:Telerik.Pdf.Gdi.GdiDeviceContent.Dispose(System.Boolean)">
            <summary>
                Delete the device context freeing the associated memory.
            </summary>
        </member>
        <member name="M:Telerik.Pdf.Gdi.GdiDeviceContent.SelectFont(Telerik.Pdf.Gdi.GdiFont)">
            <summary>
                Selects a font into a device context (DC). The new object 
                replaces the previous object of the same type. 
            </summary>
            <param name="font">Handle to object.</param>
            <returns>A handle to the object being replaced.</returns>
        </member>
        <member name="M:Telerik.Pdf.Gdi.GdiDeviceContent.GetCurrentObject(Telerik.WinControls.NativeMethods.GdiDcObject)">
            <summary>
                Gets a handle to an object of the specified type that has been 
                selected into this device context. 
            </summary>
        </member>
        <member name="P:Telerik.Pdf.Gdi.GdiDeviceContent.Handle">
            <summary>
                Returns a handle to the underlying device context
            </summary>
        </member>
        <member name="T:Telerik.Pdf.Gdi.GdiFont">
            <summary>
                A thin wrapper around a handle to a font
            </summary>
        </member>
        <member name="M:Telerik.Pdf.Gdi.GdiFont.#ctor(System.IntPtr,System.String,System.Int32)">
            <summary>
                Class constructor
            </summary>
            <param name="hFont">A handle to an existing font.</param>
            <param name="faceName"></param>
            <param name="height"></param>
        </member>
        <member name="M:Telerik.Pdf.Gdi.GdiFont.Finalize">
            <summary>
                Class destructor
            </summary>
        </member>
        <member name="M:Telerik.Pdf.Gdi.GdiFont.CreateFont(System.String,System.Int32,System.Boolean,System.Boolean)">
            <summary>
                Creates a font based on the supplied typeface name and size.
            </summary>
            <param name="faceName">The typeface name of a font.</param>
            <param name="height">
                The height, in logical units, of the font's character 
                cell or character.
            </param>
            <param name="bold"></param>
            <param name="italic"></param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Pdf.Gdi.GdiFont.CreateDesignFont(System.String,System.Boolean,System.Boolean,Telerik.Pdf.Gdi.GdiDeviceContent)">
            <summary>
                Creates a font whose height is equal to the negative value 
                of the EM Square
            </summary>
            <param name="faceName">The typeface name of a font.</param>
            <param name="bold"></param>
            <param name="italic"></param>
            <param name="dc"></param>
            <returns></returns>
        </member>
        <member name="T:Telerik.Pdf.Gdi.GdiFontCreator">
            <summary>
                Retrieves all pertinent TrueType tables by invoking GetFontData.
            </summary>
        </member>
        <member name="T:Telerik.Pdf.Gdi.GdiFontEnumerator">
            <summary>
                Summary description for GdiFontEnumerator.
            </summary>
        </member>
        <member name="M:Telerik.Pdf.Gdi.GdiFontEnumerator.#ctor(Telerik.Pdf.Gdi.GdiDeviceContent)">
            <summary>
                Class constructor.
            </summary>
            <param name="dc">A non-null reference to a wrapper around a GDI device context.</param>
        </member>
        <member name="P:Telerik.Pdf.Gdi.GdiFontEnumerator.FamilyNames">
            <summary>
                Returns a list of font family names sorted in ascending order.
            </summary>
        </member>
        <member name="M:Telerik.Pdf.Gdi.GdiFontEnumerator.GetStyles(System.String)">
            <summary>
                Returns a list of font styles associated with <i>familyName</i>.
            </summary>
            <param name="familyName"></param>
            <returns></returns>
        </member>
        <member name="T:Telerik.Pdf.Gdi.GdiFontMetrics">
            <summary>
                Class that obtains OutlineTextMetrics for a TrueType font
            </summary>
            <example>
            </example>
        </member>
        <member name="P:Telerik.Pdf.Gdi.GdiFontMetrics.FaceName">
            <summary>
                Retrieves the typeface name of the font that is selected into the 
                device context supplied to the GdiFontMetrics constructor. 
            </summary>
        </member>
        <member name="P:Telerik.Pdf.Gdi.GdiFontMetrics.EmSquare">
            <summary>
                Specifies the number of logical units defining the x- or y-dimension 
                of the em square for this font.  The common value for EmSquare is 2048.
            </summary>
            <remarks>
                The number of units in the x- and y-directions are always the same 
                for an em square.) 
            </remarks>
        </member>
        <member name="P:Telerik.Pdf.Gdi.GdiFontMetrics.ItalicAngle">
            <summary>
                Gets the main italic angle of the font expressed in tenths of 
                a degree counterclockwise from the vertical.
            </summary>
            <remarks>
                Regular (roman) fonts have a value of zero. Italic fonts typically 
                have a negative italic angle (that is, they lean to the right). 
            </remarks>
        </member>
        <member name="P:Telerik.Pdf.Gdi.GdiFontMetrics.Ascent">
            <summary>
                Specifies the maximum distance characters in this font extend 
                above the base line. This is the typographic ascent for the font. 
            </summary>
        </member>
        <member name="P:Telerik.Pdf.Gdi.GdiFontMetrics.Descent">
            <summary>
                Specifies the maximum distance characters in this font extend 
                below the base line. This is the typographic descent for the font. 
            </summary>
        </member>
        <member name="P:Telerik.Pdf.Gdi.GdiFontMetrics.CapHeight">
            <summary>
                Gets the distance between the baseline and the approximate 
                height of uppercase letters.
            </summary>
        </member>
        <member name="P:Telerik.Pdf.Gdi.GdiFontMetrics.XHeight">
            <summary>
                Gets the distance between the baseline and the approximate 
                height of non-ascending lowercase letters.
            </summary>
        </member>
        <member name="P:Telerik.Pdf.Gdi.GdiFontMetrics.StemV">
            <summary>
                TODO: The thickness, measured horizontally, of the dominant vertical 
                stems of the glyphs in the font.
            </summary>
        </member>
        <member name="P:Telerik.Pdf.Gdi.GdiFontMetrics.FirstChar">
            <summary>
                Gets the value of the first character defined in the font
            </summary>
        </member>
        <member name="P:Telerik.Pdf.Gdi.GdiFontMetrics.LastChar">
            <summary>
                Gets the value of the last character defined in the font
            </summary>
        </member>
        <member name="P:Telerik.Pdf.Gdi.GdiFontMetrics.AverageWidth">
            <summary>
                Gets the average width of glyphs in a font.
            </summary>
        </member>
        <member name="P:Telerik.Pdf.Gdi.GdiFontMetrics.MaxWidth">
            <summary>
                Gets the maximum width of glyphs in a font.
            </summary>
        </member>
        <member name="P:Telerik.Pdf.Gdi.GdiFontMetrics.IsEmbeddable">
            <summary>
                Gets a value indicating whether the font can be legally embedded 
                within a document.
            </summary>
        </member>
        <member name="P:Telerik.Pdf.Gdi.GdiFontMetrics.IsSubsettable">
            <summary>
                Gets a value indicating whether the font can be legally subsetted.
            </summary>
        </member>
        <member name="P:Telerik.Pdf.Gdi.GdiFontMetrics.BoundingBox">
            <summary>
                Gets the font's bounding box.
            </summary>
            <remarks>
                This is the smallest rectangle enclosing the shape that would 
                result if all the glyphs of the font were placed with their 
                origins cooincident and then filled.
            </remarks>
        </member>
        <member name="P:Telerik.Pdf.Gdi.GdiFontMetrics.Flags">
            <summary>
                Gets a collection of flags defining various characteristics of 
                a font (e.g. serif or sans-serif, symbolic, etc).
            </summary>
        </member>
        <member name="M:Telerik.Pdf.Gdi.GdiFontMetrics.GetFontData">
            <summary>
                Gets font metric data for a TrueType font or TrueType collection.
            </summary>
            <returns></returns>
        </member>
        <member name="P:Telerik.Pdf.Gdi.GdiFontMetrics.KerningPairs">
            <summary>
                Gets a collection of kerning pairs.
            </summary>
            <returns></returns>
        </member>
        <member name="P:Telerik.Pdf.Gdi.GdiFontMetrics.AnsiKerningPairs">
            <summary>
                Gets a collection of kerning pairs for characters defined in 
                the WinAnsiEncoding scheme only.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Telerik.Pdf.Gdi.GdiFontMetrics.GetWidths">
            <summary>
                Retrieves the widths, in PDF units, of consecutive glyphs.
            </summary>
            <returns>
                An array of integers whose size is equal to the number of glyphs 
                specified in the 'maxp' table.
                The width at location 0 is the width of glyph with index 0, 
                The width at location 1 is the width of glyph with index 1, 
                etc...
            </returns>
        </member>
        <member name="M:Telerik.Pdf.Gdi.GdiFontMetrics.GetAnsiWidths">
            <summary>
                Returns the width, in PDF units, of consecutive glyphs for the 
                WinAnsiEncoding only.
            </summary>
            <returns>An array consisting of 256 elements.</returns>
        </member>
        <member name="M:Telerik.Pdf.Gdi.GdiFontMetrics.MapCharacter(System.Char)">
            <summary>
                Translates the supplied character to a glyph index using the 
                currently selected font.
            </summary>
            <param name="c">A unicode character.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Pdf.Gdi.GdiKerningPairs.#ctor(Telerik.Pdf.Gdi.Font.KerningPairs,Telerik.Pdf.Gdi.PdfUnitConverter)">
            <summary>
                Class constructor.
            </summary>
            <param name="pairs">Kerning pairs read from the TrueType font file.</param>
            <param name="converter">Class to convert from TTF to PDF units.</param>
        </member>
        <member name="P:Telerik.Pdf.Gdi.GdiKerningPairs.Count">
            <summary>
                Gets the number of kerning pairs.
            </summary>
        </member>
        <member name="M:Telerik.Pdf.Gdi.GdiKerningPairs.HasPair(System.Int32,System.Int32)">
            <summary>
                Returns true if a kerning value exists for the supplied 
                character index pair.
            </summary>
            <param name="left"></param>
            <param name="right"></param>
            <returns></returns>
        </member>
        <member name="P:Telerik.Pdf.Gdi.GdiKerningPairs.Item(System.Int32,System.Int32)">
            <summary>
                Gets the kerning amount for the supplied index pair or 0 if 
                a kerning pair does not exist.
            </summary>
        </member>
        <member name="T:Telerik.Pdf.Gdi.GdiPrivateFontCollection">
            <summary>
                Installs a collection of private fonts on the system and uninstalls 
                them when disposed.
            </summary>
        </member>
        <member name="F:Telerik.Pdf.Gdi.GdiPrivateFontCollection.FR_PRIVATE">
            <summary>
                Specifies that only the process that called the AddFontResourceEx 
                function can use this font.
            </summary>
        </member>
        <member name="F:Telerik.Pdf.Gdi.GdiPrivateFontCollection.FR_NOT_ENUM">
            <summary>
                Specifies that no process, including the process that called the 
                AddFontResourceEx function, can enumerate this font.
            </summary>
        </member>
        <member name="F:Telerik.Pdf.Gdi.GdiPrivateFontCollection.fonts">
            <summary>
                Collection of absolute filenames.
            </summary>
        </member>
        <member name="M:Telerik.Pdf.Gdi.GdiPrivateFontCollection.AddFontFile(System.String)">
            <summary>
                Adds <i>filename</i> to this private font collection.
            </summary>
            <param name="filename">
                Absolute path to a TrueType font or collection.
            </param>
            <seealso cref="M:Telerik.Pdf.Gdi.GdiPrivateFontCollection.AddFontFile(System.IO.FileInfo)" />
            <exception cref="T:System.ArgumentNullException">If <i>filename</i> is null.</exception>
            <exception cref="T:System.ArgumentException">If <i>filename</i> is the empty string.</exception>
        </member>
        <member name="M:Telerik.Pdf.Gdi.GdiPrivateFontCollection.AddFontFile(System.IO.FileInfo)">
            <summary>
                Adds <i>fontFile</i> to this private font collection.
            </summary>
            <param name="fontFile">
                Absolute path to a TrueType font or collection.
            </param>
            <exception cref="T:System.IO.FileNotFoundException">
                If <i>fontFile</i> does not exist.
            </exception>
            <exception cref="T:System.ArgumentException">
                If <i>fontFile</i> has already been added.
            </exception>
            <exception cref="T:System.ArgumentException">
                If <i>fontFile</i> cannot be added to the system font collection.
            </exception>
        </member>
        <member name="T:Telerik.Pdf.Gdi.GdiUnicodeRanges">
            <summary>
                Custom collection that maintains a list of Unicode ranges 
                a font supports and the glyph indices of each character.
                The list of ranges is obtained by invoking GetFontUnicodeRanges,
                however the associated glyph indices are lazily instantiated as 
                required to save memory.
            </summary>
        </member>
        <member name="F:Telerik.Pdf.Gdi.GdiUnicodeRanges.unicodeRanges">
            <summary>
                List of unicode ranges in ascending numerical order.  The order 
                is important since a binary search is used to locate and 
                uicode range from a charcater.
            </summary>
        </member>
        <member name="M:Telerik.Pdf.Gdi.GdiUnicodeRanges.#ctor(Telerik.Pdf.Gdi.GdiDeviceContent)">
            <summary>
                Class constuctor.
            </summary>
            <param name="dc"></param>
        </member>
        <member name="P:Telerik.Pdf.Gdi.GdiUnicodeRanges.Count">
            <summary>
                Gets the number of unicode ranges.
            </summary>
        </member>
        <member name="M:Telerik.Pdf.Gdi.GdiUnicodeRanges.LoadRanges(Telerik.Pdf.Gdi.GdiDeviceContent)">
            <summary>
                Loads all the unicode ranges.
            </summary>
        </member>
        <member name="M:Telerik.Pdf.Gdi.GdiUnicodeRanges.GetRange(System.Char)">
            <summary>
                Locates the <see cref="T:Telerik.Pdf.Gdi.UnicodeRange"/> for the supplied character.
            </summary>
            <param name="c"></param>
            <returns>
                The <see cref="T:Telerik.Pdf.Gdi.UnicodeRange"/> object housing <i>c</i> or null 
                if a range does not exist for <i>c</i>.
            </returns>
        </member>
        <member name="M:Telerik.Pdf.Gdi.GdiUnicodeRanges.MapCharacter(System.Char)">
            <summary>
                Translates the supplied character to a glyph index.
            </summary>
            <param name="c">Any unicode character.</param>
            <returns>
                A glyph index for <i>c</i> or 0 the supplied character does 
                not exist in the font selected into the device context.
            </returns>
        </member>
        <member name="T:Telerik.Pdf.Gdi.PdfUnitConverter">
            <summary>
                Converts from logical TTF units to PDF units.
            </summary>
        </member>
        <member name="M:Telerik.Pdf.Gdi.PdfUnitConverter.#ctor(System.Int32)">
            <summary>
                Class constructor.
            </summary>
            <param name="emSquare">
                Specifies the number of logical units defining the x- or 
                y-dimension of the em square of a font.
            </param>
        </member>
        <member name="M:Telerik.Pdf.Gdi.PdfUnitConverter.ToPdfUnits(System.Int32)">
            <summary>
                Convert the supplied integer from TrueType units to PDF units 
                based on the EmSquare
            </summary>
            <param name="value"></param>
            <returns>
                If the value of <i>emSquare</i> is zero, this method will 
                always return <i>value</i>.
            </returns>
        </member>
        <member name="T:Telerik.Pdf.Gdi.Abc">
            <summary>
                The ABC structure contains the width of a character in a TrueType font. 
            </summary>
        </member>
        <member name="T:Telerik.Pdf.Gdi.OutlineTextMetric">
            <summary>
                The OUTLINETEXTMETRIC structure contains metrics describing 
                a TrueType font. 
            </summary>
        </member>
        <member name="T:Telerik.Pdf.Gdi.Panose">
            <summary>
                The PANOSE structure describes the PANOSE font-classification values 
                for a TrueType font. These characteristics are then used to associate 
                the font with other fonts of similar appearance but different names. 
            </summary>
        </member>
        <member name="T:Telerik.Pdf.Gdi.Point">
            <summary>
                The Point structure defines the x- and y- coordinates of a point. 
            </summary>
        </member>
        <member name="T:Telerik.Pdf.Gdi.Rect">
            <summary>
                The Rect structure defines the coordinates of the upper-left 
                and lower-right corners of a rectangle
            </summary>
        </member>
        <member name="T:Telerik.Pdf.Gdi.TextMetric">
            <summary>
                The TEXTMETRIC structure contains basic information about a physical 
                font.  All sizes are specified in logical units; that is, they depend 
                on the current mapping mode of the display context. 
            </summary>
        </member>
        <member name="T:Telerik.Pdf.Gdi.UnicodeRange">
            <summary>
                Class that represents a unicode character range as returned 
                by the GetFontUnicodeRanges function.
            </summary>
        </member>
        <member name="F:Telerik.Pdf.Gdi.UnicodeRange.indices">
            <summary>
                Array of glyph indices for each character represented by 
                this range begining at <see cref="P:Telerik.Pdf.Gdi.UnicodeRange.Start"/>.
            </summary>
        </member>
        <member name="M:Telerik.Pdf.Gdi.UnicodeRange.#ctor(Telerik.Pdf.Gdi.GdiDeviceContent,System.Int32,System.Int32)">
            <summary>
                Class constructor.
            </summary>
            <param name="dc"></param>
            <param name="start">Value representing start of unicode range.</param>
            <param name="end">Value representing end of unicode range.</param>
        </member>
        <member name="M:Telerik.Pdf.Gdi.UnicodeRange.MapCharacter(System.Char)">
            <summary>
                Returns the glyph index of <i>c</i>.
            </summary>
            <param name="c"></param>
            <returns></returns>
        </member>
        <member name="P:Telerik.Pdf.Gdi.UnicodeRange.Start">
            <summary>
                Gets a value representing the start of the unicode range.
            </summary>
        </member>
        <member name="P:Telerik.Pdf.Gdi.UnicodeRange.End">
            <summary>
                Gets a value representing the end of the unicode range.
            </summary>
        </member>
        <member name="M:Telerik.Pdf.Gdi.UnicodeRange.LoadGlyphIndices">
            <summary>
                Populates the <i>indices</i> array with the glyph index of each 
                character represented by this rnage starting at <see cref="P:Telerik.Pdf.Gdi.UnicodeRange.Start"/>.
            </summary>
        </member>
        <member name="T:Telerik.Pdf.Gdi.UnicodeRangeComparer">
            <summary>
            Summary description for UnicodeRangeComparer.
            </summary>
        </member>
        <member name="T:Telerik.Pdf.Gdi.WinAnsiMapping">
            <summary>
                Maps a Unicode character to a WinAnsi codepoint value.
            </summary>
        </member>
        <member name="F:Telerik.Pdf.Gdi.WinAnsiMapping.winAnsiEncoding">
            <summary>
                First column is codepoint value.  Second column is unicode value.
            </summary>
        </member>
        <member name="T:Telerik.Pdf.PdfCatalog">
            <summary>
                The root of a document's object hierarchy is the catalog dictionary.
            </summary>
            <remarks>
                The document catalog is described in section 3.6.1 of the PDF specification.
            </remarks>
        </member>
        <member name="T:Telerik.Pdf.PdfCIDFont">
            <summary>
                A dictionary that contains information about a CIDFont program.
            </summary>
            <remarks>
                A Type 0 CIDFont contains glyph descriptions based on Adobe's Type 
                1 font format, whereas those in a Type 2 CIDFont are based on the 
                TrueType font format.
            </remarks>
        </member>
        <member name="T:Telerik.Pdf.PdfCIDSystemInfo">
            <summary>
                A dictionary containing entries that define the character collection
                of the CIDFont.
            </summary>
        </member>
        <member name="T:Telerik.Pdf.PdfCMap">
            <summary>
                Class that defines a mapping between character codes (CIDs) 
                to a character selector (Identity-H encoding)
            </summary>
        </member>
        <member name="M:Telerik.Pdf.PdfCMap.AddBfRanges(System.Collections.IDictionary)">
            <summary>
                Adds the supplied glyph -> unicode pairs.
            </summary>
            <remarks>
                Both the key and value must be a int.
            </remarks>
            <param name="map"></param>
        </member>
        <member name="M:Telerik.Pdf.PdfCMap.AddBfRange(System.Int32,System.Int32)">
            <summary>
                Adds the supplied glyph index to unicode value mapping.
            </summary>
            <param name="glyphIndex"></param>
            <param name="unicodeValue"></param>
        </member>
        <member name="M:Telerik.Pdf.PdfCMap.Write(Telerik.Pdf.PdfWriter)">
            <summary>
                Overriden to create CMap content stream.
            </summary>
            <param name="writer"></param>
        </member>
        <member name="M:Telerik.Pdf.PdfCMap.WriteBfChars(Telerik.Pdf.BfEntryList)">
            <summary>
                Writes the bfchar entries to the content stream in groups of 100.
            </summary>
            <param name="entries"></param>
        </member>
        <member name="M:Telerik.Pdf.PdfCMap.WriteBfRanges(Telerik.Pdf.BfEntryList)">
            <summary>
                Writes the bfrange entries to the content stream in groups of 100.
            </summary>
            <param name="entries"></param>
        </member>
        <member name="M:Telerik.Pdf.PdfContentStream.Write(System.String)">
            <summary>
                TODO: This method is temporary.  I'm assuming that all string should 
                be represented as a PdfString object?
            </summary>
            <param name="s"></param>
        </member>
        <member name="T:Telerik.Pdf.PdfDate">
            <summary>
                PDF defines a standard date format. The PDF date format closely 
                follows the format defined by the international standard ASN.1.
            </summary>
            <remarks>
                The format of the PDF date is defined in section 3.8.2 of the 
                PDF specification.
            </remarks>
        </member>
        <member name="T:Telerik.Pdf.PdfDocument">
            <summary>
                A class that enables a well structured PDF document to be generated.
            </summary>
            <remarks>
                Responsible for allocating object identifiers.
            </remarks>
        </member>
        <member name="T:Telerik.Pdf.PdfFileTrailer">
            <summary>
                Class representing a file trailer.
            </summary>
            <remarks>
                File trailers are described in section 3.4.4 of the PDF specification.
            </remarks>
        </member>
        <member name="P:Telerik.Pdf.PdfFont.Name">
            <summary>
                Returns the internal name used for this font.
            </summary>
        </member>
        <member name="T:Telerik.Pdf.PdfFontTypeEnum">
            <summary>
                An enumeration listing all the fonts types available in Pdf.
            </summary>
        </member>
        <member name="T:Telerik.Pdf.PdfFontSubTypeEnum">
            <summary>
                An enumeration listing all the font subtypes
            </summary>
        </member>
        <member name="T:Telerik.Pdf.PdfICCStream">
            <summary>
                An International Color Code stream
            </summary>
        </member>
        <member name="T:Telerik.Pdf.PdfIdentityHEncoding">
            <summary>
                Represents a Identity-H character encoding
            </summary>
            <remarks>
                Maps 2-byte character codes ranging from 0 to 65,535 to 
                the same 2-byte CID value, interpreted high-order byte first
            </remarks>
        </member>
        <member name="M:Telerik.Pdf.PdfIdentityHEncoding.GetBytes(System.Char[],System.Int32,System.Int32,System.Byte[],System.Int32)">
            <summary>
                Do not call this method directly
            </summary>
        </member>
        <member name="M:Telerik.Pdf.PdfIdentityHEncoding.GetBytes(System.String,System.Int32,System.Int32,System.Byte[],System.Int32)">
            <summary>
                Do not call this method directly
            </summary>
        </member>
        <member name="T:Telerik.Pdf.PdfInfo">
            <summary>
                Class representing a document information dictionary.
            </summary>
            <remarks>
                Document information dictionaries are described in section 9.2.1 of the
                PDF specification.
            </remarks>
        </member>
        <member name="T:Telerik.Pdf.PdfName.Names">
            <summary>
                Well-known PDF name objects.
            </summary>
        </member>
        <member name="T:Telerik.Pdf.PdfOutline">
            <summary>
                This represents a single Outline object in a PDF, including the root Outlines
                object. Outlines provide the bookmark bar, usually rendered to the right of
                a PDF document in user agents such as Acrobat Reader
            </summary>
        </member>
        <member name="F:Telerik.Pdf.PdfOutline.subentries">
            <summary>
                List of sub-entries (outline objects)
            </summary>
        </member>
        <member name="F:Telerik.Pdf.PdfOutline.parent">
            <summary>
                Parent outline object. Root Outlines parent is null
            </summary>
        </member>
        <member name="F:Telerik.Pdf.PdfOutline.title">
            <summary>
                Title to display for the bookmark entry
            </summary>
        </member>
        <member name="M:Telerik.Pdf.PdfOutline.#ctor(Telerik.Pdf.PdfObjectId,System.String,Telerik.Pdf.PdfObjectReference)">
            <summary>
                Class constructor.
            </summary>
            <param name="objectId">The object id number</param>
            <param name="title">The title of the outline entry (can only be null for root Outlines obj)</param>
            <param name="action">The page which this outline refers to.</param>
        </member>
        <member name="M:Telerik.Pdf.PdfOutline.AddOutline(Telerik.Pdf.PdfOutline)">
            <summary>
                Add a sub element to this outline
            </summary>
            <param name="outline"></param>
        </member>
        <member name="T:Telerik.Pdf.PdfPageTree">
            <summary>
                The pages of a document are accessed through a structure known
                as the page tree.
            </summary>
            <remarks>
                The page tree is described in section 3.6.2 of the PDF specification.
            </remarks>
        </member>
        <member name="P:Telerik.Pdf.PdfString.Format">
            <summary>
                The convention used when outputing the string to the PDF document.
            </summary>
            <remarks>
               Defaults to <see cref="F:Telerik.Pdf.PdfStringFormat.Literal"/> format.
            </remarks>
        </member>
        <member name="P:Telerik.Pdf.PdfString.NeverEncrypt">
            <summary>
                Determines if the string should bypass encryption, even when 
                available.
            </summary>
            <remarks>
                Some PDF strings need to appear unencrypted in a secure PDF
                document.  Most noteably those in the encryption dictionary 
                itself.  This property allows those strings to be flagged.
            </remarks>
        </member>
        <member name="M:Telerik.Pdf.PdfString.ToPdfLiteral(System.Byte[],System.Byte[])">
            <summary>
                Returns this PdfString expressed using the 'literal' convention.
            </summary>
            <remarks>
                A literal string is written as an arbitrary number of characters 
                enclosed in parentheses.  Any characters may appear in a string 
                except unbalanced parentheses and the backslash, which must be 
                treated specially. Balanced pairs of parentheses within a string 
                require no special treatment.
            </remarks>
        </member>
        <member name="F:Telerik.Pdf.PdfString.HexDigits">
            <summary>
                Used by ToPdfHexadecimal.
            </summary>
        </member>
        <member name="M:Telerik.Pdf.PdfString.ToPdfHexadecimal(System.Byte[],System.Byte[])">
            <summary>
                Returns the PdfString expressed using the 'hexadecimal' convention.
            </summary>
            <remarks>
                Strings may also be written in hexadecimal form; this is useful for 
                including arbitrary binary data in a PDF file. A hexadecimal string 
                is written as a sequence of hexadecimal digits (0–9 and either A–F 
                or a–f) enclosed within angle brackets (&lt; and &gt;).
            </remarks>
        </member>
        <member name="T:Telerik.Pdf.PdfStringFormat">
            <summary>
                The PDF specification describes two conventions that can be
                used to embed a string in a PDF document.  This enumeration,
                along with the <see cref="P:Telerik.Pdf.PdfString.Format"/> property 
                can be used to select how a string will be formatted in the
                PDF file.
            </summary>
        </member>
        <member name="M:Telerik.Pdf.PdfTrueTypeFont.#ctor(Telerik.Pdf.PdfObjectId,System.String,System.String)">
            <param name="objectId">
                A unique object number.
            </param>
            <param name="fontName">
                The name by which the font is reference in the Font subdictionary 
            </param>
            <param name="baseFont">
                The PostScript name of the font.
            </param>
        </member>
        <member name="P:Telerik.Pdf.PdfTrueTypeFont.Encoding">
            <summary>
                Sets a value representing the character encoding.
            </summary>
        </member>
        <member name="P:Telerik.Pdf.PdfTrueTypeFont.Descriptor">
            <summary>
                Sets the font descriptor.
            </summary>
        </member>
        <member name="P:Telerik.Pdf.PdfTrueTypeFont.FirstChar">
            <summary>
                Sets the first character code defined in the font's widths array
            </summary>
            <value>
                The default value is 0.
            </value>
        </member>
        <member name="P:Telerik.Pdf.PdfTrueTypeFont.LastChar">
            <summary>
                Sets the last character code defined in the font's widths array
            </summary>
            <value>
                The default value is 255.
            </value>
        </member>
        <member name="P:Telerik.Pdf.PdfTrueTypeFont.Widths">
            <summary>
                Sets the array of character widths.
            </summary>
        </member>
        <member name="T:Telerik.Pdf.PdfType0Font">
            <summary>
                A Type 0 font is a composite font whose glyphs are obtained from a
                font like object called a CIDFont (a descendant font).
            </summary>
            <remarks>
                All versions of the PDF specification up to and including version 1.4
                only support a single descendant font.
            </remarks>
        </member>
        <member name="P:Telerik.Pdf.PdfType0Font.ToUnicode">
            <summary>
                Sets the stream containing a CMap that maps character codes to 
                unicode values.
            </summary>
        </member>
        <member name="P:Telerik.Pdf.PdfType0Font.Descendant">
            <summary>
                Sets the descendant font.
            </summary>
        </member>
        <member name="P:Telerik.Pdf.PdfType0Font.Encoding">
            <summary>
                Sets a value representing the character encoding.
            </summary>
        </member>
        <member name="P:Telerik.Pdf.PdfType1Font.Encoding">
            <summary>
                Sets a value representing the character encoding.
            </summary>
        </member>
        <member name="T:Telerik.Pdf.PdfWArray">
            <summary>
                Array class used to represent the /W entry in the CIDFont dictionary.
            </summary>
        </member>
        <member name="T:Telerik.Pdf.Security.Arc4">
            <summary>
                ARC4 is a fast, simple stream encryption algorithm that is
                compatible with RSA Security's RC4 algorithm.
            </summary>
        </member>
        <member name="M:Telerik.Pdf.Security.Arc4.Initialise(System.Byte[])">
            <summary>
                Initialises internal state from the passed key.
            </summary>
            <remarks>
                Can be called again with a new key to reuse an Arc4 instance.
            </remarks>
            <param name="key">The encryption key.</param>
        </member>
        <member name="M:Telerik.Pdf.Security.Arc4.Encrypt(System.Byte[],System.Byte[])">
            <summary>
                Encrypts or decrypts the passed byte array.
            </summary>
            <param name="dataIn">
                The data to be encrypted or decrypted.
            </param>
            <param name="dataOut">
                The location that the encrypted or decrypted data is to be placed.
                The passed array should be at least the same size as dataIn.
                It is permissible for the same array to be passed for both dataIn
                and dataOut.
            </param>
        </member>
        <member name="M:Telerik.Pdf.Security.Arc4.Arc4Byte">
            <summary>
                Generates a pseudorandom byte used to encrypt or decrypt.
            </summary>
        </member>
        <member name="T:Telerik.Pdf.Security.SecurityManager">
            <summary>
                Implements Adobe's standard security handler.  A security handler is 
                a software module that implements various aspects of the encryption 
                process.
            </summary>
        </member>
        <member name="M:Telerik.Pdf.Security.SecurityManager.#ctor(Telerik.Pdf.Security.SecurityOptions,Telerik.Pdf.FileIdentifier)">
            <summary>
                Constructs a new standard security manager.
            </summary>
            <param name="options">
                The user supplied PDF options that provides access to the passwords and 
                the access permissions.
            </param>
            <param name="fileId">
                The PDF document's file identifier (see section 8.3 of PDF specification).
            </param>
        </member>
        <member name="M:Telerik.Pdf.Security.SecurityManager.CreateMasterKey(Telerik.Pdf.Security.SecurityOptions,Telerik.Pdf.FileIdentifier)">
            <summary>
                Computes the master key that is used to encrypt string and stream data 
                in the PDF document.
            </summary>
            <param name="options">
                The user supplied PDF options that provides access to the passwords and
                the access permissions.
            </param>
            <param name="fileId">
                The PDF document's file identifier (see section 8.3 of PDF specification).
            </param>
        </member>
        <member name="M:Telerik.Pdf.Security.SecurityManager.CreateOwnerEntry(Telerik.Pdf.Security.SecurityOptions)">
            <summary>
                Computes the O(owner) value in the encryption dictionary.
            </summary>
            <remarks>
                Corresponds to algorithm 3.3 on page 69 of the PDF specficiation.
            </remarks>
            <param name="options">
                The user supplied PDF options that provides access to the passwords.
            </param>
        </member>
        <member name="M:Telerik.Pdf.Security.SecurityManager.CreateUserEntry(Telerik.Pdf.Security.SecurityOptions)">
            <summary>
                Computes the U(user) value in the encryption dictionary.
            </summary>
            <remarks>
                Corresponds to algorithm 3.4 on page 70 of the PDF specficiation.
            </remarks>
            <param name="options">
                The user supplied PDF options that provides access to the passwords.
            </param>
        </member>
        <member name="M:Telerik.Pdf.Security.SecurityManager.Encrypt(System.Byte[],Telerik.Pdf.PdfObjectId)">
            <summary>
                Encrypts the passed byte array using the ARC4 cipher.
            </summary>
        </member>
        <member name="P:Telerik.Pdf.Security.SecurityManager.UserEntry">
            <summary>
                Access to the raw user entry byte array.
            </summary>
            <remarks>
                Required for testing purposes;
            </remarks>
        </member>
        <member name="P:Telerik.Pdf.Security.SecurityManager.OwnerEntry">
            <summary>
                Access to the raw owner entry byte array.
            </summary>
            <remarks>
                Required for testing purposes;
            </remarks>
        </member>
        <member name="M:Telerik.Pdf.Security.SecurityManager.ComputeEncryptionKey31(System.Byte[],Telerik.Pdf.PdfObjectId)">
            <summary>
                Computes an encryption key that is used to encrypt string and stream data 
                in the PDF document.
            </summary>
            <remarks>
                Corresponds to algorithm 3.1 in section 3.5 of the PDF specficiation.
            </remarks>
        </member>
        <member name="M:Telerik.Pdf.Security.SecurityManager.ComputeEncryptionKey32(System.Byte[],System.Byte[],System.Int32,System.Byte[])">
            <summary>
                Computes an encryption key that is used to encrypt string and stream data 
                in the PDF document.
            </summary>
            <remarks>
                Corresponds to algorithm 3.2 in section 3.5 of the PDF specficiation.
            </remarks>
        </member>
        <member name="M:Telerik.Pdf.Security.SecurityManager.PadPassword(System.String)">
            <summary>
                Pads or truncates a password string to exactly 32-bytes.
            </summary>
            <remarks>
                Corresponds to step 1 of algorithm 3.2 on page 69 of the PDF 1.3 specficiation.
            </remarks>
            <param name="password">The password to pad or truncate.</param>
            <returns>
                A byte array of length 32 bytes containing the padded or truncated password.
            </returns>
        </member>
        <member name="M:Telerik.Pdf.Security.SecurityManager.CheckUserPassword(System.String,System.Byte[],System.Byte[],System.Int32,System.Byte[])">
            <summary>
                Determines if the passed password matches the user password
                used to initialise this security manager.
            </summary>
            <remarks>
                Used for testing purposes only.  Corresponds to algorithm 3.5 in the
                PDF 1.3 specification.
            </remarks>
            <returns>True if the password is correct.</returns>
        </member>
        <member name="M:Telerik.Pdf.Security.SecurityManager.CheckUserPassword(System.Byte[],System.Byte[],System.Byte[],System.Int32,System.Byte[])">
            <summary>
                Performs the actual checking of the user password.
            </summary>
        </member>
        <member name="M:Telerik.Pdf.Security.SecurityManager.CheckOwnerPassword(System.String,System.Byte[],System.Byte[],System.Int32,System.Byte[])">
            <summary>
                Checks the owner password.
            </summary>
        </member>
        <member name="M:Telerik.Pdf.Security.SecurityManager.CompareArray(System.Byte[],System.Byte[])">
            <summary>
                Compares two byte arrays and returns true if they are equal.
            </summary>
        </member>
        <member name="F:Telerik.Pdf.Security.SecurityOptions.m_ownerPassword">
            <summary>
                Password that disables all security permissions
            </summary>
        </member>
        <member name="F:Telerik.Pdf.Security.SecurityOptions.m_userPassword">
            <summary>
                The user password 
            </summary>
        </member>
        <member name="F:Telerik.Pdf.Security.SecurityOptions.m_permissions">
            <summary>
                Collection of flags describing permissions granted to user who opens 
                a file with the user password.
            </summary>
            <remarks>
                The given initial value zero's out first two bits.
                The PDF specification dictates that these entries must be 0.
            </remarks>
        </member>
        <member name="P:Telerik.Pdf.Security.SecurityOptions.OwnerPassword">
            <summary>
                Returns the owner password as a string.
            </summary>
            <value>
                The default value is null
            </value>
        </member>
        <member name="P:Telerik.Pdf.Security.SecurityOptions.UserPassword">
            <summary>
                Returns the user password as a string.
            </summary>
            <value>
                The default value is null
            </value>
        </member>
        <member name="P:Telerik.Pdf.Security.SecurityOptions.Permissions">
            <summary>
                The document access privileges encoded in a 32-bit unsigned integer
            </summary>
            <value>
                The default access priviliges are:
                <ul>
                <li>Printing disallowed</li>
                <li>Modifications disallowed</li>
                <li>Copy and Paste disallowed</li>
                <li>Addition or modification of annotation/form fields disallowed</li>
                </ul>
                To override any of these priviliges see the <see cref="M:Telerik.Pdf.Security.SecurityOptions.EnablePrinting(System.Boolean)"/>,
                <see cref="M:Telerik.Pdf.Security.SecurityOptions.EnableChanging(System.Boolean)"/>, <see cref="M:Telerik.Pdf.Security.SecurityOptions.EnableCopying(System.Boolean)"/>, 
                <see cref="M:Telerik.Pdf.Security.SecurityOptions.EnableAdding(System.Boolean)"/> methods
            </value>
        </member>
        <member name="M:Telerik.Pdf.Security.SecurityOptions.EnablePrinting(System.Boolean)">
            <summary>
                Enables or disables printing.
            </summary>
            <param name="enable">If true enables printing otherwise false</param>
        </member>
        <member name="M:Telerik.Pdf.Security.SecurityOptions.EnableChanging(System.Boolean)">
            <summary>
                Enable or disable changing the document other than by adding or 
                changing text notes and AcroForm fields.
            </summary>
            <param name="enable"></param>
        </member>
        <member name="M:Telerik.Pdf.Security.SecurityOptions.EnableCopying(System.Boolean)">
            <summary>
                Enable or disable copying of text and graphics from the document.
            </summary>
            <param name="enable"></param>
        </member>
        <member name="M:Telerik.Pdf.Security.SecurityOptions.EnableAdding(System.Boolean)">
            <summary>
                Enable or disable adding and changing text notes and AcroForm fields.
            </summary>
            <param name="enable"></param>
        </member>
        <member name="T:Telerik.Pdf.XRefSection">
            <summary>
                A single section in a PDF file's cross-reference table.
            </summary>
            <remarks>
                The cross-reference table is described in section 3.4.3 of
                the PDF specification.
            </remarks>
        </member>
        <member name="F:Telerik.Pdf.XRefSection.subsection">
            <summary>
                Right now we only support a single subsection.
            </summary>
        </member>
        <member name="M:Telerik.Pdf.XRefSection.Add(Telerik.Pdf.PdfObjectId,System.Int64)">
            <summary>
                Adds an entry to the section.
            </summary>
        </member>
        <member name="M:Telerik.Pdf.XRefSection.Write(Telerik.Pdf.PdfWriter)">
            <summary>
                Writes the cross reference section to the passed PDF writer.
            </summary>
        </member>
        <member name="T:Telerik.Pdf.XRefSubSection">
            <summary>
                A sub-section in a PDF file's cross-reference table.
            </summary>
            <remarks>
                The cross-reference table is described in section 3.4.3 of
                the PDF specification.
            </remarks>
        </member>
        <member name="F:Telerik.Pdf.XRefSubSection.entries">
            <summary>
                This entries contained in this subsection.
            </summary>
        </member>
        <member name="M:Telerik.Pdf.XRefSubSection.#ctor">
            <summary>
                Creates a new blank sub-section, that initially contains no entries.
            </summary>
        </member>
        <member name="T:Telerik.Pdf.XRefSubSection.Entry">
            <summary>
                Structure representing a single cross-reference entry.
            </summary>
        </member>
        <member name="F:Telerik.Pdf.XRefSubSection.Entry.objectId">
            <summary>
                The object number and generation number.
            </summary>
        </member>
        <member name="F:Telerik.Pdf.XRefSubSection.Entry.offset">
            <summary>
                The number of bytes from the beginning of the file to
                the beginning of the object.
            </summary>
        </member>
        <member name="M:Telerik.Pdf.XRefSubSection.Entry.CompareTo(System.Object)">
            <summary>
                Implementation of IComparable.
            </summary>
        </member>
        <member name="M:Telerik.Pdf.XRefSubSection.Add(Telerik.Pdf.PdfObjectId,System.Int64)">
            <summary>
                Adds an entry to the sub-section.
            </summary>
        </member>
        <member name="M:Telerik.Pdf.XRefSubSection.Write(Telerik.Pdf.PdfWriter)">
            <summary>
                Writes the cross reference sub-section to the passed PDF writer.
            </summary>
        </member>
        <member name="T:Telerik.Pdf.XRefTable">
            <summary>
                A PDF file's cross-reference table.
            </summary>
            <remarks>
                The cross-reference table is described in section 3.4.3 of
                the PDF specification.
            </remarks>
        </member>
        <member name="F:Telerik.Pdf.XRefTable.section">
            <summary>
                Right now we only support a single section.
            </summary>
        </member>
        <member name="M:Telerik.Pdf.XRefTable.Add(Telerik.Pdf.PdfObjectId,System.Int64)">
            <summary>
                Adds an entry to the table.
            </summary>
        </member>
        <member name="M:Telerik.Pdf.XRefTable.Write(Telerik.Pdf.PdfWriter)">
            <summary>
                Writes the cross reference table to the passed PDF writer.
            </summary>
        </member>
        <member name="T:TelerikData.Properties.Resources">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:TelerikData.Properties.Resources.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:TelerikData.Properties.Resources.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
    </members>
</doc>
