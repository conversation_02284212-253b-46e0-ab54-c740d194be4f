# Task 08: Permission Caching and Performance

## Objective
Enhance the permission system with advanced caching strategies, performance optimizations, and monitoring. This ensures the RBAC system performs well under load and provides fast permission checks throughout the application.

## Priority
**CORE SERVICES** - Depends on Tasks 06-07

## Estimated Time
1 hour

## Dependencies
- Task 06: Core Permission Service Logic
- Task 07: Permission Database Operations

## Files to Modify/Create
- `Modules/Services/PermissionCache.cs` (enhance existing)
- `Modules/Services/PermissionPerformanceMonitor.cs`
- `Modules/Services/PermissionPreloader.cs`

## Enhanced Caching Implementation

### Enhanced PermissionCache.cs

Extend the existing PermissionCache class:

```csharp
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;

namespace ProManage.Modules.Services
{
    public class PermissionCache
    {
        private readonly ConcurrentDictionary<string, CacheItem> _cache = new ConcurrentDictionary<string, CacheItem>();
        private readonly ConcurrentDictionary<int, UserPermissionSet> _userPermissionSets = new ConcurrentDictionary<int, UserPermissionSet>();
        private readonly TimeSpan _cacheExpiry = TimeSpan.FromMinutes(15);
        private readonly Timer _cleanupTimer;
        private readonly PermissionPerformanceMonitor _performanceMonitor;
        
        public PermissionCache()
        {
            _performanceMonitor = new PermissionPerformanceMonitor();
            
            // Setup cleanup timer to run every 5 minutes
            _cleanupTimer = new Timer(CleanupExpiredEntries, null, TimeSpan.FromMinutes(5), TimeSpan.FromMinutes(5));
        }
        
        #region Enhanced Cache Operations
        
        /// <summary>
        /// Try to get permission from cache with performance monitoring
        /// </summary>
        public bool TryGetPermission(string key, out bool permission)
        {
            var stopwatch = System.Diagnostics.Stopwatch.StartNew();
            permission = false;
            
            try
            {
                if (_cache.TryGetValue(key, out CacheItem item))
                {
                    if (DateTime.Now - item.Timestamp < _cacheExpiry)
                    {
                        permission = item.Value;
                        _performanceMonitor.RecordCacheHit(stopwatch.ElapsedMilliseconds);
                        return true;
                    }
                    else
                    {
                        // Remove expired item
                        _cache.TryRemove(key, out _);
                        _performanceMonitor.RecordCacheExpiry();
                    }
                }
                
                _performanceMonitor.RecordCacheMiss(stopwatch.ElapsedMilliseconds);
                return false;
            }
            finally
            {
                stopwatch.Stop();
            }
        }
        
        /// <summary>
        /// Set permission in cache with metadata
        /// </summary>
        public void SetPermission(string key, bool permission, PermissionSource source = PermissionSource.Role)
        {
            _cache[key] = new CacheItem 
            { 
                Value = permission, 
                Timestamp = DateTime.Now,
                Source = source,
                AccessCount = 1
            };
            
            _performanceMonitor.RecordCacheSet();
        }
        
        /// <summary>
        /// Preload user permission set for faster access
        /// </summary>
        public void PreloadUserPermissions(int userId, Dictionary<string, bool> permissions)
        {
            var permissionSet = new UserPermissionSet
            {
                UserId = userId,
                Permissions = new ConcurrentDictionary<string, bool>(permissions),
                LoadTime = DateTime.Now,
                LastAccess = DateTime.Now
            };
            
            _userPermissionSets[userId] = permissionSet;
            _performanceMonitor.RecordUserPreload(userId);
        }
        
        /// <summary>
        /// Try to get permission from preloaded user set
        /// </summary>
        public bool TryGetUserPermission(int userId, string formName, PermissionType permissionType, out bool permission)
        {
            permission = false;
            
            if (_userPermissionSets.TryGetValue(userId, out UserPermissionSet userSet))
            {
                if (DateTime.Now - userSet.LoadTime < _cacheExpiry)
                {
                    var key = $"{formName}_{permissionType}";
                    if (userSet.Permissions.TryGetValue(key, out permission))
                    {
                        userSet.LastAccess = DateTime.Now;
                        _performanceMonitor.RecordUserSetHit(userId);
                        return true;
                    }
                }
                else
                {
                    // Remove expired user set
                    _userPermissionSets.TryRemove(userId, out _);
                }
            }
            
            return false;
        }
        
        #endregion
        
        #region Cache Management
        
        /// <summary>
        /// Clear cache for specific user with performance tracking
        /// </summary>
        public void ClearUserPermissions(int userId)
        {
            var keysToRemove = _cache.Keys.Where(k => k.StartsWith($"{userId}_")).ToList();
            foreach (var key in keysToRemove)
            {
                _cache.TryRemove(key, out _);
            }
            
            _userPermissionSets.TryRemove(userId, out _);
            _performanceMonitor.RecordUserCacheClear(userId);
        }
        
        /// <summary>
        /// Clear cache for specific role (affects all users with that role)
        /// </summary>
        public void ClearRolePermissions(int roleId)
        {
            // For role changes, clear all cache since we don't track role-user mapping in cache
            var clearedCount = _cache.Count;
            ClearAll();
            _performanceMonitor.RecordRoleCacheClear(roleId, clearedCount);
        }
        
        /// <summary>
        /// Intelligent cache warming for frequently accessed permissions
        /// </summary>
        public async Task WarmCache(List<int> userIds, List<string> formNames)
        {
            await Task.Run(() =>
            {
                var preloader = new PermissionPreloader();
                
                foreach (var userId in userIds)
                {
                    var permissions = preloader.LoadUserPermissions(userId, formNames);
                    PreloadUserPermissions(userId, permissions);
                }
                
                _performanceMonitor.RecordCacheWarm(userIds.Count, formNames.Count);
            });
        }
        
        #endregion
        
        #region Performance Monitoring
        
        /// <summary>
        /// Get cache performance statistics
        /// </summary>
        public CacheStatistics GetStatistics()
        {
            return new CacheStatistics
            {
                TotalEntries = _cache.Count,
                UserSets = _userPermissionSets.Count,
                HitRate = _performanceMonitor.GetHitRate(),
                AverageAccessTime = _performanceMonitor.GetAverageAccessTime(),
                ExpiredEntries = _performanceMonitor.ExpiredEntries,
                LastCleanup = _performanceMonitor.LastCleanup
            };
        }
        
        /// <summary>
        /// Get most accessed permissions for optimization
        /// </summary>
        public List<string> GetMostAccessedPermissions(int topCount = 10)
        {
            return _cache
                .OrderByDescending(kvp => kvp.Value.AccessCount)
                .Take(topCount)
                .Select(kvp => kvp.Key)
                .ToList();
        }
        
        #endregion
        
        #region Cleanup and Maintenance
        
        /// <summary>
        /// Clean expired cache entries (called by timer)
        /// </summary>
        private void CleanupExpiredEntries(object state)
        {
            var expiredKeys = _cache
                .Where(kvp => DateTime.Now - kvp.Value.Timestamp >= _cacheExpiry)
                .Select(kvp => kvp.Key)
                .ToList();
            
            foreach (var key in expiredKeys)
            {
                _cache.TryRemove(key, out _);
            }
            
            // Clean expired user permission sets
            var expiredUserSets = _userPermissionSets
                .Where(kvp => DateTime.Now - kvp.Value.LoadTime >= _cacheExpiry)
                .Select(kvp => kvp.Key)
                .ToList();
            
            foreach (var userId in expiredUserSets)
            {
                _userPermissionSets.TryRemove(userId, out _);
            }
            
            _performanceMonitor.RecordCleanup(expiredKeys.Count + expiredUserSets.Count);
        }
        
        /// <summary>
        /// Force cleanup of all expired entries
        /// </summary>
        public void CleanExpiredEntries()
        {
            CleanupExpiredEntries(null);
        }
        
        /// <summary>
        /// Optimize cache by removing least recently used items when memory pressure is high
        /// </summary>
        public void OptimizeCache(int maxEntries = 1000)
        {
            if (_cache.Count > maxEntries)
            {
                var itemsToRemove = _cache
                    .OrderBy(kvp => kvp.Value.LastAccess)
                    .Take(_cache.Count - maxEntries)
                    .Select(kvp => kvp.Key)
                    .ToList();
                
                foreach (var key in itemsToRemove)
                {
                    _cache.TryRemove(key, out _);
                }
                
                _performanceMonitor.RecordOptimization(itemsToRemove.Count);
            }
        }
        
        #endregion
        
        public void Dispose()
        {
            _cleanupTimer?.Dispose();
        }
        
        #region Cache Item Classes
        
        private class CacheItem
        {
            public bool Value { get; set; }
            public DateTime Timestamp { get; set; }
            public DateTime LastAccess { get; set; } = DateTime.Now;
            public PermissionSource Source { get; set; }
            public int AccessCount { get; set; }
        }
        
        private class UserPermissionSet
        {
            public int UserId { get; set; }
            public ConcurrentDictionary<string, bool> Permissions { get; set; }
            public DateTime LoadTime { get; set; }
            public DateTime LastAccess { get; set; }
        }
        
        #endregion
    }
    
    public class CacheStatistics
    {
        public int TotalEntries { get; set; }
        public int UserSets { get; set; }
        public double HitRate { get; set; }
        public double AverageAccessTime { get; set; }
        public int ExpiredEntries { get; set; }
        public DateTime LastCleanup { get; set; }
    }
}
```

### PermissionPerformanceMonitor.cs

```csharp
using System;
using System.Collections.Concurrent;
using System.Linq;

namespace ProManage.Modules.Services
{
    public class PermissionPerformanceMonitor
    {
        private readonly ConcurrentQueue<PerformanceMetric> _metrics = new ConcurrentQueue<PerformanceMetric>();
        private readonly object _statsLock = new object();
        
        private long _cacheHits = 0;
        private long _cacheMisses = 0;
        private long _cacheSets = 0;
        private long _expiredEntries = 0;
        private DateTime _lastCleanup = DateTime.Now;
        
        public long CacheHits => _cacheHits;
        public long CacheMisses => _cacheMisses;
        public long ExpiredEntries => _expiredEntries;
        public DateTime LastCleanup => _lastCleanup;
        
        public void RecordCacheHit(long accessTimeMs)
        {
            Interlocked.Increment(ref _cacheHits);
            RecordMetric(MetricType.CacheHit, accessTimeMs);
        }
        
        public void RecordCacheMiss(long accessTimeMs)
        {
            Interlocked.Increment(ref _cacheMisses);
            RecordMetric(MetricType.CacheMiss, accessTimeMs);
        }
        
        public void RecordCacheSet()
        {
            Interlocked.Increment(ref _cacheSets);
            RecordMetric(MetricType.CacheSet, 0);
        }
        
        public void RecordCacheExpiry()
        {
            Interlocked.Increment(ref _expiredEntries);
        }
        
        public void RecordUserPreload(int userId)
        {
            RecordMetric(MetricType.UserPreload, 0, userId.ToString());
        }
        
        public void RecordUserSetHit(int userId)
        {
            RecordMetric(MetricType.UserSetHit, 0, userId.ToString());
        }
        
        public void RecordUserCacheClear(int userId)
        {
            RecordMetric(MetricType.UserCacheClear, 0, userId.ToString());
        }
        
        public void RecordRoleCacheClear(int roleId, int clearedCount)
        {
            RecordMetric(MetricType.RoleCacheClear, clearedCount, roleId.ToString());
        }
        
        public void RecordCacheWarm(int userCount, int formCount)
        {
            RecordMetric(MetricType.CacheWarm, userCount * formCount);
        }
        
        public void RecordCleanup(int cleanedCount)
        {
            lock (_statsLock)
            {
                _lastCleanup = DateTime.Now;
            }
            RecordMetric(MetricType.Cleanup, cleanedCount);
        }
        
        public void RecordOptimization(int removedCount)
        {
            RecordMetric(MetricType.Optimization, removedCount);
        }
        
        public double GetHitRate()
        {
            var total = _cacheHits + _cacheMisses;
            return total > 0 ? (double)_cacheHits / total * 100 : 0;
        }
        
        public double GetAverageAccessTime()
        {
            var accessMetrics = GetRecentMetrics(TimeSpan.FromMinutes(5))
                .Where(m => m.Type == MetricType.CacheHit || m.Type == MetricType.CacheMiss)
                .ToList();
            
            return accessMetrics.Count > 0 ? accessMetrics.Average(m => m.Value) : 0;
        }
        
        public List<PerformanceMetric> GetRecentMetrics(TimeSpan timeSpan)
        {
            var cutoff = DateTime.Now - timeSpan;
            return _metrics.Where(m => m.Timestamp >= cutoff).ToList();
        }
        
        private void RecordMetric(MetricType type, long value, string context = null)
        {
            _metrics.Enqueue(new PerformanceMetric
            {
                Type = type,
                Value = value,
                Timestamp = DateTime.Now,
                Context = context
            });
            
            // Keep only recent metrics (last 1000 entries)
            while (_metrics.Count > 1000)
            {
                _metrics.TryDequeue(out _);
            }
        }
    }
    
    public class PerformanceMetric
    {
        public MetricType Type { get; set; }
        public long Value { get; set; }
        public DateTime Timestamp { get; set; }
        public string Context { get; set; }
    }
    
    public enum MetricType
    {
        CacheHit,
        CacheMiss,
        CacheSet,
        UserPreload,
        UserSetHit,
        UserCacheClear,
        RoleCacheClear,
        CacheWarm,
        Cleanup,
        Optimization
    }
}
```

### PermissionPreloader.cs

```csharp
using System;
using System.Collections.Generic;
using System.Linq;
using ProManage.Modules.Connections;
using ProManage.Modules.Models;

namespace ProManage.Modules.Services
{
    public class PermissionPreloader
    {
        private readonly PermissionDatabaseService _dbService = new PermissionDatabaseService();
        
        /// <summary>
        /// Load all permissions for a user into a dictionary for fast access
        /// </summary>
        public Dictionary<string, bool> LoadUserPermissions(int userId, List<string> formNames)
        {
            var permissions = new Dictionary<string, bool>();
            
            try
            {
                foreach (var formName in formNames)
                {
                    foreach (PermissionType permType in Enum.GetValues(typeof(PermissionType)))
                    {
                        var key = $"{formName}_{permType}";
                        var hasPermission = CheckPermissionDirect(userId, formName, permType);
                        permissions[key] = hasPermission;
                    }
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error preloading permissions for user {userId}: {ex.Message}");
            }
            
            return permissions;
        }
        
        /// <summary>
        /// Direct permission check without caching (for preloading)
        /// </summary>
        private bool CheckPermissionDirect(int userId, string formName, PermissionType permissionType)
        {
            // Check user override first
            var userPermission = _dbService.GetUserPermission(userId, formName);
            if (userPermission != null)
            {
                var userValue = GetPermissionValue(userPermission, permissionType);
                if (userValue.HasValue)
                {
                    return userValue.Value;
                }
            }
            
            // Fall back to role permission
            // Note: This would need integration with user service to get role
            // For now, assume role ID 1 - this will be fixed in integration phase
            var rolePermission = _dbService.GetRolePermission(1, formName);
            if (rolePermission != null)
            {
                var roleValue = GetPermissionValue(rolePermission, permissionType);
                return roleValue ?? false;
            }
            
            return false;
        }
        
        private bool? GetPermissionValue(UserPermission permission, PermissionType type)
        {
            return type switch
            {
                PermissionType.Read => permission.ReadPermission,
                PermissionType.New => permission.NewPermission,
                PermissionType.Edit => permission.EditPermission,
                PermissionType.Delete => permission.DeletePermission,
                PermissionType.Print => permission.PrintPermission,
                _ => null
            };
        }
        
        private bool? GetPermissionValue(RolePermission permission, PermissionType type)
        {
            return type switch
            {
                PermissionType.Read => permission.ReadPermission,
                PermissionType.New => permission.NewPermission,
                PermissionType.Edit => permission.EditPermission,
                PermissionType.Delete => permission.DeletePermission,
                PermissionType.Print => permission.PrintPermission,
                _ => null
            };
        }
    }
}
```

## Integration with PermissionService

Update PermissionService to use enhanced caching:

```csharp
// In PermissionService.cs, modify HasPermission method:
public static bool HasPermission(int userId, string formName, PermissionType permissionType)
{
    try
    {
        // Try user permission set first (fastest)
        if (_cache.TryGetUserPermission(userId, formName, permissionType, out bool userSetResult))
        {
            return userSetResult;
        }
        
        // Fall back to individual cache check
        var cacheKey = $"{userId}_{formName}_{permissionType}";
        if (_cache.TryGetPermission(cacheKey, out bool cachedResult))
        {
            return cachedResult;
        }
        
        // Database lookup and cache
        var result = CheckPermissionFromDatabase(userId, formName, permissionType);
        _cache.SetPermission(cacheKey, result);
        return result;
    }
    catch (Exception ex)
    {
        System.Diagnostics.Debug.WriteLine($"Permission check error: {ex.Message}");
        return false;
    }
}
```

## Acceptance Criteria

- [ ] Enhanced caching with performance monitoring
- [ ] User permission set preloading for bulk operations
- [ ] Automatic cache cleanup and optimization
- [ ] Performance metrics collection and reporting
- [ ] Cache warming strategies for frequently accessed permissions
- [ ] Memory-efficient cache management with LRU eviction
- [ ] Thread-safe cache operations
- [ ] Cache statistics and monitoring dashboard data

## Dependencies
- Task 06: Core Permission Service Logic
- Task 07: Permission Database Operations

## Next Tasks
This task enables:
- Task 09: Permission Management Form (3-Tab UI)
- Task 13: MainFrame Ribbon Permission Filtering
- Task 16: Testing and Validation Suite
