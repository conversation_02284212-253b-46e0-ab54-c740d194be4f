using System;
using ProManage.Modules.Models.PermissionManagementForm;

namespace ProManage.Modules.Helpers.PermissionManagementForm
{
    /// <summary>
    /// Validation helper for permission-related models
    /// Contains all validation logic moved from model classes to maintain clean separation
    /// </summary>
    public static class PermissionFormValidation
    {
        #region Role Validation

        /// <summary>
        /// Validates role data
        /// </summary>
        /// <param name="role">Role to validate</param>
        /// <returns>True if valid, false otherwise</returns>
        public static bool IsValidRole(Role role)
        {
            if (role == null)
                return false;

            return !string.IsNullOrWhiteSpace(role.RoleName) &&
                   role.RoleName.Length <= 50 &&
                   (string.IsNullOrEmpty(role.Description) || role.Description.Length <= 255);
        }

        /// <summary>
        /// Validates role permission update model
        /// </summary>
        /// <param name="update">Role permission update to validate</param>
        /// <returns>True if valid, false otherwise</returns>
        public static bool IsValidRolePermissionUpdate(RolePermissionUpdate update)
        {
            if (update == null)
                return false;

            return update.RoleId > 0 && !string.IsNullOrWhiteSpace(update.FormName);
        }

        #endregion

        #region User Permission Validation

        /// <summary>
        /// Validates user permission update model
        /// </summary>
        /// <param name="update">User permission update to validate</param>
        /// <returns>True if valid, false otherwise</returns>
        public static bool IsValidUserPermissionUpdate(UserPermissionUpdate update)
        {
            if (update == null)
                return false;

            return update.UserId > 0 && !string.IsNullOrWhiteSpace(update.FormName);
        }

        /// <summary>
        /// Validates global permission update model
        /// </summary>
        /// <param name="update">Global permission update to validate</param>
        /// <returns>True if valid, false otherwise</returns>
        public static bool IsValidGlobalPermissionUpdate(GlobalPermissionUpdate update)
        {
            if (update == null)
                return false;

            return update.UserId > 0;
        }

        #endregion

        #region Permission Request Validation

        /// <summary>
        /// Validates permission request model
        /// </summary>
        /// <param name="request">Permission request to validate</param>
        /// <returns>True if valid, false otherwise</returns>
        public static bool IsValidPermissionRequest(PermissionRequest request)
        {
            if (request == null)
                return false;

            return request.UserId > 0 && !string.IsNullOrWhiteSpace(request.FormName);
        }

        /// <summary>
        /// Validates global permission request model
        /// </summary>
        /// <param name="request">Global permission request to validate</param>
        /// <returns>True if valid, false otherwise</returns>
        public static bool IsValidGlobalPermissionRequest(GlobalPermissionRequest request)
        {
            if (request == null)
                return false;

            return request.UserId > 0;
        }

        #endregion

        #region Form Configuration Validation

        /// <summary>
        /// Validates form configuration model
        /// </summary>
        /// <param name="config">Form configuration to validate</param>
        /// <returns>True if valid, false otherwise</returns>
        public static bool IsValidFormConfiguration(FormConfiguration config)
        {
            if (config == null)
                return false;

            return !string.IsNullOrWhiteSpace(config.FormName) &&
                   config.FormName.Length <= 100 &&
                   !string.IsNullOrWhiteSpace(config.DisplayName) &&
                   config.DisplayName.Length <= 100 &&
                   (string.IsNullOrEmpty(config.Category) || config.Category.Length <= 50);
        }

        /// <summary>
        /// Validates category configuration model
        /// </summary>
        /// <param name="config">Category configuration to validate</param>
        /// <returns>True if valid, false otherwise</returns>
        public static bool IsValidCategoryConfiguration(CategoryConfiguration config)
        {
            if (config == null)
                return false;

            return !string.IsNullOrWhiteSpace(config.CategoryName) &&
                   config.CategoryName.Length <= 50 &&
                   !string.IsNullOrWhiteSpace(config.DisplayName) &&
                   config.DisplayName.Length <= 100;
        }

        #endregion
    }
}
