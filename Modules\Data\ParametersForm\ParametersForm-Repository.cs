// ParametersForm Repository - Database operations for Parameters management
// Usage: Handles all database CRUD operations for parameters

using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using ProManage.Modules.Connections;
using ProManage.Modules.Models.ParametersForm;
using ProManage.Modules.Helpers;

namespace ProManage.Modules.Data.ParametersForm
{
    /// <summary>
    /// Repository class for Parameters database operations
    /// </summary>
    public static class ParametersFormRepository
    {
        /// <summary>
        /// Retrieves all parameters from the database
        /// </summary>
        /// <returns>List of ParametersFormModel objects</returns>
        public static List<ParametersFormModel> GetAllParameters()
        {
            try
            {
                Debug.WriteLine("=== GetAllParameters: Starting ===");

                var parameters = new List<ParametersFormModel>();
                string errorMessage;

                // Execute query to get all parameters
                var dataTable = QueryExecutor.ExecuteNamedQueryFromFile(
                    "Parameters", "ParametersQueries", "GetAllParameters", out errorMessage);

                if (dataTable == null)
                {
                    Debug.WriteLine($"GetAllParameters failed: {errorMessage}");
                    string detailedError = string.IsNullOrEmpty(errorMessage) ? "Unknown database error occurred" : errorMessage;
                    throw new Exception($"Failed to retrieve parameters: {detailedError}");
                }

                // Convert DataTable to List<ParametersFormModel>
                foreach (DataRow row in dataTable.Rows)
                {
                    var parameter = MapDataRowToModel(row);
                    parameters.Add(parameter);
                }

                Debug.WriteLine($"=== GetAllParameters: Retrieved {parameters.Count} parameters ===");
                return parameters;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in GetAllParameters: {ex.Message}");
                throw new Exception($"Error retrieving parameters: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Retrieves a parameter by its ID
        /// </summary>
        /// <param name="id">Parameter ID</param>
        /// <returns>ParametersFormModel or null if not found</returns>
        public static ParametersFormModel GetParameterById(int id)
        {
            try
            {
                Debug.WriteLine($"=== GetParameterById: Starting for ID {id} ===");

                string errorMessage;
                var parameters = new Dictionary<string, object> { { "@id", id } };

                var dataTable = QueryExecutor.ExecuteNamedQueryFromFile(
                    "Parameters", "ParametersQueries", "GetParameterById", out errorMessage, parameters);

                if (dataTable == null)
                {
                    Debug.WriteLine($"GetParameterById failed: {errorMessage}");
                    throw new Exception($"Failed to retrieve parameter: {errorMessage}");
                }

                if (dataTable.Rows.Count == 0)
                {
                    Debug.WriteLine($"Parameter with ID {id} not found");
                    return null;
                }

                var parameter = MapDataRowToModel(dataTable.Rows[0]);
                Debug.WriteLine($"=== GetParameterById: Found parameter {parameter.ParameterCode} ===");
                return parameter;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in GetParameterById: {ex.Message}");
                throw new Exception($"Error retrieving parameter by ID: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Inserts a new parameter into the database
        /// </summary>
        /// <param name="parameter">Parameter model to insert</param>
        /// <returns>The ID of the newly created parameter</returns>
        public static int InsertParameter(ParametersFormModel parameter)
        {
            try
            {
                Debug.WriteLine($"=== InsertParameter: Starting for {parameter.ParameterCode} ===");

                if (!parameter.IsValid())
                {
                    throw new ArgumentException("Parameter model is not valid");
                }

                string errorMessage;
                var parameters = new Dictionary<string, object>
                {
                    { "@parameter_code", parameter.ParameterCode },
                    { "@parameter_value", parameter.ParameterValue },
                    { "@parameter_type", (int)parameter.ParameterType },
                    { "@purpose", parameter.Purpose ?? (object)DBNull.Value }
                };

                var dataTable = QueryExecutor.ExecuteNamedQueryFromFile(
                    "Parameters", "ParametersQueries", "InsertParameter", out errorMessage, parameters);

                if (dataTable == null)
                {
                    Debug.WriteLine($"InsertParameter failed: {errorMessage}");
                    throw new Exception($"Failed to insert parameter: {errorMessage}");
                }

                // Get the returned ID
                int newId = Convert.ToInt32(dataTable.Rows[0][0]);
                Debug.WriteLine($"=== InsertParameter: Created parameter with ID {newId} ===");
                return newId;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in InsertParameter: {ex.Message}");
                throw new Exception($"Error inserting parameter: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Checks if a parameter code already exists in the database
        /// </summary>
        /// <param name="parameterCode">The parameter code to check</param>
        /// <param name="excludeId">Optional ID to exclude from the check (for edit operations)</param>
        /// <returns>True if the parameter code exists, false otherwise</returns>
        public static bool CheckParameterCodeExists(string parameterCode, int? excludeId = null)
        {
            try
            {
                Debug.WriteLine($"=== CheckParameterCodeExists: Checking '{parameterCode}' (exclude ID: {excludeId}) ===");

                if (string.IsNullOrWhiteSpace(parameterCode))
                {
                    return false;
                }

                string errorMessage;
                var parameters = new Dictionary<string, object>
                {
                    { "@parameter_code", parameterCode.Trim() },
                    { "@exclude_id", excludeId ?? (object)DBNull.Value }
                };

                var dataTable = QueryExecutor.ExecuteNamedQueryFromFile(
                    "Parameters", "ParametersQueries", "CheckParameterCodeExists", out errorMessage, parameters);

                if (dataTable == null || dataTable.Rows.Count == 0)
                {
                    Debug.WriteLine($"CheckParameterCodeExists failed: {errorMessage}");
                    return false; // Assume doesn't exist if check fails
                }

                int count = Convert.ToInt32(dataTable.Rows[0]["count"]);
                bool exists = count > 0;

                Debug.WriteLine($"=== CheckParameterCodeExists: {(exists ? "EXISTS" : "DOES NOT EXIST")} ===");
                return exists;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in CheckParameterCodeExists: {ex.Message}");
                return false; // Assume doesn't exist if check fails
            }
        }

        /// <summary>
        /// Updates an existing parameter in the database
        /// </summary>
        /// <param name="parameter">Parameter model to update</param>
        /// <returns>True if successful</returns>
        public static bool UpdateParameter(ParametersFormModel parameter)
        {
            try
            {
                Debug.WriteLine($"=== UpdateParameter: Starting for ID {parameter.Id} ===");

                if (!parameter.IsValid())
                {
                    throw new ArgumentException("Parameter model is not valid");
                }

                string errorMessage;
                var parameters = new Dictionary<string, object>
                {
                    { "@id", parameter.Id },
                    { "@parameter_code", parameter.ParameterCode },
                    { "@parameter_value", parameter.ParameterValue },
                    { "@parameter_type", (int)parameter.ParameterType },
                    { "@purpose", parameter.Purpose ?? (object)DBNull.Value }
                };

                string updateQuery = SQLQueryLoader.ExtractNamedQuery("Parameters", "ParametersQueries", "UpdateParameter");
                int rowsAffected = QueryExecutor.ExecuteNonQuery(updateQuery, out errorMessage, parameters);

                if (rowsAffected < 0)
                {
                    Debug.WriteLine($"UpdateParameter failed: {errorMessage}");
                    throw new Exception($"Failed to update parameter: {errorMessage}");
                }

                bool success = rowsAffected > 0;
                Debug.WriteLine($"=== UpdateParameter: {(success ? "Success" : "No rows affected")} ===");
                return success;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in UpdateParameter: {ex.Message}");
                throw new Exception($"Error updating parameter: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Deletes a parameter by its ID
        /// </summary>
        /// <param name="id">Parameter ID to delete</param>
        /// <returns>True if successful</returns>
        public static bool DeleteParameter(int id)
        {
            try
            {
                Debug.WriteLine($"=== DeleteParameter: Starting for ID {id} ===");

                string errorMessage;
                var parameters = new Dictionary<string, object> { { "@id", id } };

                string deleteQuery = SQLQueryLoader.ExtractNamedQuery("Parameters", "ParametersQueries", "DeleteParameter");
                int rowsAffected = QueryExecutor.ExecuteNonQuery(deleteQuery, out errorMessage, parameters);

                if (rowsAffected < 0)
                {
                    Debug.WriteLine($"DeleteParameter failed: {errorMessage}");
                    throw new Exception($"Failed to delete parameter: {errorMessage}");
                }

                bool success = rowsAffected > 0;
                Debug.WriteLine($"=== DeleteParameter: {(success ? "Success" : "No rows affected")} ===");
                return success;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in DeleteParameter: {ex.Message}");
                throw new Exception($"Error deleting parameter: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Deletes multiple parameters by their IDs
        /// </summary>
        /// <param name="ids">List of parameter IDs to delete</param>
        /// <returns>Number of parameters deleted</returns>
        public static int DeleteParameters(List<int> ids)
        {
            try
            {
                Debug.WriteLine($"=== DeleteParameters: Starting for {ids.Count} IDs ===");

                if (ids == null || ids.Count == 0)
                {
                    return 0;
                }

                string errorMessage;
                var parameters = new Dictionary<string, object> { { "@ids", ids.ToArray() } };

                string deleteQuery = SQLQueryLoader.ExtractNamedQuery("Parameters", "ParametersQueries", "DeleteParametersByIds");
                int rowsAffected = QueryExecutor.ExecuteNonQuery(deleteQuery, out errorMessage, parameters);

                if (rowsAffected < 0)
                {
                    Debug.WriteLine($"DeleteParameters failed: {errorMessage}");
                    throw new Exception($"Failed to delete parameters: {errorMessage}");
                }

                Debug.WriteLine($"=== DeleteParameters: Deleted {rowsAffected} parameters ===");
                return rowsAffected;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in DeleteParameters: {ex.Message}");
                throw new Exception($"Error deleting parameters: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Maps a DataRow to a ParametersFormModel
        /// </summary>
        /// <param name="row">DataRow to map</param>
        /// <returns>ParametersFormModel</returns>
        private static ParametersFormModel MapDataRowToModel(DataRow row)
        {
            return new ParametersFormModel
            {
                Id = Convert.ToInt32(row["id"]),
                ParameterCode = row["parameter_code"]?.ToString(),
                ParameterValue = row["parameter_value"]?.ToString(),
                ParameterType = ParseParameterType(row["parameter_type"]),
                Purpose = row["purpose"]?.ToString(),
                CreatedAt = Convert.ToDateTime(row["created_at"]),
                ModifiedAt = row["modified_at"] == DBNull.Value ? null : (DateTime?)Convert.ToDateTime(row["modified_at"])
            };
        }

        /// <summary>
        /// Safely parses parameter_type from database, handling both string and integer formats
        /// </summary>
        /// <param name="parameterTypeValue">The parameter_type value from database</param>
        /// <returns>ParameterType enum value</returns>
        private static ParameterType ParseParameterType(object parameterTypeValue)
        {
            try
            {
                if (parameterTypeValue == null || parameterTypeValue == DBNull.Value)
                {
                    return ParameterType.String; // Default to String
                }

                string paramTypeStr = parameterTypeValue.ToString().Trim();

                // Handle integer format (1, 2, 3, 4, 5)
                if (int.TryParse(paramTypeStr, out int paramTypeInt))
                {
                    if (Enum.IsDefined(typeof(ParameterType), paramTypeInt))
                    {
                        return (ParameterType)paramTypeInt;
                    }
                }

                // Handle string format ("string", "number", "decimal", "date", "boolean")
                switch (paramTypeStr.ToLower())
                {
                    case "string":
                    case "text":
                        return ParameterType.String;
                    case "number":
                    case "integer":
                    case "int":
                        return ParameterType.Number;
                    case "decimal":
                    case "float":
                    case "double":
                        return ParameterType.Decimal;
                    case "date":
                    case "datetime":
                        return ParameterType.Date;
                    case "boolean":
                    case "bool":
                        return ParameterType.Boolean;
                    default:
                        Debug.WriteLine($"Unknown parameter type '{paramTypeStr}', defaulting to String");
                        return ParameterType.String;
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error parsing parameter type '{parameterTypeValue}': {ex.Message}. Defaulting to String.");
                return ParameterType.String;
            }
        }
    }
}
