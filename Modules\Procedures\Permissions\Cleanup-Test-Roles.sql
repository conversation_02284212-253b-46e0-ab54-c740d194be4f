-- Cleanup-Test-Roles.sql
-- Remove all test roles and keep only actual business roles
-- This script should be run manually in pgAdmin or similar tool

-- ============================================================================
-- CLEANUP TEST ROLES
-- ============================================================================

-- [CleanupTestRoles] --
-- Remove all test roles and their associated permissions
BEGIN;

-- First, show what will be deleted
SELECT 'ROLES_TO_DELETE' as action, role_id, role_name, description
FROM roles 
WHERE role_name LIKE 'Test%' 
OR role_name LIKE 'Duplicate%'
ORDER BY role_id;

-- Show role permissions that will be deleted
SELECT 'ROLE_PERMISSIONS_TO_DELETE' as action, COUNT(*) as permission_count
FROM role_permissions 
WHERE role_id IN (
    SELECT role_id FROM roles 
    WHERE role_name LIKE 'Test%' 
    OR role_name LIKE 'Duplicate%'
);

-- Delete role permissions for test roles first (due to foreign key constraints)
DELETE FROM role_permissions 
WHERE role_id IN (
    SELECT role_id FROM roles 
    WHERE role_name LIKE 'Test%' 
    OR role_name LIKE 'Duplicate%'
);

-- Delete the test roles themselves
DELETE FROM roles 
WHERE role_name LIKE 'Test%' 
OR role_name LIKE 'Duplicate%';

-- Show what remains
SELECT 'REMAINING_ROLES' as action, role_id, role_name, description, is_active 
FROM roles 
ORDER BY role_id;

COMMIT;
-- [End] --

-- ============================================================================
-- VERIFICATION QUERIES
-- ============================================================================

-- [VerifyCleanup] --
-- Verify that only business roles remain
SELECT 
    'FINAL_ROLE_COUNT' as check_type,
    COUNT(*) as role_count,
    'Should be 4 (Administrator, Manager, User, ReadOnly)' as expected
FROM roles 
WHERE is_active = true;

-- Show final role list
SELECT 
    'FINAL_ROLES' as check_type,
    role_id, 
    role_name, 
    description,
    is_active,
    (SELECT COUNT(*) FROM role_permissions WHERE role_id = r.role_id) as permission_count
FROM roles r
ORDER BY role_id;
-- [End] --

-- ============================================================================
-- MANUAL EXECUTION INSTRUCTIONS
-- ============================================================================

/*
INSTRUCTIONS FOR MANUAL EXECUTION:

1. Open pgAdmin or your PostgreSQL client
2. Connect to your ProManage database
3. Copy and paste the cleanup section above (between BEGIN and COMMIT)
4. Execute the script
5. Verify the results using the verification queries

EXPECTED RESULTS:
- Only 4 roles should remain: Administrator, Manager, User, ReadOnly
- All test roles (TestRole_*, DuplicateTest*, etc.) should be removed
- Role permissions for deleted roles should also be removed

SAFETY NOTES:
- This script uses a transaction (BEGIN/COMMIT) for safety
- If something goes wrong, you can ROLLBACK instead of COMMIT
- The script shows what will be deleted before actually deleting
- Only test roles are targeted - business roles are preserved
*/

-- ============================================================================
-- ALTERNATIVE INDIVIDUAL CLEANUP (if batch delete fails)
-- ============================================================================

-- [IndividualCleanup] --
-- If the batch delete fails, use these individual commands:

-- Delete specific test role patterns one by one:
-- DELETE FROM role_permissions WHERE role_id IN (SELECT role_id FROM roles WHERE role_name LIKE 'TestRole_%');
-- DELETE FROM roles WHERE role_name LIKE 'TestRole_%';

-- DELETE FROM role_permissions WHERE role_id IN (SELECT role_id FROM roles WHERE role_name LIKE 'DuplicateTest%');
-- DELETE FROM roles WHERE role_name LIKE 'DuplicateTest%';

-- DELETE FROM role_permissions WHERE role_id IN (SELECT role_id FROM roles WHERE role_name LIKE 'DuplicateTestRole%');
-- DELETE FROM roles WHERE role_name LIKE 'DuplicateTestRole%';
-- [End] --
