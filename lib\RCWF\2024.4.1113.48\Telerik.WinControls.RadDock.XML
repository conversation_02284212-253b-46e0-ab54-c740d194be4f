<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Telerik.WinControls.RadDock</name>
    </assembly>
    <members>
        <member name="T:Telerik.WinControls.UI.Docking.PredefinedCommandNames">
            <summary>
            Contains the names of all predefined commands available in a RadDock instance.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.PredefinedCommandNames.DisplayQuickNavigator">
            <summary>
            The name for the command that displays the QuickNavigator in a RadDock instance.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.PredefinedCommandNames.CloseActiveDocument">
            <summary>
            The command that closes the active document in a RadDock instance.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.PredefinedCommandNames.NextDocument">
            <summary>
            The command that activates the next document in a RadDock instance.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.PredefinedCommandNames.PreviousDocument">
            <summary>
            The command that activates the previous document in a RadDock instance.
            </summary>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.RadDockCommand">
            <summary>
            Represents base command that is associated with a RadDock instance.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDockCommand.#ctor">
            <summary>
            Default constructor.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDockCommand.CanExecute(System.Object)">
            <summary>
            Determines whether the command may execute.
            The command may be executed in the following cases:
            - The currently active form is the one where the associated RadDock resides.
            - The currently active form is a FloatingWindow instance, owned by the associated RadDock.
            - The currently active form is an AutoHidePopup instance, owned by the associated RadDock.
            </summary>
            <param name="parameter">The additional parameter provided. Should be a RadDock instance.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDockCommand.IsShortcut(System.Windows.Forms.Keys,System.Windows.Forms.Keys[])">
            <summary>
            Determines whether the keyboard combination is valid for any of the registered <see cref="T:Telerik.WinControls.RadShortcut">Shortcut</see> instance.
            </summary>
            <param name="modifiers"></param>
            <param name="mappings"></param>
            <returns></returns>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDockCommand.IsMappingKey(System.Windows.Forms.Keys)">
            <summary>
            Determines whether the specified key is a mapping for any of the associated shortcuts.
            </summary>
            <param name="key"></param>
            <returns></returns>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDockCommand.IsPartialShortcut(System.Windows.Forms.Keys,System.Windows.Forms.Keys[])">
            <summary>
            Determines whether the keyboard combination is partial for any of the registered <see cref="T:Telerik.WinControls.RadShortcut">Shortcut</see> instance.
            </summary>
            <param name="modifiers"></param>
            <param name="mappings"></param>
            <returns></returns>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.RadDockCommand.Shortcuts">
            <summary>
            Gets a list with all the <see cref="T:Telerik.WinControls.RadShortcut">Shortcuts</see> registered for this command.
            </summary>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.RadDockCommandManager">
            <summary>
            Manages all the commands registered with a RadDock instance.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDockCommandManager.#ctor(Telerik.WinControls.UI.Docking.RadDock)">
            <summary>
            Constructs a new instance of the <see cref="T:Telerik.WinControls.UI.Docking.RadDockCommandManager">CommandManager</see> class.
            </summary>
            <param name="dockManager">The RadDock instance this manager is associated with.</param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDockCommandManager.OnDockManagerLoaded">
            <summary>
            The manager gets notified that the associated
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDockCommandManager.DisposeManagedResources">
            <summary>
            Unregisters this instance from the global RadMessageFilter instance.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDockCommandManager.CanProcessMessage(System.Windows.Forms.Message)">
            <summary>
            Determines whether the manager should process keyboard messages
            </summary>
            <param name="msg"></param>
            <returns></returns>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDockCommandManager.ProcessKeyDown(System.Windows.Forms.KeyEventArgs)">
            <summary>
            Processes a WM_KEYDOWN message that comes into the UI thread.
            Default implementation will attempt to execute a command that matches completely or partially the keys combination.
            </summary>
            <param name="keys"></param>
            <returns></returns>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDockCommandManager.IsModifierKey(System.Windows.Forms.Keys)">
            <summary>
            Determines whether the specified Key is a modifier.
            </summary>
            <param name="currKey"></param>
            <returns></returns>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDockCommandManager.RegisterCommand(Telerik.WinControls.UI.Docking.RadDockCommand)">
            <summary>
            Registers the specified command, using the command's Name as a key.
            </summary>
            <param name="command"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDockCommandManager.UnregisterCommand(System.String)">
            <summary>
            Removes an already registered command with the specified name.
            </summary>
            <param name="name"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDockCommandManager.FindCommandByName(System.String)">
            <summary>
            Finds the RadDockCommand instance which name equals the specified one. May be null if no such command is registered.
            </summary>
            <param name="name"></param>
            <returns></returns>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDockCommandManager.ExecuteCommand(System.String)">
            <summary>
            Attempts to execute the RadDockCommand which matches the specified name.
            </summary>
            <param name="name"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDockCommandManager.OnEnabledChanged">
            <summary>
            Notifies for a change in the Enabled state.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.RadDockCommandManager.Commands">
            <summary>
            Gets all the registered commands with this manager.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.RadDockCommandManager.Enabled">
            <summary>
            Determines whether the command manager is currently enabled.
            </summary>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.ContextMenuItemClickEventArgs">
            <summary>
            
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.ContextMenuItemClickEventArgs.Item">
            <summary>
            Gets the <see cref="T:Telerik.WinControls.UI.RadMenuItemBase">RadMenuItemBase</see> instance, that is clicked.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.ContextMenuItemClickEventArgs.Handled">
            <summary>
            Determines whether the event is handled by the user and default action should not be performed.
            </summary>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.ContextMenuService">
            <summary>
            Provides methods for displaying a context menu for a document or tool window.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.ContextMenuService.AllowToolContextMenu">
            <summary>
            Determines whether context menus, associated with a tool window may be displayed.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.ContextMenuService.AllowDocumentContextMenu">
            <summary>
            Determines whether context menus, associated with a document window may be displayed.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.ContextMenuService.AllowActiveWindowListContextMenu">
            <summary>
            Determines whether a context menu, listing all opened documents within a document strip, may be displayed.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.ContextMenuService.IsMenuDisplayed">
            <summary>
            Determines whether a context menu is currently displayed.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.ContextMenuService.DisplayedMenu">
            <summary>
            Gets the currently displayed menu.
            </summary>
        </member>
        <member name="E:Telerik.WinControls.UI.Docking.ContextMenuService.ContextMenuDisplaying">
            <summary>
            Notifies that a context menu is about to be displayed.
            </summary>
        </member>
        <member name="E:Telerik.WinControls.UI.Docking.ContextMenuService.ContextMenuItemClicked">
            <summary>
            Notifies that a context menu item has been clicked.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.ContextMenuService.DisplayContextMenu(Telerik.WinControls.UI.Docking.DockWindow,System.Drawing.Point)">
            <summary>
            Displays a context menu at the specified screen position, associated with the provided <see cref="T:Telerik.WinControls.UI.Docking.DockWindow">DockWindow</see> instance.
            </summary>
            <param name="window"></param>
            <param name="screenPos"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.ContextMenuService.DisplayContextMenu(Telerik.WinControls.RadElement,Telerik.WinControls.UI.Docking.DockWindow,System.Drawing.Point)">
            <summary>
            Displays a context menu at the specified screen position, associated with the provided <see cref="T:Telerik.WinControls.UI.Docking.DockWindow">DockWindow</see> instance.
            </summary>
            <param name="owner"></param>
            <param name="window"></param>
            <param name="screenPos"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.ContextMenuService.DisplayActiveWindowList(Telerik.WinControls.UI.Docking.DocumentTabStrip,System.Drawing.Point)">
            <summary>
            Displays a context menu, listing all currently active documents within the specified document strip.
            </summary>
            <param name="strip"></param>
            <param name="screenPos"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.ContextMenuService.GetContextMenuItems(Telerik.WinControls.UI.Docking.DockWindow)">
            <summary>
            Gets the menu items, associated with the specified DockWindow.
            </summary>
            <param name="window"></param>
            <returns></returns>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.ContextMenuService.GetContextMenuItems(Telerik.WinControls.UI.Docking.DockWindow,System.Boolean)">
            <summary>
            Gets the menu items, associated with the specified DockWindow.
            </summary>
            <param name="window"></param>
            <param name="defaultAction">True to execute the default action, associated with each item, when an item is clicked.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.ContextMenuService.CanDisplayMenu(Telerik.WinControls.UI.Docking.DockWindow)">
            <summary>
            Determines whether a context menu can be displayed for the specified window.
            </summary>
            <param name="window"></param>
            <returns></returns>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.ContextMenuService.DisplayMenuCore(System.Collections.Generic.List{Telerik.WinControls.UI.RadMenuItemBase},System.Drawing.Point)">
            <summary>
            Displays the context menu at the specified screen position, using the provided list of items.
            </summary>
            <param name="items"></param>
            <param name="screenPos"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.ContextMenuService.DisplayMenuCore(Telerik.WinControls.RadElement,System.Collections.Generic.List{Telerik.WinControls.UI.RadMenuItemBase},System.Drawing.Point)">
            <summary>
            Displays the context menu at the specified screen position, using the provided list of items.
            </summary>
            <param name="menuOwner"></param>
            <param name="items"></param>
            <param name="screenPos"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.ContextMenuService.OnContextMenuItemClick(System.Object,System.EventArgs)">
            <summary>
            The entry point used to handle menu item clicks.
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.ContextMenuService.PerformMenuItemAction(Telerik.WinControls.UI.Docking.DockWindow,Telerik.WinControls.UI.RadMenuItemBase)">
            <summary>
            Performs the core action, depending on the clicked menu item.
            </summary>
            <param name="menuItem"></param>
            <param name="window"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.ContextMenuService.BuildContextMenuItems(Telerik.WinControls.UI.Docking.DockWindow)">
            <summary>
            Prepares the list of menu items, available for the specified dock window.
            </summary>
            <param name="window"></param>
            <returns></returns>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.ContextMenuService.BuildContextMenuItems(Telerik.WinControls.UI.Docking.DocumentTabStrip)">
            <summary>
            Prepares the list of opened documents within the specified DocumentTabStrip instance.
            </summary>
            <param name="strip"></param>
            <returns></returns>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.ContextMenuService.AddDockStateMenuItems(Telerik.WinControls.UI.Docking.DockWindow,System.Collections.Generic.List{Telerik.WinControls.UI.RadMenuItemBase})">
            <summary>
            Adds the menu items, which alter the DockState member of the specified window.
            </summary>
            <param name="window"></param>
            <param name="items"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.ContextMenuService.AddCloseDocumentMenuItems(Telerik.WinControls.UI.Docking.DockWindow,System.Collections.Generic.List{Telerik.WinControls.UI.RadMenuItemBase})">
            <summary>
            Adds the menu items, associated with close action upon the specified window
            </summary>
            <param name="window"></param>
            <param name="items"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.ContextMenuService.AddTabbedGroupMenuItems(Telerik.WinControls.UI.Docking.DockWindow,System.Collections.Generic.List{Telerik.WinControls.UI.RadMenuItemBase})">
            <summary>
            Adds the menu items, related to the tabbed groups in the DocumentContainer
            </summary>
            <param name="window"></param>
            <param name="items"></param>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.ContextMenuDisplayingEventArgs">
            <summary>
            Encapsulates the arguments, associated with 
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.ContextMenuDisplayingEventArgs.MenuType">
            <summary>
            Gets the type of the context menu requested.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.ContextMenuDisplayingEventArgs.MenuItems">
            <summary>
            Gets the List of menu items, which are about to be displayed.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.ContextMenuDisplayingEventArgs.DockWindow">
            <summary>
            Gets the <see cref="P:Telerik.WinControls.UI.Docking.ContextMenuDisplayingEventArgs.DockWindow">DockWindow</see> instance, associated with the event.
            Valid when the MenuType is <see cref="F:Telerik.WinControls.UI.Docking.ContextMenuType.DockWindow">DockWindow</see>.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.ContextMenuDisplayingEventArgs.DocumentStrip">
            <summary>
            Gets the <see cref="T:Telerik.WinControls.UI.Docking.DocumentTabStrip">DocumentTabStrip</see> instance, associated with the event.
            Valid when the MenuType is <see cref="F:Telerik.WinControls.UI.Docking.ContextMenuType.ActiveWindowList">ActiveWindowList</see>.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.ContextMenuDisplayingEventArgs.DisplayPosition">
            <summary>
            Gets or sets the position (in screen coordinates) where the context menu will be displayed.
            </summary>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.ContextMenuType">
            <summary>
            Defines the different types of context menus, displayed within a RadDock instance.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.ContextMenuType.DockWindow">
            <summary>
            Context menu, associated with a DockWindow instance.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.ContextMenuType.ActiveWindowList">
            <summary>
            Context menu, listing all opened documents within a document strip.
            </summary>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.AllowedDockPosition">
            <summary>
            Defines the allowed dock positions for a DockWindow.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.AllowedDockPosition.None">
            <summary>
            No dock allowed.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.AllowedDockPosition.Left">
            <summary>
            Indicates that the DockWindow will be docked to the left edge of the drop target.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.AllowedDockPosition.Top">
            <summary>
            Indicates that the DockWindow will be docked to the top edge of the drop target.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.AllowedDockPosition.Right">
            <summary>
            Indicates that the DockWindow will be docked to the right edge of the drop target.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.AllowedDockPosition.Bottom">
            <summary>
            Indicates that the DockWindow will be docked to the bottom edge of the drop target.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.AllowedDockPosition.Fill">
            <summary>
            Indicates that the DockWindow will be added as a child of the drop target.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.AllowedDockPosition.LeftDocument">
            <summary>
            Indicates that the DockWindow will be docked as a Document Window to the left edge of the drop target.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.AllowedDockPosition.TopDocument">
            <summary>
            Indicates that the DockWindow will be docked as a Document Window  to the top edge of the drop target.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.AllowedDockPosition.RightDocument">
            <summary>
            Indicates that the DockWindow will be docked as a Document Window  to the right edge of the drop target.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.AllowedDockPosition.BottomDocument">
            <summary>
            Indicates that the DockWindow will be docked as a Document Window  to the bottom edge of the drop target.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.AllowedDockPosition.All">
            <summary>
            The default dock positions are defined.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.AllowedDockPosition.AllDocuments">
            <summary>
            All dock positions, including the document ones, are defined.
            </summary>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.AutoHideGroup">
            <summary>
            A logical structure that treats a number of <see cref="T:Telerik.WinControls.UI.Docking.DockWindow">DockWindow</see> instances as group that is auto-hidden/restored at once.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.AutoHideGroup.#ctor">
            <summary>
            Constructor generally used by serializer
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.AutoHideGroup.#ctor(System.Collections.Generic.IEnumerable{Telerik.WinControls.UI.Docking.DockWindow})">
            <summary>
            Initializes a new group, associated with the specified windows.
            </summary>
            <param name="windows"></param>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.AutoHideGroup.Windows">
            <summary>
            
            </summary>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.AutoHideTabStrip">
            <summary>
            A predefined <see cref="T:Telerik.WinControls.UI.Docking.ToolTabStrip">ToolTabStrip</see> instance that resides within an <see cref="T:Telerik.WinControls.UI.Docking.AutoHidePopup">AutoHidePopup</see> and is used to display auto-hidden dock windows.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.AutoHideTabStrip.#ctor">
            <summary>
            Default constructor.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.AutoHideTabStrip.#ctor(Telerik.WinControls.UI.Docking.RadDock)">
            <summary>
            Initializes a new <see cref="T:Telerik.WinControls.UI.Docking.AutoHideTabStrip">AutoHideTabStrip</see> instance and associates it with the provided RadDock instance.
            </summary>
            <param name="dockManager"></param>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.AutoHideTabStrip.ThemeClassName">
            <summary>
            
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.AutoHideTabStrip.SplitterWidth">
            <summary>
            Gets or sets the width of the splitter.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.AutoHideTabStrip.FixedSplitter">
            <summary>
            Determines whether the splitter is fixed (may be used to resize the owning popup).
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.AutoHideTabStrip.CreateChildItems(Telerik.WinControls.RadElement)">
            <summary>
            
            </summary>
            <param name="parent"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.AutoHideTabStrip.ShouldHandleDoubleClick">
            <summary>
            Do not handle the mouse double-click event.
            </summary>
            <returns></returns>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.AutoHideTabStrip.TabPanelBounds">
            <summary>
            
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.AutoHideTabStrip.AutoHideDock">
            <summary>
            Gets the DockStyle which determines the edge at which the owning auto-hide popup is displayed.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.AutoHideTabStrip.OnMouseDown(System.Windows.Forms.MouseEventArgs)">
            <summary>
            
            </summary>
            <param name="e"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.AutoHideTabStrip.OnMouseMove(System.Windows.Forms.MouseEventArgs)">
            <summary>
            
            </summary>
            <param name="e"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.AutoHideTabStrip.OnMouseLeave(System.EventArgs)">
            <summary>
            
            </summary>
            <param name="e"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.AutoHideTabStrip.GetAutoHideButtonChecked">
            <summary>
            The auto-hide button is not checked.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.AutoHideTabStrip.GetTabStripVisible">
            <summary>
            The tabstrip element is never displayed.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.AutoHideTabStrip.GetCollapsed">
            <summary>
            Collapsed state is not valid for this type.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.AutoHideTabStrip.OnMouseUp(System.Windows.Forms.MouseEventArgs)">
            <summary>
            
            </summary>
            <param name="e"></param>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.AutoHideGroupCollection">
            <summary>
            A collection that containes instances of type <see cref="T:Telerik.WinControls.UI.Docking.AutoHideGroup">AutoHideGroup</see>.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.AutoHideGroupCollection.GetEnumerator">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.AutoHideGroupCollection.CopyTo(System.Array,System.Int32)">
            <summary>
            Copies the collection to the destination Array, starting from the specified index.
            </summary>
            <param name="array"></param>
            <param name="index"></param>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.AutoHideGroupCollection.Count">
            <summary>
            Gets the number of AutoHideGroup instances currently contained.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.AutoHideGroupCollection.Item(System.Int32)">
            <summary>
            Gets the AutoHideGroup instance at the specified index in the collection
            </summary>
            <param name="index"></param>
            <returns></returns>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.AutoHideGroupCollection.Clear">
            <summary>
            Removes all entries from the collection.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.AutoHideGroupCollection.Add(Telerik.WinControls.UI.Docking.AutoHideGroup)">
            <summary>
            Adds the specified <see cref="T:Telerik.WinControls.UI.Docking.AutoHideGroup">AutoHideGroup</see> instance to the collection.
            </summary>
            <param name="item"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.AutoHideGroupCollection.CopyTo(Telerik.WinControls.UI.Docking.AutoHideGroup[],System.Int32)">
            <summary>
            Copies the collection to the destination Array, starting from the specified index.
            </summary>
            <param name="array"></param>
            <param name="arrayIndex"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.AutoHideGroupCollection.Remove(Telerik.WinControls.UI.Docking.AutoHideGroup)">
            <summary>
            Removes the specified instance from the collection.
            </summary>
            <param name="item"></param>
            <returns></returns>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.AutoHideTabStripElement">
            <summary>
            A predefined <see cref="T:Telerik.WinControls.UI.RadPageViewStripElement">RadPageViewStripElement</see> that holds the associated <see cref="T:Telerik.WinControls.UI.TabStripItem">RadPageViewStripItem</see> for each auto-hidden DockWindow.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.AutoHideTabStripElement.AutoHidePopupOffset">
            <summary>
            Gets or sets the offset of the auto-hide popup.
            </summary>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.DockAutoHideSerializationContainer">
            <summary>
            Represents a logical container of TabStrip instances that contain RadDock windows with "auto-hide" state
            </summary>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.AutoHidePosition">
            <summary>
            Defines the possible edges for a DockWindow to become auto-hidden.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.AutoHidePosition.Left">
            <summary>
            Left edge of RadDock's bounds.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.AutoHidePosition.Top">
            <summary>
            Top edge of RadDock's bounds.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.AutoHidePosition.Right">
            <summary>
            Right edge of RadDock's bounds.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.AutoHidePosition.Bottom">
            <summary>
            Bottom edge of RadDock's bounds.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.AutoHidePosition.Auto">
            <summary>
            The edge is automatically chosen depending on the current alignment
            of the DockWindow against the MainDocumentContainer that is hosted on RadDock.
            </summary>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.DocumentContainer">
            <summary>
            Represents a special container, which is used to store document tab strips.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DocumentContainer.#ctor">
            <summary>
            Default constructor.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DocumentContainer.#ctor(Telerik.WinControls.UI.Docking.RadDock)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.WinControls.UI.Docking.DocumentContainer">DocumentContainer</see>
            class and associates it with the provided <see cref="T:Telerik.WinControls.UI.Docking.RadDock">RadDock</see> instance.
            </summary>
            <param name="dock"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DocumentContainer.CanSelectAtDesignTime">
            <summary>
            This method is used internally.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DocumentContainer.UpdateCollapsed">
            <summary>
            this method is used internally.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DocumentContainer.Dispose(System.Boolean)">
            <summary>
            
            </summary>
            <param name="disposing"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DocumentContainer.OnControlAdded(System.Windows.Forms.ControlEventArgs)">
            <summary>
            
            </summary>
            <param name="e"></param>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DocumentContainer.DockManager">
            <summary>
            Gets the RadDock, where this container resides.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DocumentContainer.IsMainDocumentContainer">
            <summary>
            Gets or sets a value indicating whether this instance is the main document container for the associated RadDock.
            This property is used primarily for serialization purposes and is not intended to be used directly by code.
            </summary>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.DocumentManager">
            <summary>
            Manages the document windows in a RadDock instance.
            Supports additional collection of all DockWindow instances that reside within a DocumentTabStrip. The collection's sort order depends on the activation precedence.
            Provides methods for navigating to next and previous document. Keeps track of the currently active window.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DocumentManager.#ctor(Telerik.WinControls.UI.Docking.RadDock)">
            <summary>
            Constructs a new DocumentManager instance.
            </summary>
            <param name="owner"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DocumentManager.BuildDocumentList">
            <summary>
            Builds the default list of documents. Called upon initialization completion.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DocumentManager.OnDockManagerLoaded">
            <summary>
            The manager gets notified that the owning RadDock instance has been sucessfully loaded.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DocumentManager.OnActiveWindowChanged(Telerik.WinControls.UI.Docking.DockWindow)">
            <summary>
            The manager receives notification from its owning RadDock instance for a change in the currently active window.
            </summary>
            <param name="currActive"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DocumentManager.OnLoadingFromXML">
            <summary>
            Receives a notification from the owning RadDock that a LoadFromXML operation is upcoming.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DocumentManager.OnLoadedFromXML">
            <summary>
            
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DocumentManager.OnDockWindowDockStateChanged(Telerik.WinControls.UI.Docking.DockWindow)">
            <summary>
            Receives a notification for a change in the DockState property of a DockWindow.
            We may have a situation where a ToolWindow becomes a TabbedDocument and vice-versa.
            </summary>
            <param name="window"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DocumentManager.OnDockWindowAdded(Telerik.WinControls.UI.Docking.DockWindow)">
            <summary>
            Receives a notification for a DockWindow instance added to the owning RadDock.
            </summary>
            <param name="window"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DocumentManager.OnDockWindowRemoved(Telerik.WinControls.UI.Docking.DockWindow)">
            <summary>
            Receives a notification for a DockWindow instance removed from the owning RadDock.
            </summary>
            <param name="window"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DocumentManager.ActivateNextDocument">
            <summary>
            Activates the next document in the z-ordered list.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DocumentManager.ActivatePreviousDocument">
            <summary>
            Activates the previous document in the z-ordered list.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DocumentManager.GetActiveWindowList(Telerik.WinControls.UI.Docking.DocumentTabStrip)">
            <summary>
            Gets the list of menu items to be displayed on the ActiveWindowList on the specified DocumentTabStrip.
            </summary>
            <param name="strip"></param>
            <returns></returns>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DocumentManager.FindInsertIndex(Telerik.WinControls.UI.Docking.DockWindow)">
            <summary>
            Finds the insert index for the specified document, using binary search.
            </summary>
            <param name="window"></param>
            <returns></returns>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DocumentManager.DocumentCloseActivation">
            <summary>
            Determines which document should become active when the current active one is closed.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DocumentManager.DocumentInsertOrder">
            <summary>
            Gets or sets the insert order to be used when adding new documents.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DocumentManager.BoldActiveDocument">
            <summary>
            Determines whether the currently active document's Text will be displayed in bold Font in its corresponding TabItem.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DocumentManager.ActiveDocumentMenuSortOrder">
            <summary>
            Gets or sets the <see cref="T:Telerik.WinControls.UI.Docking.ActiveDocumentListSortOrder">ActiveListMenuSortOrder</see> value,
            which defines how menu items will be sorted in the active document list menu.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DocumentManager.DocumentArray">
            <summary>
            Gets an array of DockWindow instances, which DockState equals to DockState.TabbedDocument, in the order they appear in their parent strips.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DocumentManager.DocumentArrayZOrdered">
            <summary>
            Gets an array of DockWindow instances, which DockState equals to DockState.TabbedDocument.
            The array is sorted by each window's z-order.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DocumentManager.DocumentArraySortedByText">
            <summary>
            Gets an array of <see cref="T:Telerik.WinControls.UI.Docking.DockWindow">DockWindow</see> instances, which <see cref="P:Telerik.WinControls.UI.Docking.DockWindow.DockState">DockState</see> equals <see cref="F:Telerik.WinControls.UI.Docking.DockState.TabbedDocument">TabbedDocument</see>.
            The array is sorted by the Text value of each document.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DocumentManager.ActiveDocument">
            <summary>
            Gets the currently active document in the owning RadDock instance.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DocumentManager.DocumentEnumerator">
            <summary>
            Gest an enumerator, which allows for iterating all registered documents in the order they appear in their parent strips.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DocumentManager.DocumentEnumeratorZOrdered">
            <summary>
            Gets an enumerator, which allows for iterating all registered documents in their z-order.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DocumentManager.DocumentEnumeratorSortedByText">
            <summary>
            Gets an enumerator, which allows for iterating all registered documents in a sorted-by-text manner.
            </summary>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.DocumentStripButtons">
            <summary>
            Defines the available buttons for a DockWindow, residing in a DocumentTabStrip instance.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.DocumentStripButtons.None">
            <summary>
            No buttons are displayed.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.DocumentStripButtons.Close">
            <summary>
            Close button is displayed.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.DocumentStripButtons.ActiveWindowList">
            <summary>
            The drop-down button which displayes all opened windows within the strip.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.DocumentStripButtons.SystemMenu">
            <summary>
            The system menu, which is available for each active window.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.DocumentStripButtons.All">
            <summary>
            All flags are set.
            </summary>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.ActiveDocumentListSortOrder">
            <summary>
            Defines the possible order of items in the ActiveDocumentList menu, displayed for each <see cref="T:Telerik.WinControls.UI.Docking.DocumentTabStrip">DocumentTabStrip</see>.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.ActiveDocumentListSortOrder.None">
            <summary>
            No srot order is applied. The items in the menu are in the order they ppear in the owned documents.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.ActiveDocumentListSortOrder.ByText">
            <summary>
            The items are sorted by the Text value of each document.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.ActiveDocumentListSortOrder.ZOrdered">
            <summary>
            The items are sorted by their z-order, supported by the <see cref="T:Telerik.WinControls.UI.Docking.DocumentManager">DocumentManager</see>.
            </summary>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.DockWindowCloseAction">
            <summary>
            Defines the possible actions to be taken when a <see cref="M:Telerik.WinControls.UI.Docking.RadDock.CloseWindow(Telerik.WinControls.UI.Docking.DockWindow)">CloseWindow</see> request is made.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.DockWindowCloseAction.Close">
            <summary>
            The associated DockWindow is unregistered and removed from its current parent.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.DockWindowCloseAction.CloseAndDispose">
            <summary>
            The associated DockWindow is unregistered, removed from its current parent and explicitly disposed.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.DockWindowCloseAction.Hide">
            <summary>
            The associated DockWindow is removed from its current parent but kept as registered with its owning RadDock.
            A hidden window may be later on displayed again at its previous state.
            </summary>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.ToolStripCaptionButtons">
            <summary>
            Defines the visible buttons for a DockWindow, residing in a ToolTabStrip instance.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.ToolStripCaptionButtons.None">
            <summary>
            No buttons are displayed.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.ToolStripCaptionButtons.Close">
            <summary>
            Close button is displayed.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.ToolStripCaptionButtons.AutoHide">
            <summary>
            Auto-hide (pin) button is displayed.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.ToolStripCaptionButtons.SystemMenu">
            <summary>
            The built-in system menu is displayed.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.ToolStripCaptionButtons.All">
            <summary>
            All bits are set.
            </summary>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.AutoHideAnimateMode">
            <summary>
            Defines the possible modes for animating an auto-hidden window.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.AutoHideAnimateMode.None">
            <summary>
            No Animation is applied.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.AutoHideAnimateMode.AnimateShow">
            <summary>
            The window is animated when shown.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.AutoHideAnimateMode.AnimateHide">
            <summary>
            The window is animated when hidden.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.AutoHideAnimateMode.Both">
            <summary>
            The window is animated when shown and hidden.
            </summary>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.DockWindowInsertOrder">
            <summary>
            Defines the possible insertion order when adding new documents.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.DockWindowInsertOrder.Default">
            <summary>
            Default order is chosen
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.DockWindowInsertOrder.InFront">
            <summary>
            The document is put in front of the opened documents. The default mode.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.DockWindowInsertOrder.ToBack">
            <summary>
            The document is put to the end of the opened documents.
            </summary>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.DocumentCloseActivation">
            <summary>
            Defines which document should be activated upon closing active document.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.DocumentCloseActivation.Default">
            <summary>
            The document manager chooses the default action. Typically this will activate the first tab in the strip.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.DocumentCloseActivation.FirstInZOrder">
            <summary>
            Activates the last active document in the z-order. Typically this is the document, activated before the closed active one.
            </summary>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.AutoHideDisplayReason">
            <summary>
            Defines the possible reasons for displaying an auto-hidden window.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.AutoHideDisplayReason.Activate">
            <summary>
            An auto-hidden window has become the currently active one.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.AutoHideDisplayReason.TabItemHovered">
            <summary>
            Associated tab item is hovered.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.AutoHideDisplayReason.TabItemClicked">
            <summary>
            Associated tab item is clicked.
            </summary>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.AutoHideWindowDisplayingEventHandler">
            <summary>
            Defines a method signature to handle <see cref="E:Telerik.WinControls.UI.Docking.RadDock.AutoHideWindowDisplaying">AutoHideWindowDisplaying</see> event.
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.AutoHideWindowDisplayingEventArgs">
            <summary>
            Encapsulates the data associated with the <see cref="E:Telerik.WinControls.UI.Docking.RadDock.AutoHideWindowDisplaying">AutoHideWindowDisplaying</see> event.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.AutoHideWindowDisplayingEventArgs.DisplayReason">
            <summary>
            Gets the reason for the display request.
            </summary>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.DockStateChangingEventHandler">
            <summary>
            A method template that is used to handle a <see cref="T:Telerik.WinControls.UI.Docking.DockState">DockState</see> changing event.
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.DockStateChangingEventArgs">
            <summary>
            Represents the arguments associated with a DockStateChanging event.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DockStateChangingEventArgs.#ctor(Telerik.WinControls.UI.Docking.DockWindow,Telerik.WinControls.UI.Docking.DockState)">
            <summary>
            Constructs a new instance of the <see cref="T:Telerik.WinControls.UI.Docking.DockStateChangingEventArgs">DockStateChangingEventArgs</see> class.
            </summary>
            <param name="dockWindow">The <see cref="T:Telerik.WinControls.UI.Docking.DockWindow">DockWindow</see> instance which <see cref="P:Telerik.WinControls.UI.Docking.DockWindow.DockState">DockState</see> is about to change.</param>
            <param name="newDockState">The <see cref="T:Telerik.WinControls.UI.Docking.DockState">DockState</see> value that is about to be applied to the window.</param>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DockStateChangingEventArgs.NewDockState">
            <summary>
            Gets the <see cref="T:Telerik.WinControls.UI.Docking.DockState">DockState</see> that is about to be applied to the associated <see cref="T:Telerik.WinControls.UI.Docking.DockWindow">DockWindow</see> instance.
            </summary>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.DockTabStripNeededEventHandler">
            <summary>
            A method template used to handle a <see cref="E:Telerik.WinControls.UI.Docking.RadDock.DockTabStripNeeded">DockTabStripNeeded</see> event.
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.DockTabStripNeededEventArgs">
            <summary>
            Represents the arguments associated with a <see cref="E:Telerik.WinControls.UI.Docking.RadDock.DockTabStripNeeded">DockTabStripNeeded</see> event.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DockTabStripNeededEventArgs.#ctor(Telerik.WinControls.UI.Docking.DockType)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.WinControls.UI.Docking.DockTabStripNeededEventArgs">DockTabStripNeededEventArgs</see> class.
            </summary>
            <param name="type"></param>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DockTabStripNeededEventArgs.DockType">
            <summary>
            Gets the <see cref="P:Telerik.WinControls.UI.Docking.DockTabStripNeededEventArgs.DockType">DockType</see> of the needed strip.\
            If the value is <see cref="F:Telerik.WinControls.UI.Docking.DockType.ToolWindow">ToolWindow</see> then a <see cref="T:Telerik.WinControls.UI.Docking.ToolTabStrip">ToolTabStrip</see> instance is needed.
            If the value is <see cref="F:Telerik.WinControls.UI.Docking.DockType.Document">Document</see> then a <see cref="T:Telerik.WinControls.UI.Docking.DocumentTabStrip">DocumentTabStrip</see> instance is needed.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DockTabStripNeededEventArgs.Strip">
            <summary>
            Gets or sets the <see cref="T:Telerik.WinControls.UI.Docking.DockTabStrip">DockTabStrip</see> instance to be used.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DockTabStripNeededEventArgs.TabStripVisible">
            <summary>
            Determines whether the TabStripElement should be visible.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DockTabStripNeededEventArgs.ShowCloseButton">
            <summary>
            Determines whether the ShowItemCloseButton will be true for the associated DockTabStrip instance.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DockTabStripNeededEventArgs.ShowPinButton">
            <summary>
            Determines whether the ShowItemPinButton will be true for the associated DocumentTabStrip instance.
            Works only for DocumentTabStrip.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DockTabStripNeededEventArgs.TabStripAlignment">
            <summary>
            Gets or sets the alignment of the TabStripElement within the TabStrip instance.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DockTabStripNeededEventArgs.TabStripTextOrientation">
            <summary>
            Gets or sets the alignment of the TabStripElement within the TabStrip instance.
            </summary>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.DockWindowCancelEventHandler">
            <summary>
            A method template that is used to handle all cancelable events, associated with a <see cref="T:Telerik.WinControls.UI.Docking.DockWindow">DockWindow</see> instance.
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.DockWindowCancelEventArgs">
            <summary>
            Represents the arguments associated with all cancelable events, associated with a <see cref="T:Telerik.WinControls.UI.Docking.DockWindow">DockWindow</see> instance.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DockWindowCancelEventArgs.#ctor(Telerik.WinControls.UI.Docking.DockWindow)">
            <summary>
            Constructs a new instance of the <see cref="T:Telerik.WinControls.UI.Docking.DockWindowCancelEventArgs">DockWindowCancelEventArgs</see> class.
            </summary>
            <param name="dockWindow"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DockWindowCancelEventArgs.#ctor(Telerik.WinControls.UI.Docking.DockWindow,Telerik.WinControls.UI.Docking.DockWindow)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.WinControls.UI.Docking.DockWindowCancelEventArgs"/> class.
            </summary>
            <param name="oldWindow">The old dock window.</param>
            <param name="newWindow">The new dock window.</param>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DockWindowCancelEventArgs.OldWindow">
            <summary>
            Gets the old window.
            </summary>
            <value>The old window.</value>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DockWindowCancelEventArgs.NewWindow">
            <summary>
            Gets the new window.
            </summary>
            <value>The new window.</value>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.DockWindowEventHandler">
            <summary>
            A method template that is used to handle all <see cref="T:Telerik.WinControls.UI.Docking.DockWindow">DockWindow</see> events.
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.DockWindowEventArgs">
            <summary>
            Represents the arguments associated with all <see cref="P:Telerik.WinControls.UI.Docking.DockWindowEventArgs.DockWindow">DockWindow</see> events.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DockWindowEventArgs.#ctor(Telerik.WinControls.UI.Docking.DockWindow)">
            <summary>
            Constructs a new instance of the <see cref="T:Telerik.WinControls.UI.Docking.DockWindowEventArgs">DockWindowEventArgs</see> class.
            </summary>
            <param name="dockWindow">The <see cref="P:Telerik.WinControls.UI.Docking.DockWindowEventArgs.DockWindow">DockWindow</see> instance associated with the event.</param>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DockWindowEventArgs.DockWindow">
            <summary>
            Gets the <see cref="P:Telerik.WinControls.UI.Docking.DockWindowEventArgs.DockWindow">DockWindow</see> instance associated with the event.
            </summary>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.DockWindowSnapshotEventHandler">
            <summary>
            A method template that is used to handle the <see cref="E:Telerik.WinControls.UI.Docking.RadDock.QuickNavigatorSnapshotNeeded">QuickNavigatorSnapshotNeeded</see> event.
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.DockWindowSnapshotEventArgs">
            <summary>
            Represents the arguments associated with a <see cref="E:Telerik.WinControls.UI.Docking.RadDock.QuickNavigatorSnapshotNeeded">QuickNavigatorSnapshotNeeded</see> event.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DockWindowSnapshotEventArgs.#ctor(Telerik.WinControls.UI.Docking.DockWindow)">
            <summary>
            Constructs a new instance of the <see cref="T:Telerik.WinControls.UI.Docking.DockWindowSnapshotEventArgs">DockWindowSnapshotEventArgs</see> class.
            </summary>
            <param name="window"></param>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DockWindowSnapshotEventArgs.SnapShot">
            <summary>
            Gets or sets the custom image to be used as a Preview snapshot for the associated window in the <see cref="T:Telerik.WinControls.UI.Docking.QuickNavigator">QuickNavigator</see>.
            </summary>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.FloatingWindowEventHandler">
            <summary>
            Defines a method signature to handle <see cref="E:Telerik.WinControls.UI.Docking.RadDock.FloatingWindowCreated">FloatingWindowCreated</see> event.
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.FloatingWindowEventArgs">
            <summary>
            Encapsulates the data, associated with the <see cref="E:Telerik.WinControls.UI.Docking.RadDock.FloatingWindowCreated">FloatingWindowCreated</see> event.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.FloatingWindowEventArgs.Window">
            <summary>
            Gets or sets the FloatingWindow instance to be used.
            </summary>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.RadDockTransactionCancelEventHandler">
            <summary>
            A method template that is used to handle a <see cref="E:Telerik.WinControls.UI.Docking.RadDock.TransactionCommitting">TransactionCommitting</see> event.
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.RadDockTransactionCancelEventArgs">
            <summary>
            Represents the arguments associated with a <see cref="E:Telerik.WinControls.UI.Docking.RadDock.TransactionCommitting">TransactionCommitting</see> event.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDockTransactionCancelEventArgs.#ctor(Telerik.WinControls.UI.Docking.RadDockTransaction)">
            <summary>
            
            </summary>
            <param name="transaction"></param>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.RadDockTransactionCancelEventArgs.Cancel">
            <summary>
            Determines whether the transaction should be canceled.
            </summary>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.RadDockTransactionEventHandler">
            <summary>
            A method template that is used to handle all <see cref="T:Telerik.WinControls.UI.Docking.RadDockTransaction">Transaction</see> related events.
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.RadDockTransactionEventArgs">
            <summary>
            Represents the arguments associated with all <see cref="T:Telerik.WinControls.UI.Docking.RadDockTransaction">Transaction</see> related events.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDockTransactionEventArgs.#ctor(Telerik.WinControls.UI.Docking.RadDockTransaction)">
            <summary>
            Constructs a new instance of the <see cref="T:Telerik.WinControls.UI.Docking.RadDockTransactionEventArgs">RadDockTransactionEventArgs</see> class.
            </summary>
            <param name="transaction"></param>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.RadDockTransactionEventArgs.Transaction">
            <summary>
            Gets the associated <see cref="T:Telerik.WinControls.UI.Docking.RadDockTransaction">Transaction</see> instance associated with the event.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.SelectedTabChangedEventArgs.#ctor(Telerik.WinControls.UI.Docking.DockWindow,Telerik.WinControls.UI.Docking.DockWindow)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.WinControls.UI.Docking.SelectedTabChangedEventArgs"/> class.
            </summary>
            <param name="oldWindow">The old window.</param>
            <param name="dockWindow">The dock window.</param>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.SelectedTabChangedEventArgs.OldWindow">
            <summary>
            Gets the old window.
            </summary>
            <value>The old window.</value>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.SelectedTabChangedEventArgs.NewWindow">
            <summary>
            Gets the new window.
            </summary>
            <value>The new window.</value>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.SelectedTabChangedEventArgs.TabStrip">
            <summary>
            Gets the tab strip.
            </summary>
            <value>The tab strip.</value>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.SelectedTabChangedEventArgs.OldTabStripItem">
            <summary>
            Gets the old tab strip item.
            </summary>
            <value>The old tab strip item.</value>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.SelectedTabChangedEventArgs.NewTabStripItem">
            <summary>
            Gets the new tab strip item.
            </summary>
            <value>The new tab strip item.</value>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.SelectedTabChangedEventArgs.OldTabStripItemIndex">
            <summary>
            Gets the old index of the selected TabStripItem.
            </summary>
            <value>The old index of the selected TabStripItem.</value>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.SelectedTabChangedEventArgs.NewTabStripItemIndex">
            <summary>
            Gets the new index of the selected TabStripItem.
            </summary>
            <value>The new index of the selected TabStripItem.</value>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.SelectedTabChangingEventHandler">
            <summary>
            
            </summary>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.SelectedTabChangingEventArgs">
            <summary>
            
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.SelectedTabChangingEventArgs.#ctor(Telerik.WinControls.UI.Docking.DockWindow,Telerik.WinControls.UI.Docking.DockWindow)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.WinControls.UI.Docking.DockWindowCancelEventArgs"/> class.
            </summary>
            <param name="oldWindow">The old dock window.</param>
            <param name="newWindow">The new dock window.</param>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.SelectedTabChangingEventArgs.TabStrip">
            <summary>
            Gets the tab strip.
            </summary>
            <value>The tab strip.</value>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.SelectedTabChangingEventArgs.OldTabStripItem">
            <summary>
            Gets the old tab strip item.
            </summary>
            <value>The old tab strip item.</value>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.SelectedTabChangingEventArgs.NewTabStripItem">
            <summary>
            Gets the new tab strip item.
            </summary>
            <value>The new tab strip item.</value>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.SelectedTabChangingEventArgs.OldTabStripItemIndex">
            <summary>
            Gets the old index of the selected TabStripItem.
            </summary>
            <value>The old index of the selected TabStripItem.</value>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.SelectedTabChangingEventArgs.NewTabStripItemIndex">
            <summary>
            Gets the new index of the selected TabStripItem.
            </summary>
            <value>The new index of the selected TabStripItem.</value>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.SplitContainerNeededEventHandler">
            <summary>
            A method template used to handle a <see cref="E:Telerik.WinControls.UI.Docking.RadDock.DockTabStripNeeded">DockTabStripNeeded</see> event.
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.SplitContainerNeededEventArgs">
            <summary>
            Represents the arguments associated with a <see cref="E:Telerik.WinControls.UI.Docking.RadDock.DockTabStripNeeded">DockTabStripNeeded</see> event.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.SplitContainerNeededEventArgs.Container">
            <summary>
            Gets or sets the <see cref="T:Telerik.WinControls.UI.RadSplitContainer">RadSplitContainer</see> instance to be used.
            </summary>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.FloatingWindowCollection">
            <summary>
            An ICollection implementation to store all <see cref="T:Telerik.WinControls.UI.Docking.FloatingWindow">floating windows</see> associated with a RadDock instance.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.FloatingWindowCollection.GetEnumerator">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.FloatingWindowCollection.CopyTo(System.Array,System.Int32)">
            <summary>
            
            </summary>
            <param name="array"></param>
            <param name="index"></param>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.FloatingWindowCollection.Count">
            <summary>
            Gets the number of FloatingWindow instances contained within the collection.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.FloatingWindowCollection.System#Collections#ICollection#SyncRoot">
            <summary>
            
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.FloatingWindowCollection.Item(System.Int32)">
            <summary>
            Get the floting window at specified index in the collection
            </summary>
            <param name="index"></param>
            <returns></returns>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.FloatingWindowList">
            <summary>
            A serializable collection of <see cref="T:Telerik.WinControls.UI.Docking.FloatingWindow">FloatingWindow</see> instances.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.FloatingWindowList.Add(System.Object)">
            <summary>
            
            </summary>
            <param name="item"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.FloatingWindowList.AddRange(Telerik.WinControls.UI.Docking.SerializableFloatingWindow[])">
            <summary>
            
            </summary>
            <param name="floatingWindows"></param>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.GuidToNameMapping">
            <summary>
            Maps a globally unique identifier (Guid) to a human-readable name.
            Used by the new RadDock framework when converting an old DockingManager framework to its new counterpart.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.GuidToNameMapping.#ctor">
            <summary>
            Constructs a new default instance.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.GuidToNameMapping.#ctor(System.Guid,System.String)">
            <summary>
            Constructs a new instance, using the provided Guid and Name values.
            </summary>
            <param name="guid"></param>
            <param name="name"></param>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.GuidToNameMapping.Guid">
            <summary>
            Gets or sets the associated Guid
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.GuidToNameMapping.Name">
            <summary>
            Gets or sets the associated name.
            </summary>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.GuidToNameMappingCollection">
            <summary>
            A strongly-typed collection of <see cref="T:Telerik.WinControls.UI.Docking.GuidToNameMapping">GuidToNameMapping</see> instances.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.GuidToNameMappingCollection.FindNameByGuid(System.Guid)">
            <summary>
            Finds the name that matches the provided Guid.
            </summary>
            <param name="guid"></param>
            <returns></returns>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.GuidToNameMappingCollection.FindByGuid(System.Guid)">
            <summary>
            Finds the <see cref="T:Telerik.WinControls.UI.Docking.GuidToNameMapping">GuidToNameMapping</see> instance that matches the provided Guid.
            </summary>
            <param name="guid"></param>
            <returns></returns>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.GuidToNameMappingCollection.Item(System.Guid)">
            <summary>
            Indexer. Gets the Name that matches the provided Guid.
            </summary>
            <param name="key"></param>
            <returns></returns>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.MdiController">
            <summary>
            Manages the standard MDI functionality available in .NET.
            </summary>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.SerializableFloatingWindow">
            <summary>
            Represents a serializable version of a <see cref="T:Telerik.WinControls.UI.Docking.FloatingWindow">FloatingWindow</see> instance.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.SerializableFloatingWindow.#ctor">
            <summary>
            Default constructor.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.SerializableFloatingWindow.#ctor(Telerik.WinControls.UI.Docking.FloatingWindow)">
            <summary>
            Copy constructor.
            </summary>
            <param name="floatingWindow"></param>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.SerializableFloatingWindow.DockContainer">
            <summary>
            Gets the SplitContiner instance that is the root of the hierarchy of DockWindows inside this FloatingWindow
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.SerializableFloatingWindow.BackColor">
            <summary>
            Gets or sets the associated floating window's BackColor.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.SerializableFloatingWindow.Location">
            <summary>
            Gets or sets the desktop location of the associated floating window.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.SerializableFloatingWindow.ClientSize">
            <summary>
            Gets or sets the client size of the associated floating window.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.SerializableFloatingWindow.ZIndex">
            <summary>
            Gets or sets the z-idex of the associated floating window.
            </summary>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.AutoHideTransaction">
            <summary>
            Implements a transaction that is associated with a successful AutoHideWindow request.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.AutoHideTransaction.#ctor(System.Collections.Generic.IEnumerable{Telerik.WinControls.UI.Docking.DockWindow},Telerik.WinControls.UI.Docking.AutoHidePosition)">
            <summary>
            Constructs a new <see cref="T:Telerik.WinControls.UI.Docking.AutoHideTransaction">AutoHideTransaction</see> instance.
            </summary>
            <param name="windows">The DockWindow instances that are associated with the transaction.</param>
            <param name="pos">The desired auto-hide position</param>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.AutoHideTransaction.TransactionType">
            <summary>
            Returns the <see cref="F:Telerik.WinControls.UI.Docking.DockTransactionType.AutoHide">AutoHide</see> transaction type.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.AutoHideTransaction.Position">
            <summary>
            Gets the <see cref="T:Telerik.WinControls.UI.Docking.AutoHidePosition"/> instance associated with the transaction.
            </summary>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.CloseTransaction">
            <summary>
            Implements a transaction that is associated with a successful CloseWindow request.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.CloseTransaction.#ctor(System.Collections.Generic.IEnumerable{Telerik.WinControls.UI.Docking.DockWindow})">
            <summary>
            Constructs a new instance of the <see cref="T:Telerik.WinControls.UI.Docking.CloseTransaction">CloseTransaction</see> class.
            </summary>
            <param name="windows">The <see cref="T:Telerik.WinControls.UI.Docking.DockWindow">DockWindows</see> collection associated with the transaction.</param>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.CloseTransaction.TransactionType">
            <summary>
            Returns the <see cref="F:Telerik.WinControls.UI.Docking.DockTransactionType.Close">Close</see> transaction type.
            </summary>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.DockTransactionType">
            <summary>
            Defines the possible transactions in a RadDock instance.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.DockTransactionType.DragDrop">
            <summary>
            The transaction is initiated from a successful drag-and-drop operation.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.DockTransactionType.Redock">
            <summary>
            The transaction is initiated from a successful redock operation.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.DockTransactionType.DockWindow">
            <summary>
            The transaction is initiated from a successful DockWindow or AddDocument request.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.DockTransactionType.AutoHide">
            <summary>
            The transaction is initiated from an auto-hide request.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.DockTransactionType.Close">
            <summary>
            The transaction is initiated from a CloseWindow request.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.DockTransactionType.Float">
            <summary>
            The transaction is initiated from a FloatWindow request.
            </summary>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.DockWindowTransaction">
            <summary>
            Implements a transaction that is associated with a DockWindow request.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DockWindowTransaction.#ctor(Telerik.WinControls.UI.Docking.DockState,Telerik.WinControls.UI.Docking.DockWindow,Telerik.WinControls.UI.Docking.DockWindow,Telerik.WinControls.UI.SplitPanel,Telerik.WinControls.UI.Docking.DockPosition)">
            <summary>
            Constructs a new <see cref="T:Telerik.WinControls.UI.Docking.DockWindowTransaction">DockWindowTransaction</see> instance.
            </summary>
            <param name="state">The target state for the transaction.</param>
            <param name="window">The window associated with the transaction.</param>
            <param name="targetWindow">The target window for the transaction (may be null).</param>
            <param name="anchor">The <see cref="T:Telerik.WinControls.UI.SplitPanel">SplitPanel</see> instance that is used as a dock anchor.</param>
            <param name="position">The <see cref="T:Telerik.WinControls.UI.Docking.DockPosition">DockPosition</see> where the window should be docked.</param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DockWindowTransaction.#ctor(Telerik.WinControls.UI.Docking.DockState,Telerik.WinControls.UI.Docking.DockWindow[],Telerik.WinControls.UI.Docking.DockWindow,Telerik.WinControls.UI.SplitPanel,Telerik.WinControls.UI.Docking.DockPosition)">
            <summary>
            Constructs a new <see cref="T:Telerik.WinControls.UI.Docking.DockWindowTransaction">DockWindowTransaction</see> instance.
            </summary>
            <param name="state">The target state for the transaction.</param>
            <param name="windows">The windows associated with the transaction.</param>
            <param name="targetWindow">The target window for the transaction (may be null).</param>
            <param name="anchor">The <see cref="T:Telerik.WinControls.UI.SplitPanel">SplitPanel</see> instance that is used as a dock anchor.</param>
            <param name="position">The <see cref="T:Telerik.WinControls.UI.Docking.DockPosition">DockPosition</see> where the window should be docked.</param>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DockWindowTransaction.TargetWindow">
            <summary>
            Gets the DockWindow instance that is target for the transaction.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DockWindowTransaction.TransactionType">
            <summary>
            Returns the <see cref="F:Telerik.WinControls.UI.Docking.DockTransactionType.DockWindow">DockWindow</see> transaction type.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DockWindowTransaction.AssociatedWindow">
            <summary>
            Gets the DockWindow instance associated with the transaction.
            </summary>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.DragDropTransaction">
            <summary>
            Implements a transaction that is associated with a successful drag-and-drop operation.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DragDropTransaction.#ctor(Telerik.WinControls.UI.Docking.DockState,Telerik.WinControls.UI.Docking.DockWindow,Telerik.WinControls.UI.SplitPanel,Telerik.WinControls.UI.SplitPanel,Telerik.WinControls.UI.Docking.DockPosition)">
            <summary>
            Constructs a new <see cref="T:Telerik.WinControls.UI.Docking.DragDropTransaction">DragDropTransaction</see> instance.
            </summary>
            <param name="state">The target state of the transaction.</param>
            <param name="draggedWindow">The DockWindow instance that has been dragged. May be null if the drag context has been DockTabStrip instance.</param>
            <param name="panel">The SplitPanel instance that has been dragged. May be null of the drag context has been DockWindow instance.</param>
            <param name="anchor">The SplitPanel instance that is used as dock anchor.</param>
            <param name="position">The DockPosition where to dock the dragged context.</param>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DragDropTransaction.TransactionType">
            <summary>
            Returns the <see cref="F:Telerik.WinControls.UI.Docking.DockTransactionType.DragDrop">DragDrop</see> transaction type.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DragDropTransaction.AssociatedPanel">
            <summary>
            Gets the SplitPanel instance associated with this transaction. May be null, depends on the dragged context.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DragDropTransaction.DraggedWindow">
            <summary>
            Gets the DockWindow instance associated with the transaction. May be null, depends on the dragged context.
            </summary>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.FloatTransaction">
            <summary>
            Implements a transaction that is associated with a FloatWindow operation.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.FloatTransaction.#ctor(System.Collections.Generic.IEnumerable{Telerik.WinControls.UI.Docking.DockWindow},Telerik.WinControls.UI.SplitPanel,System.Drawing.Rectangle)">
            <summary>
            Initializes a new <see cref="T:Telerik.WinControls.UI.Docking.FloatTransaction">FloatTransaction</see> instance.
            </summary>
            <param name="windows"></param>
            <param name="panel"></param>
            <param name="bounds"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.FloatTransaction.#ctor(System.Collections.Generic.IEnumerable{Telerik.WinControls.UI.Docking.DockWindow},Telerik.WinControls.UI.SplitPanel,System.Drawing.Rectangle,Telerik.WinControls.UI.Docking.DockType)">
            <summary>
            Initializes a new <see cref="T:Telerik.WinControls.UI.Docking.FloatTransaction">FloatTransaction</see> instance.
            </summary>
            <param name="windows"></param>
            <param name="panel"></param>
            <param name="bounds"></param>
            <param name="containerType"></param>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.FloatTransaction.AssociatedPanel">
            <summary>
            Gets the <see cref="T:Telerik.WinControls.UI.SplitPanel">SplitPanel</see> instance, associated with the transaction.
            Valid when a ToolTabStrip has been floated.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.FloatTransaction.Bounds">
            <summary>
            Gets the bounds at which the associated windows should be floated.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.FloatTransaction.ContainerDockType">
            <summary>
            Gets the type of the container the floating windows should have.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.FloatTransaction.TransactionType">
            <summary>
            Returns the <see cref="F:Telerik.WinControls.UI.Docking.DockTransactionType.Float">Float</see> dock transaction type.
            </summary>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.PerformDockTransaction">
            <summary>
            Implements a base transaction that is associated with a successful DockWindow request.
            Concrete inheritors are <see cref="T:Telerik.WinControls.UI.Docking.DockWindowTransaction">DockWindowTransaction</see> and <see cref="T:Telerik.WinControls.UI.Docking.DragDropTransaction">DragDropTransaction</see>.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.PerformDockTransaction.#ctor(Telerik.WinControls.UI.Docking.DockState,System.Collections.Generic.IEnumerable{Telerik.WinControls.UI.Docking.DockWindow},Telerik.WinControls.UI.SplitPanel,Telerik.WinControls.UI.Docking.DockPosition)">
            <summary>
            Initializes a new <see cref="T:Telerik.WinControls.UI.Docking.PerformDockTransaction">PerformDockTransaction</see> instance.
            </summary>
            <param name="state">The target state for the transaction.</param>
            <param name="windows">The associated DockWindow instances</param>
            <param name="anchor">The SplitPanel instance, which will be treated as a DockAnchor.</param>
            <param name="position">The DockPosition to be used when performing the DockWindow operation.</param>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.PerformDockTransaction.Position">
            <summary>
            Gets the DockPosition to be used when performing the DockWindow operation.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.PerformDockTransaction.DockAnchor">
            <summary>
            Gets the SplitPanel instance, which will be treated as a DockAnchor.
            </summary>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.RadDockTransaction">
            <summary>
            A logical representation of a RadDock operation.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDockTransaction.#ctor(Telerik.WinControls.UI.Docking.DockState,System.Collections.Generic.IEnumerable{Telerik.WinControls.UI.Docking.DockWindow})">
            <summary>
            Initializes a new <see cref="T:Telerik.WinControls.UI.Docking.RadDockTransaction">RadDockTransaction</see> instance.
            </summary>
            <param name="state">The target state of the transaction.</param>
            <param name="windows">The associated <see cref="T:Telerik.WinControls.UI.Docking.DockWindow">DockWindow</see> instances.</param>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.RadDockTransaction.TransactionType">
            <summary>
            Gets the type of this transaction.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.RadDockTransaction.AssociatedWindows">
            <summary>
            Gets all the windows associated with this transaction.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.RadDockTransaction.AddedWindows">
            <summary>
            Gets all the windows that are new to the manager and are registered by the transaction.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.RadDockTransaction.RemovedWindows">
            <summary>
            Gets all the windows that are removed from the manager by the transaction.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.RadDockTransaction.TargetState">
            <summary>
            Gets the state, which is targeted by this transaction.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.RadDockTransaction.RestoreState">
            <summary>
            Determines whether the transaction will try to first restore
            the state of all associated windows before any other action is performed.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.RedockTransactionReason.ToolTabStripCaptionDoubleClick">
            <summary>
            Transaction is initiated from a double-click within the caption of a ToolTabStrip.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.RedockTransactionReason.FloatingWindowCaptionDoubleClick">
            <summary>
            Transaction is initiated from a double-click within the caption of a FloatingWindow.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.RedockTransactionReason.TabItemDoubleClick">
            <summary>
            The associated TabItem of a DockWindow is double-clicked.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.RedockTransactionReason.Explicit">
            <summary>
            Transaction is initiated explicitly through code.
            </summary>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.RedockTransaction">
            <summary>
            Implements a transaction that is associated with a successful Redock request.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RedockTransaction.#ctor(Telerik.WinControls.UI.Docking.DockState,System.Collections.Generic.IEnumerable{Telerik.WinControls.UI.Docking.DockWindow},System.Boolean)">
            <summary>
            Constructs a new <see cref="T:Telerik.WinControls.UI.Docking.RedockTransaction">RedockTransaction</see> instance.
            </summary>
            <param name="state">The target <see cref="T:Telerik.WinControls.UI.Docking.DockState">dock state</see> for the transaction.</param>
            <param name="windows"></param>
            <param name="defaultAction"></param>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.RedockTransaction.TransactionType">
            <summary>
            Returns the <see cref="F:Telerik.WinControls.UI.Docking.DockTransactionType.Redock">Redock</see> transaction type.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.RedockTransaction.Reason">
            <summary>
            Gets the reason for the redock transaction.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.RedockTransaction.PerformDefaultAction">
            <summary>
            Determines whether a default action will be performed for dock windows without previously saved state.
            </summary>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.DragDropDockPositionEventHandler">
            <summary>
            Defines method signature to handle a <see cref="E:Telerik.WinControls.UI.Docking.DragDropService.PreviewDockPosition">PreviewDockPosition</see> event.
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.DragDropDockPositionEventArgs">
            <summary>
            Represents the arguments, associated with a <see cref="E:Telerik.WinControls.UI.Docking.DragDropService.PreviewDockPosition">PreviewDockPosition</see> event.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DragDropDockPositionEventArgs.#ctor(Telerik.WinControls.UI.SplitPanel,Telerik.WinControls.UI.Docking.AllowedDockPosition,Telerik.WinControls.UI.Docking.DockingGuidesPosition)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.WinControls.UI.Docking.DragDropDockPositionEventArgs">DragDropDockPositionEventArgs</see> class.
            </summary>
            <param name="dropTarget"></param>
            <param name="position"></param>
            <param name="guidePosition"></param>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DragDropDockPositionEventArgs.AllowedDockPosition">
            <summary>
            Gets or sets the allowed dock position for the hit-tested drop target.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DragDropDockPositionEventArgs.DropTarget">
            <summary>
            Gets the current drop target of the drag-drop operation.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DragDropDockPositionEventArgs.GuidePosition">
            <summary>
            Gets the position of the docking guide that is currently hit-tested.
            </summary>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.DragDropHitTestEventHandler">
            <summary>
            Defines method signature to handle a <see cref="E:Telerik.WinControls.UI.Docking.DragDropService.PreviewHitTest">PreviewHitTest</see> event.
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.DragDropHitTestEventArgs">
            <summary>
            Represents the arguments, associated with a <see cref="E:Telerik.WinControls.UI.Docking.DragDropService.PreviewDropTarget">PreviewDropTarget</see> event.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DragDropHitTestEventArgs.#ctor(Telerik.WinControls.UI.SplitPanel,Telerik.WinControls.UI.Docking.DockingGuideHitTest)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.WinControls.UI.Docking.DragDropHitTestEventArgs">DragDropHitTestEventArgs</see> class.
            </summary>
            <param name="dropTarget"></param>
            <param name="hitTest"></param>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DragDropHitTestEventArgs.HitTest">
            <summary>
            Gets or sets the currently generated hit-test result.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DragDropHitTestEventArgs.DropTarget">
            <summary>
            Gets the current drop target of the drag-drop operation.
            </summary>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.DragDropTargetEventHandler">
            <summary>
            Defines method signature to handle a <see cref="E:Telerik.WinControls.UI.Docking.DragDropService.PreviewDropTarget">PreviewDropTarget</see> event.
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.DragDropTargetEventArgs">
            <summary>
            Represents the arguments, associated with a <see cref="E:Telerik.WinControls.UI.Docking.DragDropService.PreviewDropTarget">PreviewDropTarget</see> event.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DragDropTargetEventArgs.#ctor(Telerik.WinControls.UI.SplitPanel)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.WinControls.UI.Docking.DragDropTargetEventArgs">DragDropTargetEventArgs</see> class.
            </summary>
            <param name="target"></param>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DragDropTargetEventArgs.DropTarget">
            <summary>
            Gets or sets the currently hit-tested drop target.
            </summary>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.DragDropMode">
            <summary>
            Defines the supported drag-and-drop modes by a DragDropService.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.DragDropMode.Auto">
            <summary>
            The associated RadDock instance decides which is the appropriate mode, depending on the drag context.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.DragDropMode.Immediate">
            <summary>
            Upon a drag-and-drop request, all the associated windows' state will immediately chang to "Floating"
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.DragDropMode.Preview">
            <summary>
            A preview mode, which does not change the state of the dragged windows
            and simply hints where the windows will be positioned upon a commit.
            </summary>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.DragDropContext">
            <summary>
            Specifies the possible drag-and-drop contexts.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.DragDropContext.FloatingWindow">
            <summary>
            The drag context is a FloatingWindow instance.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.DragDropContext.ToolTabStrip">
            <summary>
            The drag context is a ToolTabStrip instance.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.DragDropContext.ToolWindow">
            <summary>
            The drag context is a ToolWindow
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.DragDropContext.DocumentWindow">
            <summary>
            The drag context is a DockWindow, residing on a DocumentTabStrip
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.DragDropContext.Invalid">
            <summary>
            The drag context is invalid.
            </summary>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.DragDropBehavior">
            <summary>
            Defines the behavior of a started <see cref="T:Telerik.WinControls.UI.Docking.DragDropService">DragDropService</see> instance.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.DragDropBehavior.Auto">
            <summary>
            When the <see cref="T:Telerik.WinControls.UI.Docking.DragDropService">DragDropService</see> is started, it will automatically handle user input and perform actions upon it.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.DragDropBehavior.Manual">
            <summary>
            When the <see cref="T:Telerik.WinControls.UI.Docking.DragDropService">DragDropService</see> is started, it will not handle user input and will rely on explicit manual commands to perform action.
            </summary>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.DockingGuideImage">
            <summary>
            Encapsulates a docking guide image. Includes additional settings allowing for proper Docking Guides visualization.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DockingGuideImage.ShouldSerializeProperty(System.String)">
            <summary>
            Determines which properties should be serialized.
            </summary>
            <param name="propName"></param>
            <returns></returns>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DockingGuideImage.Predefined">
            <summary>
            Determines whether the image is internally created by the framework.
            Such images may not be modified.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DockingGuideImage.PreferredSize">
            <summary>
            Gets or sets the desired sized to be used when rendering image.
            By default the size of the Image itself is used.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DockingGuideImage.LocationOnCenterGuide">
            <summary>
            Gets or sets the location of the image when displayed on the "Center" docking guide.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DockingGuideImage.Image">
            <summary>
            Gets or sets the default image.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DockingGuideImage.HotImage">
            <summary>
            Gets or sets the hot image (the image to be displayed when the mouse hovers the guide displaying this image).
            </summary>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.ToolWindowCaptionElement">
            <summary>
            Implements the Caption element in a <see cref="T:Telerik.WinControls.UI.Docking.ToolTabStrip">ToolTabStrip</see> control.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.ToolWindowCaptionElement.IsActiveProperty">
            <summary>
            A property to determine whether the element is currently active (associated with the <see cref="P:Telerik.WinControls.UI.Docking.RadDock.ActiveWindow">ActiveWindow</see> instance within a RadDock).
            </summary>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.DockingGuidesTemplate">
            <summary>
            Defines settings for the Docking Guides used by the DragDropService.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DockingGuidesTemplate.#ctor">
            <summary>
            Default constructor.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DockingGuidesTemplate.DockingHintBorderWidth">
            <summary>
            Gets or sets the color used to fill the area of the docking hint popup.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DockingGuidesTemplate.DefaultDockingHintBorderWidth">
            <summary>
            Gets the default DockingHintBackColor value.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DockingGuidesTemplate.DockingHintBackColor">
            <summary>
            Gets or sets the color used to fill the area of the docking hint popup.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DockingGuidesTemplate.DefaultDockingHintBackColor">
            <summary>
            Gets the default DockingHintBackColor value.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DockingGuidesTemplate.DockingHintBorderColor">
            <summary>
            Gets or sets the color used to outline the area of the docking hint popup.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DockingGuidesTemplate.DefaultDockingHintBorderColor">
            <summary>
            Gets the default DockingHintBorderColor value.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DockingGuidesTemplate.LeftImage">
            <summary>
            Gets the DockingGuideImage instance that represents the left guide image.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DockingGuidesTemplate.TopImage">
            <summary>
            Gets the DockingGuideImage instance that represents the top guide image.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DockingGuidesTemplate.RightImage">
            <summary>
            Gets the DockingGuideImage instance that represents the right guide image.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DockingGuidesTemplate.BottomImage">
            <summary>
            Gets the DockingGuideImage instance that represents the bottom guide image.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DockingGuidesTemplate.FillImage">
            <summary>
            Gets the DockingGuideImage instance that represents the fill guide image.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DockingGuidesTemplate.InnerLeftImage">
            <summary>
            Gets the DockingGuideImage instance that represents the inner left guide image.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DockingGuidesTemplate.InnerTopImage">
            <summary>
            Gets the DockingGuideImage instance that represents the inner top guide image.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DockingGuidesTemplate.InnerRightImage">
            <summary>
            Gets the DockingGuideImage instance that represents the inner right guide image.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DockingGuidesTemplate.InnerBottomImage">
            <summary>
            Gets the DockingGuideImage instance that represents the inner bottom guide image.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DockingGuidesTemplate.CenterBackgroundImage">
            <summary>
            Gets the DockingGuideImage instance that represents center guide's background image.
            </summary>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.PredefinedDockingGuidesTemplate">
            <summary>
            Represents a DockingGuidesTemplate which uses embedded images and may not be modified by the user.
            </summary>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.DockHelper">
            <summary>
            A helper class that exposes common methods, used across a docking framework.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DockHelper.EnsureSizeMaxBounds(System.Drawing.Size,System.Drawing.Size)">
            <summary>
            Applies Maximum constraint to the specified Size structure.
            </summary>
            <param name="size">The size, which is the constraint target.</param>
            <param name="max">The size, which is the maximum allowed.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DockHelper.EnsureSizeMinBounds(System.Drawing.Size,System.Drawing.Size)">
            <summary>
            Applies Minimum constraint to the specified Size structure.
            </summary>
            <param name="size">The size, which is the constraint target.</param>
            <param name="min">The size, which is the minimum allowed.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DockHelper.EnsureSizeBounds(System.Drawing.Size,System.Drawing.Size,System.Drawing.Size)">
            <summary>
            Applies Minimum and Maximum constraints to the specified Size structure.
            </summary>
            <param name="size">The size, which is the constraint target.</param>
            <param name="min">The size, which is the minimum allowed.</param>
            <param name="max">The size, which is the maximum allowed.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DockHelper.EnsureSizeBounds(System.Drawing.SizeF,System.Drawing.SizeF,System.Drawing.SizeF)">
            <summary>
            Applies Minimum and Maximum constraints to the specified SizeF structure.
            </summary>
            <param name="size">The size, which is the constraint target.</param>
            <param name="min">The size, which is the minimum allowed.</param>
            <param name="max">he size, which is the maximum allowed.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DockHelper.CenterRect(System.Drawing.Rectangle,System.Drawing.Size)">
            <summary>
            Retrieves a Rectangle structure, that is aligned within the specified bounds, and is with the desired size.
            </summary>
            <param name="bounds">The outer Rectangle structure, used as an alignment</param>
            <param name="size">The size of the newly created rectangle.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DockHelper.ShouldBeginDrag(System.Drawing.Point,System.Drawing.Point)">
            <summary>
            Determines whether a Drag operation should be started.
            </summary>
            <param name="curr">The current cursor location.</param>
            <param name="capture">The cursor location </param>
            <returns></returns>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DockHelper.GetAutoHidePosition(Telerik.WinControls.UI.Docking.DockPosition)">
            <summary>
            Retrieves an <see cref="T:Telerik.WinControls.UI.Docking.AutoHidePosition">AutoHidePosition</see> value from the specified <see cref="T:Telerik.WinControls.UI.Docking.DockPosition">DockPosition</see>.
            </summary>
            <param name="position"></param>
            <returns></returns>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DockHelper.GetAllowedDockState(Telerik.WinControls.UI.Docking.DockState)">
            <summary>
            Retrieves an <see cref="T:Telerik.WinControls.UI.Docking.AllowedDockState">AllowedDockState</see> value from the specified <see cref="T:Telerik.WinControls.UI.Docking.DockState">DockState</see>.
            </summary>
            <param name="state"></param>
            <returns></returns>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DockHelper.GetDockPosition(Telerik.WinControls.UI.Docking.AllowedDockPosition)">
            <summary>
            Retrieves a <see cref="T:Telerik.WinControls.UI.Docking.DockPosition">DockPosition</see> value from the specified <see cref="T:Telerik.WinControls.UI.Docking.AllowedDockPosition">AllowedDockPosition</see>.
            </summary>
            <param name="position"></param>
            <returns></returns>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DockHelper.FindCommonAncestor(Telerik.WinControls.UI.SplitPanel,Telerik.WinControls.UI.SplitPanel)">
            <summary>
            Finds the first RadSplitContainer instance, which contains both specified panels.
            </summary>
            <param name="child1"></param>
            <param name="child2"></param>
            <returns></returns>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DockHelper.GetDirectChildContainingPanel(Telerik.WinControls.UI.RadSplitContainer,Telerik.WinControls.UI.SplitPanel)">
            <summary>
            Traverses the tree of split containers and finds the panel,
            which is direct child of the specified container and contains the specified split panel as a descendant.
            </summary>
            <param name="container"></param>
            <param name="descendant"></param>
            <returns></returns>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DockHelper.CleanupContainer(Telerik.WinControls.UI.RadSplitContainer,Telerik.WinControls.UI.Docking.RadDock)">
            <summary>
            Performs a clean-up logic upon the specified RadSplitContainer instance, associated with the specified RadDock instance.
            </summary>
            <param name="container"></param>
            <param name="dockManager"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DockHelper.CollapseOrDisposeStrips(Telerik.WinControls.UI.RadSplitContainer,Telerik.WinControls.UI.Docking.RadDock)">
            <summary>
            Asks all <see cref="T:Telerik.WinControls.UI.Docking.DockTabStrip">DockTabStrip</see> instances to check whether they need to be collapsed or disposed.
            Used in a clean-up pass of RadDock for a control tree defragmentation.
            </summary>
            <param name="container"></param>
            <param name="dockManager"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DockHelper.GetDockWindows(System.Windows.Forms.Control,System.Boolean,Telerik.WinControls.UI.Docking.RadDock)">
            <summary>
            Collects all the DockWindow instances, residing on the specified parent, and associated with the provided RadDock instance.
            </summary>
            <param name="parent"></param>
            <param name="recursive"></param>
            <param name="dockManager"></param>
            <returns></returns>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DockHelper.GetSplitContainers(System.Windows.Forms.Control,System.Boolean,Telerik.WinControls.UI.Docking.RadDock)">
            <summary>
            Collects all the RadSplitContainer instances, residing on the specified parent, and associated with the provided RadDock instance.
            </summary>
            <param name="parent"></param>
            <param name="recursive"></param>
            <param name="dockManager"></param>
            <returns></returns>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DockHelper.GetDockTabStrips``1(System.Windows.Forms.Control,System.Boolean,Telerik.WinControls.UI.Docking.RadDock)">
            <summary>
            Collects all the DockTabStrip instances, residing on the specified parent, and associated with the provided RadDock instance.
            </summary>
            <param name="parent"></param>
            <param name="recursive"></param>
            <param name="dockManager"></param>
            <returns></returns>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DockHelper.MergeContainers(Telerik.WinControls.UI.RadSplitContainer)">
            <summary>
            Defragments the tree of RadSplitContainer instances.
            Used by a RadDock control to clean-up unnecessary containers.
            </summary>
            <param name="parentContainer"></param>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.DockHelper.MaxSize">
            <summary>
            Default maximum size.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.DockHelper.MinSize">
            <summary>
            Default minimum size.
            </summary>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.AutoHidePopup">
            <summary>
            Represents a popup Form that is used to display auto-hidden windows.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.AutoHidePopup.AutoHideDock">
            <summary>
            Gets the DockStyle that determines at which edge the popup should be displayed.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.AutoHidePopup.AnimationDuration">
            <summary>
            Gets or sets the duration of the animation.
            </summary>
            <value>The duration of the animation.</value>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.AutoHidePopup.FixedSplitter">
            <summary>
            
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.AutoHidePopup.ToolStrip">
            <summary>
            
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.AutoHidePopup.ActiveWindow">
            <summary>
            
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.AutoHidePopup.SplitterWidth">
            <summary>
            
            </summary>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.DockWindowCollection">
            <summary>
            A collection that stores all DockWindow instances available per RadDock basis.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DockWindowCollection.#ctor(Telerik.WinControls.UI.Docking.RadDock)">
            <summary>
            
            </summary>
            <param name="owner"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DockWindowCollection.GetWindows(Telerik.WinControls.UI.Docking.DockState)">
            <summary>
            Gets all DockWindow instances that has the specified DockState.
            </summary>
            <param name="state"></param>
            <returns></returns>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DockWindowCollection.GetWindows(System.Predicate{Telerik.WinControls.UI.Docking.DockWindow})">
            <summary>
            Gets all the DockWindow instances that match the specified predicate.
            </summary>
            <param name="predicate"></param>
            <returns></returns>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DockWindowCollection.ToolWindows">
            <summary>
            Gets all the ToolWindow instances available.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DockWindowCollection.DocumentWindows">
            <summary>
            Gets all the DocumentWindow instances available.
            This will not include ToolWindow instances that are currently TabbedDocuments.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DockWindowCollection.Item(System.Int32)">
            <summary>
            Gets the DockWindow instance at the specified index.
            </summary>
            <param name="index"></param>
            <returns></returns>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DockWindowCollection.Item(System.String)">
            <summary>
            Gets the DockWindow instances that matches the specified name.
            </summary>
            <param name="name"></param>
            <returns></returns>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DockWindowCollection.CopyTo(System.Array,System.Int32)">
            <summary>
            Copies the collection to the destination Array, starting from the specified index.
            </summary>
            <param name="array"></param>
            <param name="index"></param>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DockWindowCollection.Count">
            <summary>
            Gest the number of DockWindow instances registered with the owning RadDock.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DockWindowCollection.GetEnumerator">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.DockingGuideHitTest">
            <summary>
            Contains the hit-test information of a DragDropService.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DockingGuideHitTest.#ctor(System.Nullable{Telerik.WinControls.UI.Docking.DockPosition},System.Nullable{Telerik.WinControls.UI.Docking.DockingGuidesPosition})">
            <summary>
            Initializes new instances of the <see cref="T:Telerik.WinControls.UI.Docking.DockingGuideHitTest">DockingGuideHitTest</see> class, using the provided parameters.
            </summary>
            <param name="dockPos"></param>
            <param name="guidePos"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DockingGuideHitTest.#ctor(System.Nullable{Telerik.WinControls.UI.Docking.DockPosition},System.Nullable{Telerik.WinControls.UI.Docking.DockingGuidesPosition},System.Nullable{System.Boolean})">
            <summary>
            Initializes new instances of the <see cref="T:Telerik.WinControls.UI.Docking.DockingGuideHitTest">DockingGuideHitTest</see> class, using the provided parameters.
            </summary>
            <param name="dockPos"></param>
            <param name="guidePos"></param>
            <param name="dockAsDocument"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DockingGuideHitTest.op_Equality(Telerik.WinControls.UI.Docking.DockingGuideHitTest,Telerik.WinControls.UI.Docking.DockingGuideHitTest)">
            <summary>
            Determines whether the specified two <see cref="T:Telerik.WinControls.UI.Docking.DockingGuideHitTest">DockingGuideHitTest</see> instances are equal.
            </summary>
            <param name="ht1"></param>
            <param name="ht2"></param>
            <returns></returns>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DockingGuideHitTest.op_Inequality(Telerik.WinControls.UI.Docking.DockingGuideHitTest,Telerik.WinControls.UI.Docking.DockingGuideHitTest)">
            <summary>
            Determines whether the specified two <see cref="T:Telerik.WinControls.UI.Docking.DockingGuideHitTest">DockingGuideHitTest</see> instances are not equal.
            </summary>
            <param name="ht1"></param>
            <param name="ht2"></param>
            <returns></returns>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DockingGuideHitTest.IsEmpty">
            <summary>
            Determines whether this instance is empty.
            </summary>
            <returns></returns>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.DockingGuideHitTest.DockPosition">
            <summary>
            A nullable member that defines the current <see cref="F:Telerik.WinControls.UI.Docking.DockingGuideHitTest.DockPosition">DockPosition</see> of the hit-test operation.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.DockingGuideHitTest.GuidePosition">
            <summary>
            A nullable member that defines the current <see cref="T:Telerik.WinControls.UI.Docking.DockingGuidesPosition">DockingGuidesPosition</see> of the hit-test operation.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.DockingGuideHitTest.Empty">
            <summary>
            The default empty instance.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DockingGuideHitTest.GetHashCode">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DockingGuideHitTest.Equals(System.Object)">
            <summary>
            
            </summary>
            <param name="obj"></param>
            <returns></returns>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.DockingGuidePopup">
            <summary>
            Represents a popup layered window that displays a docking guide
            </summary>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.DockingHintPopup">
            <summary>
            Represents a RadLayeredWindow, used by a DragDropService to 
            display a semi-transparent hint about where a dock pane is about to be dropped.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DockingHintPopup.BorderColor">
            <summary>
            Gets or sets the Color to be used to fill the docking hint rectangle.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DockingHintPopup.FillColor">
            <summary>
            Gets or sets the Color to be used to fill the docking hint rectangle.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DockingHintPopup.BorderWidth">
            <summary>
            Gets or sets the width of the border, drawn around the window. Defaults to 2 pixels.
            </summary>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.RedockAutoHideState">
            <summary>
            A predefined <see cref="T:Telerik.WinControls.UI.Docking.RedockState">RedockState</see>, used by a <see cref="T:Telerik.WinControls.UI.Docking.RedockService">RedockService</see> to restore an auto-hide state.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RedockAutoHideState.#ctor(Telerik.WinControls.UI.Docking.DockWindow)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.WinControls.UI.Docking.RedockAutoHideState">RedockAutoHideState</see> class.
            </summary>
            <param name="window"></param>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.RedockFloatingState">
            <summary>
            A predefined <see cref="T:Telerik.WinControls.UI.Docking.RedockState">RedockState</see>, used by a <see cref="T:Telerik.WinControls.UI.Docking.RedockService">RedockService</see> to restore a floating state.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RedockFloatingState.#ctor(Telerik.WinControls.UI.Docking.DockWindow)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.WinControls.UI.Docking.RedockFloatingState">RedockFloatingState</see> class.
            </summary>
            <param name="window"></param>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.RedockFloatingState.IsValid">
            <summary>
            
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.RedockFloatingState.FloatingWindow">
            <summary>
            Gets the floating window, target of the redock operation.
            </summary>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.RedockService">
            <summary>
            Represents a service that allows a DockWindow state to be saved and restored later on.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RedockService.#ctor">
            <summary>
            Default constructor.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RedockService.SuspendCleanUp">
            <summary>
            Temporary suspends any clean-up operations.
            Used by the owning RadDock instance to prevent undesired processing during multiple transactions stack.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RedockService.Redock(System.Collections.Generic.IEnumerable{Telerik.WinControls.UI.Docking.DockWindow},System.Boolean)">
            <summary>
            Performs a redock operation upon specified windows.
            </summary>
            <param name="windows"></param>
            <param name="defaultAction">True to perform default action if no state is recorded for a window, false otherwise.</param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RedockService.Redock(System.Collections.Generic.IEnumerable{Telerik.WinControls.UI.Docking.DockWindow},Telerik.WinControls.UI.Docking.RedockTransactionReason,System.Boolean)">
            <summary>
            Performs a redock operation upon specified windows.
            </summary>
            <param name="windows"></param>
            <param name="reason"></param>
            <param name="defaultAction">True to perform default action if no state is recorded for a window, false otherwise.</param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RedockService.Redock(Telerik.WinControls.UI.Docking.DockWindow,Telerik.WinControls.UI.Docking.RedockTransactionReason,System.Boolean)">
            <summary>
            Performs a redock operation upon specified window.
            </summary>
            <param name="window"></param>
            <param name="reason"></param>
            <param name="defaultAction">True to perform default action if no state is recorded for a window, false otherwise.</param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RedockService.RestoreState(System.Collections.Generic.IEnumerable{Telerik.WinControls.UI.Docking.DockWindow},Telerik.WinControls.UI.Docking.DockState,System.Boolean)">
            <summary>
            Attempts to restore the state of each window to the specified one.
            </summary>
            <param name="windows"></param>
            <param name="state">The target state which is to be restored.</param>
            <param name="defaultAction">True to perform default action if no state is recorded for a window, false otherwise.</param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RedockService.RestoreState(Telerik.WinControls.UI.Docking.DockWindow,Telerik.WinControls.UI.Docking.DockState,System.Boolean)">
            <summary>
            Attempts to restore the state the window to the desired dock state.
            </summary>
            <param name="window"></param>
            <param name="state">The target state which is to be restored.</param>
            <param name="defaultAction">True to perform default action if no state is recorded for a window, false otherwise.</param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RedockService.ResumeCleanUp(System.Boolean)">
            <summary>
            Resumes previously suspended clean-up.
            </summary>
            <param name="forceCleanUp">True to force a clean-up pass, false otherwise.</param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RedockService.SaveState(Telerik.WinControls.UI.Docking.DockWindow)">
            <summary>
            Saves the current state of the specified window.
            </summary>
            <param name="window"></param>
            <returns></returns>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RedockService.IsSaveApplicable(Telerik.WinControls.UI.Docking.DockWindow)">
            <summary>
            Determines whether a Save operation is applicable for the specified window.
            Currently supported states are <see cref="F:Telerik.WinControls.UI.Docking.DockState.Docked">Docked</see> and <see cref="F:Telerik.WinControls.UI.Docking.DockState.Floating">Floating</see>.
            </summary>
            <param name="window"></param>
            <returns></returns>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RedockService.ClearState(Telerik.WinControls.UI.Docking.DockWindow,Telerik.WinControls.UI.Docking.DockState)">
            <summary>
            Removes previously saved redock state for the specified window for the specified DockState.
            </summary>
            <param name="window"></param>
            <param name="dockState"></param>
            <returns></returns>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RedockService.ClearAllStates(Telerik.WinControls.UI.Docking.DockWindow)">
            <summary>
            Removes all the redock states saved for the specified window.
            </summary>
            <param name="window"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RedockService.GetNewDockState(Telerik.WinControls.UI.Docking.DockState)">
            <summary>
            Retrieves the new DockState for a redock operation, depending on the provided current DockState.
            </summary>
            <param name="currState"></param>
            <returns></returns>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RedockService.CleanUp">
            <summary>
            Removes previous redock states which are no longer valid.
            For example, if we save a state for a dock window in a Docked state, we need to remove any previous redock state for the Docked state.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RedockService.SaveStateCore(Telerik.WinControls.UI.Docking.DockWindow)">
            <summary>
            Preforms the core Save logic.
            </summary>
            <param name="window"></param>
            <returns></returns>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RedockService.GetState(Telerik.WinControls.UI.Docking.DockWindow,Telerik.WinControls.UI.Docking.DockState,System.Boolean)">
            <summary>
            Gets a <see cref="T:Telerik.WinControls.UI.Docking.RedockState">RedockState</see> instance for the specified window and DockState.
            </summary>
            <param name="window">The window for which to look-up a state.</param>
            <param name="state">The DockState to look-up for.</param>
            <param name="remove">True to remove the redock state from the list, false otherwise.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RedockService.RemovePreviousState(Telerik.WinControls.UI.Docking.RedockState)">
            <summary>
            Releases previous redock state.
            Current implementation simply notifies the referenced <see cref="T:Telerik.WinControls.UI.Docking.DockTabStrip">DockTabStrip</see> that it is not a redock target anymore.
            </summary>
            <param name="oldState"></param>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.RedockService.IsCleanUpSuspended">
            <summary>
            Determines whether clean-up is currently suspended.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.RedockService.StatesForCleanup">
            <summary>
            Gets an array with all the states that are currently marked for clean-up.
            </summary>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.RedockState">
            <summary>
            Represents an object that stores information about what was a docking window's previous state.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RedockState.#ctor(Telerik.WinControls.UI.Docking.DockWindow,Telerik.WinControls.UI.Docking.DockState)">
            <summary>
            Initializes a new <see cref="T:Telerik.WinControls.UI.Docking.RedockState">RedockState</see> for the specified window and desired DockState.
            </summary>
            <param name="window"></param>
            <param name="state"></param>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.RedockState.IsValid">
            <summary>
            Determines whether the state is valid (the information needed for the state to be restored later on is valid).
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.RedockState.TargetState">
            <summary>
            Gets the redock position this state targets.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.RedockState.DockWindow">
            <summary>
            Gets the redock state provider.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.RedockState.TargetStrip">
            <summary>
            Gets the target strip for the redock operation.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.RedockState.SizeInfo">
            <summary>
            Gets the recorded size info.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.RedockState.TabIndex">
            <summary>
            Gets the index of the state provider on the redock target TabStrip.
            </summary>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.DockWindowPlaceholder">
            <summary>
            A serializable placeholder, used primarily during RadDock's serialization process.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DockWindowPlaceholder.#ctor">
            <summary>
            Default constructor.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DockWindowPlaceholder.#ctor(Telerik.WinControls.UI.Docking.DockWindow)">
            <summary>
            Initializes the placeholder for the specified DockWindow instance.
            </summary>
            <param name="target"></param>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DockWindowPlaceholder.DockWindowName">
            <summary>
            Gets the name of the associated DockWindow instance.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DockWindowPlaceholder.DockWindowText">
            <summary>
            Gets the name of the associated DockWindow instance.
            </summary>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.DockWindowSerializableCollection">
            <summary>
            A collection, which supports serialization of the contained <see cref="T:Telerik.WinControls.UI.Docking.DockWindow">DockWindow</see> instances.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DockWindowSerializableCollection.#ctor(System.Collections.Generic.List{Telerik.WinControls.UI.Docking.DockWindow})">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.WinControls.UI.Docking.DockWindowSerializableCollection">DockWindowSerializableCollection</see> class.
            </summary>
            <param name="innerList">The list of DockWindow instances to initialize with.</param>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DockWindowSerializableCollection.Item(System.Int32)">
            <summary>
            Gets the AutoHideGroup instance at the specified index in the collection
            </summary>
            <param name="index"></param>
            <returns></returns>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.Serialization.DockXmlSerializerBase.#ctor(Telerik.WinControls.UI.Docking.RadDock,Telerik.WinControls.XmlSerialization.ComponentXmlSerializationInfo)">
            <summary>
            Creates a new instance of DockXmlSerializer
            </summary>
            <param name="dock">RadDock component that is used to write DockWindow states and info or to map existing DockWindows when deserializing</param>
            <param name="componentSerializationInfo">Serialization info. By default <see cref="P:Telerik.WinControls.UI.Docking.RadDock.XmlSerializationInfo"/> could be used.</param>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.DragDropService">
            <summary>
            Encapsulates the core drag-and-drop functionality for a RadDock instance.
            What RadDock does is to start and stop the service when drag operation is instanciated.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DragDropService.#ctor">
            <summary>
            Default constructor.
            </summary>
        </member>
        <member name="E:Telerik.WinControls.UI.Docking.DragDropService.Dragging">
            <summary>
            Notifies for a Drag event of the service. While working, this notification will be raised upon each mouse move event.
            </summary>
        </member>
        <member name="E:Telerik.WinControls.UI.Docking.DragDropService.PreviewHitTest">
            <summary>
            Allows listeners to preview and optionally change the current hit-test result generated by the service upon a drag operation.
            </summary>
        </member>
        <member name="E:Telerik.WinControls.UI.Docking.DragDropService.PreviewDropTarget">
            <summary>
            Allows listeners to preview and optionally change the currently hit-tested drop target.
            </summary>
        </member>
        <member name="E:Telerik.WinControls.UI.Docking.DragDropService.PreviewDockPosition">
            <summary>
            Allows listeners to preview and optionally modify the allowed dock position for the hit-tested drop target.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DragDropService.AvailableAtDesignTime">
            <summary>
            Drag-and-drop service is available at design-time (in preview mode only).
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DragDropService.DisposeManagedResources">
            <summary>
            
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DragDropService.OnDockManagerChanged">
            <summary>
            
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DragDropService.IsContextValid(System.Object)">
            <summary>
            Determines whether the provides context is valid and a drag-drop operation may be instanciated.
            </summary>
            <param name="context"></param>
            <returns></returns>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DragDropService.CanStart(System.Object)">
            <summary>
            Determines whether the service can start using the provided context.
            </summary>
            <param name="context"></param>
            <returns></returns>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DragDropService.PerformStart">
            <summary>
            Preforms the core start process.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DragDropService.PerformStop">
            <summary>
            Performs the core stop process.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DragDropService.OnStopping(Telerik.WinControls.UI.Docking.StateServiceStoppingEventArgs)">
            <summary>
            Notifies for a Stop request.
            </summary>
            <param name="e"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DragDropService.Commit">
            <summary>
            Commits the drag-and-drop operation.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DragDropService.GetDockingGuideBounds(Telerik.WinControls.UI.Docking.DockingGuidesPosition)">
            <summary>
            Gets the current screen bounds for the specified DockingGuide.
            </summary>
            <param name="position"></param>
            <returns></returns>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DragDropService.GetDockingGuideAllowedPosition(Telerik.WinControls.UI.Docking.DockingGuidesPosition)">
            <summary>
            Gets the allowed position for the currently displayed guide for the specified guide position.
            </summary>
            <param name="guidePosition">The position of the guide.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DragDropService.PerformDrag(System.Drawing.Point)">
            <summary>
            Performs a drag pass. Allows for service automation.
            The service must be started for this method to execute properly.
            </summary>
            <param name="screenMousePos">The position of the mouse in screen coordinates.</param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DragDropService.Start(System.Object,System.Drawing.Point)">
            <summary>
            Starts the service with the specified context, using the provided Point as initial.
            </summary>
            <param name="context"></param>
            <param name="start"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DragDropService.OnDockingGuidesTemplateChanged">
            <summary>
            Receives notification for a change in the current docking guides template.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DragDropService.Drag(System.Drawing.Point)">
            <summary>
            Preforms a drag pass, during which a hit-test operation is performed and docking guides and hints are updated respectively.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DragDropService.OnDragging(System.ComponentModel.CancelEventArgs)">
            <summary>
            Raises the Dragging notification.
            </summary>
            <param name="args"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DragDropService.GetDropTarget">
            <summary>
            Searches for a drop target under the cursor position. May return null if no appropriate control is found.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DragDropService.PositonDockingGuides">
            <summary>
            Updates all docking guides' bounds on the screen.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DragDropService.GetDragLocation">
            <summary>
            Gets the current location for a dragged object.
            The associated object depends on the current drag mode - it may be a FloatingWindow or a DockingHintPopup instance.
            </summary>
            <returns></returns>
        </member>
        <member name="E:Telerik.WinControls.UI.Docking.DragDropService.GuidesRecreated">
            <summary>
            Internal event, used for testing purposes.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DragDropService.UpdateDragPreview">
            <summary>
            Updates the drag preview popup when the drag mode is DragDropMode.Preview
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DragDropService.AllowedDockManagerEdges">
            <summary>
            Determines what of the owning <see cref="T:Telerik.WinControls.UI.Docking.RadDock">RadDock</see> instance's edges will be allowed for drop.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DragDropService.DragDropMode">
            <summary>
            Gets or sets the mode used by the service to perform drag-and-drop operations.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DragDropService.DragDropBehavior">
            <summary>
            Gets or sets the behavior of the service when it is running.
            Allows for specifying manual behavior, which enables service automation.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DragDropService.AllowedStates">
            <summary>
            Gets or sets the allowed states for committing drag-and-drop operation.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DragDropService.DropAnchor">
            <summary>
            Gets the target of the drop operation.
            This member is valid only while the service is started and may be null.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DragDropService.HitTestResult">
            <summary>
            Gets the hit-test result of the current drag-drop operation.
            Valid only while the service is started.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DragDropService.DragDropContext">
            <summary>
            Gets the DragDropContext value, which defines what is the current drag context.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DragDropService.ShowDockingGuides">
            <summary>
            Gets or Sets the value which indicates whether the docking guides will be shown.
            </summary>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.DockingGuidesPosition">
            <summary>
            Defines the position of the docking guides.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.DockingGuidesPosition.Left">
            <summary>
            Indicates left position of the docking guides.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.DockingGuidesPosition.Top">
            <summary>
            Indicates top position of the docking guides.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.DockingGuidesPosition.Right">
            <summary>
            Indicates right position of the docking guides.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.DockingGuidesPosition.Bottom">
            <summary>
            Indicates left position of the docking guides.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.DockingGuidesPosition.Center">
            <summary>
            Indicates central position of the docking guides.
            </summary>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.IDockWindow">
            <summary>
            Defines the common properties for a RadDock-related object such as DockWindow, FloatingWindow, AutoHidePopup, etc.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.IDockWindow.Name">
            <summary>
            Gets the unique name of this instance.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.IDockWindow.Text">
            <summary>
            Gets or sets the text associated with this instance.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.IDockWindow.DockManager">
            <summary>
            Gets the RadDock instance this object is associated with.
            </summary>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.DockWindow">
            <summary>
            Base class for all tool and document windows available per <see cref="T:Telerik.WinControls.UI.Docking.RadDock">RadDock</see> instance.
            Implements the IDockWindow interface.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DockWindow.#ctor">
            <summary>
            Initializes a new <see cref="T:Telerik.WinControls.UI.Docking.DockWindow">DockWindow</see> instance.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DockWindow.DefaultDockState">
            <summary>
            Retrieves the default DockState for this instance. Will be <see cref="F:Telerik.WinControls.UI.Docking.DockState.Docked">Docked</see> for <see cref="T:Telerik.WinControls.UI.Docking.ToolWindow">ToolWindow</see> instances
            and <see cref="F:Telerik.WinControls.UI.Docking.DockState.TabbedDocument">TabbedDocument</see> for <see cref="T:Telerik.WinControls.UI.Docking.DocumentWindow">DocumentWindow</see> instances.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DockWindow.DefaultCloseAction">
            <summary>
            Retrieves the default <see cref="T:Telerik.WinControls.UI.Docking.DockWindowCloseAction">CloseAction</see> for this instance. Will be <see cref="F:Telerik.WinControls.UI.Docking.DockWindowCloseAction.Hide">Hide</see> for <see cref="T:Telerik.WinControls.UI.Docking.ToolWindow">ToolWindow</see> instances
            and <see cref="F:Telerik.WinControls.UI.Docking.DockWindowCloseAction.CloseAndDispose">CloseAndDispose</see> for <see cref="T:Telerik.WinControls.UI.Docking.DocumentWindow">DocumentWindow</see> instances.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DockWindow.WndProc(System.Windows.Forms.Message@)">
            <summary>
            Provides special handling for the WM_SETFOCUS notification.
            </summary>
            <param name="m"></param>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DockWindow.ToolCaptionButtons">
            <summary>
            Gets or sets the visibility of the associated command buttons when the window resides in a ToolTabStrip instance.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DockWindow.DocumentButtons">
            <summary>
            Gets or sets the visibility of the associated command buttons when the window resides in a DocumentTabStrip instance.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DockWindow.DockTabStrip">
            <summary>
            Gets the DockTabStrip which hosts this window.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DockWindow.DefaultFloatingSize">
            <summary>
            Gets or sets the size to be used when the window is floated for the first time and does not have previous floating state saved.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DockWindow.AutoHideSize">
            <summary>
            
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DockWindow.Close">
            <summary>
            Asks the current <see cref="P:Telerik.WinControls.UI.Docking.DockWindow.DockManager">DockManager</see> instance (if any) to close the window.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DockWindow.EnsureVisible">
            <summary>
            Ensures that the window is currently visible on its hosting <see cref="P:Telerik.WinControls.UI.Docking.DockWindow.DockTabStrip">DockTabStrip</see>.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DockWindow.EnsureVisible(System.Boolean)">
            <summary>
            Ensures that the window is currently visible on its hosting <see cref="P:Telerik.WinControls.UI.Docking.DockWindow.DockTabStrip">DockTabStrip</see>.
            </summary>
            <param name="suspendShowAutoHide">Indicates whether showing autohide windows should be suspended.</param>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DockWindow.AllowedDockState">
            <summary>
            Get or set allowed dock states for DockWindow instance
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DockWindow.CloseAction">
            <summary>
            Gets or sets the action to be used when a Close request occurs for this window.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DockWindow.DockTo(Telerik.WinControls.UI.Docking.DockWindow,Telerik.WinControls.UI.Docking.DockPosition)">
            <summary>
            Asks the current <see cref="P:Telerik.WinControls.UI.Docking.DockWindow.DockManager">DockManager</see> instance (if any) to dock the window to the specified target, using the desired position.
            </summary>
            <param name="target"></param>
            <param name="position"></param>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DockWindow.DesiredDockState">
            <summary>
            Gets or sets the DockState which is desired for the window
            and will be applied when the RadDock instance this window is attached to is completely initialized.
            This property is used internally by the framework and is not intended to be used directly by code.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DockWindow.FloatingParent">
            <summary>
            Get the parent floating window when the window is in floating mode
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.DockWindow.forceDesiredDockStateSerialization">
            <summary>
            This field is used internally.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DockWindow.DockState">
            <summary>
            Gets the current dock state of the window.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DockWindow.Hide">
            <summary>
            Hide the ToolWindow from RadDock manager
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DockWindow.Show">
            <summary>
            Display the ToolWindow if was previously hidden.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DockWindow.IsInFloatingMode">
            <summary>
            Get the floating status of window
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DockWindow.Site">
            <summary>
            
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DockWindow.Name">
            <summary>
            Gets or sets value representing unique Name of the DockWindow
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DockWindow.OnNameChanged(System.String)">
            <summary>
            Notifies the owning <see cref="T:Telerik.WinControls.UI.Docking.RadDock">RadDock</see> instance for a change in the Name value.
            </summary>
            <param name="oldName"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DockWindow.UpdateOnTextChanged">
            <summary>
            Updates all associated UI - such as TabItems, Captions, etc. upon TextChanged event.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DockWindow.ToString">
            <summary>
            
            </summary>
            <returns></returns>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DockWindow.Dispose(System.Boolean)">
            <summary>
            
            </summary>
            <param name="disposing"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DockWindow.OnClosing(Telerik.WinControls.UI.Docking.DockWindowCancelEventArgs)">
            <summary>
            Called by the owning RadDock instance when this window is about to close.
            Allows specific DockWindow implementations to optionally cancel the operation and/or perform additional actions.
            </summary>
            <param name="e"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DockWindow.OnClosed(Telerik.WinControls.UI.Docking.DockWindowEventArgs)">
            <summary>
            Called by the owning RadDock instance when the window has been successfully closed.
            Depending on the current CloseAction, the window will be either hidden, removed or both plus disposed when entering this method.
            </summary>
            <param name="e"></param>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DockWindow.DockManager">
            <summary>
            Gets the current <see cref="T:Telerik.WinControls.UI.Docking.RadDock">RadDock</see> instance this window is associated with.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DockWindow.Padding">
            <summary>
            
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DockWindow.DockType">
            <summary>
            Gets the <see cref="P:Telerik.WinControls.UI.Docking.DockWindow.DockType">DockType</see> of this instance.
            </summary>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.DockPopupForm">
            <summary>
            Base class for all Popup Forms that are used with a RadDock instance.
            Such Forms are <see cref="T:Telerik.WinControls.UI.Docking.FloatingWindow"/> and <see cref="T:Telerik.WinControls.UI.Docking.AutoHidePopup"/> classes.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DockPopupForm.#ctor(Telerik.WinControls.UI.Docking.RadDock)">
            <summary>
            Constructs new DockPopupForm instance, associated with the specified RadDock instance.
            </summary>
            <param name="dockManager">The RadDock instance this Popup is attached to.</param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DockPopupForm.Dispose(System.Boolean)">
            <summary>
            Releases the resources used by this instance.
            </summary>
            <param name="disposing"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DockPopupForm.OnClosed(System.EventArgs)">
            <summary>
            
            </summary>
            <param name="e"></param>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DockPopupForm.DockManager">
            <summary>
            Gets the RadDock instance this Popup Form is associated with.
            </summary>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.DockPosition">
            <summary>
            Defines the docking position of a DockWindow.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.DockPosition.Left">
            <summary>
            Indicates that the DockWindow will be docked to the left side of the target.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.DockPosition.Top">
            <summary>
            Indicates that the DockWindow will be docked to the top side of the target.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.DockPosition.Right">
            <summary>
            Indicates that the DockWindow will be docked to the right side of the target.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.DockPosition.Bottom">
            <summary>
            Indicates that the DockWindow will be docked to the bottom side of the target.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.DockPosition.Fill">
            <summary>
            Indicates that the DockWindow will fill the target.
            </summary>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.DockState">
            <summary>
            Defines the possible valid states for IDockWindow instances.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.DockState.Docked">
            <summary>
            Indicates that the IDockWindow instance is docked.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.DockState.TabbedDocument">
            <summary>
            Indicates that the IDockWindow instance is docked inside a TabbedDocument interface.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.DockState.Hidden">
            <summary>
            Indicates that the IDockWindow instance is in a hidden/removed state.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.DockState.AutoHide">
            <summary>
            Indicates that the IDockWindow instance is managed by the auto-hide sub-system of the docking system.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.DockState.Floating">
            <summary>
            Indicates that the IDockWindow instance is floating.
            </summary>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.AllowedDockState">
            <summary>
            Defines the states, allowed per DockWindow instance.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.AllowedDockState.Docked">
            <summary>
            Indicates that the IDockWindow instance is docked.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.AllowedDockState.TabbedDocument">
            <summary>
            Indicates that the IDockWindow instance is docked inside a TabbedDocument interface.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.AllowedDockState.Hidden">
            <summary>
            Indicates that the IDockWindow instance is in a hidden/removed state.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.AllowedDockState.AutoHide">
            <summary>
            Indicates that the IDockWindow instance is managed by the auto-hide sub-system of the docking system.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.AllowedDockState.Floating">
            <summary>
            Indicates that the IDockWindow instance is floating.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.AllowedDockState.All">
            <summary>
            All dock states are defined.
            </summary>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.DockTabStrip">
            <summary>
            Base class for a <see cref="T:Telerik.WinControls.UI.TabStripPanel">TabStripPanel</see> instance that resides on a <see cref="T:Telerik.WinControls.UI.Docking.RadDock">RadDock</see> scene.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DockTabStrip.#ctor(Telerik.WinControls.UI.Docking.RadDock)">
            <summary>
            Initializes a new <see cref="T:Telerik.WinControls.UI.Docking.DockTabStrip">DockTabStrip</see> instance and associates it with the specified RadDock instance.
            </summary>
            <param name="dockManager"></param>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DockTabStrip.IsRedockTarget">
            <summary>
            Determines whether this tabstrip is referenced for the RedockService as a target of Redock operation.
            This flag is used to determine whether a RadSplitContainer may be freely cleaned-up or should be reserved.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DockTabStrip.BackColor">
            <summary>
            Gets or sets the BackColor of the strip.
            Transparent BackColor is a special case, further controlled by the <see cref="P:Telerik.WinControls.UI.Docking.DockTabStrip.AllowTransparentBackColor">AllowTransparentBackColor</see> property.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DockTabStrip.AllowTransparentBackColor">
            <summary>
            Determines whether the control accepts Color.Transparent as its BackColor.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DockTabStrip.DockType">
            <summary>
            Gets the <see cref="P:Telerik.WinControls.UI.Docking.DockTabStrip.DockType">DockType</see> member of this instance.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DockTabStrip.Collapsed">
            <summary>
            
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DockTabStrip.TabPanels">
            <summary>
            
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DockTabStrip.DockManager">
            <summary>
            
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DockTabStrip.ActiveWindow">
            <summary>
            Gets or sets the currently active <see cref="T:Telerik.WinControls.UI.Docking.DockWindow">DockWindow</see> instance.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DockTabStrip.OnMouseDown(System.Windows.Forms.MouseEventArgs)">
            <summary>
            
            </summary>
            <param name="e"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DockTabStrip.OnMouseMove(System.Windows.Forms.MouseEventArgs)">
            <summary>
            
            </summary>
            <param name="e"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DockTabStrip.OnMouseCaptureChanged(System.EventArgs)">
            <summary>
            
            </summary>
            <param name="e"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DockTabStrip.OnDragInitialized(System.Drawing.Point)">
            <summary>
            Overrides the method to provide support for instanciating a DragDropService operation.
            </summary>
            <param name="mouse"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DockTabStrip.OnDockManagerChanged">
            <summary>
            Allows inheritors to provide additional functionality upon owning RadDock instance change.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DockTabStrip.OnTabCloseButtonClicked(Telerik.WinControls.UI.TabStripItem)">
            <summary>
            Closes the corresponding <see cref="T:Telerik.WinControls.UI.Docking.DockWindow">DockWindow</see> instance.
            </summary>
            <param name="item"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DockTabStrip.CheckCollapseOrDispose">
            <summary>
            Provides routine which allows the strip to decide whether it should be collapsed or disposed.
            Internally used by the docking framework to defragment the dock tree.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DockTabStrip.UpdateButtons">
            <summary>
            Updates the additional buttons, associated with the strip.
            E.g. a ToolTabStrip will have caption buttons, while a DocumentTabStrip will have strip buttons.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DockTabStrip.UpdateAfterTransaction">
            <summary>
            Allows an affected strip to perform additional update after a transaction completion.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DockTabStrip.UpdateActiveWindow(Telerik.WinControls.UI.Docking.DockWindow,System.Boolean)">
            <summary>
            Allows a DockTabStrip to perform some additional operations upon activation of an owned DockWindow.
            For example a ToolTabStrip will update its Caption with Active or Inactive state correspondingly.
            </summary>
            <param name="window"></param>
            <param name="active">True if the window is currently active, false otherwise.</param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DockTabStrip.UpdateTabSelection(System.Boolean)">
            <summary>
            this method is used internally.
            </summary>
            <param name="updateFocus"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DockTabStrip.CopyTo(Telerik.WinControls.UI.Docking.DockTabStrip)">
            <summary>
            Copies the settings of the current strip to the target one. Currently the SizeInfo member is copied.
            </summary>
            <param name="clone"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DockTabStrip.GetCollapsed">
            <summary>
            Determines whether the strip should be collapsed. E.g. it may not have child panels but should not be disposed as it may be a redock target.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DockTabStrip.IsDragAllowed(System.Drawing.Point)">
            <summary>
            Determines whether a drag operation is currently allowed.
            </summary>
            <param name="location">The location to examine, in client coordinates.</param>
            <returns></returns>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.DockType">
            <summary>
            Defines the possible valid types for IDockable instances.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.DockType.ToolWindow">
            <summary>
            Indicates that the IDockable instance is ToolWindow.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.DockType.Document">
            <summary>
            Indicates that the IDockable instance is docked inside a TabbedDocument interface.
            </summary>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.DocumentWindow">
            <summary>
            Implements a special <see cref="T:Telerik.WinControls.UI.Docking.DockWindow">DockWindow</see> that may reside within a document container only.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DocumentWindow.#ctor">
            <summary>
            Default contructor.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DocumentWindow.#ctor(System.String)">
            <summary>
            Initializes new DocumentWindow instance, using the provided Text.
            </summary>
            <param name="text"></param>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DocumentWindow.IsPinned">
            <summary>
            Gets or sets a value indicating whether the window is pinned.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DocumentWindow.DefaultCloseAction">
            <summary>
            
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DocumentWindow.DefaultDockState">
            <summary>
            
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DocumentWindow.DockType">
            <summary>
            Returns <see cref="F:Telerik.WinControls.UI.Docking.DockType.Document">Document</see>.
            </summary>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.DocumentTabStrip">
            <summary>
            A predefined <see cref="T:Telerik.WinControls.UI.Docking.DockTabStrip">DockTabStrip</see> instance that resides within a <see cref="T:Telerik.WinControls.UI.Docking.DocumentContainer">DocumentContainer</see> and hosts documents.
            For a document is considered a DocumentWindow instance or a ToolWindow, which has a TabbedDocument DockState.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DocumentTabStrip.#ctor">
            <summary>
            Default constructor.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DocumentTabStrip.#ctor(Telerik.WinControls.UI.Docking.RadDock)">
            <summary>
            Initializes a new <see cref="T:Telerik.WinControls.UI.Docking.DocumentTabStrip">DocumentTabStrip</see> instance and associates it with the provided RadDock instance.
            </summary>
            <param name="dockManager"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DocumentTabStrip.CreateChildItems(Telerik.WinControls.RadElement)">
            <summary>
            
            </summary>
            <param name="parent"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DocumentTabStrip.Dispose(System.Boolean)">
            <summary>
            
            </summary>
            <param name="disposing"></param>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DocumentTabStrip.DocumentButtonsLayout">
            <summary>
            Gets the document buttons layout element.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DocumentTabStrip.OverflowMenuButton">
            <summary>
            
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DocumentTabStrip.DockType">
            <summary>
            Returns <see cref="F:Telerik.WinControls.UI.Docking.DockType.Document">Document</see> dock type.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DocumentTabStrip.DefaultTabStripAlignment">
            <summary>
            The default tabstrip alignment is Top.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.DocumentTabStrip.IsRedockTarget">
            <summary>
            This predefined instance is never a redock target.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DocumentTabStrip.OnLayout(System.Windows.Forms.LayoutEventArgs)">
            <summary>
            
            </summary>
            <param name="e"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DocumentTabStrip.OnMouseDown(System.Windows.Forms.MouseEventArgs)">
            <summary>
            
            </summary>
            <param name="e"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DocumentTabStrip.OnMouseClick(System.Windows.Forms.MouseEventArgs)">
            <summary>
            
            </summary>
            <param name="e"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DocumentTabStrip.OnControlAdded(System.Windows.Forms.ControlEventArgs)">
            <summary>
            
            </summary>
            <param name="e"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DocumentTabStrip.OnControlRemoved(System.Windows.Forms.ControlEventArgs)">
            <summary>
            
            </summary>
            <param name="e"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DocumentTabStrip.IsDragAllowed(System.Drawing.Point)">
            <summary>
            
            </summary>
            <param name="location"></param>
            <returns></returns>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DocumentTabStrip.UpdateButtons">
            <summary>
            
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DocumentTabStrip.UpdateAfterTransaction">
            <summary>
            
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DocumentTabStrip.UpdateActiveWindow(Telerik.WinControls.UI.Docking.DockWindow,System.Boolean)">
            <summary>
            Updates the currently active window by setting bold if the specified window instance is the currently active document within the owning RadDock instance.
            </summary>
            <param name="window"></param>
            <param name="active"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.DocumentTabStrip.ControlDefinesThemeForElement(Telerik.WinControls.RadElement)">
            <summary>
            
            </summary>
            <param name="element"></param>
            <returns></returns>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.FloatingWindow">
            <summary>
            Represents a popup form that is used to host floating <see cref="T:Telerik.WinControls.UI.Docking.DockWindow">DockWindow</see> instances.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.FloatingWindow.DefaultFloatingSize">
            <summary>
            Defines the default size for floating windows.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.FloatingWindow.#ctor">
            <summary>
            This method supports internal RadDock infrastructure and should not be called directly from your code
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.FloatingWindow.#ctor(Telerik.WinControls.UI.Docking.RadDock)">
            <summary>
            Creates a new FloatingWindow instance passing an owner RadDock instance as a parameter
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.FloatingWindow.DockContainer">
            <summary>
            Gets the SplitContiner instance that is the root of the hierarchy of DockWindows inside this FloatingWindow
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.FloatingWindow.Standalone">
            <summary>
            Gets or sets a value whether the floating form should be shown in the task bar and behave like normal form,
            or it should be kept as a child form of the form which owns the RadDock.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.FloatingWindow.SnapToScreen">
            <summary>
            Indicates whether this window should snap to the screen's edges.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.FloatingWindow.SnapToOthers">
            <summary>
            Indicates whether this window should snap to other windows.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.FloatingWindow.SnapOnResize">
            <summary>
            Indicates whether this window should snap while resizing.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.FloatingWindow.SnapOnMove">
            <summary>
            Indicates whether this window should snap while dragging.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.FloatingWindow.SnapThreshold">
            <summary>
            Indicates the threshold between edges before the window snaps.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.FloatingWindow.OnThemeChanged">
            <summary>
            Delegate the event to all nested ToolTabStrip instances.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.FloatingWindow.OnActivated(System.EventArgs)">
            <summary>
            Provides support for z-ordering of all popup-forms of the owning Form.
            </summary>
            <param name="e"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.FloatingWindow.GetSnappedBounds(System.Drawing.Rectangle,Telerik.WinControls.UI.ResizeDirection,System.Boolean)">
            <summary>
            Gets the target bounds when the window snaps to another one.
            </summary>
            <param name="desired">The original window bounds.</param>
            <param name="direction">The direction in which the window is being resized/moved.</param>
            <param name="resize">[true] if this is a resize operation, [false] otherwise.</param>
            <returns>The bounds of the window after snapping.</returns>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.HostWindow">
            <summary>
            Represents a special type of DockWindow, which is used internally by RadDock to wrap custom controls as part of the docking framework.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.HostWindow.Content">
            <summary>
            Gets the Control instance that is hosted on this window.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.HostWindow.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.WinControls.UI.Docking.HostWindow"/> class.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.HostWindow.#ctor(System.Windows.Forms.Control)">
            <summary>
            Initializes a new <see cref="T:Telerik.WinControls.UI.Docking.HostWindow">HostWindow</see> instance with the specified Control as a content.
            </summary>
            <param name="control"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.HostWindow.LoadContent(System.Windows.Forms.Control)">
            <summary>
            Loads the content.
            </summary>
            <param name="content">The content.</param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.HostWindow.Show">
            <summary>
            Display the HostWindow if was previously hidden.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.HostWindow.#ctor(System.Windows.Forms.Control,Telerik.WinControls.UI.Docking.DockType)">
            <summary>
            Initializes a new <see cref="T:Telerik.WinControls.UI.Docking.HostWindow">HostWindow</see> instance with the specified Control as a content and using the desired DockType.
            </summary>
            <param name="control"></param>
            <param name="dockType"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.HostWindow.OnControlRemoved(System.Windows.Forms.ControlEventArgs)">
            <summary>
            
            </summary>
            <param name="e"></param>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.HostWindow.MdiChild">
            <summary>
            Gets the associated Content as a Form instance. Valid when used in standard MDI mode.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.HostWindow.DockType">
            <summary>
            
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.HostWindow.SerializedDockType">
            <summary>
            Used internally by the XML serialization.
            </summary>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.RadDock">
            <summary>
            Represents the major control in the Telerik.WinForms.UI.Docking assembly. Root component for the entire docking framework.
            Provides highly extensible and completely transparent transaction-based API for managing a set of tool and document windows registered within the docking framework.
            Mimics the user experience found in Microsoft Visual Studio - dockable panes, tabbed documents, document navigation plus much more.
            <example>A typical scenario of utilizing a docking framework is as follows:
            <list type="1">
            <item>Create a new RadDock instance.</item>
            <item>Create the desired <see cref="T:Telerik.WinControls.UI.Docking.ToolWindow">ToolWindow</see> and/or <see cref="T:Telerik.WinControls.UI.Docking.DocumentWindow">DocumentWindow</see> instances.</item>
            <item>Register the newly created dock windows with the RadDock using the <see cref="M:Telerik.WinControls.UI.Docking.RadDock.DockWindow(Telerik.WinControls.UI.Docking.DockWindow,Telerik.WinControls.UI.Docking.DockPosition)">DockWindow</see> or the <see cref="M:Telerik.WinControls.UI.Docking.RadDock.AddDocument(Telerik.WinControls.UI.Docking.DockWindow)">AddDocument</see> methods respectively.</item>
            <item>Add the new RadDock instance to a Form.</item>
            <item>Display the Form.</item>
            </list>
            </example> 
            <remarks>
            Although a rare scenario, sometimes the need of nested RadDock instances may emerge. This is fully supported by the RadDock component and unlimited levels of nesting are allowed.
            </remarks>
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.#ctor">
            <summary>
            Initializes a new <see cref="T:Telerik.WinControls.UI.Docking.RadDock">RadDock</see> instance.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.Dispose(System.Boolean)">
            <summary>
            
            </summary>
            <param name="disposing"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.RestoreWindowsStatesAfterLoad(System.Boolean)">
            <summary>
            This method is used internally.
            </summary>
            <param name="beginInitWindows"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.RestoreWindowsStatesAfterLoad">
            <summary>
            This method is used internally.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.CreateChildItems(Telerik.WinControls.RadElement)">
            <summary>
            
            </summary>
            <param name="parent"></param>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.RadDock.EnableFloatingWindowSnapping">
            <summary>
            Determines whether all floating windows' will snap to their edges.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.RadDock.EnableFloatingWindowTheming">
            <summary>
            Determines whether all floating windows' frames within the framework will be themed using Telerik's TPF or will be let with their system appearance.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.RadDock.Collapsed">
            <summary>
            
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.RadDock.SplitContainer">
            <summary>
            
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.RadDock.RootContainer">
            <summary>
            
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.RadDock.SizeInfo">
            <summary>
            
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.RadDock.AutoScroll">
            <summary>
            
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.RadDock.AllowDrop">
            <summary>
            
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.RadDock.AutoScrollMargin">
            <summary>
            
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.RadDock.AutoScrollMinSize">
            <summary>
            
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.RadDock.SplitPanels">
            <summary>
            
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.OnLoad(System.Drawing.Size)">
            <summary>
            Called when control's creation is complete.
            </summary>
            <param name="desiredSize"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.EndInit">
            <summary>
            
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.BeginInit">
            <summary>
            
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.EnsureInitialized">
            <summary>
            This method is used internally.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.OnThemeChanged">
            <summary>
            Delegates the ThemeChanged event to all owned controls and elements.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.RadDock.AutoDetectMdiChildren">
            <summary>
            Gets or sets a value indicating whether [auto detect MDI child].
            </summary>
            <value><c>true</c> if [auto detect MDI child]; otherwise, <c>false</c>.</value>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.RadDock.SingleScreen">
            <summary>
            Gets or sets a value indicating whether the RadDock control behavior and layout apply only to primary screen.
            </summary>\
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.RadDock.MdiChildrenDockType">
            <summary>
            Gets or sets a value indicating dock type of MDI child host windows added to RadDock.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.RadDock.MdiChildren">
            <summary>
            Gets an array with all the standard Forms added as TabbedDocument to this RadDock instance.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.RadDock.MdiController">
            <summary>
            Gets the MdiController instance that listens for added MDI children and wraps them into a HostWindow.
            Exposed for the sake of tests.
            </summary>
        </member>
        <member name="E:Telerik.WinControls.UI.Docking.RadDock.AutoHideWindowDisplaying">
            <summary>
            Notifies that an auto-hidden window is about to be displayed. Cancelable.
            </summary>
        </member>
        <member name="E:Telerik.WinControls.UI.Docking.RadDock.AutoHideWindowDisplayed">
            <summary>
            Notifies that an auto-hidden window is about to be displayed. Cancelable.
            </summary>
        </member>
        <member name="E:Telerik.WinControls.UI.Docking.RadDock.AutoHideWindowHiding">
            <summary>
            Notifies that a window with AutoHide DockState is hiding.
            </summary>
        </member>
        <member name="E:Telerik.WinControls.UI.Docking.RadDock.AutoHideWindowHidden">
            <summary>
            Notifies that a window which DockState is AutoHide has been hidden.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.RadDock.AutoHideAnimationDuration">
            <summary>
            Gets or sets the duration of the auto hide window animation. The default value is 200 milliseconds.
            </summary>
            <value>The duration of the auto hide window animation. The default value is 200 milliseconds.</value>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.OnParentChanged(System.EventArgs)">
            <summary>
            
            </summary>
            <param name="e"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.CloseAutoHidePopup">
            <summary>
            Closes the popup of the currently opened auto-hide window. 
            </summary>\
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.GetAutoHideTab(Telerik.WinControls.UI.Docking.DockWindow)">
            <summary>
            Gets the auto hide tab item.
            </summary>
            <param name="dockWindow">The dock window.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.GetAutoHideTabStrip(Telerik.WinControls.UI.Docking.AutoHidePosition)">
            <summary>
            Gets the auto hide tab strip.
            </summary>
            <param name="position">The position.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.OnAutoHideWindowDisplaying(Telerik.WinControls.UI.Docking.AutoHideWindowDisplayingEventArgs)">
            <summary>
            Raises the <see cref="E:Telerik.WinControls.UI.Docking.RadDock.AutoHideWindowDisplaying">AutoHideWindowDisplaying</see> event.
            </summary>
            <param name="e"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.OnAutoHideWindowDisplayed(Telerik.WinControls.UI.Docking.DockWindowEventArgs)">
            <summary>
            Raises the <see cref="E:Telerik.WinControls.UI.Docking.RadDock.AutoHideWindowDisplayed">AutoHideWindowDisplayed</see> event.
            </summary>
            <param name="e"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.OnAutoHideWindowHiding(Telerik.WinControls.UI.Docking.DockWindowCancelEventArgs)">
            <summary>
            Raises the <see cref="E:Telerik.WinControls.UI.Docking.RadDock.AutoHideWindowHiding">AutoHideWindowHiding</see> event.
            </summary>
            <param name="e"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.OnAutoHideWindowHidden(Telerik.WinControls.UI.Docking.DockWindowEventArgs)">
            <summary>
            Raises the <see cref="E:Telerik.WinControls.UI.Docking.RadDock.AutoHideWindowHidden">AutoHideWindowHidden</see> event.
            </summary>
            <param name="e"></param>
        </member>
        <member name="E:Telerik.WinControls.UI.Docking.RadDock.QuickNavigatorSnapshotNeeded">
            <summary>
            Raised when the QuickNavigator is displayed and a preview snapshot for the currently selected window is needed.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.DisplayQuickNavigator">
            <summary>
            Visualizes the QuickNavigator control allowing for active tool windows and documents browsing.
            </summary>
            <returns>The <see cref="T:Telerik.WinControls.UI.Docking.QuickNavigator">QuickNavigator</see> instance that is currently displayed. May return null if operation was unsuccessful.</returns>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.RadDock.QuickNavigatorSettings">
            <summary>
            Gets the object which controls the appearance of the QuickNavigator control.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.OnQuickNavigatorSnapshotNeeded(Telerik.WinControls.UI.Docking.DockWindowSnapshotEventArgs)">
            <summary>
            Raises the <see cref="E:Telerik.WinControls.UI.Docking.RadDock.QuickNavigatorSnapshotNeeded"/> event.
            </summary>
            <param name="e"></param>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.RadDock.AutoSize">
            <summary>
            Gets or sets whether the edit control is auto-sized
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.RadDock.ToolWindowInsertOrder">
            <summary>
            Gets or sets the insert order to be used when adding dock windows to a ToolTabStrip.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.RadDock.AutoHideAnimation">
            <summary>
            Determines what animation will be used when displaying/hiding auto-hidden windows.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.RadDock.ContentRectangle">
            <summary>
            Overrides the content rectangle to add any visible auto-hide tabstrips to the calculations.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.RadDock.LayoutStrategyType">
            <summary>
            Gets or sets the Type to be used when a new RadSplitContainer instance is internally created and a layout strategy is initialized for it.
            Allows plugging of completely custom layout behavior.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.RadDock.Enabled">
            <summary>
            
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.RadDock.Item(System.String)">
            <summary>
            Gets DockWindow instance with the corresponding Name.
            </summary>
            <param name="dockWindowName"></param>
            <returns>DockWindow instance with matching Name. Null (Nothing in VB.NET) otherwise.</returns>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.GetWindow``1(System.String)">
            <summary>
            Gets DockWindow instance with the corresponding Name by the specified type.
            </summary>
            <typeparam name="T"></typeparam>
            <param name="dockWindowName"></param>
            <returns>DockWindow instance with matching Name. Null (Nothing in VB.NET) otherwise.</returns>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.GetWindows``1">
            <summary>
            Gets the windows by the specified type.
            </summary>
            <typeparam name="T"></typeparam>
            <returns></returns>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.GetWindows(System.Type[])">
            <summary>
            Gets the windows by the specified types.
            </summary>
            <param name="types">The types of the windows to get.</param>
            <returns></returns>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.RadDock.DockWindows">
            <summary>
            Gets a collection with all the currently attached <see cref="T:Telerik.WinControls.UI.Docking.DockWindow">DockWindow</see> instances.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.RadDock.Text">
            <summary>
            This property is not relevant for this class.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.RadDock.ShowDocumentCloseButton">
            <summary>
            Determines whether DocumentTabStrip instances will display a Close Button next to each item.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.RadDock.ShowDocumentPinButton">
            <summary>
            Determines whether DocumentTabStrip instances will display a Pin Button next to each item.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.RadDock.ShowToolCloseButton">
            <summary>
            Determines whether ToolTabStrip instances will display Close Button next to each item.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.RadDock.DocumentTabsTextOrientation">
            <summary>
            Gets or sets the text orientation of the TabStripElement in all ToolTabStrip instances.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.RadDock.ToolTabsTextOrientation">
            <summary>
            Gets or sets the text orientation of the TabStripElement in all ToolTabStrip instances.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.RadDock.ToolTabsAlignment">
            <summary>
            Gets or sets the alignment of the TabStripElement in all ToolTabStrip instances.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.RadDock.DocumentTabsAlignment">
            <summary>
            Gets or sets the alignment of the TabStripElement in all DocumentTabStrip instances.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.RadDock.DocumentTabsVisible">
            <summary>
            Determines whether the TabStripElement in DocumentTabStrip instances is visible.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.RadDock.ToolTabsVisible">
            <summary>
            Determines whether the TabStripElement in ToolTabStrip instances is visible.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.BeginUpdate">
            <summary>
            Begins an update operation. Internally used by the transaction mechanism.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.EndUpdate">
            <summary>
            Ends a BeginUpdate block.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.EndUpdate(System.Boolean)">
            <summary>
            Ends a BeginUpdate block and optionally performs update.
            </summary>
            <param name="update"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.PerformUpdate">
            <summary>
            Performs the core update logic after an EndUpdate call.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.CleanUp">
            <summary>
            Performs a clean-up pass which removes all unnecessary internally created split panels and/or collapses or disposes them.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.BeginTransactionBlock">
            <summary>
            Opens a transaction (batch) operation. 
            This is used by the framework to indicate some lengthy operations, during which no updates should be performed.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.BeginTransactionBlock(System.Boolean)">
            <summary>
            Opens a transaction (batch) operation. 
            This is used by the framework to indicate some lengthy operations, during which no updates should be performed.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.EnqueueTransaction(Telerik.WinControls.UI.Docking.RadDockTransaction)">
            <summary>
            Adds the 
            </summary>
            <param name="transaction"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.FlushTransactions">
            <summary>
            Commits all pending transactions without exitting the transaction block.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.RadDock.IsInTransactionBlock">
            <summary>
            Determines whether the RadDock is currently in a transaction (internal operation).
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.EndTransactionBlock">
            <summary>
            Ends previously opened transaction.
            Optionally preforms update.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.RegisterTransaction(Telerik.WinControls.UI.Docking.RadDockTransaction,System.Boolean)">
            <summary>
            An internal helper method that will either enqueue the transaction if a block is currently running
            or will open block, commit the transaction and close that block.
            </summary>
            <param name="transaction"></param>
            <param name="saveActiveWindow"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.CommitTransactions">
            <summary>
            Commits all transactions available on the transaction stack.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.CommitTransaction(Telerik.WinControls.UI.Docking.RadDockTransaction)">
            <summary>
            Commits the specified transaction.
            </summary>
            <param name="transaction"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.OnTransactionCommitted(Telerik.WinControls.UI.Docking.RadDockTransaction)">
            <summary>
            Final step of processing a transaction.
            Performs clean-up logic, raises DockStateChanged, DockWindowClosed, DockWindowAdded and DockWindowRemoved events as needed.
            </summary>
            <param name="transaction"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.PreCommitTransaction(Telerik.WinControls.UI.Docking.RadDockTransaction)">
            <summary>
            Allows inheritors to provide additional pre-commit processing of a transaction.
            </summary>
            <param name="transaction"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.PostCommitTransaction(Telerik.WinControls.UI.Docking.RadDockTransaction)">
            <summary>
            Allows inheritors to provide additional post-commit processing of a transaction.
            </summary>
            <param name="transaction"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.CommitAutoHideTransaction(Telerik.WinControls.UI.Docking.AutoHideTransaction)">
            <summary>
            Commits an auto-hide transaction and puts associated windows in AutoHide dock state.
            </summary>
            <param name="transaction"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.CommitDockWindowTransaction(Telerik.WinControls.UI.Docking.DockWindowTransaction)">
            <summary>
            Commits a transaction associated with DockWindow calls.
            </summary>
            <param name="transaction"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.CommitDragDropTransaction(Telerik.WinControls.UI.Docking.DragDropTransaction)">
            <summary>
            Commits a transaction, created by the DragDrop service.
            </summary>
            <param name="transaction"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.CommitRedockTransaction(Telerik.WinControls.UI.Docking.RedockTransaction)">
            <summary>
            Commits a transaction, instanciated by the Redock service.
            </summary>
            <param name="transaction"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.CommitChangeBehaviorTransaction(Telerik.WinControls.UI.Docking.RadDockTransaction)">
            <summary>
            Commits a transaction, initiated by a request for a DockBehavior change.
            </summary>
            <param name="transaction"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.CommitChangeStateTransaction(Telerik.WinControls.UI.Docking.RadDockTransaction)">
            <summary>
            Commits a transaction, which explicitly changes the state of its associated dock windows.
            </summary>
            <param name="transaction"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.UpdateDockState(Telerik.WinControls.UI.Docking.RadDockTransaction)">
            <summary>
            Sets the DockState of each DockWindow, depending on the provided transaction.
            </summary>
            <param name="transaction"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.EnsureTransaction(Telerik.WinControls.UI.Docking.RadDockTransaction)">
            <summary>
            Prepares and validates the transaction.
            </summary>
            <param name="transaction"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.EnsureActiveWindow(System.Boolean)">
            <summary>
            Ensures that any previously saved active window is re-activated after transaction commitment
            </summary>
        </member>
        <member name="E:Telerik.WinControls.UI.Docking.RadDock.DockTabStripNeeded">
            <summary>
            Raised whenever a new <see cref="T:Telerik.WinControls.UI.Docking.DockTabStrip">DockTabStrip</see> instance is needed internally by the framework.
            Allows for providing custom <see cref="T:Telerik.WinControls.UI.Docking.ToolTabStrip">ToolTabStrip</see> and <see cref="T:Telerik.WinControls.UI.Docking.DocumentTabStrip">DocumentTabStrip</see> implementations.
            </summary>
        </member>
        <member name="E:Telerik.WinControls.UI.Docking.RadDock.PageViewInstanceCreated">
            <summary>
            Fires after RadPageViewElement is created.
            </summary>
        </member>
        <member name="E:Telerik.WinControls.UI.Docking.RadDock.SplitContainerNeeded">
            <summary>
            Raised whenever a new <see cref="T:Telerik.WinControls.UI.RadSplitContainer">RadSplitContainer</see> instance is needed internally by the framework.
            Allows for providing custom <see cref="T:Telerik.WinControls.UI.RadSplitContainer">RadSplitContainer</see> implementation.
            </summary>
        </member>
        <member name="E:Telerik.WinControls.UI.Docking.RadDock.TransactionBlockStarted">
            <summary>
            Notifies for a successful BeginTransactionBlock operation.
            </summary>
        </member>
        <member name="E:Telerik.WinControls.UI.Docking.RadDock.TransactionBlockEnded">
            <summary>
            Notifies for a successful EndTransactionBlock operation, when all transactions are committed, the DockTree is cleaned, and updates are resumed.
            </summary>
        </member>
        <member name="E:Telerik.WinControls.UI.Docking.RadDock.TransactionCommitted">
            <summary>
            Raised when a RadDockTransaction is successfully committed.
            Allows listeners to perform some additional operations.
            </summary>
        </member>
        <member name="E:Telerik.WinControls.UI.Docking.RadDock.TransactionCommitting">
            <summary>
            Raised when a RadDockTransaction is about to be committed.
            Allows listeners to investigate the transaction, perform some additional actions and/or cancel it.
            </summary>
        </member>
        <member name="E:Telerik.WinControls.UI.Docking.RadDock.DockWindowAdded">
            <summary>
            Notifies for a new DockWindow registered with this RadDock instance.
            </summary>
        </member>
        <member name="E:Telerik.WinControls.UI.Docking.RadDock.DockWindowRemoved">
            <summary>
            Notifies for a DockWindow removed from this RadDock instance. This event will not be raised for hidden windows.
            </summary>
        </member>
        <member name="E:Telerik.WinControls.UI.Docking.RadDock.DockWindowClosing">
            <summary>
            Raised before a DockWindow.Close method is called.
            </summary>
        </member>
        <member name="E:Telerik.WinControls.UI.Docking.RadDock.DockWindowClosed">
            <summary>
            Raised after a DockWindow has been closed.
            </summary>
        </member>
        <member name="E:Telerik.WinControls.UI.Docking.RadDock.DockStateChanging">
            <summary>
            Notifies for an upcomming change in the DockState of the associated window. Cancelable.
            </summary>
        </member>
        <member name="E:Telerik.WinControls.UI.Docking.RadDock.DockStateChanged">
            <summary>
            Notifies for a change in the DockState of the associated window.
            </summary>
        </member>
        <member name="E:Telerik.WinControls.UI.Docking.RadDock.ActiveWindowChanging">
            <summary>
            Notifies for an upcomming change of the ActiveWindow property. Cancelable.
            </summary>
        </member>
        <member name="E:Telerik.WinControls.UI.Docking.RadDock.SelectedTabChanging">
            <summary>
            Occurs when selected tab changing in currently manipulated DockTabStrip.
            </summary>
        </member>
        <member name="E:Telerik.WinControls.UI.Docking.RadDock.SelectedTabChanged">
            <summary>
            Occurs when selected tab changed in currently manipulated DockTabStrip.
            </summary>
        </member>
        <member name="E:Telerik.WinControls.UI.Docking.RadDock.ActiveWindowChanged">
            <summary>
            Notifies for an actual change of the ActiveWindow property.
            </summary>
        </member>
        <member name="E:Telerik.WinControls.UI.Docking.RadDock.FloatingWindowCreated">
            <summary>
            Notifies that a FloatingWindow instance is internally created by the framework.
            Allows listeners to examine and optionally change the window itself.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.OnFloatingWindowCreated(Telerik.WinControls.UI.Docking.FloatingWindowEventArgs)">
            <summary>
            Raises the FloatingWindowCreated event.
            </summary>
            <param name="e"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.OnTransactionCommitting(Telerik.WinControls.UI.Docking.RadDockTransactionCancelEventArgs)">
            <summary>
            Raises the TransactionCommitting event.
            </summary>
            <param name="e"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.OnTransactionCommitted(Telerik.WinControls.UI.Docking.RadDockTransactionEventArgs)">
            <summary>
            Raises the TransactionCommitted event.
            </summary>
            <param name="e"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.OnTransactionBlockEnded(System.EventArgs)">
            <summary>
            Raises the TransactionBlockEnded event.
            </summary>
            <param name="e"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.OnTransactionBlockStarted(System.EventArgs)">
            <summary>
            Raises the <see cref="E:Telerik.WinControls.UI.Docking.RadDock.TransactionBlockStarted"/> event.
            </summary>
            <param name="e"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.OnActiveWindowChanging(Telerik.WinControls.UI.Docking.DockWindowCancelEventArgs)">
            <summary>
            Raises the <see cref="E:Telerik.WinControls.UI.Docking.RadDock.ActiveWindowChanging">ActiveWindowChanging</see> event.
            </summary>
            <param name="e"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.OnActiveWindowChanged(Telerik.WinControls.UI.Docking.DockWindowEventArgs)">
            <summary>
            Raises the <see cref="E:Telerik.WinControls.UI.Docking.RadDock.ActiveWindowChanged">ActiveWindowChanged</see> event.
            </summary>
            <param name="e"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.OnDockWindowClosing(Telerik.WinControls.UI.Docking.DockWindowCancelEventArgs)">
            <summary>
            Raises the <see cref="E:Telerik.WinControls.UI.Docking.RadDock.DockWindowClosing">DockWindowClosing</see> event.
            </summary>
            <param name="e"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.OnDockWindowClosed(Telerik.WinControls.UI.Docking.DockWindowEventArgs)">
            <summary>
            Raises the <see cref="E:Telerik.WinControls.UI.Docking.RadDock.DockWindowClosed">DockWindowClosed</see> event.
            </summary>
            <param name="e"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.OnDockStateChanging(Telerik.WinControls.UI.Docking.DockStateChangingEventArgs)">
            <summary>
            Raises the <see cref="E:Telerik.WinControls.UI.Docking.RadDock.DockStateChanging">DockStateChanging</see> event.
            </summary>
            <param name="e"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.OnDockStateChanged(Telerik.WinControls.UI.Docking.DockWindowEventArgs)">
            <summary>
            Raises the <see cref="E:Telerik.WinControls.UI.Docking.RadDock.DockStateChanged">DockStateChanged</see> event.
            </summary>
            <param name="e"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.OnDockWindowAdded(Telerik.WinControls.UI.Docking.DockWindowEventArgs)">
            <summary>
            Raises the <see cref="E:Telerik.WinControls.UI.Docking.RadDock.DockWindowAdded">DockWindowAdded</see> event.
            </summary>
            <param name="e"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.OnDockWindowRemoved(Telerik.WinControls.UI.Docking.DockWindowEventArgs)">
            <summary>
            Raises the <see cref="E:Telerik.WinControls.UI.Docking.RadDock.DockWindowRemoved">DockWindowRemoved</see> event.
            </summary>
            <param name="e"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.OnToolTabStripDoubleClick(Telerik.WinControls.UI.Docking.ToolTabStrip,System.Drawing.Point)">
            <summary>
            Receives a notification from a tooltabstrip that the mouse was clicked inside its area.
            The default implementation will try to restore the state of the associated windows.
            </summary>
            <param name="strip"></param>
            <param name="click"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.OnAutoHideButtonClicked(Telerik.WinControls.UI.Docking.ToolTabStrip)">
            <summary>
            An Auto-hide button click notification, received from a registered ToolTabStrip.
            </summary>
            <param name="strip"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.OnDockStateContextMenuClicked(Telerik.WinControls.UI.Docking.DockWindow,Telerik.WinControls.UI.Docking.DockState)">
            <summary>
            Recieves a notification for a change state request, made from the window's associated context menu.
            </summary>
            <param name="window"></param>
            <param name="newState"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.CallOnControlTreeChanged(Telerik.WinControls.UI.Docking.ControlTreeChangedEventArgs)">
            <summary>
            Calls the OnControlTreeChanged method. This method is used internally.
            </summary>
            <param name="args"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.OnControlTreeChanged(Telerik.WinControls.UI.Docking.ControlTreeChangedEventArgs)">
            <summary>
            
            </summary>
            <param name="args"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.OnDockWindowNameChanged(Telerik.WinControls.UI.Docking.DockWindow,System.String)">
            <summary>
            The manager gets notified for a change in the specified <see cref="T:Telerik.WinControls.UI.Docking.DockWindow">DockWindow</see> instance Name property.
            </summary>
            <param name="dockWindow">The DockWindow which name has changed.</param>
            <param name="oldName">The window's previous name.</param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.RegisterControl(System.Windows.Forms.Control)">
            <summary>
            Registers all DockWindow or DockTabStrip instances, residing in the provided control tree.
            </summary>
            <param name="parent"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.UnregisterControl(System.Windows.Forms.Control)">
            <summary>
            Unregisters all DockWindow or DockTabStrip instances, residing in the provided control tree.
            </summary>
            <param name="parent"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.ShouldProcessNotification">
            <summary>
            This method is used internally.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.AddDocument(Telerik.WinControls.UI.Docking.DockWindow)">
            <summary>
            Adds the specified DockWindow instance to the main document tab strip.
            </summary>
            <param name="dockWindow">The dock window.</param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.AddDocument(Telerik.WinControls.UI.Docking.DockWindow,Telerik.WinControls.UI.Docking.DockPosition)">
            <summary>
            Adds the the specified DockWindow instance at the specified dock position, aligned with the main document tabstrip.
            </summary>
            <param name="dockWindow">The dock window.</param>
            <param name="position">The desired dock position.</param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.AddDocument(Telerik.WinControls.UI.Docking.DockWindow,Telerik.WinControls.UI.Docking.DockWindow,Telerik.WinControls.UI.Docking.DockPosition)">
            <summary>
            Adds the the specified DockWindow instance to the specified DocumentTabStrip instance.
            </summary>
            <param name="dockWindow">The dock window.</param>
            <param name="target">The target of the dock opeation.</param>
            <param name="position">The desired dock position.</param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.AddDocument(Telerik.WinControls.UI.Docking.DockWindow,Telerik.WinControls.UI.Docking.DocumentTabStrip,Telerik.WinControls.UI.Docking.DockPosition)">
            <summary>
            Adds the specified DockWindow instance to a new DocumentTabStrip instance.
            </summary>
            <param name="dockWindow">The dock window.</param>
            <param name="tabStrip">The DocumentTabStrip, which is the target of the operation.</param>
            <param name="position">The position.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.AddDocuments(Telerik.WinControls.UI.Docking.DockWindow[],Telerik.WinControls.UI.Docking.DocumentTabStrip,Telerik.WinControls.UI.Docking.DockPosition)">
            <summary>
            Adds a collection of DockWindows to a new DocumentTabStrip instance.
            </summary>
            <param name="dockWindows">The dock windows.</param>
            <param name="tabStrip">The DocumentTabStrip, which is the target of the operation.</param>
            <param name="position">The position.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.MoveToPreviousDocumentTabStrip(Telerik.WinControls.UI.Docking.DockWindow)">
            <summary>
            Move DockWindow to previuos document tab strip if exist
            when DockWindow is in document mode
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.MoveToNextDocumentTabStrip(Telerik.WinControls.UI.Docking.DockWindow)">
            <summary>
            Move DockWindow to next document tab strip if exist
            when DockWindow is in document mode
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.GetPreviousDocumentStrip(Telerik.WinControls.UI.Docking.DockWindow)">
            <summary>
            Gets the previous document tab strip, regarding the specified DockWindow.
            The window should be in TabbedDocument state for the method to work.
            </summary>
            <param name="window"></param>
            <returns></returns>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.GetNextDocumentStrip(Telerik.WinControls.UI.Docking.DockWindow)">
            <summary>
            Gets the next document tab strip, regarding the specified DockWindow.
            The window should be in TabbedDocument state for the method to work.
            </summary>
            <param name="window"></param>
            <returns></returns>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.Contains(Telerik.WinControls.UI.Docking.DockWindow)">
            <summary>
            Determines whether the specified DockWindow is registered with this RadDock instances.
            </summary>
            <param name="dockWindow"></param>
            <returns></returns>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.Contains(System.String)">
            <summary>
            Determines whether a DockWindow instance with the specified name is registered with this RadDock instance.
            </summary>
            <param name="name"></param>
            <returns></returns>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.GetHostWindow(System.Windows.Forms.Control)">
            <summary>
            Get the host window for Control instance docked with DockControl method or for MDI children Form object
            </summary>
            <param name="control"></param>
            <returns>found HostWindow instance or null</returns>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.ActivateMdiChild(System.Windows.Forms.Form)">
            <summary>Activates the MDI child of a form.</summary>
            <param name="form">The child form to activate.</param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.DockControl(System.Windows.Forms.Control,Telerik.WinControls.UI.Docking.DockPosition)">
            <summary>
            Registers the specified control as part of the docking framework.
            </summary>
            <param name="control"></param>
            <param name="position"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.DockControl(System.Windows.Forms.Control,Telerik.WinControls.UI.Docking.DockPosition,Telerik.WinControls.UI.Docking.DockType)">
            <summary>
            Registers the specified control as part of the docking framework.
            </summary>
            <param name="control"></param>
            <param name="position"></param>
            <param name="dockType"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.DockControl(System.Windows.Forms.Control,Telerik.WinControls.UI.Docking.DockWindow,Telerik.WinControls.UI.Docking.DockPosition)">
            <summary>
            Registers the specified control as part of the docking framework.
            </summary>
            <param name="control"></param>
            <param name="target"></param>
            <param name="position"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.DockControl(System.Windows.Forms.Control,Telerik.WinControls.UI.Docking.DockWindow,Telerik.WinControls.UI.Docking.DockPosition,Telerik.WinControls.UI.Docking.DockType)">
            <summary>
            Registers the specified control as part of the docking framework.
            </summary>
            <param name="control"></param>
            <param name="target"></param>
            <param name="position"></param>
            <param name="dockType"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.DockControl(System.Windows.Forms.Control,Telerik.WinControls.UI.Docking.DockTabStrip,Telerik.WinControls.UI.Docking.DockPosition)">
            <summary>
            Registers the specified control as part of the docking framework.
            </summary>
            <param name="control"></param>
            <param name="dockAnchor"></param>
            <param name="position"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.DockControl(System.Windows.Forms.Control,Telerik.WinControls.UI.Docking.DockTabStrip,Telerik.WinControls.UI.Docking.DockPosition,Telerik.WinControls.UI.Docking.DockType)">
            <summary>
            Registers the specified control as part of the docking framework.
            </summary>
            <param name="control"></param>
            <param name="dockAnchor"></param>
            <param name="position"></param>
            <param name="dockType"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.DockWindow(Telerik.WinControls.UI.Docking.DockWindow,Telerik.WinControls.UI.Docking.DockPosition)">
            <summary>
            Registers the specified DockWindow instance and docks it at the desired psotion.
            </summary>
            <param name="dockWindow"></param>
            <param name="position"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.DockWindow(Telerik.WinControls.UI.Docking.DockWindow,Telerik.WinControls.UI.Docking.DockWindow,Telerik.WinControls.UI.Docking.DockPosition)">
            <summary>
            Registers the specified DockWindow instance and docks it at the desired psotion, using the provided target window as an anchor.
            </summary>
            <param name="dockWindow"></param>
            <param name="target"></param>
            <param name="position"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.DockWindow(Telerik.WinControls.UI.Docking.DockWindow,Telerik.WinControls.UI.SplitPanel,Telerik.WinControls.UI.Docking.DockPosition)">
            <summary>
            Registers the specified DockWindow instance and docks it at the desired position, using the provided SplitPanel as an anchor.
            </summary>
            <param name="window"></param>
            <param name="dockAnchor">Any split panel instance that resides </param>
            <param name="position"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.RemoveWindow(Telerik.WinControls.UI.Docking.DockWindow)">
            <summary>
            Removes the specified window, without disposing it, from the dock manager.
            </summary>
            <param name="window"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.RemoveWindow(Telerik.WinControls.UI.Docking.DockWindow,Telerik.WinControls.UI.Docking.DockWindowCloseAction)">
            <summary>
            Removes the specified window, using the specified DockWindowCloseAction.
            </summary>
            <param name="window"></param>
            <param name="closeAction">The desired action to be taken.</param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.RemoveAllWindows">
            <summary>
            Removes all DockWindows, without disposing them.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.RemoveAllWindows(Telerik.WinControls.UI.Docking.DockWindowCloseAction)">
            <summary>
            Removes all DockWindows, using the specified close action.
            </summary>
            <param name="closeAction">The action to be taken when closing a window.</param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.RemoveAllDocumentWindows">
            <summary>
            Removes all DocumentWindows, without disposing it.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.RemoveAllDocumentWindows(Telerik.WinControls.UI.Docking.DockWindowCloseAction)">
            <summary>
            Removes all DocumentWindows, using the specified close action.
            </summary>
            <param name="closeAction">The action to be taken when closing a window.</param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.RemoveAllToolWindows(Telerik.WinControls.UI.Docking.DockWindowCloseAction,Telerik.WinControls.UI.Docking.AllowedDockState)">
            <summary>
            Removes all ToolWindows with the specified DockState, using the specified close action.
            </summary>
            <param name="closeAction">The action to be taken when closing a window.</param>
            <param name="allowedDockState">Specifies the DockState of the ToolWindows that will be removed. The enumeration flags can be combined.</param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.CloseAllWindows">
            <summary>
            Calls the Close method for all currently registered windows, using each window's CloseAction.
            </summary>
            <returns>[true] if all windows were successfully closed, [false] otherwise</returns>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.RadDock.InnerList">
            <summary>
            Gets the internal sorted list, which stores 
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.GetDefaultDocumentTabStrip(System.Boolean)">
            <summary>
            Gets the default DocumentTabStrip instance, used to add documents without explicitly specified dock target.
            </summary>
            <param name="createNew">True to create a new DocumentTabStrip instance and add it to the document container.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.EnumFrameworkControls``1">
            <summary>
            Enumerates all currently alive T instances available within the framework.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.ApplyLayoutStrategy">
            <summary>
            Applies the LayoutStrategy as defined by the layoutStrategyType field.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.CreateLayoutStrategy">
            <summary>
            Creates new SplitContainerLayoutStrategy instance. Allows inheritors to provide custom type.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.CreateDefaultTabStrip(Telerik.WinControls.UI.Docking.DockType)">
            <summary>
            Creates and returns the default DockTabStrip for a given DockType.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.AddAsTabItems(Telerik.WinControls.UI.SplitPanel,Telerik.WinControls.UI.Docking.DockTabStrip)">
            <summary>
            Inserts all DockWindow instances that reside on the specified split panel in the provided strip's TabPanels collection.
            </summary>
            <param name="panel"></param>
            <param name="strip"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.DockSplitPanel(Telerik.WinControls.UI.SplitPanel,Telerik.WinControls.UI.SplitPanel,Telerik.WinControls.UI.Docking.DockPosition)">
            <summary>
            Docks the provided SplitPanel at the specified position.
            Used internally by the framework to dock DockTabStrips and entire containers.
            </summary>
            <param name="panel"></param>
            <param name="dockAnchor"></param>
            <param name="position"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.PerformDock(Telerik.WinControls.UI.RadSplitContainer,Telerik.WinControls.UI.SplitPanel,Telerik.WinControls.UI.SplitPanel,Telerik.WinControls.UI.Docking.DockPosition)">
            <summary>
            Performs the core dock operation and splits the layout in different orientations if needed.
            </summary>
            <param name="targetContainer"></param>
            <param name="panel"></param>
            <param name="dockAnchor"></param>
            <param name="position"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.SetBoundsCore(System.Int32,System.Int32,System.Int32,System.Int32,System.Windows.Forms.BoundsSpecified)">
            <summary>
            
            </summary>
            <param name="x"></param>
            <param name="y"></param>
            <param name="width"></param>
            <param name="height"></param>
            <param name="specified"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.GetSplitterElementAtPoint(System.Drawing.Point)">
            <summary>
            
            </summary>
            <param name="clientPoint"></param>
            <returns></returns>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.CanChangeWindowState(Telerik.WinControls.UI.Docking.DockWindow,Telerik.WinControls.UI.Docking.DockState)">
            <summary>
            Determines whether the provided dock state may be applied to the specified DockWindow.
            </summary>
            <param name="window"></param>
            <param name="state"></param>
            <returns></returns>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.CanChangeWindowState(Telerik.WinControls.UI.Docking.DockWindow,Telerik.WinControls.UI.Docking.DockState,System.Boolean)">
            <summary>
            Determines whether the specified window's DockState value can be changed to the specified new one.
            </summary>
            <param name="window">The window, which should be examined.</param>
            <param name="state">The new target state.</param>
            <param name="raiseChanging">True to raise DockStateChanging notification, false otherwise.</param>
            <returns>True if the state can be changed, false otherwise.</returns>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.FloatWindow(Telerik.WinControls.UI.Docking.DockWindow)">
            <summary>
            Makes the specified window floating.
            </summary>
            <param name="window"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.FloatWindow(Telerik.WinControls.UI.Docking.DockWindow,System.Drawing.Rectangle)">
            <summary>
            Makes the specified window floating, using the provided bounds.
            </summary>
            <param name="window"></param>
            <param name="bounds">The desired floating bounds. Pass Rectangle.Empty to set the default bounds.</param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.FloatWindows(System.Collections.Generic.IEnumerable{Telerik.WinControls.UI.Docking.DockWindow},System.Drawing.Rectangle)">
            <summary>
            Makes the specified windows floating, using the provided bounds.
            </summary>
            <param name="windows"></param>
            <param name="bounds">The desired floating bounds. Pass Rectangle.Empty to set the default bounds.</param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.FloatWindows(System.Collections.Generic.IEnumerable{Telerik.WinControls.UI.Docking.DockWindow},System.Drawing.Rectangle,Telerik.WinControls.UI.Docking.DockType)">
            <summary>
            Makes the specified windows floating, using the provided bounds.
            </summary>
            <param name="windows"></param>
            <param name="bounds">The desired floating bounds. Pass Rectangle.Empty to set the default bounds.</param>
            <param name="containerType">The type of the container inside the floating form - ToolWindow or DocumentWindow.</param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.FloatToolTabStrip(Telerik.WinControls.UI.Docking.ToolTabStrip,System.Drawing.Rectangle)">
            <summary>
            Makes the specified tool tab strip floating.
            </summary>
            <param name="strip"></param>
            <param name="bounds">The desired floating bounds. Pass Rectangle.Empty to set the default bounds.</param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.AutoHideWindow(Telerik.WinControls.UI.Docking.DockWindow)">
            <summary>
            Makes the specified DockWindow auto-hidden.
            </summary>
            <param name="window"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.AutoHideWindows(System.Collections.Generic.IEnumerable{Telerik.WinControls.UI.Docking.DockWindow},Telerik.WinControls.UI.Docking.AutoHidePosition)">
            <summary>
            Makes the specified DockWindow collection auto-hidden.
            </summary>
            <param name="windows"></param>
            <param name="position">The edge at which the windows should be auto-hidden against.</param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.CloseWindow(Telerik.WinControls.UI.Docking.DockWindow)">
            <summary>
            Removes or hides (depending on the CloseAction) the specified window.
            </summary>
            <param name="window"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.CloseWindows(System.Collections.Generic.IEnumerable{Telerik.WinControls.UI.Docking.DockWindow})">
            <summary>
            Removes or hides (depending on the CloseAction) the specified windows.
            </summary>
            <param name="windows"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.DisplayWindow(Telerik.WinControls.UI.Docking.DockWindow)">
            <summary>
            Displays the specified window if was previously hidden.
            </summary>
            <param name="window"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.DisplayWindows(System.Collections.Generic.IEnumerable{Telerik.WinControls.UI.Docking.DockWindow})">
            <summary>
            Displays the provided dock windows if they were previously hidden and are registered with this RadDock instance.
            </summary>
            <param name="windows"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.SetWindowState(Telerik.WinControls.UI.Docking.DockWindow,Telerik.WinControls.UI.Docking.DockState)">
            <summary>
            Applies the desired DockState to the specified DockWindow.
            If a previously RedockState is saved for the desired DockState, this state is restored, else the default action is performed.
            </summary>
            <param name="window"></param>
            <param name="state"></param>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.RadDock.CommandManager">
            <summary>
            Gets the <see cref="T:Telerik.WinControls.UI.Docking.RadDockCommandManager">CommandManager</see> registered with this RadDock instance.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.OnHandleCreated(System.EventArgs)">
            <summary>
            
            </summary>
            <param name="e"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.OnHandleDestroyed(System.EventArgs)">
            <summary>
            
            </summary>
            <param name="e"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.OnEnabledChanged(System.EventArgs)">
            <summary>
            
            </summary>
            <param name="e"></param>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.RadDock.DocumentManager">
            <summary>
            Gets the <see cref="P:Telerik.WinControls.UI.Docking.RadDock.DocumentManager">DocumentManager</see> instance that manages all the DockWindow instances which are parented <see cref="P:Telerik.WinControls.UI.Docking.RadDock.MainDocumentContainer">MainDocumentContainer</see>.
            For a Document is considered either a DocumentWindow instance or a ToolWindow, which current <see cref="P:Telerik.WinControls.UI.Docking.DockWindow.DockState">DockState</see> is <see cref="F:Telerik.WinControls.UI.Docking.DockState.TabbedDocument">TabbedDocument</see>.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.RadDock.MainDocumentContainer">
            <summary>
            Gets or sets the document container for this RadDock instance.
            This property is used primarily for serialization purposes and is not intended to be used directly by code.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.RadDock.MainDocumentContainerVisible">
            <summary>
            Determines whether the main document container is visible.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.RegisterService(System.Int32,Telerik.WinControls.UI.Docking.RadDockService)">
            <summary>
            Registers the specified service with ourselves.
            </summary>
            <param name="key"></param>
            <param name="service"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.GetService``1">
            <summary>
            Retrieves currently registered <see cref="T:Telerik.WinControls.UI.Docking.RadDockService">Service</see> by the specified type.
            </summary>
            <typeparam name="T"></typeparam>
            <returns></returns>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.GetService``1(System.Int32)">
            <summary>
            Retrieves currently registered Service by the provided key.
            All predefined service keys may be found in ServiceConstants class.
            </summary>
            <typeparam name="T"></typeparam>
            <param name="key"></param>
            <returns></returns>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.RadDock.DockingGuidesTemplate">
            <summary>
            Gets or sets the template, which defines the appearance of the guides displayed upon drag-and-drop operation.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.RadDock.IsDragging">
            <summary>
            Determines whether the DragDropService is currently working (a drag-and-drop operation is running).
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.RadDock.DragDropMode">
            <summary>
            Gets or sets the mode used by the DragDropService when a drag-and-drop request is made.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.RadDock.DragDropAllowedDockStates">
            <summary>
            Gets or sets the allowed dock states for a committing drag-and-drop operation.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.RadDock.TreatTabbedDocumentsAsToolWindows">
            <summary>
            Determines whether tabbed documents will have special behavior that differs from a ToolWindow's one.
            For example if this is true, a tabbed document will be allowed to become floating by a drag-and-drop operation.
            </summary>
        </member>
        <member name="E:Telerik.WinControls.UI.Docking.RadDock.LoadedFromXml">
            <summary>
            Notifies that manager's state has been loaded from an external XML source.
            </summary>
        </member>
        <member name="E:Telerik.WinControls.UI.Docking.RadDock.SavedToXml">
            <summary>
            Notifies that manager's state has been saved to an external XML source.
            </summary>
        </member>
        <member name="E:Telerik.WinControls.UI.Docking.RadDock.DockWindowSerializing">
            <summary>
            Fired when a dock window is being serialized. Allows excluding some windows
            from serialization.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.OnDockWindowSerializing(Telerik.WinControls.UI.Docking.DockWindow)">
            <summary>
            Raises the <see cref="E:Telerik.WinControls.UI.Docking.RadDock.DockWindowSerializing">DockWindowSerializing</see> event.
            </summary>
            <returns>[true] if canceled, [false] otherwise.</returns>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.OnLoadedFromXml">
            <summary>
            Raises the <see cref="E:Telerik.WinControls.UI.Docking.RadDock.LoadedFromXml">LoadedFromXml</see> event.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.OnSavedToXml">
            <summary>
            Raises the <see cref="E:Telerik.WinControls.UI.Docking.RadDock.SavedToXml">SavedToXml</see> event.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.LoadFromXml(System.String,System.Boolean)">
            <summary>
            Loads the docking layout configuration from a file. 
            This method will try to guess if the xml in the file was written with 
            the DockingManager or with the new RadDock in order to determine how to 
            read it successfully.
            </summary>
            <param name="fileName">Name of the file.</param>
            <param name="clearDocking">if set to <c>true</c> removes the existing dock windows using the <see cref="F:Telerik.WinControls.UI.Docking.DockWindowCloseAction.CloseAndDispose"/> action.</param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.LoadFromXml(System.IO.Stream,System.Boolean)">
            <summary>
            Loads the docking layout configuration from a stream.
            This method will try to guess if the xml in the stream was written with 
            the DockingManager or with the new RadDock in order to determine how to 
            read it successfully.
            </summary>
            <param name="stream">The stream.</param>
            <param name="clearDocking">if set to <c>true</c> removes the existing dock windows using the <see cref="F:Telerik.WinControls.UI.Docking.DockWindowCloseAction.CloseAndDispose"/> action.</param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.LoadFromXml(System.IO.TextReader,System.Boolean)">
            <summary>
            Loads the docking layout configuration from a TextReader.
            Note that this is a new method and does not support loading xml 
            that was written using DockingManager.
            </summary>
            <param name="textReader">The text reader.</param>
            <param name="clearDocking">if set to <c>true</c> removes the existing dock windows using the <see cref="F:Telerik.WinControls.UI.Docking.DockWindowCloseAction.CloseAndDispose"/> action.</param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.LoadFromXmlCore(System.Xml.XmlReader,System.Boolean,System.Boolean)">
            <summary>
            Overrwrite this method to change the loading of docking layout configuration.
            </summary>
            <param name="reader">The XmlReader instance that will be used to load the layoiut configuration.</param>
            <param name="oldXmlFormat">if set to <c>true</c> the layout configuration is in the old DockingManager format.</param>
            <param name="clearDocking">if set to <c>true</c> removes the existing dock windows using the <see cref="F:Telerik.WinControls.UI.Docking.DockWindowCloseAction.CloseAndDispose"/> action.</param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.CreateXmlWriterSettings">
            <summary>
            Creates the core XmlWritterSettings to be used by the serialization manager.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.SaveToXml(System.String)">
            <summary>
            Saves the docking layout configuration in an XML format in the specified file.
            </summary>
            <param name="fileName">Name of the file.</param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.SaveToXml(System.IO.Stream)">
            <summary>
            Saves the docking layout configuration in an XML format in the specified stream.
            </summary>
            <param name="stream">The stream.</param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.SaveToXml(System.IO.TextWriter)">
            <summary>
            Saves the docking layout configuration in an XML format in the specified TextWriter.
            </summary>
            <param name="textWriter">The text writer.</param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.GetDefaultXmlSerializationInfo">
             <summary>
             Gets the default serialization info for RadDock used by Save/Load loyout methods to persist grid settings to/from XML.
             </summary>
            <remarks>
             You can use the serialization info to include/exclude properties of RadDock and related objects from XML serialization.
            </remarks>
             <returns></returns>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.RadDock.XmlSerializationInfo">
            <summary>
            Gets the serialization info for RadDock used by Save/Load loyout methods to persist grid settings to/from XML.
            By default, or when set to null the ComponentXmlSerializationInfo provided by GetDefaultXmlSerializationInfo() will be used.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.SaveToXmlCore(System.Xml.XmlWriter)">
            <summary>
            Performs the core save logic.
            </summary>
            <param name="writer"></param>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.RadDock.GuidToNameMappings">
            <summary>
            
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.RadDock.SerializableAutoHideContainer">
            <summary>
            Gets an instance of DockAutoHideSerializationContainer that contains information about auto-hidden windows which have been 
            set up through designer or xml file. This nethod is no intended for use directly from your code.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.RadDock.SerializableFloatingWindows">
            <summary>
            Gets an instance of FloatingWindowList that contains information about floating windows which have been 
            set up through designer or xml file. This nethod is no intended for use directly from your code.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.LoadDeserializedFloatingAndAutoHideWindows">
            <summary>
            The method is intended fot use within RadDcok and Advanced Layout Designer, to float windows after loading radDock layout from designer
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.RadDock.ThemeClassName">
            <summary>
            
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.RadDock.BackColor">
            <summary>
            RadDock consists of multiple visual elements and separate settings are provided to customize their appearance.
            Current BackColor property might be ignored.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.RadDock.ForeColor">
            <summary>
            RadDock consists of multiple visual elements and separate settings are provided to customize their appearance.
            Current ForeColor property might be ignored.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.RadDock.ActiveWindow">
            <summary>
            Gets or sets the DockWindow instance, which is currently active (meaning it contains the Keyboard focus).
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.SetDefaultActiveWindow">
            <summary>
            Sets the first available DockWindow as currently active.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.ActivateWindow(Telerik.WinControls.UI.Docking.DockWindow)">
            <summary>
            Activates the specified DockWindow and gives it the keyboard focus.
            </summary>
            <param name="window"></param>
            <returns></returns>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDock.ActivateWindow(Telerik.WinControls.UI.Docking.DockWindow,System.Boolean)">
            <summary>
            Activates the specified window and gives it the keyboard focus.
            </summary>
            <param name="window"></param>
            <param name="forceNotify">
            True to force ActiveWindowChanging and ActiveWindowChanged notifications, false otherwise.
            Sometimes a transaction may change the current active window several times and the method needs to know when to raise the notifications.
            </param>
            <returns></returns>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.RadDock.FloatingWindows">
            <summary>
            Gets a list of the floating windows. Can contain duplicates, empty or hidden toolwindows
            which are used internally as re-dock targets.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.RadDock.ActiveFloatingWindows">
            <summary>
            
            </summary>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.ToolWindow">
            <summary>
            Implements a <see cref="T:Telerik.WinControls.UI.Docking.DockWindow">DockWindow</see> that resides within a ToolTabStrip.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.ToolWindow.#ctor">
            <summary>
            Default constructor. Initializes a new instance of a <see cref="T:Telerik.WinControls.UI.Docking.DockWindow">DockWindow</see> class.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.ToolWindow.#ctor(System.String)">
            <summary>
            Initializes a new instance of a <see cref="T:Telerik.WinControls.UI.Docking.DockWindow">DockWindow</see> class setting the provided Text.
            </summary>
            <param name="text"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.ToolWindow.AutoHide">
            <summary>
            Change the window state to auto-hide mode
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.ToolWindow.Float(System.Drawing.Point,System.Drawing.Size)">
            <summary>
            Change the window to be in floating mode
            </summary>
            <param name="location"></param>
            <param name="size"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.ToolWindow.Float(System.Drawing.Rectangle)">
            <summary>
            Change the window to be in floating mode
            </summary>
            <param name="bounds"></param>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.ToolWindow.CloseAction">
            <summary>
            
            </summary>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.ToolTabStrip">
            <summary>
            A predefined <see cref="T:Telerik.WinControls.UI.Docking.DockTabStrip">DockTabStrip</see> instance that is used to store ToolWindow instances.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.ToolTabStrip.window">
            <summary>
            The root element which describes the layout of this instance.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.ToolTabStrip.#ctor">
            <summary>
            Initializes a default instance of the <see cref="T:Telerik.WinControls.UI.Docking.ToolTabStrip">ToolTabStrip</see> class.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.ToolTabStrip.#ctor(Telerik.WinControls.UI.Docking.RadDock)">
            <summary>
            Initializes a default instance of the <see cref="T:Telerik.WinControls.UI.Docking.ToolTabStrip">ToolTabStrip</see> class and associates it with the provided RadDock instance.
            </summary>
            <param name="dockManager"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.ToolTabStrip.CreateChildItems(Telerik.WinControls.RadElement)">
            <summary>
            
            </summary>
            <param name="parent"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.ToolTabStrip.Dispose(System.Boolean)">
            <summary>
            
            </summary>
            <param name="disposing"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.ToolTabStrip.ControlDefinesThemeForElement(Telerik.WinControls.RadElement)">
            <summary>
            Checks whether the <paramref name="element"/>'s theme is defined by the control.
            </summary>
            <remarks>
            If true is returned the ThemeResolutionService would not not set any theme to the element 
            to avoid duplicating the style settings of the element.
            </remarks>
            <param name="element">The element to should be checked.</param>
            <returns>true if the control defines theme for this element, false otherwise.</returns>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.ToolTabStrip.OnMouseDown(System.Windows.Forms.MouseEventArgs)">
            <summary>
            
            </summary>
            <param name="e"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.ToolTabStrip.OnMouseDoubleClick(System.Windows.Forms.MouseEventArgs)">
            <summary>
            
            </summary>
            <param name="e"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.ToolTabStrip.OnLayout(System.Windows.Forms.LayoutEventArgs)">
            <summary>
            
            </summary>
            <param name="e"></param>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.ToolTabStrip.ThemeClassName">
            <summary>
            
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.ToolTabStrip.TabPanelBounds">
            <summary>
            
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.ToolTabStrip.CaptionElement">
            <summary>
            Gets the ToolWindowCaptionElement that appears as titlebar of ToolTabStrip
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.ToolTabStrip.CaptionLayoutPanel">
            <summary>
            Gets an instance of the <see cref="T:Telerik.WinControls.Layouts.DockLayoutPanel"/>class
            that represents the layout panel that holds the caption.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.ToolTabStrip.CloseButton">
            <summary>
            Gets the close RadButtonElement that appears as titlebar of ToolTabStrip
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.ToolTabStrip.ActionMenuButton">
            <summary>
            Gets the system menu RadDropDownButtonElement that appears as titlebar of ToolTabStrip
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.ToolTabStrip.AutoHideButton">
            <summary>
            Gets the auto-hide RadButtonElement that appears as titlebar of ToolTabStrip
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.ToolTabStrip.AutoHidePosition">
            <summary>
            Gets or sets the auto-hide position for the strip.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.ToolTabStrip.DockType">
            <summary>
            Returns <see cref="F:Telerik.WinControls.UI.Docking.DockType.ToolWindow">ToolWindow</see> dock type.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.ToolTabStrip.CaptionVisible">
            <summary>
            Determines whether the Caption element of the strip is visible.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.ToolTabStrip.UpdateButtons">
            <summary>
            
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.ToolTabStrip.UpdateActiveWindow(Telerik.WinControls.UI.Docking.DockWindow,System.Boolean)">
            <summary>
            
            </summary>
            <param name="window"></param>
            <param name="active"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.ToolTabStrip.ShouldHandleDoubleClick">
            <summary>
            Determines whether a mouse double-click event should be handled.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.ToolTabStrip.IsDragAllowed(System.Drawing.Point)">
            <summary>
            
            </summary>
            <param name="location"></param>
            <returns></returns>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.ToolTabStrip.GetTabStripVisible">
            <summary>
            Determines whether the tabstrip element is visible.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.ToolTabStrip.GetAutoHideButtonChecked">
            <summary>
            Determines whether the auto-hide button is checked.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.ToolTabStrip.UpdateCaptionText">
            <summary>
            Updates the caption text, depending on the currently selected dock window.
            </summary>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.Properties.Resources">
            <summary>
              A strongly-typed resource class, for looking up localized strings, etc.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.Properties.Resources.ResourceManager">
            <summary>
              Returns the cached ResourceManager instance used by this class.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.Properties.Resources.Culture">
            <summary>
              Overrides the current thread's CurrentUICulture property for all
              resource lookups using this strongly typed resource class.
            </summary>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.QuickNavigator">
            <summary>
            Represents a predefined RadControl instance which hosts a QuickNavigatorElement.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.QuickNavigator.#ctor">
            <summary>
            Default constructor used by the VisualStyleBuilder to allow for control's styling.
            Internally mock dock windows are created and displayed in the navigator.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.QuickNavigator.ThemeClassName">
            <summary>
            
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.QuickNavigator.Dispose(System.Boolean)">
            <summary>
            
            </summary>
            <param name="disposing"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.QuickNavigator.CreateChildItems(Telerik.WinControls.RadElement)">
            <summary>
            
            </summary>
            <param name="parent"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.QuickNavigator.InitializeRootElement(Telerik.WinControls.RootRadElement)">
            <summary>
            
            </summary>
            <param name="rootElement"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.QuickNavigator.OnMouseDown(System.Windows.Forms.MouseEventArgs)">
            <summary>
            
            </summary>
            <param name="e"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.QuickNavigator.Initialize">
            <summary>
            Fills tool window and document lists.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.QuickNavigator.BuildPanes">
            <summary>
            Separate tool from document panes to determine the size of each visual list.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.QuickNavigator.GetPreferredSize">
            <summary>
            Calculates the preferred size for the control, 
            including tool windows, documents, preview as well as header and footer.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.QuickNavigator.NavigateLeft">
            <summary>
            Moves the current selection to the left.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.QuickNavigator.NavigateRight">
            <summary>
            Moves the current selection to the right.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.QuickNavigator.NavigateUp">
            <summary>
            Moves the current selection upwards.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.QuickNavigator.NavigateDown">
            <summary>
            Moves the current selection downwards.
            </summary>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.QuickNavigatorList">
            <summary>
            Represents a dock layout panel, which hosts a header plus a scrollable list with items.
            </summary>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.QuickNavigatorDisplayPosition">
            <summary>
            Defines the possible start-up positions for a RadDock's QuickNavigator.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.QuickNavigatorDisplayPosition.CenterDockManager">
            <summary>
            The navigator is centered against its owning RadDock's screen bounds.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.QuickNavigatorDisplayPosition.CenterMainForm">
            <summary>
            The navigator is centered against its owning Form's screen bounds.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.QuickNavigatorDisplayPosition.CenterScreen">
            <summary>
            The navigator is centered in the screen working area.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.QuickNavigatorDisplayPosition.Manual">
            <summary>
            Manual position is defined for the navigator.
            </summary>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.QuickNavigatorListItem">
            <summary>
            Represents an item that may reside in a QuickNavigator DockWindow list.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.QuickNavigatorListItem.OnPropertyChanged(Telerik.WinControls.RadPropertyChangedEventArgs)">
            <summary>
            
            </summary>
            <param name="e"></param>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.QuickNavigatorListItem.IsActiveProperty">
            <summary>
            A property to indicate whether an item is currently active (displayed with bold text on an owning QuickNavigator).
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.QuickNavigatorListItem.IsActive">
            <summary>
            Determines whether the item represents an active window.
            </summary>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.QuickNavigatorPopup">
            <summary>
            Represents a pop-up form which displays all currently opened tool windows and documents.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.QuickNavigatorPopup.SelectedPane">
            <summary>
            Gets the currently DockWindow that is currently selected in the navigator.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.QuickNavigatorPopup.Navigator">
            <summary>
            Gets the QuickNavigator instance displayed on this popup.
            </summary>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.QuickNavigatorElement">
            <summary>
            Represents the RadElement structure that is hosted on a DockQuickNavigator control.
            The element has the following structure:
            1. Header
            2. Dock Layout with three elements:
               - Tool Window List
               - Document List
               - Preview
            3. Footer
            </summary>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.QuickNavigatorSettings">
            <summary>
            Encapsulates all the settings that control the appearance and behavior of the QuickNavigator for a RadDock.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.QuickNavigatorSettings.#ctor">
            <summary>
            Default constructor.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.QuickNavigatorSettings.DisplayPosition">
            <summary>
            Determines the display position of the QuickNavigator.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.QuickNavigatorSettings.ListItemType">
            <summary>
            Gets or sets the Type used to create items in the QuickNavigator's lists.
            This should be a valid QuickNavigatorListItem assignable type.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.QuickNavigatorSettings.HeaderPadding">
            <summary>
            Gets or sets the padding to be applied on the header part of the navigator.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.QuickNavigatorSettings.FooterPadding">
            <summary>
            Gets or sets the padding to be applied on the footer part of the navigator.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.QuickNavigatorSettings.Enabled">
            <summary>
            Determines whether the navigator is enabled (displayed when the shortcut combination is triggered).
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.QuickNavigatorSettings.ShowHeader">
            <summary>
            Determines whether the header area of the QuickNavigator is visible.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.QuickNavigatorSettings.ShowFooter">
            <summary>
            Determines whether the footer area of the QuickNavigator is visible.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.QuickNavigatorSettings.ShowToolPanes">
            <summary>
            Determines whether the ToolWindow list is visible.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.QuickNavigatorSettings.ShowDocumentPanes">
            <summary>
            Determines whether the DocumentWindow list is visible.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.QuickNavigatorSettings.ShowPreview">
            <summary>
            Determines whether the Preview element is visible.
            The Preview displays a snapshot of the selected DockWindow in either of the QuickNavigator's lists.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.QuickNavigatorSettings.DropShadow">
            <summary>
            Determines whether the Form that hosts the QuickNavigator will drop shadow or not.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.QuickNavigatorSettings.ToolPaneColumns">
            <summary>
            Determines the number of columns in the ToolWindow list.
            Defaults to zero which specifies that the number of columns is automatically calculated.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.QuickNavigatorSettings.DocumentPaneColumns">
            <summary>
            Determines the number of columns in the DocumentWindow list.
            Defaults to zero which specifies that the number of columns is automatically calculated.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.QuickNavigatorSettings.PreviewSize">
            <summary>
            Gets or sets the minimum size of the Preview element.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.QuickNavigatorSettings.MaxSize">
            <summary>
            Gets or sets the maximum size the navigator will occupy on the screen.
            Defaults to the screen bounds and will always be truncated
            to that value if the provided custom size exceeds screen's bounds.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.QuickNavigatorSettings.ListItemSize">
            <summary>
            Gets or sets the size of a single item in the pane lists.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.QuickNavigatorSettings.ItemsPerColumn">
            <summary>
            Gets or sets the number of items per column in the dock pane lists.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.QuickNavigatorSettings.ColumnSpacing">
            <summary>
            Gets or sets the spacing between columns in panes lists.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.QuickNavigatorSettings.ToolPaneListHeader">
            <summary>
            Gets or sets the text for the header of the ToolWindow list.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.QuickNavigatorSettings.DocumentPaneListHeader">
            <summary>
            Gets or sets the text for the header of the DocumentWindow list.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.QuickNavigatorSettings.ForceSnapshot">
            <summary>
            Gets or sets a value indicating whether the QuickNavigator will try to
            update the selected DockWindow's bounds, so that a preview snapshot may be taken.
            </summary>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.RadDockService">
            <summary>
            Defines base for all services registered with a RadDock instance.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDockService.#ctor">
            <summary>
            Initializes a new <see cref="T:Telerik.WinControls.UI.Docking.RadDockService">RadDockService</see> instance.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDockService.OnDockManagerChanged">
            <summary>
            The service gets notified that its current dock manager has changed.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDockService.OnEnabledChanged">
            <summary>
            Notifies for a change in the Enabled state.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.RadDockService.CanOperate">
            <summary>
            Determines whether the service is operational and may perform actions.
            </summary>
            <returns></returns>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.RadDockService.DockManagerDesignMode">
            <summary>
            Determines whether the associated RadDock instance (if any) is in design mode.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.RadDockService.DockManager">
            <summary>
            Gets the RadDock instance this service is registered with.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.RadDockService.Enabled">
            <summary>
            Determines whether the Service is enabled (may be started).
            If the Service is working and its is disabled, it will end its current operation.
            </summary>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.StateService">
            <summary>
            Represents a service that is state-based. E.g. it may start, perform some action and stop.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.StateService.#ctor">
            <summary>
            Default constructor.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.StateService.Start(System.Object)">
            <summary>
            Starts the Service.
            If the service was previously paused, it should be re-started with the Resume method.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.StateService.Stop(System.Boolean)">
            <summary>
            Stops currently working or previously stopped service.
            </summary>
            <param name="commit">True to indicate that current operation ended successfully, false otherwise.</param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.StateService.Pause">
            <summary>
            Pauses a currently running operation.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.StateService.Resume">
            <summary>
            Resumes previously paused operation.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.StateService.OnEnabledChanged">
            <summary>
            Provides additional processing for a change in the Enabled state.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.StateService.OnDockManagerChanged">
            <summary>
            Provides additional processing when a change in the owning RadDock instance occurs.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.StateService.CanStart(System.Object)">
            <summary>
            Determines whether the service may be started.
            Validation is as follows:
            1. Check whether Enabled is true.
            2. Check the context through IsContextValid method. An exception is thrown if context is invalid.
            3. Checks the current state - it should be Initial or Stopped.
            </summary>
            <param name="context"></param>
            <returns></returns>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.StateService.OnStarted">
            <summary>
            Notifies that the service has been successfully started.
            Allows inheritors to perform some additional logic upon start.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.StateService.OnStarting(Telerik.WinControls.UI.Docking.StateServiceStartingEventArgs)">
            <summary>
            Notifies that a start request has occured. Cancelable.
            </summary>
            <param name="e"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.StateService.OnStopped">
            <summary>
            Notifies that a running operation has stopped.
            Allows inheritors to perform some additional logic upon stop.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.StateService.OnStopping(Telerik.WinControls.UI.Docking.StateServiceStoppingEventArgs)">
            <summary>
            Notifies that a stop request has occured. Cancelable.
            </summary>
            <param name="e"></param>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.StateService.IsContextValid(System.Object)">
            <summary>
            Evaluates the provided context. Some services may not operate without certain context provided.
            </summary>
            <param name="context"></param>
            <returns></returns>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.StateService.PerformStart">
            <summary>
            Performs the core Start logic.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.StateService.PerformStop">
            <summary>
            Stops the service. Performs the core logic.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.StateService.Abort">
            <summary>
            Aborts the current operation without applying any changes.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.StateService.Commit">
            <summary>
            Ends the current operation and applies all changes.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.StateService.PerformResume">
            <summary>
            Performs the core Resume logic.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.StateService.PerformPause">
            <summary>
            Performs the core Pause logic.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.StateService.SetContext(System.Object)">
            <summary>
            Sets the provided object as the current context.
            </summary>
            <param name="context"></param>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.StateService.Context">
            <summary>
            Gets the context associated with the current operation.
            This member is valid only while the Service is started or paused.
            </summary>
        </member>
        <member name="E:Telerik.WinControls.UI.Docking.StateService.Starting">
            <summary>
            Raised when the service is about to be started.
            </summary>
        </member>
        <member name="E:Telerik.WinControls.UI.Docking.StateService.Started">
            <summary>
            Raised right after the service is started.
            </summary>
        </member>
        <member name="E:Telerik.WinControls.UI.Docking.StateService.Stopping">
            <summary>
            Raised when the service is about to be stopped.
            </summary>
        </member>
        <member name="E:Telerik.WinControls.UI.Docking.StateService.Stopped">
            <summary>
            Raised when the service is stopped.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.StateService.AvailableAtDesignTime">
            <summary>
            Determines whether the service is available at design-time. False by default.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.StateService.State">
            <summary>
            Gets the current state of the service.
            </summary>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.XmlDockable">
            <exclude/>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.XmlDockNode">
            <exclude/>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.XmlDockSite">
            <exclude/>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.XmlDockingManager">
            <exclude/>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.ServiceState">
            <summary>
            Defines the possible states for a RadDockService instance.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.ServiceState.Initial">
            <summary>
            The service is in its initial state.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.ServiceState.Stopped">
            <summary>
            The service has finished some operation(s) and is currently not working.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.ServiceState.Working">
            <summary>
            The service is working.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.ServiceState.Paused">
            <summary>
            The service has been started and paused while performing some operation.
            </summary>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.ServiceConstants">
            <summary>
            Defines the available constants used by a RadDock instance to store its services.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.ServiceConstants.DragDrop">
            <summary>
            Used to store the DragDropService.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.ServiceConstants.Redock">
            <summary>
            Used to store the RedockService.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.Docking.ServiceConstants.ContextMenu">
            <summary>
            Used to store the ContextMenuService
            </summary>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.StateServiceStartingEventHandler">
            <summary>
            Defines an entry point for methods that can handle events like <see cref="E:Telerik.WinControls.UI.Docking.StateService.Starting">StateService.Starting</see> one.
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.StateServiceStartingEventArgs">
            <summary>
            Represents the arguments associated with a <see cref="E:Telerik.WinControls.UI.Docking.StateService.Starting">StateService.Starting</see> event.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.StateServiceStartingEventArgs.#ctor(System.Object)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.WinControls.UI.Docking.StateServiceStartingEventArgs">StateServiceStartingEventArgs</see> class.
            </summary>
            <param name="context">The context that is passed prior to the Start request.</param>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.StateServiceStartingEventArgs.Context">
            <summary>
            Gets the Context, passed to the service as a start parameter.
            </summary>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.StateServiceStoppingEventHandler">
            <summary>
            Defines an entry point for methods that can handle events like <see cref="E:Telerik.WinControls.UI.Docking.StateService.Stopping">StateService.Stopping</see> one.
            </summary>
            <param name="sender"></param>
            <param name="e"></param>
        </member>
        <member name="T:Telerik.WinControls.UI.Docking.StateServiceStoppingEventArgs">
            <summary>
            Represents the arguments associated with a <see cref="E:Telerik.WinControls.UI.Docking.StateService.Stopping">StateService.Stopping</see> event.
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Docking.StateServiceStoppingEventArgs.#ctor(System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.WinControls.UI.Docking.StateServiceStoppingEventArgs">StateServiceStoppingEventArgs</see> class.
            </summary>
            <param name="commit"></param>
        </member>
        <member name="P:Telerik.WinControls.UI.Docking.StateServiceStoppingEventArgs.Commit">
            <summary>
            Gets or sets the Commit parameter of the Stop request.
            </summary>
        </member>
        <member name="T:Telerik.WinControls.UI.RadDockEvents">
            <summary>
            Provides static events for RadDock instances for cases where subscribing to the event
            cannot happen before they actually need to fire.
            </summary>
        </member>
        <member name="E:Telerik.WinControls.UI.RadDockEvents.PageViewInstanceCreated">
            <summary>
            Fires after RadPageViewElement is created.
            </summary>
        </member>
        <member name="E:Telerik.WinControls.UI.RadDockEvents.TabStripItemCreating">
            <summary>
            Fired when a new <see cref="T:Telerik.WinControls.UI.TabStripItem"/> is being created. Allows for replacing the tab items with custom ones.
            </summary>
        </member>
        <member name="E:Telerik.WinControls.UI.RadDockEvents.SplitContainerNeeded">
            <summary>
            Raised whenever a new <see cref="T:Telerik.WinControls.UI.RadSplitContainer">RadSplitContainer</see> instance is needed internally by the framework.
            Allows for providing custom <see cref="T:Telerik.WinControls.UI.RadSplitContainer">RadSplitContainer</see> implementation.
            </summary>
        </member>
        <member name="T:Telerik.WinControls.UI.TabStripItemCreatingEventArgs">
            <summary>
            Provides event arguments for the <see cref="E:Telerik.WinControls.UI.RadDockEvents.TabStripItemCreating"/> event.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.TabStripItemCreatingEventArgs.TabItem">
            <summary>
            Gets or sets the instance that is going to be used.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.TabStripItemCreatingEventArgs.Window">
            <summary>
            The window associated with the tab item.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.TabStripItemCreatingEventArgs.AutoHide">
            <summary>
            [true] if the tab item is going to be used in the auto-hide tab strip, [false] otherwise.
            </summary>
        </member>
        <member name="T:Telerik.WinControls.UI.OverflowDropDownButtonElement">
            <summary>
            Represents a drop-down button element with support for an additional overflow image.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.OverflowDropDownButtonElement.OverflowImageProperty">
            <summary>
            A property to specify an additional image to be displayed when <see cref="P:Telerik.WinControls.UI.OverflowDropDownButtonElement.OverflowMode">OverflowMode</see> is true.
            </summary>
        </member>
        <member name="F:Telerik.WinControls.UI.OverflowDropDownButtonElement.OverflowModeProperty">
            <summary>
            A property to determine whether a <see cref="T:Telerik.WinControls.UI.OverflowDropDownButtonElement">OverflowDropDownButtonElement</see> instance is currently in overflow mode.
            </summary>
        </member>
        <member name="P:Telerik.WinControls.UI.OverflowDropDownButtonElement.OverflowImage">
            <summary>
             Gets or sets the overflow image that is displayed on a button element.
             </summary>		
        </member>
        <member name="P:Telerik.WinControls.UI.OverflowDropDownButtonElement.OverflowMode">
            <summary>
            Gets or sets the drop down button is in overflow mode
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.OverflowDropDownButtonElement.OnPropertyChanged(Telerik.WinControls.RadPropertyChangedEventArgs)">
            <summary>
            Provides additional logic for synchronizing the overflow image.
            </summary>
            <param name="e"></param>
        </member>
        <member name="T:Telerik.WinControls.UI.Localization.RadDockLocalizationProvider">
            <summary>
            Provides localization services for RadDock
            </summary>
        </member>
        <member name="M:Telerik.WinControls.UI.Localization.RadDockLocalizationProvider.GetLocalizedString(System.String)">
            <summary>
            Gets the string corresponding to the given ID.
            </summary>
            <param name="id">String ID</param>
            <returns>The string corresponding to the given ID.</returns>
        </member>
        <member name="T:Telerik.WinControls.UI.Localization.RadDockStringId">
            <summary>
            Contains the predefined Ids to be used by the Localization Provider.
            </summary>
        </member>
    </members>
</doc>
