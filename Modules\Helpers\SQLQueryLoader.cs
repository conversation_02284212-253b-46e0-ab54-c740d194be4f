using System;
using System.Collections.Concurrent;
using System.Diagnostics;
using System.IO;
using System.Threading;

namespace ProManage.Modules.Helpers
{
    /// <summary>
    /// Utility class for loading SQL queries from files in the Modules/Procedures folder.
    /// Provides caching to avoid repeated file reads and methods to load queries by name/path.
    /// </summary>
    public static class SQLQueryLoader
    {
        // Cache to store loaded SQL queries
        private static readonly ConcurrentDictionary<string, string> _queryCache = new ConcurrentDictionary<string, string>();

        // Cache to store extracted named queries
        private static readonly ConcurrentDictionary<string, string> _namedQueryCache = new ConcurrentDictionary<string, string>();

        // Cache statistics
        private static int _cacheHits = 0;
        private static int _cacheMisses = 0;

        // Base directory for SQL query files
        private static readonly string _baseDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Modules", "Procedures");

        /// <summary>
        /// Loads a SQL query from a file in the Modules/Procedures folder.
        /// </summary>
        /// <param name="moduleName">The module name (subfolder in Modules/Procedures)</param>
        /// <param name="queryName">The query file name without extension</param>
        /// <returns>The SQL query text</returns>
        public static string LoadQuery(string moduleName, string queryName)
        {
            // Construct the full path to the query file
            string queryPath = Path.Combine(_baseDirectory, moduleName, $"{queryName}.sql");
            return LoadQueryFromPath(queryPath);
        }

        /// <summary>
        /// Loads a SQL query from a specific file path.
        /// </summary>
        /// <param name="filePath">The full path to the SQL query file</param>
        /// <returns>The SQL query text</returns>
        public static string LoadQueryFromPath(string filePath)
        {
            try
            {
                // Check if the query is already in the cache
                if (_queryCache.TryGetValue(filePath, out string cachedQuery))
                {
                    Interlocked.Increment(ref _cacheHits);
                    Debug.WriteLine($"Using cached SQL query: {filePath}");
                    return cachedQuery;
                }

                Interlocked.Increment(ref _cacheMisses);

                // Check if the file exists
                if (!File.Exists(filePath))
                {
                    throw new FileNotFoundException($"SQL query file not found: {filePath}");
                }

                // Read the query from the file
                Debug.WriteLine($"Loading SQL query from file: {filePath}");
                string query = File.ReadAllText(filePath);

                // Add the query to the cache
                _queryCache[filePath] = query;

                return query;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error loading SQL query: {ex.Message}");
                throw new Exception($"Failed to load SQL query from {filePath}: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Loads a named query from a file that contains multiple queries.
        /// This is an alias for ExtractNamedQuery to maintain API consistency.
        /// </summary>
        /// <param name="moduleName">The module name (subfolder in Modules/Procedures)</param>
        /// <param name="queryFileName">The query file name without extension</param>
        /// <param name="queryName">The specific query name to extract</param>
        /// <returns>The extracted SQL query</returns>
        public static string LoadNamedQuery(string moduleName, string queryFileName, string queryName)
        {
            return ExtractNamedQuery(moduleName, queryFileName, queryName);
        }

        /// <summary>
        /// Extracts a specific query from a file that may contain multiple queries.
        /// Looks for a query between markers like "-- [QueryName]" and the next marker or end of file.
        /// </summary>
        /// <param name="moduleName">The module name (subfolder in Modules/Procedures)</param>
        /// <param name="queryFileName">The query file name without extension</param>
        /// <param name="queryName">The specific query name to extract</param>
        /// <returns>The extracted SQL query</returns>
        public static string ExtractNamedQuery(string moduleName, string queryFileName, string queryName)
        {
            // Create a cache key for this named query
            string cacheKey = $"{moduleName}_{queryFileName}_{queryName}";

            // Check if the named query is already in the cache
            if (_namedQueryCache.TryGetValue(cacheKey, out string cachedQuery))
            {
                Interlocked.Increment(ref _cacheHits);
                Debug.WriteLine($"Using cached named query: {cacheKey}");
                return cachedQuery;
            }

            Interlocked.Increment(ref _cacheMisses);

            // Load the full query file
            string fullQuery = LoadQuery(moduleName, queryFileName);

            // Look for the query marker
            string markerStart = $"-- [{queryName}]";
            int startIndex = fullQuery.IndexOf(markerStart);

            if (startIndex < 0)
            {
                throw new ArgumentException($"Query '{queryName}' not found in file {queryFileName}");
            }

            // Move past the marker
            startIndex += markerStart.Length;

            // Find the next marker or use the end of the file
            int endIndex = fullQuery.IndexOf("-- [", startIndex);
            if (endIndex < 0)
            {
                endIndex = fullQuery.Length;
            }

            // Extract the query
            string extractedQuery = fullQuery.Substring(startIndex, endIndex - startIndex).Trim();

            // Add to the named query cache
            _namedQueryCache[cacheKey] = extractedQuery;

            return extractedQuery;
        }

        /// <summary>
        /// Clears the query cache to force reloading from files.
        /// </summary>
        public static void ClearCache()
        {
            _queryCache.Clear();
            _namedQueryCache.Clear();
            Interlocked.Exchange(ref _cacheHits, 0);
            Interlocked.Exchange(ref _cacheMisses, 0);
            Debug.WriteLine("SQL query caches cleared and statistics reset");
        }

        /// <summary>
        /// Gets cache statistics for monitoring and diagnostics.
        /// </summary>
        /// <returns>A string containing cache statistics</returns>
        public static string GetCacheStatistics()
        {
            int hits = _cacheHits;
            int misses = _cacheMisses;
            int total = hits + misses;
            double hitRate = total > 0 ? (double)hits / total * 100 : 0;

            return $"Cache Statistics:\n" +
                   $"  File Queries Cached: {_queryCache.Count}\n" +
                   $"  Named Queries Cached: {_namedQueryCache.Count}\n" +
                   $"  Cache Hits: {hits}\n" +
                   $"  Cache Misses: {misses}\n" +
                   $"  Hit Rate: {hitRate:F2}%";
        }

        /// <summary>
        /// Preloads all SQL queries from a specific module folder.
        /// </summary>
        /// <param name="moduleName">The module name (subfolder in Modules/Procedures)</param>
        /// <returns>The number of queries preloaded</returns>
        public static int PreloadModuleQueries(string moduleName)
        {
            try
            {
                string moduleDir = Path.Combine(_baseDirectory, moduleName);

                if (!Directory.Exists(moduleDir))
                {
                    Debug.WriteLine($"Module directory not found: {moduleDir}");
                    return 0;
                }

                // Get all SQL files in the module directory
                string[] sqlFiles = Directory.GetFiles(moduleDir, "*.sql");

                Debug.WriteLine($"Preloading {sqlFiles.Length} SQL queries from module: {moduleName}");

                int loadedCount = 0;
                int errorCount = 0;

                // Load each file into the cache
                foreach (string filePath in sqlFiles)
                {
                    try
                    {
                        LoadQueryFromPath(filePath);
                        loadedCount++;

                        // Also try to preload any named queries if the file contains markers
                        string query = _queryCache[filePath];
                        PreloadNamedQueriesFromContent(moduleName, Path.GetFileNameWithoutExtension(filePath), query);
                    }
                    catch (Exception ex)
                    {
                        errorCount++;
                        Debug.WriteLine($"Error preloading query file {filePath}: {ex.Message}");
                    }
                }

                Debug.WriteLine($"Preloaded {loadedCount} SQL queries from module: {moduleName} (Errors: {errorCount})");
                return loadedCount;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error preloading module queries: {ex.Message}");
                return 0;
            }
        }

        /// <summary>
        /// Preloads named queries from a query file content.
        /// </summary>
        /// <param name="moduleName">The module name</param>
        /// <param name="queryFileName">The query file name without extension</param>
        /// <param name="content">The content of the query file</param>
        private static void PreloadNamedQueriesFromContent(string moduleName, string queryFileName, string content)
        {
            try
            {
                // Find all query markers in the content
                int index = 0;
                int preloadedCount = 0;

                while ((index = content.IndexOf("-- [", index)) >= 0)
                {
                    // Find the end of the marker
                    int endMarker = content.IndexOf("]", index);
                    if (endMarker > index)
                    {
                        // Extract the query name
                        string queryName = content.Substring(index + 4, endMarker - index - 4);

                        try
                        {
                            // Preload this named query
                            string cacheKey = $"{moduleName}_{queryFileName}_{queryName}";
                            if (!_namedQueryCache.ContainsKey(cacheKey))
                            {
                                ExtractNamedQuery(moduleName, queryFileName, queryName);
                                preloadedCount++;
                            }
                        }
                        catch (Exception ex)
                        {
                            Debug.WriteLine($"Error preloading named query '{queryName}': {ex.Message}");
                        }
                    }

                    // Move past this marker
                    index = endMarker + 1;
                }

                if (preloadedCount > 0)
                {
                    Debug.WriteLine($"Preloaded {preloadedCount} named queries from {moduleName}/{queryFileName}.sql");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error preloading named queries: {ex.Message}");
            }
        }
    }
}
