<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Telerik.Windows.Documents.Flow.FormatProviders.Pdf</name>
    </assembly>
    <members>
        <member name="T:Telerik.Windows.Documents.Flow.FormatProviders.Pdf.Export.NumberingFieldsPrecisionLevel">
            <summary>
            Represents precision level when updating numbering fields.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Flow.FormatProviders.Pdf.Export.NumberingFieldsPrecisionLevel.Normal">
            <summary>
            Represents the normal level. The fields are updated once, not taking into account if their new values would have shifted the already measured layout. In such cases, the results could be outdated. This is the MS Word-like behavior. If you need more accurate results, use NumberingFieldsPrecisionLevel.High.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Flow.FormatProviders.Pdf.Export.NumberingFieldsPrecisionLevel.High">
            <summary>
            Represents the high level. The fields are updated until their values become more accurate. This precision level is more accurate than NumberingFieldsPrecisionLevel.Normal but requires more resources.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Flow.FormatProviders.Pdf.Export.PdfExportSettings">
            <summary>
            Represents PDF export settings.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Flow.FormatProviders.Pdf.Export.PdfExportSettings.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Flow.FormatProviders.Pdf.Export.PdfExportSettings" /> class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Flow.FormatProviders.Pdf.Export.PdfExportSettings.ExtensibilityManager">
            <summary>
            Gets the extensibility manager.
            </summary>
            <value>The extensibility manager.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Flow.FormatProviders.Pdf.Export.PdfExportSettings.NumberingFieldsPrecision">
            <summary>
            Gets or sets the precision level when updating numbering fields.
            </summary>
            <value>The precision level.</value>
        </member>
        <member name="E:Telerik.Windows.Documents.Flow.FormatProviders.Pdf.Export.PdfExportSettings.DocumentUnhandledException">
            <summary>
            Occurs when unhandled exception is thrown during document import.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Flow.FormatProviders.Pdf.Export.PdfExportSettings.Telerik#Windows#Documents#Fixed#FormatProviders#Pdf#ExceptionHandling#IOnExceptionEventRaiser#CanRaiseEvent">
            <summary>
            Determines whether the object can raise an exception event.
            </summary>
            <returns>True if the object can raise an exception event; otherwise, false.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Flow.FormatProviders.Pdf.Export.PdfExportSettings.Telerik#Windows#Documents#Fixed#FormatProviders#Pdf#ExceptionHandling#IOnExceptionEventRaiser#RaiseEvent(Telerik.Windows.Documents.Fixed.Model.DocumentUnhandledExceptionEventArgs)">
            <summary>
            Raises the <see cref="T:Telerik.Windows.Documents.Fixed.Model.DocumentUnhandledExceptionEventArgs"/> event.
            </summary>
            <param name="args">The <see cref="T:Telerik.Windows.Documents.Fixed.Model.DocumentUnhandledExceptionEventArgs"/> containing the event data.</param>
        </member>
        <member name="T:Telerik.Windows.Documents.Flow.FormatProviders.Pdf.PdfFormatProvider">
            <summary>
            Represents PDF format provider.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Flow.FormatProviders.Pdf.PdfFormatProvider.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Flow.FormatProviders.Pdf.PdfFormatProvider" /> class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Flow.FormatProviders.Pdf.PdfFormatProvider.ExportSettings">
            <summary>
            Gets or sets the export settings.
            </summary>
            <value>The export settings.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Flow.FormatProviders.Pdf.PdfFormatProvider.CanExport">
            <summary>
            Gets a value indicating whether format provider can export.
            </summary>
            <value>The value indicating whether can export.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Flow.FormatProviders.Pdf.PdfFormatProvider.CanImport">
            <summary>
            Gets a value indicating whether format provider can import.
            </summary>
            <value>The value indicating whether can import.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Flow.FormatProviders.Pdf.PdfFormatProvider.SupportedExtensions">
            <summary>
            Gets the supported extensions.
            </summary>
            <value>The supported extensions.</value>
        </member>
        <member name="M:Telerik.Windows.Documents.Flow.FormatProviders.Pdf.PdfFormatProvider.ExportToFixedDocument(Telerik.Windows.Documents.Flow.Model.RadFlowDocument)">
            <summary>
            Exports to fixed document.
            </summary>
            <param name="document">The document.</param>
            <returns>The fixed document.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Flow.FormatProviders.Pdf.PdfFormatProvider.ExportToFixedDocument(Telerik.Windows.Documents.Flow.Model.RadFlowDocument,System.Nullable{System.TimeSpan})">
            <summary>
            Exports to fixed document.
            </summary>
            <param name="document">The document.</param>
            <param name="timeout">The timeout after which the operation will be cancelled.</param>
            <returns>The fixed document.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Flow.FormatProviders.Pdf.PdfFormatProvider.ExportOverride(Telerik.Windows.Documents.Flow.Model.RadFlowDocument,System.IO.Stream)">
            <summary>
            Exports the specified document.
            </summary>
            <param name="document">The document.</param>
            <param name="output">The output.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Flow.FormatProviders.Pdf.PdfFormatProvider.ExportOverride(Telerik.Windows.Documents.Flow.Model.RadFlowDocument,System.IO.Stream,System.Threading.CancellationToken)">
            <summary>
            Exports the specified document.
            </summary>
            <param name="document">The document.</param>
            <param name="output">The output.</param>
            <param name="cancellationToken">The cancellation token used to cancel the operation.</param>
        </member>
        <member name="T:Telerik.Windows.Documents.Flow.FormatProviders.Pdf.Utils.ExtensibilityManager">
            <summary>
            Provides methods for extending the functionality of the PdfFormatProvider.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Flow.FormatProviders.Pdf.Utils.ExtensibilityManager.RegisterNumberingStyleConverter(Telerik.Windows.Documents.Flow.Model.Lists.NumberingStyle,Telerik.Windows.Documents.Flow.FormatProviders.Pdf.Utils.INumberingStyleConverter)">
            <summary>
            Registers the numbering style converter.
            </summary>
            <param name="numberingStyle">The numbering style.</param>
            <param name="converter">The converter.</param>
        </member>
        <member name="T:Telerik.Windows.Documents.Flow.FormatProviders.Pdf.Utils.INumberingStyleConverter">
            <summary>
            Represents a numbering style converter.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Flow.FormatProviders.Pdf.Utils.INumberingStyleConverter.ConvertNumberToText(System.Int32)">
            <summary>
            Converts the number to text.
            </summary>
            <param name="number">The number.</param>
            <returns>The text representation of the given number.</returns>
        </member>
        <member name="T:Telerik.Windows.Documents.Flow.FormatProviders.Pdf.NumberingFieldsProvider">
            <summary>
            Represents fields` page numbering provider.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Flow.FormatProviders.Pdf.NumberingFieldsProvider.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Flow.FormatProviders.Pdf.NumberingFieldsProvider" /> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Flow.FormatProviders.Pdf.NumberingFieldsProvider.RegisterNumberingStyleConverter(Telerik.Windows.Documents.Flow.Model.Lists.NumberingStyle,Telerik.Windows.Documents.Flow.FormatProviders.Pdf.Utils.INumberingStyleConverter)">
            <summary>
            Registers the numbering style converter.
            </summary>
            <param name="numberingStyle">The numbering style.</param>
            <param name="converter">The converter.</param>
        </member>
    </members>
</doc>
