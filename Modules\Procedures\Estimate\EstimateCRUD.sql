-- EstimateCRUD.sql
-- Consolidated CRUD operations for estimates using UPSERT approach
-- Replaces: SaveEstimate.sql with improved logic
--
-- Uses PostgreSQL UPSERT (INSERT ... ON CONFLICT) for efficient save operations

-- [UpsertHeader] --
-- Insert new estimate or update existing one
INSERT INTO estimateheader (
    id,
    estimate_no,
    customer_name,
    vin,
    brand,
    date,
    location,
    vehicle_model,
    salesman_name,
    status,
    created_at,
    modify_at,
    remarks
) VALUES (
    @estimate_id,
    @estimate_number,
    @customer_name,
    @vehicle_vin,
    @vehicle_brand,
    @estimate_date,
    @location,
    @vehicle_model,
    @salesman_name,
    @status,
    COALESCE(@created_date, CURRENT_TIMESTAMP),
    CURRENT_TIMESTAMP,
    @remarks
)
ON CONFLICT (id)
DO UPDATE SET
    estimate_no = EXCLUDED.estimate_no,
    customer_name = EXCLUDED.customer_name,
    vin = EXCLUDED.vin,
    brand = EXCLUDED.brand,
    date = EXCLUDED.date,
    location = EXCLUDED.location,
    vehicle_model = EXCLUDED.vehicle_model,
    salesman_name = EXCLUDED.salesman_name,
    status = EXCLUDED.status,
    modify_at = CURRENT_TIMESTAMP,
    remarks = EXCLUDED.remarks
RETURNING id, estimate_no;
-- [End] --

-- [InsertHeader] --
-- Insert new estimate header (for new estimates without ID)
INSERT INTO estimateheader (
    estimate_no,
    customer_name,
    vin,
    brand,
    date,
    location,
    vehicle_model,
    salesman_name,
    status,
    created_at,
    modify_at,
    remarks
) VALUES (
    @estimate_number,
    @customer_name,
    @vehicle_vin,
    @vehicle_brand,
    @estimate_date,
    @location,
    @vehicle_model,
    @salesman_name,
    @status,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP,
    @remarks
)
RETURNING id, estimate_no;
-- [End] --

-- [UpdateHeader] --
-- Update existing estimate header
UPDATE estimateheader
SET
    estimate_no = @estimate_number,
    customer_name = @customer_name,
    vin = @vehicle_vin,
    brand = @vehicle_brand,
    date = @estimate_date,
    location = @location,
    vehicle_model = @vehicle_model,
    salesman_name = @salesman_name,
    status = @status,
    modify_at = CURRENT_TIMESTAMP,
    remarks = @remarks
WHERE
    id = @estimate_id
RETURNING id, estimate_no;
-- [End] --

-- [DeleteAllDetails] --
-- Delete all details for an estimate (used before inserting new details)
DELETE FROM estimatedetails
WHERE estimate_id = @estimate_id;
-- [End] --

-- [InsertDetail] --
-- Insert a single estimate detail
INSERT INTO estimatedetails (
    estimate_id,
    part_no,
    description,
    qty,
    oe_price,
    afm_price,
    remarks,
    approve_status,
    created_at,
    modify_at
) VALUES (
    @estimate_id,
    @part_no,
    @description,
    @qty,
    @oe_price,
    @afm_price,
    @remarks,
    @approve_status,
    CURRENT_TIMESTAMP,
    CURRENT_TIMESTAMP
)
RETURNING id;
-- [End] --

-- [UpsertDetail] --
-- Insert or update estimate detail
INSERT INTO estimatedetails (
    id,
    estimate_id,
    part_no,
    description,
    qty,
    oe_price,
    afm_price,
    remarks,
    approve_status,
    created_at,
    modify_at
) VALUES (
    @detail_id,
    @estimate_id,
    @part_no,
    @description,
    @qty,
    @oe_price,
    @afm_price,
    @remarks,
    @approve_status,
    COALESCE(@created_at, CURRENT_TIMESTAMP),
    CURRENT_TIMESTAMP
)
ON CONFLICT (id)
DO UPDATE SET
    part_no = EXCLUDED.part_no,
    description = EXCLUDED.description,
    qty = EXCLUDED.qty,
    oe_price = EXCLUDED.oe_price,
    afm_price = EXCLUDED.afm_price,
    remarks = EXCLUDED.remarks,
    approve_status = EXCLUDED.approve_status,
    modify_at = CURRENT_TIMESTAMP
RETURNING id;
-- [End] --
