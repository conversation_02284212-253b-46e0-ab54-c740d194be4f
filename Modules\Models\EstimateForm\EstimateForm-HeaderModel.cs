using System;
using System.Collections.Generic;

namespace ProManage.Modules.Models.EstimateForm
{
    /// <summary>
    /// Data model for estimate header information
    /// </summary>
    public class EstimateFormHeaderModel
    {
        /// <summary>
        /// Primary key ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// Estimate number (unique)
        /// </summary>
        public string EstimateNo { get; set; }

        /// <summary>
        /// Customer name
        /// </summary>
        public string CustomerName { get; set; }

        /// <summary>
        /// Vehicle Identification Number
        /// </summary>
        public string VIN { get; set; }

        /// <summary>
        /// Vehicle brand
        /// </summary>
        public string Brand { get; set; }

        /// <summary>
        /// Document date (maps to 'date' in database)
        /// </summary>
        public DateTime? DocDate { get; set; }

        /// <summary>
        /// Location
        /// </summary>
        public string Location { get; set; }

        /// <summary>
        /// Vehicle model
        /// </summary>
        public string VehicleModel { get; set; }

        /// <summary>
        /// Salesman name
        /// </summary>
        public string SalesmanName { get; set; }

        /// <summary>
        /// Created date (maps to 'created_at' in database)
        /// </summary>
        public DateTime? CreatedAt { get; set; }

        /// <summary>
        /// Created by user (maps to 'created_by' in database)
        /// </summary>
        public string CreatedBy { get; set; }

        /// <summary>
        /// Modified date (maps to 'modify_at' in database)
        /// </summary>
        public DateTime? ModifyAt { get; set; }

        /// <summary>
        /// Remarks (maps to 'remarks' in database)
        /// </summary>
        public string Remarks { get; set; }

        /// <summary>
        /// Status of the estimate (True = Open/Active/Editable, False = Closed/Locked)
        /// Maps to 'status' in database
        /// </summary>
        public bool Status { get; set; } = true;

        /// <summary>
        /// List of detail items
        /// </summary>
        public List<EstimateFormDetailModel> Details { get; set; }

        /// <summary>
        /// Constructor
        /// </summary>
        public EstimateFormHeaderModel()
        {
            Details = new List<EstimateFormDetailModel>();
        }
    }
}
