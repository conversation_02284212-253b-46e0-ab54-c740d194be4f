﻿using System;
using System.Collections;
using System.ComponentModel;
using System.Drawing;
using System.Data;
using System.Diagnostics;
using DevExpress.XtraReports.UI;
using ProManage.Modules.Models.EstimateForm;
using System.Collections.Generic;
using System.Linq;

namespace ProManage.Reports
{
    public partial class EstimatePrint : DevExpress.XtraReports.UI.XtraReport
    {
        public EstimatePrint()
        {
            InitializeComponent();
        }

        /// <summary>
        /// Populates the report with estimate data
        /// </summary>
        /// <param name="headerData">Estimate header information</param>
        /// <param name="detailData">List of estimate detail items</param>
        public void PopulateReportData(EstimateFormHeaderModel headerData, List<EstimateFormDetailModel> detailData)
        {
            try
            {
                Debug.WriteLine("=== PopulateReportData: Starting ===");

                // Populate header information
                PopulateHeaderData(headerData);

                // Populate detail data
                PopulateDetailData(detailData);

                // Calculate and populate totals
                PopulateTotals(detailData);

                Debug.WriteLine("=== PopulateReportData: Completed ===");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in PopulateReportData: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Populates the header section with estimate information
        /// </summary>
        private void PopulateHeaderData(EstimateFormHeaderModel headerData)
        {
            try
            {
                if (headerData == null)
                {
                    Debug.WriteLine("Header data is null, using default values");
                    return;
                }

                // Customer information
                customerName.Text = headerData.CustomerName ?? "N/A";
                customerAddress.Text = headerData.VIN ?? "N/A";
                xrTableCell1.Text = $"{headerData.Brand ?? ""} {headerData.VehicleModel ?? ""}".Trim();
                if (string.IsNullOrWhiteSpace(xrTableCell1.Text))
                    xrTableCell1.Text = "N/A";

                // Estimate information
                invoiceNumber.Text = headerData.EstimateNo ?? "N/A";
                invoiceDate.Text = headerData.DocDate?.ToString("dd MMM yyyy") ?? DateTime.Now.ToString("dd MMM yyyy");

                // Valid till (72 hours after estimate date)
                var validTillDate = (headerData.DocDate ?? DateTime.Now).AddHours(72);
                total2.Text = validTillDate.ToString("dd MMM yyyy");

                // Salesman and location information
                vendorName.Text = headerData.SalesmanName ?? "N/A";
                vendorPhone.Text = headerData.Location ?? "N/A";
                vendorAddress.Text = "ProManage System"; // Company info

                // Remarks
                thankYouLabel.Text = !string.IsNullOrWhiteSpace(headerData.Remarks)
                    ? headerData.Remarks
                    : "Thank you for your business!";

                Debug.WriteLine($"Header data populated - Customer: {customerName.Text}, Estimate: {invoiceNumber.Text}");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in PopulateHeaderData: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Populates the detail section with line items
        /// </summary>
        private void PopulateDetailData(List<EstimateFormDetailModel> detailData)
        {
            try
            {
                if (detailData == null || !detailData.Any())
                {
                    Debug.WriteLine("No detail data available");
                    // Create a DataTable with no rows to clear the detail section
                    var emptyTable = CreateDetailDataTable();
                    this.DataSource = emptyTable;
                    return;
                }

                // Create DataTable for detail binding
                var detailTable = CreateDetailDataTable();

                foreach (var detail in detailData)
                {
                    var row = detailTable.NewRow();
                    row["ProductName"] = detail.PartNo ?? "";
                    row["ProductDescription"] = detail.Description ?? "";
                    row["Quantity"] = detail.Qty;
                    row["UnitPrice"] = detail.AFMPrice; // Using AFM price as primary
                    row["LineTotal"] = detail.Qty * detail.AFMPrice;
                    detailTable.Rows.Add(row);
                }

                // Bind the data to the report
                this.DataSource = detailTable;

                Debug.WriteLine($"Detail data populated with {detailData.Count} items");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in PopulateDetailData: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Creates a DataTable structure for detail data binding
        /// </summary>
        private DataTable CreateDetailDataTable()
        {
            var table = new DataTable();
            table.Columns.Add("ProductName", typeof(string));
            table.Columns.Add("ProductDescription", typeof(string));
            table.Columns.Add("Quantity", typeof(decimal));
            table.Columns.Add("UnitPrice", typeof(decimal));
            table.Columns.Add("LineTotal", typeof(decimal));
            return table;
        }

        /// <summary>
        /// Calculates and populates the totals section
        /// </summary>
        private void PopulateTotals(List<EstimateFormDetailModel> detailData)
        {
            try
            {
                decimal subtotalAmount = 0;
                decimal taxAmount = 0;
                decimal totalAmount = 0;

                if (detailData != null && detailData.Any())
                {
                    // Calculate subtotal - handle nullable values
                    subtotalAmount = detailData.Sum(d => (d.Qty ?? 0) * (d.AFMPrice ?? 0m));

                    // Calculate tax (5% VAT - can be made configurable later)
                    taxAmount = subtotalAmount * 0.05m;

                    // Calculate total
                    totalAmount = subtotalAmount + taxAmount;
                }

                // Populate total controls
                subtotal.Text = $"AED {subtotalAmount:F2}";
                tax.Text = $"AED {taxAmount:F2}";
                total.Text = $"AED {totalAmount:F2}";

                Debug.WriteLine($"Totals calculated - Subtotal: {subtotalAmount:F2}, Tax: {taxAmount:F2}, Total: {totalAmount:F2}");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in PopulateTotals: {ex.Message}");
                throw;
            }
        }
    }
}
