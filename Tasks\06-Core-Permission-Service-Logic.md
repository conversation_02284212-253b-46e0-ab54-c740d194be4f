# Task 06: Core Permission Service Logic

## Objective
Implement the main permission service that provides the core logic for checking user permissions. This service implements the 2-level permission system (role permissions + user overrides) and serves as the primary interface for all permission checks throughout the application.

## Priority
**CORE SERVICES** - Depends on Tasks 01-05

## Estimated Time
2 hours

## Dependencies
- Task 01: Database Schema Verification and Setup
- Task 02: Permission Data Models Creation
- Task 03: Forms Configuration Setup
- Task 04: Database Connection Service for Permissions
- Task 05: Form Discovery Service Implementation

## Files to Create
- `Modules/Services/PermissionService.cs`
- `Modules/Services/PermissionCache.cs`

## Core Permission Service Implementation

### PermissionService.cs
```csharp
using System;
using System.Collections.Generic;
using System.Linq;
using ProManage.Modules.Connections;
using ProManage.Modules.Models;

namespace ProManage.Modules.Services
{
    public static class PermissionService
    {
        private static readonly PermissionDatabaseService _dbService = new PermissionDatabaseService();
        private static readonly PermissionCache _cache = new PermissionCache();
        
        #region Core Permission Checking
        
        /// <summary>
        /// Check if user has specific permission for a form
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="formName">Form name</param>
        /// <param name="permissionType">Permission type (read, new, edit, delete, print)</param>
        /// <returns>True if user has permission</returns>
        public static bool HasPermission(int userId, string formName, PermissionType permissionType)
        {
            try
            {
                // Check cache first
                var cacheKey = $"{userId}_{formName}_{permissionType}";
                if (_cache.TryGetPermission(cacheKey, out bool cachedResult))
                {
                    return cachedResult;
                }
                
                // 1. Check user override (NULL = inherit from role)
                var userPermission = GetUserPermissionValue(userId, formName, permissionType);
                if (userPermission.HasValue)
                {
                    _cache.SetPermission(cacheKey, userPermission.Value);
                    return userPermission.Value;
                }
                
                // 2. Check role permission
                var rolePermission = GetRolePermissionValue(userId, formName, permissionType);
                _cache.SetPermission(cacheKey, rolePermission);
                return rolePermission;
            }
            catch (Exception ex)
            {
                // Log error and deny permission for security
                System.Diagnostics.Debug.WriteLine($"Permission check error: {ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// Check if user has global permission (user management)
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="permissionType">Global permission type</param>
        /// <returns>True if user has global permission</returns>
        public static bool HasGlobalPermission(int userId, GlobalPermissionType permissionType)
        {
            try
            {
                // Check cache first
                var cacheKey = $"global_{userId}_{permissionType}";
                if (_cache.TryGetPermission(cacheKey, out bool cachedResult))
                {
                    return cachedResult;
                }
                
                var globalPermissions = _dbService.GetGlobalPermissions(userId);
                if (globalPermissions == null)
                {
                    _cache.SetPermission(cacheKey, false);
                    return false;
                }
                
                bool result = permissionType switch
                {
                    GlobalPermissionType.CanCreateUsers => globalPermissions.CanCreateUsers,
                    GlobalPermissionType.CanEditUsers => globalPermissions.CanEditUsers,
                    GlobalPermissionType.CanDeleteUsers => globalPermissions.CanDeleteUsers,
                    GlobalPermissionType.CanPrintUsers => globalPermissions.CanPrintUsers,
                    _ => false
                };
                
                _cache.SetPermission(cacheKey, result);
                return result;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Global permission check error: {ex.Message}");
                return false;
            }
        }
        
        #endregion
        
        #region Permission Resolution
        
        /// <summary>
        /// Get user permission value (returns null if inherit from role)
        /// </summary>
        private static bool? GetUserPermissionValue(int userId, string formName, PermissionType permissionType)
        {
            var userPermission = _dbService.GetUserPermission(userId, formName);
            if (userPermission == null) return null;
            
            return permissionType switch
            {
                PermissionType.Read => userPermission.ReadPermission,
                PermissionType.New => userPermission.NewPermission,
                PermissionType.Edit => userPermission.EditPermission,
                PermissionType.Delete => userPermission.DeletePermission,
                PermissionType.Print => userPermission.PrintPermission,
                _ => null
            };
        }
        
        /// <summary>
        /// Get role permission value
        /// </summary>
        private static bool GetRolePermissionValue(int userId, string formName, PermissionType permissionType)
        {
            // First get user's role
            var user = GetUserWithRole(userId);
            if (user == null) return false;
            
            var rolePermission = _dbService.GetRolePermission(user.RoleId, formName);
            if (rolePermission == null) return false;
            
            return permissionType switch
            {
                PermissionType.Read => rolePermission.ReadPermission,
                PermissionType.New => rolePermission.NewPermission,
                PermissionType.Edit => rolePermission.EditPermission,
                PermissionType.Delete => rolePermission.DeletePermission,
                PermissionType.Print => rolePermission.PrintPermission,
                _ => false
            };
        }
        
        /// <summary>
        /// Get user with role information (implement based on existing user service)
        /// </summary>
        private static UserWithRole GetUserWithRole(int userId)
        {
            // This should integrate with existing user management
            // For now, return a placeholder - will be implemented in integration phase
            return new UserWithRole { UserId = userId, RoleId = 1 }; // TODO: Implement
        }
        
        #endregion
        
        #region Bulk Permission Operations
        
        /// <summary>
        /// Get all effective permissions for a user
        /// </summary>
        public static List<EffectivePermission> GetUserEffectivePermissions(int userId)
        {
            try
            {
                var effectivePermissions = new List<EffectivePermission>();
                var allForms = FormsConfigurationService.GetAllForms();
                
                foreach (var form in allForms)
                {
                    var permission = new EffectivePermission
                    {
                        FormName = form.FormName,
                        ReadPermission = HasPermission(userId, form.FormName, PermissionType.Read),
                        NewPermission = HasPermission(userId, form.FormName, PermissionType.New),
                        EditPermission = HasPermission(userId, form.FormName, PermissionType.Edit),
                        DeletePermission = HasPermission(userId, form.FormName, PermissionType.Delete),
                        PrintPermission = HasPermission(userId, form.FormName, PermissionType.Print),
                        Source = GetPermissionSource(userId, form.FormName)
                    };
                    
                    effectivePermissions.Add(permission);
                }
                
                return effectivePermissions;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting effective permissions: {ex.Message}");
                return new List<EffectivePermission>();
            }
        }
        
        /// <summary>
        /// Get list of forms user can access (has read permission)
        /// </summary>
        public static List<string> GetVisibleForms(int userId)
        {
            try
            {
                var visibleForms = new List<string>();
                var allForms = FormsConfigurationService.GetAllForms();
                
                foreach (var form in allForms)
                {
                    if (HasPermission(userId, form.FormName, PermissionType.Read))
                    {
                        visibleForms.Add(form.FormName);
                    }
                }
                
                return visibleForms;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error getting visible forms: {ex.Message}");
                return new List<string>();
            }
        }
        
        /// <summary>
        /// Determine if permission comes from role or user override
        /// </summary>
        private static PermissionSource GetPermissionSource(int userId, string formName)
        {
            var userPermission = _dbService.GetUserPermission(userId, formName);
            return userPermission != null ? PermissionSource.UserOverride : PermissionSource.Role;
        }
        
        #endregion
        
        #region Permission Updates
        
        /// <summary>
        /// Update role permissions
        /// </summary>
        public static bool UpdateRolePermissions(int roleId, List<RolePermissionUpdate> updates)
        {
            try
            {
                var success = _dbService.UpdateRolePermissions(updates);
                if (success)
                {
                    // Clear cache for affected users
                    _cache.ClearRolePermissions(roleId);
                }
                return success;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error updating role permissions: {ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// Update user permission overrides
        /// </summary>
        public static bool UpdateUserPermissions(int userId, List<UserPermissionUpdate> updates)
        {
            try
            {
                var success = _dbService.UpdateUserPermissions(updates);
                if (success)
                {
                    // Clear cache for user
                    _cache.ClearUserPermissions(userId);
                }
                return success;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error updating user permissions: {ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// Update global permissions
        /// </summary>
        public static bool UpdateGlobalPermissions(int userId, GlobalPermissionUpdate update)
        {
            try
            {
                var success = _dbService.UpdateGlobalPermissions(update);
                if (success)
                {
                    // Clear global permission cache for user
                    _cache.ClearGlobalPermissions(userId);
                }
                return success;
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error updating global permissions: {ex.Message}");
                return false;
            }
        }
        
        #endregion
        
        #region Cache Management
        
        /// <summary>
        /// Clear all permission cache
        /// </summary>
        public static void ClearPermissionCache()
        {
            _cache.ClearAll();
        }
        
        /// <summary>
        /// Clear permission cache for specific user
        /// </summary>
        public static void ClearUserPermissionCache(int userId)
        {
            _cache.ClearUserPermissions(userId);
        }
        
        #endregion
        
        #region Validation
        
        /// <summary>
        /// Validate permission request
        /// </summary>
        public static bool ValidatePermissionRequest(PermissionRequest request)
        {
            if (request == null) return false;
            if (request.UserId <= 0) return false;
            if (string.IsNullOrEmpty(request.FormName)) return false;
            
            // Check if form exists in configuration
            return FormsConfigurationService.FormExists(request.FormName);
        }
        
        #endregion
    }
    
    // Helper class for user with role information
    internal class UserWithRole
    {
        public int UserId { get; set; }
        public int RoleId { get; set; }
    }
}
```

### PermissionCache.cs
```csharp
using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;

namespace ProManage.Modules.Services
{
    public class PermissionCache
    {
        private readonly ConcurrentDictionary<string, CacheItem> _cache = new ConcurrentDictionary<string, CacheItem>();
        private readonly TimeSpan _cacheExpiry = TimeSpan.FromMinutes(15);
        
        /// <summary>
        /// Try to get permission from cache
        /// </summary>
        public bool TryGetPermission(string key, out bool permission)
        {
            permission = false;
            
            if (_cache.TryGetValue(key, out CacheItem item))
            {
                if (DateTime.Now - item.Timestamp < _cacheExpiry)
                {
                    permission = item.Value;
                    return true;
                }
                else
                {
                    // Remove expired item
                    _cache.TryRemove(key, out _);
                }
            }
            
            return false;
        }
        
        /// <summary>
        /// Set permission in cache
        /// </summary>
        public void SetPermission(string key, bool permission)
        {
            _cache[key] = new CacheItem { Value = permission, Timestamp = DateTime.Now };
        }
        
        /// <summary>
        /// Clear all cache
        /// </summary>
        public void ClearAll()
        {
            _cache.Clear();
        }
        
        /// <summary>
        /// Clear cache for specific user
        /// </summary>
        public void ClearUserPermissions(int userId)
        {
            var keysToRemove = _cache.Keys.Where(k => k.StartsWith($"{userId}_")).ToList();
            foreach (var key in keysToRemove)
            {
                _cache.TryRemove(key, out _);
            }
        }
        
        /// <summary>
        /// Clear cache for specific role (affects all users with that role)
        /// </summary>
        public void ClearRolePermissions(int roleId)
        {
            // For role changes, clear all cache since we don't track role-user mapping in cache
            ClearAll();
        }
        
        /// <summary>
        /// Clear global permissions for user
        /// </summary>
        public void ClearGlobalPermissions(int userId)
        {
            var keysToRemove = _cache.Keys.Where(k => k.StartsWith($"global_{userId}_")).ToList();
            foreach (var key in keysToRemove)
            {
                _cache.TryRemove(key, out _);
            }
        }
        
        /// <summary>
        /// Clean expired cache entries
        /// </summary>
        public void CleanExpiredEntries()
        {
            var expiredKeys = _cache
                .Where(kvp => DateTime.Now - kvp.Value.Timestamp >= _cacheExpiry)
                .Select(kvp => kvp.Key)
                .ToList();
            
            foreach (var key in expiredKeys)
            {
                _cache.TryRemove(key, out _);
            }
        }
        
        private class CacheItem
        {
            public bool Value { get; set; }
            public DateTime Timestamp { get; set; }
        }
    }
}
```

## Usage Examples

### Basic Permission Check
```csharp
// Check if user can read EstimateForm
bool canRead = PermissionService.HasPermission(userId, "EstimateForm", PermissionType.Read);

// Check if user can create new users
bool canCreateUsers = PermissionService.HasGlobalPermission(userId, GlobalPermissionType.CanCreateUsers);
```

### Form Integration
```csharp
private void EstimateForm_Load(object sender, EventArgs e)
{
    var userId = UserManager.Instance.CurrentUser.UserId;
    
    btnNew.Enabled = PermissionService.HasPermission(userId, "EstimateForm", PermissionType.New);
    btnSave.Enabled = PermissionService.HasPermission(userId, "EstimateForm", PermissionType.Edit);
    btnDelete.Enabled = PermissionService.HasPermission(userId, "EstimateForm", PermissionType.Delete);
}
```

## Acceptance Criteria

- [ ] Implements 2-level permission system (role + user override)
- [ ] Provides core permission checking methods
- [ ] Includes performance caching with expiration
- [ ] Handles global permissions for user management
- [ ] Provides bulk permission operations
- [ ] Includes proper error handling and logging
- [ ] Cache invalidation on permission updates
- [ ] Thread-safe cache implementation
- [ ] Validation of permission requests

## Dependencies
- Task 01: Database Schema Verification and Setup
- Task 02: Permission Data Models Creation
- Task 03: Forms Configuration Setup
- Task 04: Database Connection Service for Permissions
- Task 05: Form Discovery Service Implementation

## Next Tasks
This task enables:
- Task 09: Permission Management Form (3-Tab UI)
- Task 13: MainFrame Ribbon Permission Filtering
- Task 14: Individual Form Permission Checks
