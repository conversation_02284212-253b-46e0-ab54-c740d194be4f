using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Linq;
using System.Threading;

namespace ProManage.Modules.Services
{
    /// <summary>
    /// Performance monitoring for permission cache operations.
    /// Tracks cache hits, misses, access times, and other performance metrics.
    /// </summary>
    public class PermissionPerformanceMonitor
    {
        private readonly ConcurrentQueue<PerformanceMetric> _metrics = new ConcurrentQueue<PerformanceMetric>();
        private readonly object _statsLock = new object();
        
        private long _cacheHits = 0;
        private long _cacheMisses = 0;
        private long _cacheSets = 0;
        private long _expiredEntries = 0;
        private DateTime _lastCleanup = DateTime.Now;
        
        public long CacheHits => _cacheHits;
        public long CacheMisses => _cacheMisses;
        public long ExpiredEntries => _expiredEntries;
        public DateTime LastCleanup => _lastCleanup;

        /// <summary>
        /// Record a cache hit with access time
        /// </summary>
        /// <param name="accessTimeMs">Access time in milliseconds</param>
        public void RecordCacheHit(long accessTimeMs)
        {
            Interlocked.Increment(ref _cacheHits);
            RecordMetric(MetricType.CacheHit, accessTimeMs);
        }

        /// <summary>
        /// Record a cache miss with access time
        /// </summary>
        /// <param name="accessTimeMs">Access time in milliseconds</param>
        public void RecordCacheMiss(long accessTimeMs)
        {
            Interlocked.Increment(ref _cacheMisses);
            RecordMetric(MetricType.CacheMiss, accessTimeMs);
        }

        /// <summary>
        /// Record a cache set operation
        /// </summary>
        public void RecordCacheSet()
        {
            Interlocked.Increment(ref _cacheSets);
            RecordMetric(MetricType.CacheSet, 0);
        }

        /// <summary>
        /// Record a cache expiry
        /// </summary>
        public void RecordCacheExpiry()
        {
            Interlocked.Increment(ref _expiredEntries);
        }

        /// <summary>
        /// Record user permission set preload
        /// </summary>
        /// <param name="userId">User ID</param>
        public void RecordUserPreload(int userId)
        {
            RecordMetric(MetricType.UserPreload, 0, userId.ToString());
        }

        /// <summary>
        /// Record user permission set hit
        /// </summary>
        /// <param name="userId">User ID</param>
        public void RecordUserSetHit(int userId)
        {
            RecordMetric(MetricType.UserSetHit, 0, userId.ToString());
        }

        /// <summary>
        /// Record user cache clear operation
        /// </summary>
        /// <param name="userId">User ID</param>
        public void RecordUserCacheClear(int userId)
        {
            RecordMetric(MetricType.UserCacheClear, 0, userId.ToString());
        }

        /// <summary>
        /// Record role cache clear operation
        /// </summary>
        /// <param name="roleId">Role ID</param>
        /// <param name="clearedCount">Number of entries cleared</param>
        public void RecordRoleCacheClear(int roleId, int clearedCount)
        {
            RecordMetric(MetricType.RoleCacheClear, clearedCount, roleId.ToString());
        }

        /// <summary>
        /// Record cache warming operation
        /// </summary>
        /// <param name="userCount">Number of users warmed</param>
        /// <param name="formCount">Number of forms warmed</param>
        public void RecordCacheWarm(int userCount, int formCount)
        {
            RecordMetric(MetricType.CacheWarm, userCount * formCount);
        }

        /// <summary>
        /// Record cache cleanup operation
        /// </summary>
        /// <param name="cleanedCount">Number of entries cleaned</param>
        public void RecordCleanup(int cleanedCount)
        {
            lock (_statsLock)
            {
                _lastCleanup = DateTime.Now;
            }
            RecordMetric(MetricType.Cleanup, cleanedCount);
        }

        /// <summary>
        /// Record cache optimization operation
        /// </summary>
        /// <param name="removedCount">Number of entries removed</param>
        public void RecordOptimization(int removedCount)
        {
            RecordMetric(MetricType.Optimization, removedCount);
        }

        /// <summary>
        /// Get cache hit rate percentage
        /// </summary>
        /// <returns>Hit rate as percentage (0-100)</returns>
        public double GetHitRate()
        {
            var total = _cacheHits + _cacheMisses;
            return total > 0 ? (double)_cacheHits / total * 100 : 0;
        }

        /// <summary>
        /// Get average access time for recent operations
        /// </summary>
        /// <returns>Average access time in milliseconds</returns>
        public double GetAverageAccessTime()
        {
            var accessMetrics = GetRecentMetrics(TimeSpan.FromMinutes(5))
                .Where(m => m.Type == MetricType.CacheHit || m.Type == MetricType.CacheMiss)
                .ToList();
            
            return accessMetrics.Count > 0 ? accessMetrics.Average(m => m.Value) : 0;
        }

        /// <summary>
        /// Get recent metrics within specified time span
        /// </summary>
        /// <param name="timeSpan">Time span to look back</param>
        /// <returns>List of recent metrics</returns>
        public List<PerformanceMetric> GetRecentMetrics(TimeSpan timeSpan)
        {
            var cutoff = DateTime.Now - timeSpan;
            return _metrics.Where(m => m.Timestamp >= cutoff).ToList();
        }

        /// <summary>
        /// Get performance statistics summary
        /// </summary>
        /// <returns>Performance statistics</returns>
        public PerformanceStatistics GetStatistics()
        {
            return new PerformanceStatistics
            {
                CacheHits = _cacheHits,
                CacheMisses = _cacheMisses,
                CacheSets = _cacheSets,
                ExpiredEntries = _expiredEntries,
                HitRate = GetHitRate(),
                AverageAccessTime = GetAverageAccessTime(),
                LastCleanup = _lastCleanup,
                TotalMetrics = _metrics.Count
            };
        }

        /// <summary>
        /// Record a performance metric
        /// </summary>
        /// <param name="type">Metric type</param>
        /// <param name="value">Metric value</param>
        /// <param name="context">Optional context information</param>
        private void RecordMetric(MetricType type, long value, string context = null)
        {
            _metrics.Enqueue(new PerformanceMetric
            {
                Type = type,
                Value = value,
                Timestamp = DateTime.Now,
                Context = context
            });
            
            // Keep only recent metrics (last 1000 entries)
            while (_metrics.Count > 1000)
            {
                _metrics.TryDequeue(out _);
            }
        }
    }

    /// <summary>
    /// Performance metric data structure
    /// </summary>
    public class PerformanceMetric
    {
        public MetricType Type { get; set; }
        public long Value { get; set; }
        public DateTime Timestamp { get; set; }
        public string Context { get; set; }
    }

    /// <summary>
    /// Performance statistics summary
    /// </summary>
    public class PerformanceStatistics
    {
        public long CacheHits { get; set; }
        public long CacheMisses { get; set; }
        public long CacheSets { get; set; }
        public long ExpiredEntries { get; set; }
        public double HitRate { get; set; }
        public double AverageAccessTime { get; set; }
        public DateTime LastCleanup { get; set; }
        public int TotalMetrics { get; set; }
    }

    /// <summary>
    /// Types of performance metrics
    /// </summary>
    public enum MetricType
    {
        CacheHit,
        CacheMiss,
        CacheSet,
        UserPreload,
        UserSetHit,
        UserCacheClear,
        RoleCacheClear,
        CacheWarm,
        Cleanup,
        Optimization
    }
}
