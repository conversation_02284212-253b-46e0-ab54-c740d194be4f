﻿<?xml version="1.0" encoding="utf-8"?>
<root>
  <!-- 
    Microsoft ResX Schema 
    
    Version 2.0
    
    The primary goals of this format is to allow a simple XML format 
    that is mostly human readable. The generation and parsing of the 
    various data types are done through the TypeConverter classes 
    associated with the data types.
    
    Example:
    
    ... ado.net/XML headers & schema ...
    <resheader name="resmimetype">text/microsoft-resx</resheader>
    <resheader name="version">2.0</resheader>
    <resheader name="reader">System.Resources.ResXResourceReader, System.Windows.Forms, ...</resheader>
    <resheader name="writer">System.Resources.ResXResourceWriter, System.Windows.Forms, ...</resheader>
    <data name="Name1"><value>this is my long string</value><comment>this is a comment</comment></data>
    <data name="Color1" type="System.Drawing.Color, System.Drawing">Blue</data>
    <data name="Bitmap1" mimetype="application/x-microsoft.net.object.binary.base64">
        <value>[base64 mime encoded serialized .NET Framework object]</value>
    </data>
    <data name="Icon1" type="System.Drawing.Icon, System.Drawing" mimetype="application/x-microsoft.net.object.bytearray.base64">
        <value>[base64 mime encoded string representing a byte array form of the .NET Framework object]</value>
        <comment>This is a comment</comment>
    </data>
                
    There are any number of "resheader" rows that contain simple 
    name/value pairs.
    
    Each data row contains a name, and value. The row also contains a 
    type or mimetype. Type corresponds to a .NET class that support 
    text/value conversion through the TypeConverter architecture. 
    Classes that don't support this are serialized and stored with the 
    mimetype set.
    
    The mimetype is used for serialized objects, and tells the 
    ResXResourceReader how to depersist the object. This is currently not 
    extensible. For a given mimetype the value must be set accordingly:
    
    Note - application/x-microsoft.net.object.binary.base64 is the format 
    that the ResXResourceWriter will generate, however the reader can 
    read any of the formats listed below.
    
    mimetype: application/x-microsoft.net.object.binary.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Binary.BinaryFormatter
            : and then encoded with base64 encoding.
    
    mimetype: application/x-microsoft.net.object.soap.base64
    value   : The object must be serialized with 
            : System.Runtime.Serialization.Formatters.Soap.SoapFormatter
            : and then encoded with base64 encoding.

    mimetype: application/x-microsoft.net.object.bytearray.base64
    value   : The object must be serialized into a byte array 
            : using a System.ComponentModel.TypeConverter
            : and then encoded with base64 encoding.
    -->
  <xsd:schema id="root" xmlns="" xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata">
    <xsd:import namespace="http://www.w3.org/XML/1998/namespace" />
    <xsd:element name="root" msdata:IsDataSet="true">
      <xsd:complexType>
        <xsd:choice maxOccurs="unbounded">
          <xsd:element name="metadata">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" />
              </xsd:sequence>
              <xsd:attribute name="name" use="required" type="xsd:string" />
              <xsd:attribute name="type" type="xsd:string" />
              <xsd:attribute name="mimetype" type="xsd:string" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="assembly">
            <xsd:complexType>
              <xsd:attribute name="alias" type="xsd:string" />
              <xsd:attribute name="name" type="xsd:string" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="data">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
                <xsd:element name="comment" type="xsd:string" minOccurs="0" msdata:Ordinal="2" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" msdata:Ordinal="1" />
              <xsd:attribute name="type" type="xsd:string" msdata:Ordinal="3" />
              <xsd:attribute name="mimetype" type="xsd:string" msdata:Ordinal="4" />
              <xsd:attribute ref="xml:space" />
            </xsd:complexType>
          </xsd:element>
          <xsd:element name="resheader">
            <xsd:complexType>
              <xsd:sequence>
                <xsd:element name="value" type="xsd:string" minOccurs="0" msdata:Ordinal="1" />
              </xsd:sequence>
              <xsd:attribute name="name" type="xsd:string" use="required" />
            </xsd:complexType>
          </xsd:element>
        </xsd:choice>
      </xsd:complexType>
    </xsd:element>
  </xsd:schema>
  <resheader name="resmimetype">
    <value>text/microsoft-resx</value>
  </resheader>
  <resheader name="version">
    <value>2.0</value>
  </resheader>
  <resheader name="reader">
    <value>System.Resources.ResXResourceReader, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <resheader name="writer">
    <value>System.Resources.ResXResourceWriter, System.Windows.Forms, Version=4.0.0.0, Culture=neutral, PublicKeyToken=b77a5c561934e089</value>
  </resheader>
  <data name="vendorLogo2.ImageSource" xml:space="preserve">
    <value>iVBORw0KGgoAAAANSUhEUgAAAP0AAAB4CAMAAAAOlgSlAAAABGdBTUEAALGPC/xhBQAAADBQTFRFw8PD+/v7y8vLz8/P09PT19fX39/f4+Pj5+fn6+vr8/Pz9/f3x8fH7+/v29vb////x2qpKQAAAAlwSFlzAAAOvAAADrwBlbxySQAABOFJREFUeF7tmu2WoyAMhus3Wq33f7d7RCAvEV2Y02x3bJ5fMxRT3hJCAj4eiqIoiqIoiqIoiqIoiqIoiqIoiqIov5bmlYb3uyfDmmbmHW/Jd6tvn1z3RjXwfnelWyomfel4n1vTTKT9VfNPb89I6r9PvKpX9ape1X8Zn1DftrzlU/x79e2yrv+L/HL19WsYl+k5LWa4zI/qtuvNuHUcDXWsbXr1ZF0zTb6dQvW1iauDpzmZxhckkRuVabbm1uXWkFFnmxSgSH0DvQOLlRVRD6yAsEz14+X/Hn3XXJMylKjvcYTAyIris34rfZl3/bOu3KQQ+ernhbo+jcH6cIoeJZOV6buuHxOOsNfR2SalyFY/h4VcvfaJedF6reBZOjXxChrqOA071kK2STGy1VPHsCj3+G2ZQr82tFXBe+cwqdRWYFKOXPU0oz011uSqxreRM8MpkUk1ZpuUI1N9HXpV2BxieJi++dCy0YRGejzbpCCZ6pOTF2l1e1hHLeDk0C98S7ZJQfLU0zyxJBVyGhe3wv/RjJI/++fzTQqSpx5Ov+MRwX69L17qGWWzFMv9Gs83KUie+tNITAt6XWwDzf2Jeu/m+SYFyVIPa5EPiD7ZPR02POxGnu9uygpMCpKlHkIZ34YgPbPPg6p01HOLvMCkIFnqYSnyqx6oz3Zdx/gWeUQiwf+rSTmy1NPmdAhEEKH3qpUWPswpfYuXWmJSjiz10IkPFeoUt6IpeQ8GKZA9/XIoMilGlvqL8cDzblohr/NrnNL84MpFJsW4VF8bYycmb6h+DknsOo3DMELdRuu4zKQUl+oHt+lcjAc+Cirq5N34uk6QuBeaFOJS/aZia4UQxX0R5pBC1Jw62XpGUkpNynCl3u5Tm7NCVso3ZxhqtD21YHjz+ZFtXj8wKcCVevvZNmOQmvC6C7an6Hl7WjmYcRzN0HcH0z8xKcCF+j1B21Yl1GM8LSUXj/J6O7XcpZFykxJcqN/zFisBwnbcB1JYcGAX9i79ttCkDBfqdw+0I4AYFfeiFBaDnvPdq7kvNCnEuXrnm9YvoeqMNyFK12EGQ0Qz/AcFykwKca7eadirb4rDcTVOEQp2bdjup6E9O6ApMinEuXqnYR8ahGg8a4SsFkRC0N6YTCLmF5oU4lS9X34u7lK/UKhErbhEE2+Bpq4mS0wKcaref+AW30zuvISxUjUbBzjIV4iK/wBFJgWYOwMp6TIivtWHnga24X1WZopPbMueIaAjVbySS0y+neTl8RHfHS6Z1moxBv497svNkK50ntH8F5l8L2eXx5zwwHzya1XHBTp39uI2UevE21uByTeTiE1J4JHtlRtONRwCunOqqn20A4v/XH6uybfzA/WPR8teM5n2u2ik9vO5O/n8Yvf3fCPLMPkfAa8YdYlxhkQVIlwbecAxmP/F5O+BbqAj7+1h/v/FxfxnoD2cJelwLy+fvn8KKtD4mQXcy/9q576CtlG+YVPZft+5Pyvc8HLy8MltoODOZ5iOsuQL108BuRs7iw/rnu/3NwKK9ip66Yhi/n2nPjrZcHWbhV5Y5NHwVsC+vq5T12xuXsPVxq3FR6+lWir8X752+zRnRxtb7XbfgEfUyaONqf8G7ZamHyfI7BfjXsL+Iua6btqmqb9OuKIoiqIoiqIoiqIoiqIoiqIoZ/wBx9LJq3YeRQ4AAAAASUVORK5CYII=</value>
  </data>
  <data name="vendorLogo.ImageSource" xml:space="preserve">
    <value>iVBORw0KGgoAAAANSUhEUgAAAP0AAAB4CAMAAAAOlgSlAAAABGdBTUEAALGPC/xhBQAAADBQTFRFw8PD+/v7y8vLz8/P09PT19fX39/f4+Pj5+fn6+vr8/Pz9/f3x8fH7+/v29vb////x2qpKQAAAAlwSFlzAAAOvAAADrwBlbxySQAABOFJREFUeF7tmu2WoyAMhus3Wq33f7d7RCAvEV2Y02x3bJ5fMxRT3hJCAj4eiqIoiqIoiqIoiqIoiqIoiqIoiqIov5bmlYb3uyfDmmbmHW/Jd6tvn1z3RjXwfnelWyomfel4n1vTTKT9VfNPb89I6r9PvKpX9ape1X8Zn1DftrzlU/x79e2yrv+L/HL19WsYl+k5LWa4zI/qtuvNuHUcDXWsbXr1ZF0zTb6dQvW1iauDpzmZxhckkRuVabbm1uXWkFFnmxSgSH0DvQOLlRVRD6yAsEz14+X/Hn3XXJMylKjvcYTAyIris34rfZl3/bOu3KQQ+ernhbo+jcH6cIoeJZOV6buuHxOOsNfR2SalyFY/h4VcvfaJedF6reBZOjXxChrqOA071kK2STGy1VPHsCj3+G2ZQr82tFXBe+cwqdRWYFKOXPU0oz011uSqxreRM8MpkUk1ZpuUI1N9HXpV2BxieJi++dCy0YRGejzbpCCZ6pOTF2l1e1hHLeDk0C98S7ZJQfLU0zyxJBVyGhe3wv/RjJI/++fzTQqSpx5Ov+MRwX69L17qGWWzFMv9Gs83KUie+tNITAt6XWwDzf2Jeu/m+SYFyVIPa5EPiD7ZPR02POxGnu9uygpMCpKlHkIZ34YgPbPPg6p01HOLvMCkIFnqYSnyqx6oz3Zdx/gWeUQiwf+rSTmy1NPmdAhEEKH3qpUWPswpfYuXWmJSjiz10IkPFeoUt6IpeQ8GKZA9/XIoMilGlvqL8cDzblohr/NrnNL84MpFJsW4VF8bYycmb6h+DknsOo3DMELdRuu4zKQUl+oHt+lcjAc+Cirq5N34uk6QuBeaFOJS/aZia4UQxX0R5pBC1Jw62XpGUkpNynCl3u5Tm7NCVso3ZxhqtD21YHjz+ZFtXj8wKcCVevvZNmOQmvC6C7an6Hl7WjmYcRzN0HcH0z8xKcCF+j1B21Yl1GM8LSUXj/J6O7XcpZFykxJcqN/zFisBwnbcB1JYcGAX9i79ttCkDBfqdw+0I4AYFfeiFBaDnvPdq7kvNCnEuXrnm9YvoeqMNyFK12EGQ0Qz/AcFykwKca7eadirb4rDcTVOEQp2bdjup6E9O6ApMinEuXqnYR8ahGg8a4SsFkRC0N6YTCLmF5oU4lS9X34u7lK/UKhErbhEE2+Bpq4mS0wKcaref+AW30zuvISxUjUbBzjIV4iK/wBFJgWYOwMp6TIivtWHnga24X1WZopPbMueIaAjVbySS0y+neTl8RHfHS6Z1moxBv497svNkK50ntH8F5l8L2eXx5zwwHzya1XHBTp39uI2UevE21uByTeTiE1J4JHtlRtONRwCunOqqn20A4v/XH6uybfzA/WPR8teM5n2u2ik9vO5O/n8Yvf3fCPLMPkfAa8YdYlxhkQVIlwbecAxmP/F5O+BbqAj7+1h/v/FxfxnoD2cJelwLy+fvn8KKtD4mQXcy/9q576CtlG+YVPZft+5Pyvc8HLy8MltoODOZ5iOsuQL108BuRs7iw/rnu/3NwKK9ip66Yhi/n2nPjrZcHWbhV5Y5NHwVsC+vq5T12xuXsPVxq3FR6+lWir8X752+zRnRxtb7XbfgEfUyaONqf8G7ZamHyfI7BfjXsL+Iua6btqmqb9OuKIoiqIoiqIoiqIoiqIoiqIoZ/wBx9LJq3YeRQ4AAAAASUVORK5CYII=</value>
  </data>
</root>