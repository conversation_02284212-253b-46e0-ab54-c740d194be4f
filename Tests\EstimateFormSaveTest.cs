using System;
using System.Collections.Generic;
using Microsoft.VisualStudio.TestTools.UnitTesting;
using ProManage.Modules.Data.EstimateForm;
using ProManage.Modules.Models.EstimateForm;
using ProManage.Modules.Connections;

namespace ProManage.Tests
{
    [TestClass]
    public class EstimateFormSaveTest
    {
        private string _testPrefix;
        private List<string> _testEstimateNumbers;

        [TestInitialize]
        public void TestInitialize()
        {
            // Generate unique prefix for this test run to avoid conflicts
            _testPrefix = $"TEST-{DateTime.Now:yyyyMMddHHmmss}";
            _testEstimateNumbers = new List<string>();
        }

        [TestMethod]
        public void SaveEstimate_NewEstimate_ShouldSucceed()
        {
            // Arrange
            var estimateNo = $"{_testPrefix}-001";
            _testEstimateNumbers.Add(estimateNo);
            
            var estimate = new EstimateFormHeaderModel
            {
                EstimateNo = estimateNo,
                CustomerName = "Test Customer",
                VIN = "TEST123456789",
                Brand = "Test Brand",
                DocDate = DateTime.Now,
                Location = "Test Location",
                VehicleModel = "Test Model",
                SalesmanName = "Test Salesman",
                Status = true,
                Remarks = "Test remarks",
                Details = new List<EstimateFormDetailModel>
                {
                    new EstimateFormDetailModel
                    {
                        PartNo = "PART001",
                        Description = "Test Part",
                        Qty = 1,
                        OEPrice = 100.00m,
                        AFMPrice = 80.00m,
                        Remarks = "Test detail remarks"
                    }
                }
            };

            // Act
            bool result = false;
            try
            {
                // Show progress indicator before database operation (to match repository behavior)
                ProManage.Modules.UI.ProgressIndicatorService.Instance.ShowProgress();
                
                result = EstimateFormRepository.SaveEstimate(estimate);
            }
            catch (Exception ex)
            {
                Assert.Fail($"Save operation failed with exception: {ex.Message}");
            }
            finally
            {
                // Hide progress indicator (the repository should do this, but we ensure it's hidden)
                ProManage.Modules.UI.ProgressIndicatorService.Instance.HideProgress();
            }

            // Assert
            Assert.IsTrue(result, "Save operation should succeed");
            Assert.IsTrue(estimate.Id > 0, "Estimate should have a valid ID after save");
        }

        [TestMethod]
        public void SaveEstimate_UpdateExisting_ShouldSucceed()
        {
            // Arrange - First create an estimate
            var estimateNo = $"{_testPrefix}-002";
            _testEstimateNumbers.Add(estimateNo);
            
            var estimate = new EstimateFormHeaderModel
            {
                EstimateNo = estimateNo,
                CustomerName = "Test Customer 2",
                VIN = "TEST987654321",
                Brand = "Test Brand 2",
                DocDate = DateTime.Now,
                Location = "Test Location 2",
                VehicleModel = "Test Model 2",
                SalesmanName = "Test Salesman 2",
                Status = true,
                Remarks = "Original remarks",
                Details = new List<EstimateFormDetailModel>()
            };

            // Save the initial estimate
            bool initialSave = EstimateFormRepository.SaveEstimate(estimate);
            Assert.IsTrue(initialSave, "Initial save should succeed");

            // Act - Update the estimate
            estimate.CustomerName = "Updated Customer";
            estimate.Remarks = "Updated remarks";
            estimate.Details.Add(new EstimateFormDetailModel
            {
                PartNo = "PART002",
                Description = "Updated Part",
                Qty = 2,
                OEPrice = 200.00m,
                AFMPrice = 160.00m,
                Remarks = "Updated detail remarks"
            });

            bool updateResult = false;
            try
            {
                // Show progress indicator before database operation (to match repository behavior)
                ProManage.Modules.UI.ProgressIndicatorService.Instance.ShowProgress();
                
                updateResult = EstimateFormRepository.SaveEstimate(estimate);
            }
            catch (Exception ex)
            {
                Assert.Fail($"Update operation failed with exception: {ex.Message}");
            }
            finally
            {
                // Hide progress indicator (the repository should do this, but we ensure it's hidden)
                ProManage.Modules.UI.ProgressIndicatorService.Instance.HideProgress();
            }

            // Assert
            Assert.IsTrue(updateResult, "Update operation should succeed");
        }

        [TestMethod]
        public void SaveEstimate_WithNullValues_ShouldHandleGracefully()
        {
            // Arrange
            var estimateNo = $"{_testPrefix}-003";
            _testEstimateNumbers.Add(estimateNo);
            
            var estimate = new EstimateFormHeaderModel
            {
                EstimateNo = estimateNo,
                CustomerName = null, // Test null handling
                VIN = null,
                Brand = null,
                DocDate = null,
                Location = null,
                VehicleModel = null,
                SalesmanName = null,
                Status = false,
                Remarks = null,
                Details = new List<EstimateFormDetailModel>
                {
                    new EstimateFormDetailModel
                    {
                        PartNo = null,
                        Description = null,
                        Qty = null,
                        OEPrice = null,
                        AFMPrice = null,
                        Remarks = null
                    }
                }
            };

            // Act
            bool result = false;
            try
            {
                // Show progress indicator before database operation (to match repository behavior)
                ProManage.Modules.UI.ProgressIndicatorService.Instance.ShowProgress();
                
                result = EstimateFormRepository.SaveEstimate(estimate);
            }
            catch (Exception ex)
            {
                Assert.Fail($"Save operation with null values failed with exception: {ex.Message}");
            }
            finally
            {
                // Hide progress indicator (the repository should do this, but we ensure it's hidden)
                ProManage.Modules.UI.ProgressIndicatorService.Instance.HideProgress();
            }

            // Assert
            Assert.IsTrue(result, "Save operation with null values should succeed");
        }

        [TestCleanup]
        public void Cleanup()
        {
            // Clean up test data to avoid conflicts in future test runs
            try
            {
                // Show progress indicator before database operations
                ProManage.Modules.UI.ProgressIndicatorService.Instance.ShowProgress();
                
                foreach (var estimateNo in _testEstimateNumbers)
                {
                    // Get the estimate first, then delete by ID
                    var estimate = EstimateFormRepository.GetEstimateByNumber(estimateNo);
                    if (estimate != null)
                    {
                        EstimateFormRepository.DeleteEstimate(estimate.Id);
                        System.Diagnostics.Debug.WriteLine($"Cleaned up test estimate: {estimateNo} (ID: {estimate.Id})");
                    }
                }
            }
            catch (Exception ex)
            {
                // Log cleanup failure but don't fail the test
                System.Diagnostics.Debug.WriteLine($"Test cleanup failed: {ex.Message}");
            }
            finally
            {
                // Hide progress indicator
                ProManage.Modules.UI.ProgressIndicatorService.Instance.HideProgress();
            }
        }
    }
}
