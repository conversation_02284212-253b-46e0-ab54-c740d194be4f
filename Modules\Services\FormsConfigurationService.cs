using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Text.Json;
using System.Windows.Forms;
using ProManage.Modules.Models.PermissionManagementForm;

namespace ProManage.Modules.Services
{
    /// <summary>
    /// Service for managing form configurations and metadata.
    /// Provides centralized access to form definitions, display names, categories, and settings.
    /// </summary>
    public class FormsConfigurationService
    {
        private static readonly string ConfigFilePath = Path.Combine(
            AppDomain.CurrentDomain.BaseDirectory, "Modules", "Config", "FormsConfig.json");
        
        private static FormsConfigurationCollection _cachedConfig;
        private static DateTime _lastLoadTime = DateTime.MinValue;
        private static readonly object _lockObject = new object();
        
        /// <summary>
        /// Get all active form configurations
        /// </summary>
        /// <returns>List of active form configurations</returns>
        public static List<FormConfiguration> GetAllForms()
        {
            LoadConfigurationIfNeeded();
            return _cachedConfig?.Forms?.Where(f => f.IsActive).OrderBy(f => f.SortOrder).ToList() ?? new List<FormConfiguration>();
        }
        
        /// <summary>
        /// Get form configuration by name
        /// </summary>
        /// <param name="formName">Name of the form to find</param>
        /// <returns>Form configuration or null if not found</returns>
        public static FormConfiguration GetFormConfiguration(string formName)
        {
            LoadConfigurationIfNeeded();
            return _cachedConfig?.Forms?.FirstOrDefault(f => 
                f.FormName.Equals(formName, StringComparison.OrdinalIgnoreCase) && f.IsActive);
        }
        
        /// <summary>
        /// Get forms by category
        /// </summary>
        /// <param name="category">Category name to filter by</param>
        /// <returns>List of forms in the specified category</returns>
        public static List<FormConfiguration> GetFormsByCategory(string category)
        {
            LoadConfigurationIfNeeded();
            return _cachedConfig?.Forms?.Where(f => 
                f.Category.Equals(category, StringComparison.OrdinalIgnoreCase) && f.IsActive)
                .OrderBy(f => f.SortOrder)
                .ToList() ?? new List<FormConfiguration>();
        }
        
        /// <summary>
        /// Check if form exists in configuration
        /// </summary>
        /// <param name="formName">Name of the form to check</param>
        /// <returns>True if form exists and is active</returns>
        public static bool FormExists(string formName)
        {
            return GetFormConfiguration(formName) != null;
        }
        
        /// <summary>
        /// Get display name for form
        /// </summary>
        /// <param name="formName">Internal form name</param>
        /// <returns>Display name or form name if not configured</returns>
        public static string GetFormDisplayName(string formName)
        {
            var config = GetFormConfiguration(formName);
            return config?.DisplayName ?? formName;
        }
        
        /// <summary>
        /// Get all available categories
        /// </summary>
        /// <returns>List of category configurations</returns>
        public static List<CategoryConfiguration> GetAllCategories()
        {
            LoadConfigurationIfNeeded();
            return _cachedConfig?.Categories?.Where(c => c.IsActive).OrderBy(c => c.SortOrder).ToList() ?? new List<CategoryConfiguration>();
        }
        
        /// <summary>
        /// Add new form to configuration
        /// </summary>
        /// <param name="formConfig">Form configuration to add</param>
        /// <returns>True if added successfully, false if form already exists</returns>
        public static bool AddFormConfiguration(FormConfiguration formConfig)
        {
            try
            {
                lock (_lockObject)
                {
                    LoadConfigurationIfNeeded();
                    
                    if (_cachedConfig.Forms.Any(f => f.FormName.Equals(formConfig.FormName, StringComparison.OrdinalIgnoreCase)))
                    {
                        return false; // Form already exists
                    }
                    
                    _cachedConfig.Forms.Add(formConfig);
                    _cachedConfig.LastUpdated = DateTime.UtcNow;
                    
                    SaveConfiguration();
                    return true;
                }
            }
            catch
            {
                return false;
            }
        }
        
        /// <summary>
        /// Remove form from configuration
        /// </summary>
        /// <param name="formName">Name of the form to remove</param>
        /// <returns>True if removed successfully</returns>
        public static bool RemoveFormConfiguration(string formName)
        {
            try
            {
                lock (_lockObject)
                {
                    LoadConfigurationIfNeeded();
                    
                    var form = _cachedConfig.Forms.FirstOrDefault(f => 
                        f.FormName.Equals(formName, StringComparison.OrdinalIgnoreCase));
                    
                    if (form != null)
                    {
                        _cachedConfig.Forms.Remove(form);
                        _cachedConfig.LastUpdated = DateTime.UtcNow;
                        SaveConfiguration();
                        return true;
                    }
                    
                    return false;
                }
            }
            catch
            {
                return false;
            }
        }
        
        /// <summary>
        /// Reload configuration from file
        /// </summary>
        public static void ReloadConfiguration()
        {
            lock (_lockObject)
            {
                _cachedConfig = null;
                _lastLoadTime = DateTime.MinValue;
                LoadConfigurationIfNeeded();
            }
        }
        
        /// <summary>
        /// Load configuration if needed (file changed or not loaded)
        /// </summary>
        private static void LoadConfigurationIfNeeded()
        {
            if (_cachedConfig == null || 
                (File.Exists(ConfigFilePath) && File.GetLastWriteTime(ConfigFilePath) > _lastLoadTime))
            {
                LoadConfiguration();
            }
        }
        
        /// <summary>
        /// Load configuration from file
        /// </summary>
        private static void LoadConfiguration()
        {
            try
            {
                if (File.Exists(ConfigFilePath))
                {
                    var json = File.ReadAllText(ConfigFilePath);
                    var options = new JsonSerializerOptions
                    {
                        PropertyNameCaseInsensitive = true,
                        WriteIndented = true
                    };
                    _cachedConfig = JsonSerializer.Deserialize<FormsConfigurationCollection>(json, options);
                    _lastLoadTime = DateTime.Now;
                }
                else
                {
                    CreateDefaultConfiguration();
                }
            }
            catch
            {
                CreateDefaultConfiguration();
            }
        }
        
        /// <summary>
        /// Create default configuration if file is missing or corrupt
        /// </summary>
        private static void CreateDefaultConfiguration()
        {
            _cachedConfig = new FormsConfigurationCollection
            {
                LastUpdated = DateTime.UtcNow,
                Forms = GetDefaultFormConfigurations(),
                Categories = GetDefaultCategories()
            };
            
            SaveConfiguration();
        }
        
        /// <summary>
        /// Save configuration to file
        /// </summary>
        private static void SaveConfiguration()
        {
            try
            {
                var directory = Path.GetDirectoryName(ConfigFilePath);
                if (!Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }
                
                var options = new JsonSerializerOptions
                {
                    WriteIndented = true,
                    PropertyNamingPolicy = JsonNamingPolicy.CamelCase
                };
                var json = JsonSerializer.Serialize(_cachedConfig, options);
                File.WriteAllText(ConfigFilePath, json);
                _lastLoadTime = DateTime.Now;
            }
            catch (Exception ex)
            {
                // Log error but don't throw - configuration service should be resilient
                System.Diagnostics.Debug.WriteLine($"Error saving forms configuration: {ex.Message}");
            }
        }
        
        /// <summary>
        /// Get default form configurations for current MainForms
        /// </summary>
        /// <returns>List of default form configurations</returns>
        private static List<FormConfiguration> GetDefaultFormConfigurations()
        {
            return new List<FormConfiguration>
            {
                new FormConfiguration { FormName = "DatabaseForm", DisplayName = "Database Management", Category = "System", IsActive = true, SortOrder = 10 },
                new FormConfiguration { FormName = "ParametersForm", DisplayName = "System Parameters", Category = "System", IsActive = true, SortOrder = 20 },
                new FormConfiguration { FormName = "RoleMasterForm", DisplayName = "Role Management", Category = "Security", IsActive = true, SortOrder = 30 },
                new FormConfiguration { FormName = "SQLQueryForm", DisplayName = "SQL Query Tool", Category = "System", IsActive = true, SortOrder = 40 },
                new FormConfiguration { FormName = "UserManagementListForm", DisplayName = "User List", Category = "Security", IsActive = true, SortOrder = 50 },
                new FormConfiguration { FormName = "UserMasterForm", DisplayName = "User Management", Category = "Security", IsActive = true, SortOrder = 60 }
            };
        }
        
        /// <summary>
        /// Get default category configurations
        /// </summary>
        /// <returns>List of default categories</returns>
        private static List<CategoryConfiguration> GetDefaultCategories()
        {
            return new List<CategoryConfiguration>
            {
                new CategoryConfiguration { CategoryName = "System", DisplayName = "System Administration", SortOrder = 10, IsActive = true },
                new CategoryConfiguration { CategoryName = "Security", DisplayName = "Security & Access", SortOrder = 20, IsActive = true }
            };
        }
    }
}
