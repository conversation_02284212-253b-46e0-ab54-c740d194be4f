// Parameter Type Enumeration - Defines the supported parameter data types
// Usage: Used for type-safe parameter entry and validation in the parameter system

using System;
using System.ComponentModel;
using System.IO;
using System.Linq;

namespace ProManage.Modules.Models.ParametersForm
{
    /// <summary>
    /// Enumeration of supported parameter data types
    /// Used for type validation and conversion in the unified parameter system
    /// </summary>
    public enum ParameterType
    {
        /// <summary>
        /// String/Text parameter type - stores text values
        /// </summary>
        [Description("String")]
        String = 1,

        /// <summary>
        /// Integer/Number parameter type - stores whole numbers
        /// </summary>
        [Description("Number")]
        Number = 2,

        /// <summary>
        /// Decimal parameter type - stores decimal numbers with precision
        /// </summary>
        [Description("Decimal")]
        Decimal = 3,

        /// <summary>
        /// Date parameter type - stores date and time values
        /// </summary>
        [Description("Date")]
        Date = 4,

        /// <summary>
        /// Boolean parameter type - stores true/false values
        /// </summary>
        [Description("Boolean")]
        Boolean = 5,

        /// <summary>
        /// Object parameter type - stores file paths and object references
        /// </summary>
        [Description("Object")]
        Object = 6
    }

    /// <summary>
    /// Helper class for ParameterType enumeration operations
    /// </summary>
    public static class ParameterTypeHelper
    {
        /// <summary>
        /// Gets the display name for a parameter type
        /// </summary>
        /// <param name="parameterType">The parameter type</param>
        /// <returns>Display name for the parameter type</returns>
        public static string GetDisplayName(ParameterType parameterType)
        {
            var field = parameterType.GetType().GetField(parameterType.ToString());
            var attribute = (DescriptionAttribute)Attribute.GetCustomAttribute(field, typeof(DescriptionAttribute));
            return attribute?.Description ?? parameterType.ToString();
        }

        /// <summary>
        /// Gets all available parameter types with their display names
        /// </summary>
        /// <returns>Dictionary of parameter types and their display names</returns>
        public static System.Collections.Generic.Dictionary<ParameterType, string> GetAllTypes()
        {
            var types = new System.Collections.Generic.Dictionary<ParameterType, string>();
            
            foreach (ParameterType type in Enum.GetValues(typeof(ParameterType)))
            {
                types.Add(type, GetDisplayName(type));
            }
            
            return types;
        }

        /// <summary>
        /// Parses a parameter type from its display name
        /// </summary>
        /// <param name="displayName">The display name of the parameter type</param>
        /// <returns>The corresponding ParameterType enum value, or String as default</returns>
        public static ParameterType ParseFromDisplayName(string displayName)
        {
            if (string.IsNullOrWhiteSpace(displayName))
                return ParameterType.String;

            foreach (ParameterType type in Enum.GetValues(typeof(ParameterType)))
            {
                if (string.Equals(GetDisplayName(type), displayName.Trim(), StringComparison.OrdinalIgnoreCase))
                {
                    return type;
                }
            }

            // Default to String if not found
            return ParameterType.String;
        }

        /// <summary>
        /// Validates if a value is compatible with the specified parameter type
        /// </summary>
        /// <param name="value">The value to validate</param>
        /// <param name="parameterType">The expected parameter type</param>
        /// <param name="errorMessage">Error message if validation fails</param>
        /// <returns>True if valid, false otherwise</returns>
        public static bool ValidateValue(string value, ParameterType parameterType, out string errorMessage)
        {
            errorMessage = null;

            if (string.IsNullOrWhiteSpace(value))
            {
                errorMessage = "Value cannot be empty";
                return false;
            }

            switch (parameterType)
            {
                case ParameterType.String:
                    // String values are always valid
                    return true;

                case ParameterType.Number:
                    if (!int.TryParse(value, out _))
                    {
                        errorMessage = "Value must be a valid whole number";
                        return false;
                    }
                    return true;

                case ParameterType.Decimal:
                    if (!decimal.TryParse(value, out _))
                    {
                        errorMessage = "Value must be a valid decimal number";
                        return false;
                    }
                    // Require a decimal point for decimal parameter type
                    if (!value.Contains("."))
                    {
                        errorMessage = "Value must include a decimal point (e.g., 123.0)";
                        return false;
                    }
                    return true;

                case ParameterType.Date:
                    // Allow date format patterns as valid parameter values
                    string[] validDateFormatPatterns = new string[]
                    {
                        "DD-MM-YYYY",
                        "DD-MMM-YYYY", 
                        "DD-MM-YY",
                        "MM/DD/YYYY",
                        "YYYY-MM-DD",
                        "MMM DD, YYYY"
                    };
                    
                    // Check if the value is a valid date format pattern
                    if (validDateFormatPatterns.Contains(value))
                    {
                        return true;
                    }
                    
                    // Try parsing as an actual date with various formats
                    string[] dateFormats = new string[] 
                    { 
                        "dd-MM-yyyy",    // DD-MM-YYYY
                        "dd-MMM-yyyy",   // DD-MMM-YYYY  
                        "dd-MM-yy",      // DD-MM-YY
                        "MM/dd/yyyy",    // MM/DD/YYYY
                        "yyyy-MM-dd",    // YYYY-MM-DD
                        "MMM dd, yyyy"   // MMM DD, YYYY
                    };
                    
                    if (!DateTime.TryParseExact(value, dateFormats, 
                        System.Globalization.CultureInfo.InvariantCulture, 
                        System.Globalization.DateTimeStyles.None, out _))
                    {
                        errorMessage = "Value must be either a valid date format pattern (DD-MM-YYYY, DD-MMM-YYYY, DD-MM-YY, MM/DD/YYYY, YYYY-MM-DD, MMM DD, YYYY) or an actual date in one of these formats";
                        return false;
                    }
                    return true;

                case ParameterType.Boolean:
                    string lowerValue = value.ToLower().Trim();
                    if (lowerValue != "true" && lowerValue != "false" &&
                        lowerValue != "1" && lowerValue != "0" &&
                        lowerValue != "yes" && lowerValue != "no" &&
                        lowerValue != "y" && lowerValue != "n")
                    {
                        errorMessage = "Value must be true/false, 1/0, yes/no, or y/n";
                        return false;
                    }
                    return true;

                case ParameterType.Object:
                    // For object type, validate file path or object reference
                    if (string.IsNullOrWhiteSpace(value))
                    {
                        errorMessage = "Object reference cannot be empty";
                        return false;
                    }

                    // Check if it's a file path
                    if (value.Contains("\\") || value.Contains("/"))
                    {
                        // Validate file path format
                        try
                        {
                            string fullPath = Path.GetFullPath(value);
                            // Check if file exists (optional - might be a future file)
                            if (!File.Exists(fullPath) && !Directory.Exists(fullPath))
                            {
                                errorMessage = "File or directory does not exist";
                                return false;
                            }
                        }
                        catch
                        {
                            errorMessage = "Invalid file path format";
                            return false;
                        }
                    }
                    return true;

                default:
                    errorMessage = "Unknown parameter type";
                    return false;
            }
        }

        /// <summary>
        /// Gets a sample value for the specified parameter type
        /// </summary>
        /// <param name="parameterType">The parameter type</param>
        /// <returns>Sample value string</returns>
        public static string GetSampleValue(ParameterType parameterType)
        {
            switch (parameterType)
            {
                case ParameterType.String:
                    return "Sample text";
                case ParameterType.Number:
                    return "123";
                case ParameterType.Decimal:
                    return "123.45";
                case ParameterType.Date:
                    return DateTime.Today.ToString("MM/dd/yyyy");
                case ParameterType.Boolean:
                    return "true";
                case ParameterType.Object:
                    return "C:\\path\\to\\file.jpg";
                default:
                    return "";
            }
        }
    }
}
