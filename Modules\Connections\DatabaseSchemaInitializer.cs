using System;
using System.Diagnostics;
using Npgsql;

namespace ProManage.Modules.Connections
{
    /// <summary>
    /// Database schema initializer for ProManage application.
    /// Ensures all required tables and schema exist before the application starts.
    /// </summary>
    public static class DatabaseSchemaInitializer
    {
        /// <summary>
        /// Initialize all database schemas required by ProManage
        /// </summary>
        /// <returns>True if initialization successful, false otherwise</returns>
        public static bool InitializeDatabase()
        {
            try
            {
                Debug.WriteLine("Starting database schema initialization...");
                
                // Initialize RBAC schema
                if (!PermissionDatabaseService.VerifyAndCreateSchema())
                {
                    Debug.WriteLine("Failed to initialize RBAC schema");
                    return false;
                }
                
                // Verify core tables exist
                if (!VerifyCoreTables())
                {
                    Debug.WriteLine("Core tables verification failed");
                    return false;
                }
                
                Debug.WriteLine("Database schema initialization completed successfully");
                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Database schema initialization failed: {ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// Verify that core application tables exist
        /// </summary>
        /// <returns>True if all core tables exist, false otherwise</returns>
        private static bool VerifyCoreTables()
        {
            try
            {
                using (var connection = DatabaseConnectionManager.Instance.CreateNewConnection())
                {
                    connection.Open();
                    
                    // Check if users table exists
                    if (!TableExists(connection, "users"))
                    {
                        Debug.WriteLine("Users table does not exist - this should be created by the main application setup");
                        return false;
                    }
                    
                    // Check RBAC tables
                    string[] rbacTables = { "roles", "role_permissions", "user_permissions", "global_permissions" };
                    
                    foreach (var tableName in rbacTables)
                    {
                        if (!TableExists(connection, tableName))
                        {
                            Debug.WriteLine($"RBAC table '{tableName}' does not exist");
                            return false;
                        }
                    }
                    
                    Debug.WriteLine("All core tables verified successfully");
                    return true;
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error verifying core tables: {ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// Check if a table exists in the database
        /// </summary>
        /// <param name="connection">Database connection</param>
        /// <param name="tableName">Name of the table to check</param>
        /// <returns>True if table exists, false otherwise</returns>
        private static bool TableExists(NpgsqlConnection connection, string tableName)
        {
            const string query = @"
                SELECT COUNT(*) 
                FROM information_schema.tables 
                WHERE table_schema = 'public' 
                AND table_name = @tableName";
            
            using (var command = new NpgsqlCommand(query, connection))
            {
                command.Parameters.AddWithValue("@tableName", tableName);
                var count = Convert.ToInt32(command.ExecuteScalar());
                return count > 0;
            }
        }
        
        /// <summary>
        /// Get database schema status for diagnostics
        /// </summary>
        /// <returns>Schema status information</returns>
        public static string GetSchemaStatus()
        {
            try
            {
                using (var connection = DatabaseConnectionManager.Instance.CreateNewConnection())
                {
                    connection.Open();
                    
                    var status = "Database Schema Status:\n";
                    
                    // Check core tables
                    string[] coreTables = { "users", "roles", "role_permissions", "user_permissions", "global_permissions" };
                    
                    foreach (var tableName in coreTables)
                    {
                        bool exists = TableExists(connection, tableName);
                        status += $"  {tableName}: {(exists ? "✓ EXISTS" : "✗ MISSING")}\n";
                    }
                    
                    // Check if role_id column exists in users table
                    if (TableExists(connection, "users"))
                    {
                        bool roleIdExists = ColumnExists(connection, "users", "role_id");
                        status += $"  users.role_id column: {(roleIdExists ? "✓ EXISTS" : "✗ MISSING")}\n";
                    }
                    
                    return status;
                }
            }
            catch (Exception ex)
            {
                return $"Error getting schema status: {ex.Message}";
            }
        }
        
        /// <summary>
        /// Check if a column exists in a table
        /// </summary>
        /// <param name="connection">Database connection</param>
        /// <param name="tableName">Name of the table</param>
        /// <param name="columnName">Name of the column</param>
        /// <returns>True if column exists, false otherwise</returns>
        private static bool ColumnExists(NpgsqlConnection connection, string tableName, string columnName)
        {
            const string query = @"
                SELECT COUNT(*) 
                FROM information_schema.columns 
                WHERE table_schema = 'public' 
                AND table_name = @tableName 
                AND column_name = @columnName";
            
            using (var command = new NpgsqlCommand(query, connection))
            {
                command.Parameters.AddWithValue("@tableName", tableName);
                command.Parameters.AddWithValue("@columnName", columnName);
                var count = Convert.ToInt32(command.ExecuteScalar());
                return count > 0;
            }
        }
    }
}
