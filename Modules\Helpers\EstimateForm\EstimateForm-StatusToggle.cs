using System;
using System.Diagnostics;
using System.Windows.Forms;
using DevExpress.XtraBars;
using ProManage.Modules.Models.EstimateForm;
using ProManage.Modules.Data.EstimateForm;
using ProManage.Modules.UI;

namespace ProManage.Modules.Helpers.EstimateForm
{
    /// <summary>
    /// <PERSON><PERSON> estimate status toggle functionality
    /// Manages the lock/unlock status of estimates with database synchronization
    /// </summary>
    public static class EstimateFormStatusToggle
    {
        #region Public Methods

        /// <summary>
        /// Sets up the status toggle button with proper event handling
        /// </summary>
        /// <param name="form">The EstimateForm instance</param>
        public static void SetupStatusToggle(dynamic form)
        {
            try
            {
                Debug.WriteLine("=== EstimateFormStatusToggle.SetupStatusToggle: Starting ===");

                // Remove existing event handler to avoid duplicates
                form.BarToggleSwitchItem1.CheckedChanged -= (EventHandler)((s, e) => OnStatusToggleChanged(form, s, e));

                // Add event handler
                form.BarToggleSwitchItem1.CheckedChanged += (EventHandler)((s, e) => OnStatusToggleChanged(form, s, e));

                Debug.WriteLine("Status toggle setup completed");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error setting up status toggle: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Updates the toggle button state based on current estimate status
        /// </summary>
        /// <param name="form">The EstimateForm instance</param>
        /// <param name="estimate">The current estimate</param>
        public static void UpdateToggleState(dynamic form, EstimateFormHeaderModel estimate)
        {
            try
            {
                if (estimate == null)
                {
                    // Temporarily disable event handler
                    form.BarToggleSwitchItem1.CheckedChanged -= (EventHandler)((s, e) => OnStatusToggleChanged(form, s, e));

                    // No estimate loaded - disable toggle
                    form.BarToggleSwitchItem1.Enabled = false;
                    form.BarToggleSwitchItem1.Caption = "No Estimate";
                    form.BarToggleSwitchItem1.Checked = false;

                    // Re-enable event handler
                    form.BarToggleSwitchItem1.CheckedChanged += (EventHandler)((s, e) => OnStatusToggleChanged(form, s, e));
                    return;
                }

                // Temporarily disable event handler to prevent triggering during update
                form.BarToggleSwitchItem1.CheckedChanged -= (EventHandler)((s, e) => OnStatusToggleChanged(form, s, e));

                // Enable toggle
                form.BarToggleSwitchItem1.Enabled = true;

                // Update toggle state and caption based on estimate status
                // RIGHT (checked/true) = Closed, LEFT (unchecked/false) = Active
                form.BarToggleSwitchItem1.Checked = estimate.Status;
                form.BarToggleSwitchItem1.Caption = estimate.Status ? "Closed" : "Active";

                // Re-enable event handler
                form.BarToggleSwitchItem1.CheckedChanged += (EventHandler)((s, e) => OnStatusToggleChanged(form, s, e));

                Debug.WriteLine($"Toggle updated - Status: {estimate.Status}, Caption: {form.BarToggleSwitchItem1.Caption}");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error updating toggle state: {ex.Message}");
                // Make sure to re-enable event handler even if there was an error
                try
                {
                    form.BarToggleSwitchItem1.CheckedChanged += (EventHandler)((s, e) => OnStatusToggleChanged(form, s, e));
                }
                catch { }
            }
        }

        /// <summary>
        /// Updates form controls based on estimate status (locked/unlocked)
        /// </summary>
        /// <param name="form">The EstimateForm instance</param>
        /// <param name="isActive">True if estimate is active/unlocked, false if closed/locked</param>
        public static void UpdateFormControlsForStatus(dynamic form, bool isActive)
        {
            try
            {
                Debug.WriteLine($"=== UpdateFormControlsForStatus: Setting to {(isActive ? "Active" : "Closed")} ===");

                if (!isActive)
                {
                    // Estimate is closed/locked - make form read-only
                    EstimateFormHelper.EnableControls(form, false);
                    EstimateFormGridManager.SetGridEditMode(form, false);

                    // Disable edit-related buttons
                    form.BarButtonItemEdit.Enabled = false;
                    form.BarButtonItemSave.Enabled = false;
                    form.BarButtonItemCancel.Enabled = false;
                    form.BarButtonItemAddRow.Enabled = false;
                    form.BarButtonItemDelete.Enabled = false;

                    Debug.WriteLine("Form set to read-only mode (estimate is closed/locked)");
                }
                else
                {
                    // Estimate is active/open - allow normal operations
                    // Note: Don't automatically enable edit mode, let user click Edit button
                    EstimateFormHelper.EnableControls(form, false); // Keep in view mode until Edit is clicked
                    EstimateFormGridManager.SetGridEditMode(form, false);

                    // Enable buttons based on normal business logic
                    EstimateFormHelper.UpdateButtonStates(form, false); // Not in edit mode initially

                    Debug.WriteLine("Form set to normal mode (estimate is active/open)");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error updating form controls for status: {ex.Message}");
            }
        }

        /// <summary>
        /// Handles toggle button change event - public wrapper for the private method
        /// </summary>
        /// <param name="form">The EstimateForm instance</param>
        /// <param name="toggleSwitch">The toggle switch that was changed</param>
        /// <param name="currentEstimate">Reference to the current estimate</param>
        public static void HandleToggleChanged(dynamic form, BarToggleSwitchItem toggleSwitch, ref EstimateFormHeaderModel currentEstimate)
        {
            try
            {
                Debug.WriteLine("=== HandleToggleChanged: Starting ===");

                if (currentEstimate == null)
                {
                    Debug.WriteLine("No current estimate - ignoring toggle change");
                    return;
                }

                // Get new status from toggle
                bool newStatus = toggleSwitch.Checked;
                string statusText = newStatus ? "Closed" : "Active";

                Debug.WriteLine($"Toggle changed to: {newStatus} ({statusText})");

                // Confirm the change with user
                var result = MessageBox.Show(
                    $"Are you sure you want to change the estimate status to '{statusText}'?\n\n" +
                    $"• Active: Estimate can be edited and modified\n" +
                    $"• Closed: Estimate becomes read-only and locked",
                    "Confirm Status Change",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);

                if (result != DialogResult.Yes)
                {
                    // User cancelled - revert toggle state
                    toggleSwitch.Checked = currentEstimate.Status;
                    Debug.WriteLine("User cancelled status change");
                    return;
                }

                // Update the estimate status
                currentEstimate.Status = newStatus;

                // Save the status change to database
                bool saveSuccess = SaveStatusToDatabase(currentEstimate);

                if (saveSuccess)
                {
                    // Update toggle caption
                    toggleSwitch.Caption = statusText;

                    // Update form controls based on new status
                    // Convert database status to isActive: true=Closed -> false=NotActive, false=Active -> true=Active
                    UpdateFormControlsForStatus(form, !newStatus);

                    MessageBox.Show(
                        $"Estimate status changed to '{statusText}' successfully.",
                        "Status Updated",
                        MessageBoxButtons.OK,
                        MessageBoxIcon.Information);

                    Debug.WriteLine($"Status change successful: {statusText}");
                }
                else
                {
                    // Save failed - revert changes
                    currentEstimate.Status = !newStatus;
                    toggleSwitch.Checked = !newStatus;
                    toggleSwitch.Caption = !newStatus ? "Closed" : "Active";

                    MessageBox.Show(
                        "Failed to update estimate status. Please check the database connection and try again.",
                        "Status Update Failed",
                        MessageBoxButtons.OK,
                        MessageBoxIcon.Error);

                    Debug.WriteLine("Status change failed - reverted");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in HandleToggleChanged: {ex.Message}");
                MessageBox.Show(
                    $"Error changing estimate status: {ex.Message}",
                    "Status Change Error",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error);
            }
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// Handles the status toggle changed event
        /// </summary>
        private static void OnStatusToggleChanged(dynamic form, object sender, EventArgs e)
        {
            try
            {
                Debug.WriteLine("=== OnStatusToggleChanged: Starting ===");

                var toggleSwitch = sender as BarToggleSwitchItem;
                if (toggleSwitch == null) return;

                var currentEstimate = form.CurrentEstimate as EstimateFormHeaderModel;
                if (currentEstimate == null)
                {
                    Debug.WriteLine("No current estimate - ignoring toggle change");
                    return;
                }

                // Get new status from toggle
                bool newStatus = toggleSwitch.Checked;
                string statusText = newStatus ? "Closed" : "Active";

                Debug.WriteLine($"Toggle changed to: {newStatus} ({statusText})");

                // Confirm the change with user
                var result = MessageBox.Show(
                    $"Are you sure you want to change the estimate status to '{statusText}'?\n\n" +
                    $"• Active: Estimate can be edited and modified\n" +
                    $"• Closed: Estimate becomes read-only and locked",
                    "Confirm Status Change",
                    MessageBoxButtons.YesNo,
                    MessageBoxIcon.Question);

                if (result != DialogResult.Yes)
                {
                    // User cancelled - revert toggle state
                    toggleSwitch.Checked = currentEstimate.Status;
                    Debug.WriteLine("User cancelled status change");
                    return;
                }

                // Update the estimate status
                currentEstimate.Status = newStatus;

                // Save the status change to database
                bool saveSuccess = SaveStatusToDatabase(currentEstimate);

                if (saveSuccess)
                {
                    // Update toggle caption
                    toggleSwitch.Caption = statusText;

                    // Update form controls based on new status
                    // Convert database status to isActive: true=Closed -> false=NotActive, false=Active -> true=Active
                    UpdateFormControlsForStatus(form, !newStatus);

                    MessageBox.Show(
                        $"Estimate status changed to '{statusText}' successfully.",
                        "Status Updated",
                        MessageBoxButtons.OK,
                        MessageBoxIcon.Information);

                    Debug.WriteLine($"Status change successful: {statusText}");
                }
                else
                {
                    // Save failed - revert changes
                    currentEstimate.Status = !newStatus;
                    toggleSwitch.Checked = !newStatus;
                    toggleSwitch.Caption = !newStatus ? "Closed" : "Active";

                    MessageBox.Show(
                        "Failed to update estimate status. Please check the database connection and try again.",
                        "Status Update Failed",
                        MessageBoxButtons.OK,
                        MessageBoxIcon.Error);

                    Debug.WriteLine("Status change failed - reverted");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in OnStatusToggleChanged: {ex.Message}");
                MessageBox.Show(
                    $"Error changing estimate status: {ex.Message}",
                    "Status Change Error",
                    MessageBoxButtons.OK,
                    MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Saves the status change to the database
        /// </summary>
        private static bool SaveStatusToDatabase(EstimateFormHeaderModel estimate)
        {
            try
            {
                Debug.WriteLine($"Saving status change to database: ID={estimate.Id}, Status={estimate.Status}");

                ProgressIndicatorService.Instance.ShowProgress();
                try
                {
                    // Use the existing repository to save the estimate
                    // This will update all fields including the status
                    bool result = EstimateFormRepository.SaveEstimate(estimate);

                    Debug.WriteLine($"Database save result: {result}");
                    return result;
                }
                finally
                {
                    ProgressIndicatorService.Instance.HideProgress();
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error saving status to database: {ex.Message}");
                return false;
            }
        }

        #endregion
    }
}
