using System;
using System.Windows.Forms;

namespace ProManage
{
    /// <summary>
    /// Provides application-wide properties and methods
    /// Equivalent to the My.Application namespace in VB.NET
    /// </summary>
    internal static class Application
    {
        /// <summary>
        /// Gets or sets a value indicating whether user settings should be saved when the application exits
        /// </summary>
        public static bool SaveMySettingsOnExit { get; set; } = true;
    }
}
