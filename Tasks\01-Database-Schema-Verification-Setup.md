# Task 01: Database Schema Verification and Setup

## Objective
Verify and create the required database tables for the RBAC permission system. Ensure all tables exist with proper structure, relationships, and initial data.

## Priority
**FOUNDATION** - Must be completed first (No dependencies)

## Estimated Time
1.5 hours

## Files to Create/Modify
- `Modules/Procedures/Permissions/RBAC-Schema-Setup.sql`
- `Modules/Procedures/Permissions/RBAC-Initial-Data.sql`
- `Modules/Connections/RBACDatabaseSetup.cs`

## Database Tables Required

### 1. roles
```sql
CREATE TABLE roles (
    role_id INT IDENTITY(1,1) PRIMARY KEY,
    role_name NVARCHAR(50) NOT NULL UNIQUE,
    description NVARCHAR(255),
    is_active BIT DEFAULT 1,
    created_date DATETIME DEFAULT GETDATE(),
    modified_date DATETIME DEFAULT GETDATE()
);
```

### 2. role_permissions
```sql
CREATE TABLE role_permissions (
    permission_id INT IDENTITY(1,1) PRIMARY KEY,
    role_id INT NOT NULL,
    form_name NVARCHAR(100) NOT NULL,
    read_permission BIT DEFAULT 0,
    new_permission BIT DEFAULT 0,
    edit_permission BIT DEFAULT 0,
    delete_permission BIT DEFAULT 0,
    print_permission BIT DEFAULT 0,
    created_date DATETIME DEFAULT GETDATE(),
    FOREIGN KEY (role_id) REFERENCES roles(role_id) ON DELETE CASCADE,
    UNIQUE(role_id, form_name)
);
```

### 3. user_permissions
```sql
CREATE TABLE user_permissions (
    user_permission_id INT IDENTITY(1,1) PRIMARY KEY,
    user_id INT NOT NULL,
    form_name NVARCHAR(100) NOT NULL,
    read_permission BIT NULL,     -- NULL = inherit from role
    new_permission BIT NULL,      -- NULL = inherit from role
    edit_permission BIT NULL,     -- NULL = inherit from role
    delete_permission BIT NULL,   -- NULL = inherit from role
    print_permission BIT NULL,    -- NULL = inherit from role
    created_date DATETIME DEFAULT GETDATE(),
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    UNIQUE(user_id, form_name)
);
```

### 4. global_permissions
```sql
CREATE TABLE global_permissions (
    global_permission_id INT IDENTITY(1,1) PRIMARY KEY,
    user_id INT NOT NULL,
    can_create_users BIT DEFAULT 0,
    can_edit_users BIT DEFAULT 0,
    can_delete_users BIT DEFAULT 0,
    can_print_users BIT DEFAULT 0,
    created_date DATETIME DEFAULT GETDATE(),
    FOREIGN KEY (user_id) REFERENCES users(user_id) ON DELETE CASCADE,
    UNIQUE(user_id)
);
```

### 5. Verify users table has role_id
```sql
-- Add role_id column to users table if it doesn't exist
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID('users') AND name = 'role_id')
BEGIN
    ALTER TABLE users ADD role_id INT;
    ALTER TABLE users ADD CONSTRAINT FK_users_roles FOREIGN KEY (role_id) REFERENCES roles(role_id);
END
```

## Initial Data Setup

### Default Roles
```sql
INSERT INTO roles (role_name, description) VALUES
('Administrator', 'Full system access with all permissions'),
('Manager', 'Management level access with most permissions'),
('User', 'Standard user with basic permissions'),
('ReadOnly', 'Read-only access to most forms');
```

### MainForms List (Current)
Based on existing MainForms folder:
- DatabaseForm
- ParametersForm  
- RoleMasterForm
- SQLQueryForm
- UserManagementListForm
- UserMasterForm

### Default Role Permissions
```sql
-- Administrator: All permissions
INSERT INTO role_permissions (role_id, form_name, read_permission, new_permission, edit_permission, delete_permission, print_permission)
SELECT 1, form_name, 1, 1, 1, 1, 1
FROM (VALUES 
    ('DatabaseForm'), ('ParametersForm'), ('RoleMasterForm'), 
    ('SQLQueryForm'), ('UserManagementListForm'), ('UserMasterForm')
) AS forms(form_name);

-- Manager: Most permissions except user management
INSERT INTO role_permissions (role_id, form_name, read_permission, new_permission, edit_permission, delete_permission, print_permission)
SELECT 2, form_name, 
    CASE WHEN form_name IN ('UserManagementListForm', 'UserMasterForm') THEN 1 ELSE 1 END,
    CASE WHEN form_name IN ('UserManagementListForm', 'UserMasterForm') THEN 0 ELSE 1 END,
    CASE WHEN form_name IN ('UserManagementListForm', 'UserMasterForm') THEN 0 ELSE 1 END,
    CASE WHEN form_name IN ('UserManagementListForm', 'UserMasterForm') THEN 0 ELSE 1 END,
    1
FROM (VALUES 
    ('DatabaseForm'), ('ParametersForm'), ('RoleMasterForm'), 
    ('SQLQueryForm'), ('UserManagementListForm'), ('UserMasterForm')
) AS forms(form_name);

-- User: Basic permissions
INSERT INTO role_permissions (role_id, form_name, read_permission, new_permission, edit_permission, delete_permission, print_permission)
SELECT 3, form_name, 
    CASE WHEN form_name IN ('DatabaseForm', 'SQLQueryForm', 'UserManagementListForm', 'UserMasterForm', 'RoleMasterForm') THEN 0 ELSE 1 END,
    CASE WHEN form_name IN ('DatabaseForm', 'SQLQueryForm', 'UserManagementListForm', 'UserMasterForm', 'RoleMasterForm') THEN 0 ELSE 0 END,
    CASE WHEN form_name IN ('DatabaseForm', 'SQLQueryForm', 'UserManagementListForm', 'UserMasterForm', 'RoleMasterForm') THEN 0 ELSE 0 END,
    0, 1
FROM (VALUES 
    ('DatabaseForm'), ('ParametersForm'), ('RoleMasterForm'), 
    ('SQLQueryForm'), ('UserManagementListForm'), ('UserMasterForm')
) AS forms(form_name);

-- ReadOnly: Only read permissions
INSERT INTO role_permissions (role_id, form_name, read_permission, new_permission, edit_permission, delete_permission, print_permission)
SELECT 4, form_name, 1, 0, 0, 0, 1
FROM (VALUES 
    ('DatabaseForm'), ('ParametersForm'), ('RoleMasterForm'), 
    ('SQLQueryForm'), ('UserManagementListForm'), ('UserMasterForm')
) AS forms(form_name);
```

## Implementation Steps

1. **Create SQL Scripts**
   - Create schema setup script with all table definitions
   - Create initial data script with default roles and permissions
   - Include proper error handling and transaction management

2. **Create Database Setup Service**
   - C# class to execute schema setup
   - Verify table existence before creation
   - Execute initial data population
   - Log all operations

3. **Integration Point**
   - Add call to setup service in application startup
   - Add manual setup option in DatabaseForm

## Acceptance Criteria

- [ ] All 5 tables created with proper structure
- [ ] Foreign key relationships established
- [ ] 4 default roles created with appropriate permissions
- [ ] All current MainForms have permission entries for all roles
- [ ] Setup can be run multiple times without errors
- [ ] Proper error handling and logging implemented
- [ ] Transaction rollback on any failure

## Dependencies
None - This is the foundation task

## Next Tasks
This task enables:
- Task 02: Permission Data Models Creation
- Task 03: Forms Configuration Setup
- Task 04: Database Connection Service for Permissions
