using System;
using System.Collections.Generic;
using System.Configuration;
using System.IO;
using System.Diagnostics;

namespace ProManage.Modules.Helpers
{
    /// <summary>
    /// Helper class for managing application configuration settings
    /// </summary>
    public class ConfigurationHelper
    {
        /// <summary>
        /// Path to the development configuration file
        /// </summary>
        private static string DevelopmentConfigPath
        {
            get
            {
                // First check in the output directory
                string outputPath = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Development.config");

                // If not found in output directory, try to find it in the project directory
                if (!File.Exists(outputPath))
                {
                    // Try to find it in the project directory (one level up from bin)
                    string projectDir = Path.GetDirectoryName(Path.GetDirectoryName(AppDomain.CurrentDomain.BaseDirectory));
                    if (projectDir != null)
                    {
                        return Path.Combine(projectDir, "Development.config");
                    }
                }

                return outputPath;
            }
        }

        /// <summary>
        /// Loads database connection settings from Development.config in debug mode or app.config
        /// </summary>
        /// <returns>Connection string parameters as a dictionary</returns>
        public static Dictionary<string, string> LoadDatabaseSettings()
        {
            var settings = new Dictionary<string, string>();

            try
            {
                // Check if we're in debug mode and should load from Development.config
#if DEBUG
                Debug.WriteLine("Looking for Development.config at: " + DevelopmentConfigPath);

                if (File.Exists(DevelopmentConfigPath))
                {
                    Debug.WriteLine("Development.config found, loading settings from it");

                    // Load the configuration file
                    var configMap = new ExeConfigurationFileMap();
                    configMap.ExeConfigFilename = DevelopmentConfigPath;

                    var config = ConfigurationManager.OpenMappedExeConfiguration(configMap, ConfigurationUserLevel.None);

                    // Get the connection string
                    var devConnString = config.ConnectionStrings.ConnectionStrings["MyConnection"]?.ConnectionString;

                    if (!string.IsNullOrWhiteSpace(devConnString))
                    {
                        Debug.WriteLine("Loading connection string from Development.config: " + devConnString);

                        // Parse the connection string
                        ParseConnectionString(devConnString, settings);

                        Debug.WriteLine("Connection settings loaded from Development.config");
                        return settings;
                    }
                }
#endif

                // If we're not in debug mode or couldn't load from Development.config, use app.config
                string connString = ConfigurationManager.ConnectionStrings["MyConnection"]?.ConnectionString;

                if (!string.IsNullOrWhiteSpace(connString))
                {
                    Debug.WriteLine("Loading connection string from app.config: " + connString);

                    // Parse the connection string
                    ParseConnectionString(connString, settings);

                    Debug.WriteLine("Connection settings loaded from app.config");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine("Error loading database settings: " + ex.Message);
                // Don't throw - just return empty settings
            }

            return settings;
        }

        /// <summary>
        /// Parses a connection string into a dictionary of key-value pairs
        /// </summary>
        /// <param name="connectionString">The connection string to parse</param>
        /// <param name="settings">Dictionary to populate with settings</param>
        private static void ParseConnectionString(string connectionString, Dictionary<string, string> settings)
        {
            // Parse the connection string
            var connParams = connectionString.Split(new char[] { ';' }, StringSplitOptions.RemoveEmptyEntries);

            foreach (var param in connParams)
            {
                var keyValue = param.Split(new char[] { '=' }, 2);
                if (keyValue.Length == 2)
                {
                    string key = keyValue[0].Trim();
                    string value = keyValue[1].Trim();

                    // Add to dictionary
                    settings[key] = value;
                }
            }
        }

        /// <summary>
        /// Saves database connection settings to app.config and Development.config in debug mode
        /// </summary>
        /// <param name="server">Database server</param>
        /// <param name="port">Database port</param>
        /// <param name="database">Database name</param>
        /// <param name="username">Database username</param>
        /// <param name="password">Database password</param>
        /// <returns>True if successful, False otherwise</returns>
        public static bool SaveDatabaseSettings(string server, string port, string database, string username, string password)
        {
            try
            {
                // Build the connection string
                string connString = string.Format("Host={0};Port={1};Database={2};Username={3};Password={4};SslMode=Prefer;Trust Server Certificate=true;",
                                                server, port, database, username, password);

                // Save to App.config
                bool appConfigSaved = SaveToAppConfig(connString);

                // In debug mode, also save to Development.config
#if DEBUG
                bool devConfigSaved = SaveToDevelopmentConfig(connString);
                Debug.WriteLine($"Settings saved to Development.config: {devConfigSaved}");
#endif

                return appConfigSaved;
            }
            catch (Exception ex)
            {
                Debug.WriteLine("Error saving database settings: " + ex.Message);
                return false;
            }
        }

        /// <summary>
        /// Saves connection string to App.config
        /// </summary>
        /// <param name="connString">The connection string to save</param>
        /// <returns>True if successful, False otherwise</returns>
        private static bool SaveToAppConfig(string connString)
        {
            try
            {
                // Open the App.config file
                var config = ConfigurationManager.OpenExeConfiguration(ConfigurationUserLevel.None);

                // Update or add the connection string
                if (config.ConnectionStrings.ConnectionStrings["MyConnection"] != null)
                {
                    config.ConnectionStrings.ConnectionStrings["MyConnection"].ConnectionString = connString;
                    config.ConnectionStrings.ConnectionStrings["MyConnection"].ProviderName = "Npgsql";
                }
                else
                {
                    var csSettings = new ConnectionStringSettings("MyConnection", connString, "Npgsql");
                    config.ConnectionStrings.ConnectionStrings.Add(csSettings);
                }

                // Save the changes to App.config
                config.Save(ConfigurationSaveMode.Modified);

                // Refresh the connection strings section
                ConfigurationManager.RefreshSection("connectionStrings");

                Debug.WriteLine("Database settings saved to app.config");
                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine("Error saving to app.config: " + ex.Message);
                return false;
            }
        }

        /// <summary>
        /// Saves connection string to Development.config
        /// </summary>
        /// <param name="connString">The connection string to save</param>
        /// <returns>True if successful, False otherwise</returns>
        private static bool SaveToDevelopmentConfig(string connString)
        {
            try
            {
                // Check if Development.config exists
                if (!File.Exists(DevelopmentConfigPath))
                {
                    // Create a new Development.config file
                    string configContent = "<?xml version=\"1.0\" encoding=\"utf-8\" ?>" + Environment.NewLine +
                                        "<configuration>" + Environment.NewLine +
                                        "  <connectionStrings>" + Environment.NewLine +
                                        $"    <add name=\"MyConnection\" connectionString=\"{connString}\" providerName=\"Npgsql\" />" + Environment.NewLine +
                                        "  </connectionStrings>" + Environment.NewLine +
                                        "</configuration>";

                    File.WriteAllText(DevelopmentConfigPath, configContent);
                    Debug.WriteLine($"Created new Development.config at {DevelopmentConfigPath}");
                    return true;
                }
                else
                {
                    // Update existing Development.config
                    var configMap = new ExeConfigurationFileMap();
                    configMap.ExeConfigFilename = DevelopmentConfigPath;

                    var config = ConfigurationManager.OpenMappedExeConfiguration(configMap, ConfigurationUserLevel.None);

                    // Update or add the connection string
                    if (config.ConnectionStrings.ConnectionStrings["MyConnection"] != null)
                    {
                        config.ConnectionStrings.ConnectionStrings["MyConnection"].ConnectionString = connString;
                        config.ConnectionStrings.ConnectionStrings["MyConnection"].ProviderName = "Npgsql";
                    }
                    else
                    {
                        var csSettings = new ConnectionStringSettings("MyConnection", connString, "Npgsql");
                        config.ConnectionStrings.ConnectionStrings.Add(csSettings);
                    }

                    // Save the changes to Development.config
                    config.Save(ConfigurationSaveMode.Modified);

                    Debug.WriteLine($"Updated existing Development.config at {DevelopmentConfigPath}");
                    return true;
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine("Error saving to Development.config: " + ex.Message);
                return false;
            }
        }

        /// <summary>
        /// Clears database connection settings from app.config and Development.config in debug mode
        /// </summary>
        /// <returns>True if successful, False otherwise</returns>
        public static bool ClearDatabaseSettings()
        {
            try
            {
                // Clear App.config
                bool appConfigCleared = ClearAppConfig();

                // In debug mode, also clear Development.config
#if DEBUG
                bool devConfigCleared = ClearDevelopmentConfig();
                Debug.WriteLine($"Settings cleared from Development.config: {devConfigCleared}");
#endif

                return appConfigCleared;
            }
            catch (Exception ex)
            {
                Debug.WriteLine("Error clearing database settings: " + ex.Message);
                return false;
            }
        }

        /// <summary>
        /// Clears connection string from App.config
        /// </summary>
        /// <returns>True if successful, False otherwise</returns>
        private static bool ClearAppConfig()
        {
            try
            {
                // Open the App.config file
                var config = ConfigurationManager.OpenExeConfiguration(ConfigurationUserLevel.None);

                // Update the connection string to empty
                if (config.ConnectionStrings.ConnectionStrings["MyConnection"] != null)
                {
                    config.ConnectionStrings.ConnectionStrings["MyConnection"].ConnectionString = "";

                    // Save the changes to App.config
                    config.Save(ConfigurationSaveMode.Modified);

                    // Refresh the connection strings section
                    ConfigurationManager.RefreshSection("connectionStrings");

                    Debug.WriteLine("Database settings cleared from app.config");
                    return true;
                }

                return false;
            }
            catch (Exception ex)
            {
                Debug.WriteLine("Error clearing app.config: " + ex.Message);
                return false;
            }
        }

        /// <summary>
        /// Clears connection string from Development.config
        /// </summary>
        /// <returns>True if successful, False otherwise</returns>
        private static bool ClearDevelopmentConfig()
        {
            try
            {
                // Check if Development.config exists
                if (File.Exists(DevelopmentConfigPath))
                {
                    // Update existing Development.config
                    var configMap = new ExeConfigurationFileMap();
                    configMap.ExeConfigFilename = DevelopmentConfigPath;

                    var config = ConfigurationManager.OpenMappedExeConfiguration(configMap, ConfigurationUserLevel.None);

                    // Update the connection string to empty
                    if (config.ConnectionStrings.ConnectionStrings["MyConnection"] != null)
                    {
                        config.ConnectionStrings.ConnectionStrings["MyConnection"].ConnectionString = "";

                        // Save the changes to Development.config
                        config.Save(ConfigurationSaveMode.Modified);

                        Debug.WriteLine($"Database settings cleared from Development.config");
                        return true;
                    }
                }

                return false;
            }
            catch (Exception ex)
            {
                Debug.WriteLine("Error clearing Development.config: " + ex.Message);
                return false;
            }
        }
    }
}
