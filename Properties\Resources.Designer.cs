﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace ProManage.Properties {
    using System;
    
    
    /// <summary>
    ///   A strongly-typed resource class, for looking up localized strings, etc.
    /// </summary>
    // This class was auto-generated by the StronglyTypedResourceBuilder
    // class via a tool like ResGen or Visual Studio.
    // To add or remove a member, edit your .ResX file then rerun ResGen
    // with the /str option, or rebuild your VS project.
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("System.Resources.Tools.StronglyTypedResourceBuilder", "17.0.0.0")]
    [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    public class Resources {
        
        private static global::System.Resources.ResourceManager resourceMan;
        
        private static global::System.Globalization.CultureInfo resourceCulture;
        
        [global::System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1811:AvoidUncalledPrivateCode")]
        internal Resources() {
        }
        
        /// <summary>
        ///   Returns the cached ResourceManager instance used by this class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Resources.ResourceManager ResourceManager {
            get {
                if (object.ReferenceEquals(resourceMan, null)) {
                    global::System.Resources.ResourceManager temp = new global::System.Resources.ResourceManager("ProManage.Properties.Resources", typeof(Resources).Assembly);
                    resourceMan = temp;
                }
                return resourceMan;
            }
        }
        
        /// <summary>
        ///   Overrides the current thread's CurrentUICulture property for all
        ///   resource lookups using this strongly typed resource class.
        /// </summary>
        [global::System.ComponentModel.EditorBrowsableAttribute(global::System.ComponentModel.EditorBrowsableState.Advanced)]
        public static global::System.Globalization.CultureInfo Culture {
            get {
                return resourceCulture;
            }
            set {
                resourceCulture = value;
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        public static DevExpress.Utils.Svg.SvgImage _new {
            get {
                object obj = ResourceManager.GetObject("new", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        public static DevExpress.Utils.Svg.SvgImage actions_edit {
            get {
                object obj = ResourceManager.GetObject("actions_edit", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        public static DevExpress.Utils.Svg.SvgImage admin {
            get {
                object obj = ResourceManager.GetObject("admin", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        public static DevExpress.Utils.Svg.SvgImage attachment {
            get {
                object obj = ResourceManager.GetObject("attachment", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        public static DevExpress.Utils.Svg.SvgImage del {
            get {
                object obj = ResourceManager.GetObject("del", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        public static DevExpress.Utils.Svg.SvgImage export {
            get {
                object obj = ResourceManager.GetObject("export", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        public static DevExpress.Utils.Svg.SvgImage find {
            get {
                object obj = ResourceManager.GetObject("find", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        public static DevExpress.Utils.Svg.SvgImage home {
            get {
                object obj = ResourceManager.GetObject("home", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        public static DevExpress.Utils.Svg.SvgImage key {
            get {
                object obj = ResourceManager.GetObject("key", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        public static DevExpress.Utils.Svg.SvgImage parameters {
            get {
                object obj = ResourceManager.GetObject("parameters", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        public static DevExpress.Utils.Svg.SvgImage preview {
            get {
                object obj = ResourceManager.GetObject("preview", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        public static DevExpress.Utils.Svg.SvgImage quickprint {
            get {
                object obj = ResourceManager.GetObject("quickprint", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        public static DevExpress.Utils.Svg.SvgImage save {
            get {
                object obj = ResourceManager.GetObject("save", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        public static DevExpress.Utils.Svg.SvgImage Save1 {
            get {
                object obj = ResourceManager.GetObject("Save1", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        public static DevExpress.Utils.Svg.SvgImage save2 {
            get {
                object obj = ResourceManager.GetObject("save2", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        public static DevExpress.Utils.Svg.SvgImage settings {
            get {
                object obj = ResourceManager.GetObject("settings", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        public static DevExpress.Utils.Svg.SvgImage toggle {
            get {
                object obj = ResourceManager.GetObject("toggle", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
        
        /// <summary>
        ///   Looks up a localized resource of type DevExpress.Utils.Svg.SvgImage.
        /// </summary>
        public static DevExpress.Utils.Svg.SvgImage tools {
            get {
                object obj = ResourceManager.GetObject("tools", resourceCulture);
                return ((DevExpress.Utils.Svg.SvgImage)(obj));
            }
        }
    }
}
