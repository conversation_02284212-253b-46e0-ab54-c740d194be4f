using System;
using System.Diagnostics;
using System.Reflection;
using System.IO;

namespace ProManage
{
    /// <summary>
    /// Resolves type references and ensures proper namespace resolution
    /// This class helps resolve namespace issues that can occur when converting from VB.NET to C#
    /// </summary>
    public static class TypeResolver
    {
        /// <summary>
        /// Initializes type references to ensure they are properly loaded by the compiler
        /// Call this method at application startup to prevent type resolution issues
        /// </summary>
        public static void InitializeTypeReferences()
        {
            Debug.WriteLine("Initializing type references...");

            // Reference model types
            EnsureTypeLoaded(typeof(ProManage.Modules.Models.EstimateFormHeaderModel));
            EnsureTypeLoaded(typeof(ProManage.Modules.Models.EstimateFormDetailModel));
            EnsureTypeLoaded(typeof(ProManage.Modules.Models.LoginFormUserModel));

            // Reference module types
            EnsureTypeLoaded(typeof(ProManage.Modules.Validation.EstimateFormValidation));
            EnsureTypeLoaded(typeof(ProManage.Modules.UI.EstimateFormUI));
            EnsureTypeLoaded(typeof(ProManage.Modules.Helpers.EstimateFormGridHelpers));
            EnsureTypeLoaded(typeof(ProManage.Modules.EventHandlers.EstimateFormEventHandlers));
            EnsureTypeLoaded(typeof(ProManage.Modules.Data.EstimateFormDataAccess));

            // Initialize Telerik UI component references
            InitializeTelerikReferences();
            
            // Check if RadRibbonBar is available
            CheckRadRibbonBarAvailability();

            Debug.WriteLine("Type references initialized successfully");
        }
        
        /// <summary>
        /// Checks if the RadRibbonBar component is available in the loaded assemblies
        /// </summary>
        private static void CheckRadRibbonBarAvailability()
        {
            try
            {
                Debug.WriteLine("Checking RadRibbonBar availability...");
                
                // Look for the RadRibbonBar type in all loaded assemblies
                foreach (Assembly assembly in AppDomain.CurrentDomain.GetAssemblies())
                {
                    if (assembly.FullName.Contains("Telerik"))
                    {
                        try
                        {
                            Type[] types = assembly.GetExportedTypes();
                            Type ribbonBarType = types.FirstOrDefault(t => t.Name == "RadRibbonBar");
                            
                            if (ribbonBarType != null)
                            {
                                Debug.WriteLine($"RadRibbonBar found in assembly: {assembly.FullName}");
                                return;
                            }
                        }
                        catch (Exception ex)
                        {
                            Debug.WriteLine($"Error getting types from assembly {assembly.FullName}: {ex.Message}");
                        }
                    }
                }
                
                Debug.WriteLine("WARNING: RadRibbonBar type not found in any loaded assembly");
                
                // Check if the UI.RibbonBar.dll exists, which might contain the RadRibbonBar component
                string baseDir = AppDomain.CurrentDomain.BaseDirectory;
                string telerikPath = Path.Combine(baseDir, "lib", "RCWF", "2024.4.1113.48");
                string ribbonBarPath = Path.Combine(telerikPath, "Telerik.WinControls.UI.RibbonBar.dll");
                
                if (File.Exists(ribbonBarPath))
                {
                    Debug.WriteLine($"Found Telerik.WinControls.UI.RibbonBar.dll at: {ribbonBarPath}");
                    try
                    {
                        Assembly ribbonAssembly = Assembly.LoadFrom(ribbonBarPath);
                        Debug.WriteLine($"Loaded RibbonBar assembly: {ribbonAssembly.FullName}");
                    }
                    catch (Exception ex)
                    {
                        Debug.WriteLine($"Error loading RibbonBar assembly: {ex.Message}");
                    }
                }
                else
                {
                    Debug.WriteLine("Telerik.WinControls.UI.RibbonBar.dll not found");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error checking RadRibbonBar availability: {ex.Message}");
            }
        }

        /// <summary>
        /// Ensures a type is loaded by the compiler
        /// </summary>
        /// <param name="type">The type to ensure is loaded</param>
        private static void EnsureTypeLoaded(Type type)
        {
            Debug.WriteLine($"Ensuring type is loaded: {type.FullName}");
        }
        
        /// <summary>
        /// Initializes Telerik UI component references to prevent designer conflicts
        /// </summary>
        private static void InitializeTelerikReferences()
        {
            try
            {
                Debug.WriteLine("Initializing Telerik UI references...");
                
                // Register the assembly resolve event to help locate Telerik assemblies
                AppDomain.CurrentDomain.AssemblyResolve += CurrentDomain_AssemblyResolve;
                
                // Get the current directory and check for Telerik assemblies in multiple locations
                string baseDir = AppDomain.CurrentDomain.BaseDirectory;
                string telerikPath = Path.Combine(baseDir, "lib", "RCWF", "2024.4.1113.48");
                
                // If the path doesn't exist, try to find the correct path
                if (!Directory.Exists(telerikPath))
                {
                    Debug.WriteLine($"Telerik path not found at: {telerikPath}");
                    
                    // Try to find the RCWF directory
                    string projectDir = Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location);
                    string[] possiblePaths = new string[]
                    {
                        Path.Combine(projectDir, "lib", "RCWF", "2024.4.1113.48"),
                        Path.Combine(projectDir, "..", "lib", "RCWF", "2024.4.1113.48"),
                        Path.Combine(projectDir, "..", "..", "lib", "RCWF", "2024.4.1113.48")
                    };
                    
                    foreach (string path in possiblePaths)
                    {
                        if (Directory.Exists(path))
                        {
                            telerikPath = path;
                            Debug.WriteLine($"Found Telerik path at: {telerikPath}");
                            break;
                        }
                    }
                }
                
                // Preload key Telerik UI assemblies to avoid designer conflicts
                LoadTelerikAssembly(telerikPath, "Telerik.WinControls.dll");
                LoadTelerikAssembly(telerikPath, "Telerik.WinControls.UI.dll");
                LoadTelerikAssembly(telerikPath, "Telerik.WinControls.UI.Design.dll");
                LoadTelerikAssembly(telerikPath, "TelerikCommon.dll");
                
                // Load additional Telerik assemblies that might be needed for RadRibbonBar
                LoadTelerikAssembly(telerikPath, "Telerik.WinControls.RadDock.dll");
                LoadTelerikAssembly(telerikPath, "TelerikData.dll");
                
                Debug.WriteLine("Telerik UI references initialized successfully");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error initializing Telerik references: {ex.Message}");
            }
        }

        /// <summary>
        /// Loads a Telerik assembly from the specified path
        /// </summary>
        /// <param name="path">Base path for Telerik assemblies</param>
        /// <param name="assemblyName">Assembly filename to load</param>
        private static void LoadTelerikAssembly(string path, string assemblyName)
        {
            try
            {
                string fullPath = Path.Combine(path, assemblyName);
                Debug.WriteLine($"Attempting to load assembly from: {fullPath}");
                
                if (File.Exists(fullPath))
                {
                    try
                    {
                        Assembly assembly = Assembly.LoadFrom(fullPath);
                        Debug.WriteLine($"Successfully loaded Telerik assembly: {assembly.FullName}");
                        
                        // Verify that the assembly contains the expected types
                        if (assemblyName.Equals("Telerik.WinControls.UI.dll", StringComparison.OrdinalIgnoreCase))
                        {
                            Type[] types = assembly.GetExportedTypes();
                            bool hasRibbonBar = types.Any(t => t.Name == "RadRibbonBar");
                            Debug.WriteLine($"Assembly contains RadRibbonBar type: {hasRibbonBar}");
                            
                            if (!hasRibbonBar)
                            {
                                Debug.WriteLine("WARNING: RadRibbonBar type not found in Telerik.WinControls.UI.dll");
                            }
                        }
                    }
                    catch (BadImageFormatException bex)
                    {
                        Debug.WriteLine($"Bad image format for {assemblyName}: {bex.Message}");
                        Debug.WriteLine("This may indicate a 32-bit/64-bit mismatch or corrupted assembly");
                    }
                    catch (Exception ex)
                    {
                        Debug.WriteLine($"Error loading assembly {assemblyName}: {ex.Message}");
                    }
                }
                else
                {
                    Debug.WriteLine($"Warning: Telerik assembly not found: {fullPath}");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error loading Telerik assembly {assemblyName}: {ex.Message}");
                if (ex.InnerException != null)
                {
                    Debug.WriteLine($"Inner exception: {ex.InnerException.Message}");
                }
            }
        }

        /// <summary>
        /// Assembly resolve handler to help locate Telerik assemblies
        /// </summary>
        private static Assembly CurrentDomain_AssemblyResolve(object sender, ResolveEventArgs args)
        {
            try
            {
                // Only handle Telerik assemblies
                if (!args.Name.StartsWith("Telerik") && !args.Name.StartsWith("TelerikCommon"))
                {
                    return null;
                }
                
                Debug.WriteLine($"Resolving assembly: {args.Name}");
                
                // Extract the simple name from the full assembly name
                string simpleName = args.Name.Split(',')[0];
                
                // Try multiple possible locations for the Telerik assemblies
                string[] possiblePaths = new string[]
                {
                    // Try the application base directory first
                    Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "lib", "RCWF", "2024.4.1113.48"),
                    
                    // Try the executable directory
                    Path.Combine(Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location), "lib", "RCWF", "2024.4.1113.48"),
                    
                    // Try one level up from the executable directory
                    Path.Combine(Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location), "..", "lib", "RCWF", "2024.4.1113.48"),
                    
                    // Try two levels up from the executable directory
                    Path.Combine(Path.GetDirectoryName(Assembly.GetExecutingAssembly().Location), "..", "..", "lib", "RCWF", "2024.4.1113.48")
                };
                
                foreach (string basePath in possiblePaths)
                {
                    string assemblyPath = Path.Combine(basePath, simpleName + ".dll");
                    
                    if (File.Exists(assemblyPath))
                    {
                        Debug.WriteLine($"Found assembly at: {assemblyPath}");
                        try
                        {
                            return Assembly.LoadFrom(assemblyPath);
                        }
                        catch (Exception loadEx)
                        {
                            Debug.WriteLine($"Error loading assembly from {assemblyPath}: {loadEx.Message}");
                            // Continue to the next path if loading fails
                        }
                    }
                }
                
                Debug.WriteLine($"Could not find assembly: {simpleName}.dll in any of the search paths");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in assembly resolve: {ex.Message}");
                if (ex.InnerException != null)
                {
                    Debug.WriteLine($"Inner exception: {ex.InnerException.Message}");
                }
            }
            
            return null;
        }
    }
}
