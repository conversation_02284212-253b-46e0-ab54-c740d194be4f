-- EstimateNavigation.sql
-- Clean, efficient navigation queries for estimates
-- <PERSON>les proper JOIN between estimateheader and estimatedetails tables
-- Supports all four navigation operations: First, Previous, Next, Last

-- [GetFirstEstimate] --
SELECT
    h.id,
    h.estimate_no,
    h.customer_name,
    h.vin,
    h.brand,
    h.date,
    h.location,
    h.vehicle_model,
    h.salesman_name,
    h.status,
    h.remarks,
    h.created_at,
    h.modify_at
FROM
    estimateheader h
WHERE
    h.id = (SELECT MIN(id) FROM estimateheader WHERE id > 0)
ORDER BY h.id;
-- [End] --

-- [GetLastEstimate] --
SELECT
    h.id,
    h.estimate_no,
    h.customer_name,
    h.vin,
    h.brand,
    h.date,
    h.location,
    h.vehicle_model,
    h.salesman_name,
    h.status,
    h.remarks,
    h.created_at,
    h.modify_at
FROM
    estimateheader h
WHERE
    h.id = (SELECT MAX(id) FROM estimateheader WHERE id > 0)
ORDER BY h.id;
-- [End] --

-- [GetPreviousEstimate] --
SELECT
    h.id,
    h.estimate_no,
    h.customer_name,
    h.vin,
    h.brand,
    h.date,
    h.location,
    h.vehicle_model,
    h.salesman_name,
    h.status,
    h.remarks,
    h.created_at,
    h.modify_at
FROM
    estimateheader h
WHERE
    h.id = (SELECT MAX(id) FROM estimateheader WHERE id < @current_id)
ORDER BY h.id;
-- [End] --

-- [GetNextEstimate] --
SELECT
    h.id,
    h.estimate_no,
    h.customer_name,
    h.vin,
    h.brand,
    h.date,
    h.location,
    h.vehicle_model,
    h.salesman_name,
    h.status,
    h.remarks,
    h.created_at,
    h.modify_at
FROM
    estimateheader h
WHERE
    h.id = (SELECT MIN(id) FROM estimateheader WHERE id > @current_id)
ORDER BY h.id;
-- [End] --

-- [GetEstimateDetails] --
SELECT
    d.id,
    d.estimate_id,
    d.part_no,
    d.description,
    d.qty,
    d.oe_price,
    d.afm_price,
    d.remarks,
    d.approve_status,
    d.created_at,
    d.modify_at
FROM
    estimatedetails d
WHERE
    d.estimate_id = @estimate_id
ORDER BY
    d.id;
-- [End] --

-- [GetEstimateCount] --
SELECT COUNT(*) FROM estimateheader WHERE id > 0;
-- [End] --

-- [GetEstimatePosition] --
SELECT COUNT(*) + 1
FROM estimateheader
WHERE id < @estimate_id AND id > 0;
-- [End] --
