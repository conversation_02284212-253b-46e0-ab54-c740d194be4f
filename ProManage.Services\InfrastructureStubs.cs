using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.Windows.Forms;

namespace ProManage.Services.Models
{
    /// <summary>
    /// Data model for estimate header information
    /// </summary>
    public class EstimateHeader
    {
        /// <summary>
        /// Primary key ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// Estimate number (unique)
        /// </summary>
        public string EstimateNo { get; set; }

        /// <summary>
        /// Customer name
        /// </summary>
        public string CustomerName { get; set; }

        /// <summary>
        /// Vehicle Identification Number
        /// </summary>
        public string VIN { get; set; }

        /// <summary>
        /// Vehicle brand
        /// </summary>
        public string Brand { get; set; }

        /// <summary>
        /// Document date
        /// </summary>
        public DateTime? DocDate { get; set; }

        /// <summary>
        /// Location
        /// </summary>
        public string Location { get; set; }

        /// <summary>
        /// Vehicle model
        /// </summary>
        public string VehicleModel { get; set; }

        /// <summary>
        /// Salesman name
        /// </summary>
        public string SalesmanName { get; set; }

        /// <summary>
        /// Created date
        /// </summary>
        public DateTime? CreatedAt { get; set; }

        /// <summary>
        /// Created by username
        /// </summary>
        public string CreatedBy { get; set; }

        /// <summary>
        /// Status of the estimate (True = Closed/Locked, False = Open/Editable)
        /// </summary>
        public bool Status { get; set; } = false;

        /// <summary>
        /// List of detail items
        /// </summary>
        public List<EstimateDetail> Details { get; set; }

        /// <summary>
        /// Constructor
        /// </summary>
        public EstimateHeader()
        {
            Details = new List<EstimateDetail>();
        }
    }

    /// <summary>
    /// Data model for estimate detail line items
    /// </summary>
    public class EstimateDetail
    {
        /// <summary>
        /// Primary key ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// Foreign key to EstimateHeader
        /// </summary>
        public int EstimateId { get; set; }

        /// <summary>
        /// Part number
        /// </summary>
        public string PartNo { get; set; }

        /// <summary>
        /// Part description
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// Quantity
        /// </summary>
        public int? Qty { get; set; }

        /// <summary>
        /// OE Price
        /// </summary>
        public decimal? OEPrice { get; set; }

        /// <summary>
        /// AFM Price
        /// </summary>
        public decimal? AFMPrice { get; set; }

        /// <summary>
        /// Remarks
        /// </summary>
        public string Remarks { get; set; }
    }
}

namespace ProManage.Services
{
    // Database transaction stub
    public static class DatabaseTransactionService
    {
        public static void Begin()   { /* no-op */ }
        public static void Commit()  { /* no-op */ }
        public static void Rollback(){ /* no-op */ }
    }

    // Query stub for Estimates
    public static class EstimateQueryService
    {
        public static DataTable GetEstimateHeaders(int userId)
        {
            return new DataTable(); // TODO: real implementation
        }

        public static Models.EstimateHeader GetEstimateById(int id)
        {
            Debug.WriteLine($"EstimateQueryService: Getting estimate by ID: {id}");
            return new Models.EstimateHeader(); // Stub implementation
        }

        public static bool SaveEstimate(Models.EstimateHeader estimate)
        {
            Debug.WriteLine($"EstimateQueryService: Saving estimate: {estimate.EstimateNo}");
            return true; // Stub implementation
        }

        public static string GetNextEstimateNumber()
        {
            Debug.WriteLine("EstimateQueryService: Getting next estimate number");
            return DateTime.Now.ToString("yyyyMMdd-") + "001"; // Stub implementation
        }
    }

    // Progress indicator stub
    public class ProgressIndicatorService
    {
        private bool _isVisible = false;
        private static ProgressIndicatorService _instance;

        // Private constructor for singleton pattern
        private ProgressIndicatorService() { }

        // Singleton instance
        public static ProgressIndicatorService Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = new ProgressIndicatorService();
                }
                return _instance;
            }
        }

        public void Show(string msg)
        {
            Debug.WriteLine($"ProgressIndicator: {msg}");
            _isVisible = true;
        }

        public void Hide()
        {
            Debug.WriteLine("ProgressIndicator: Hidden");
            _isVisible = false;
        }

        public void ShowProgress()
        {
            Show("Processing...");
        }

        public void HideProgress()
        {
            Hide();
        }

        public bool IsVisible => _isVisible;

        // Static methods for backward compatibility
        public static void ShowStatic(string msg)
        {
            Instance.Show(msg);
        }

        public static void HideStatic()
        {
            Instance.Hide();
        }

        public static void ShowProgressStatic()
        {
            Instance.ShowProgress();
        }

        public static void HideProgressStatic()
        {
            Instance.HideProgress();
        }

        public static bool IsVisibleStatic => Instance.IsVisible;
    }

    // Simple DI placeholder
    public static class TypeResolver
    {
        public static T Resolve<T>()
        {
            return default(T);
        }

        public static void InitializeTypeReferences()
        {
            Debug.WriteLine("TypeResolver: Initializing type references");
            // No-op in stub implementation
        }
    }

    // Theme manager stub
    public static class ThemeManager
    {
        public static void ApplyDarkTheme(Form f) { /* no-op */ }

        public static void Initialize()
        {
            Debug.WriteLine("ThemeManager: Initializing");
            // No-op in stub implementation
        }
    }

    // License manager stub
    public static class LicenseManager
    {
        public static bool Validate(string key)
        {
            return true;
        }

        public static string UsageMode
        {
            get { return "Full"; }
        }
    }

    // Connection manager stub
    public static class DatabaseConnectionManager
    {
        public static bool IsConfigured()
        {
            return true;
        }
    }

    // Form control helper stub
    public static class FormControlHelper
    {
        public static void SetContainerControlsEnabled(Control container, bool enabled)
        {
            if (container == null) return;

            foreach (Control control in container.Controls)
            {
                // Skip labels and other non-interactive controls
                if (control is Label) continue;

                // Enable/disable the control
                control.Enabled = enabled;

                // Recursively process child controls if this is a container
                if (control.Controls.Count > 0)
                {
                    SetContainerControlsEnabled(control, enabled);
                }
            }
        }
    }
}
