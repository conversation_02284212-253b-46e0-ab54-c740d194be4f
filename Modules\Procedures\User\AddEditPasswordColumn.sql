-- AddEditPasswordColumn.sql
-- <PERSON><PERSON><PERSON> to add edit password column for simplified password management (hash only, no salt)

-- Add edit_password_hash column to users table 
-- This will store the editable password in hash format without salt
ALTER TABLE users
ADD COLUMN edit_password_hash VARCHAR(255);

-- Add comment for documentation
COMMENT ON COLUMN users.edit_password_hash IS 'Stores editable password hash (no salt required) for user password updates';

-- Verify the changes
SELECT column_name, data_type, character_maximum_length, is_nullable, column_default
FROM information_schema.columns 
WHERE table_schema = 'public' AND table_name = 'users' 
AND column_name = 'edit_password_hash';
