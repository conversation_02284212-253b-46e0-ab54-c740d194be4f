using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using ProManage.Modules.Services;
using ProManage.Modules.Connections;
using ProManage.Modules.Models.PermissionManagementForm;

namespace ProManage.Modules.Testing
{
    /// <summary>
    /// Helper class for permission system testing and validation
    /// </summary>
    public static class PermissionTestHelper
    {
        #region Test Data Management

        /// <summary>
        /// Create test role with specified permissions
        /// </summary>
        /// <param name="roleName">Name of the test role</param>
        /// <param name="description">Role description</param>
        /// <param name="permissions">Dictionary of form permissions</param>
        /// <returns>Created role ID</returns>
        public static int CreateTestRole(string roleName, string description, Dictionary<string, FormPermissions> permissions = null)
        {
            try
            {
                var roleRequest = new RoleCreateRequest
                {
                    RoleName = roleName,
                    Description = description,
                    IsActive = true
                };

                var roleId = PermissionDatabaseService.CreateRole(roleRequest);

                if (permissions != null)
                {
                    var rolePermissions = new List<RolePermissionUpdate>();
                    foreach (var kvp in permissions)
                    {
                        rolePermissions.Add(new RolePermissionUpdate
                        {
                            RoleId = roleId,
                            FormName = kvp.Key,
                            ReadPermission = kvp.Value.Read ?? false,
                            NewPermission = kvp.Value.New ?? false,
                            EditPermission = kvp.Value.Edit ?? false,
                            DeletePermission = kvp.Value.Delete ?? false,
                            PrintPermission = kvp.Value.Print ?? false
                        });
                    }

                    PermissionService.UpdateRolePermissions(roleId, rolePermissions);
                }

                Debug.WriteLine($"Created test role: {roleName} with ID: {roleId}");
                return roleId;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error creating test role {roleName}: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Set user permission overrides for testing
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="formName">Form name</param>
        /// <param name="permissions">Permission overrides</param>
        public static void SetUserPermissionOverrides(int userId, string formName, FormPermissions permissions)
        {
            try
            {
                var userPermissions = new List<UserPermissionUpdate>
                {
                    new UserPermissionUpdate
                    {
                        UserId = userId,
                        FormName = formName,
                        ReadPermission = permissions.Read,
                        NewPermission = permissions.New,
                        EditPermission = permissions.Edit,
                        DeletePermission = permissions.Delete,
                        PrintPermission = permissions.Print
                    }
                };

                PermissionService.UpdateUserPermissions(userId, userPermissions);
                Debug.WriteLine($"Set user permission overrides for user {userId} on form {formName}");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error setting user permission overrides: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Set global permissions for testing
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <param name="canCreateUsers">Can create users permission</param>
        /// <param name="canEditUsers">Can edit users permission</param>
        /// <param name="canDeleteUsers">Can delete users permission</param>
        /// <param name="canPrintUsers">Can print users permission</param>
        public static void SetGlobalPermissions(int userId, bool canCreateUsers = false, bool canEditUsers = false, 
            bool canDeleteUsers = false, bool canPrintUsers = false)
        {
            try
            {
                var globalPermission = new GlobalPermissionUpdate
                {
                    UserId = userId,
                    CanCreateUsers = canCreateUsers,
                    CanEditUsers = canEditUsers,
                    CanDeleteUsers = canDeleteUsers,
                    CanPrintUsers = canPrintUsers
                };

                PermissionService.UpdateGlobalPermissions(userId, globalPermission);
                Debug.WriteLine($"Set global permissions for user {userId}");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error setting global permissions: {ex.Message}");
                throw;
            }
        }

        #endregion

        #region Validation Methods

        /// <summary>
        /// Validate that permission system is working correctly
        /// </summary>
        /// <returns>Validation result with details</returns>
        public static ValidationResult ValidatePermissionSystem()
        {
            var result = new ValidationResult();
            var stopwatch = Stopwatch.StartNew();

            try
            {
                // Test 1: Basic permission checking
                result.Tests.Add(ValidateBasicPermissionChecking());

                // Test 2: Role permission inheritance
                result.Tests.Add(ValidateRolePermissionInheritance());

                // Test 3: User permission overrides
                result.Tests.Add(ValidateUserPermissionOverrides());

                // Test 4: Global permissions
                result.Tests.Add(ValidateGlobalPermissions());

                // Test 5: Cache performance
                result.Tests.Add(ValidateCachePerformance());

                // Test 6: Database integrity
                result.Tests.Add(ValidateDatabaseIntegrity());

                stopwatch.Stop();
                result.TotalExecutionTime = stopwatch.ElapsedMilliseconds;
                result.OverallSuccess = result.Tests.All(t => t.Success);

                Debug.WriteLine($"Permission system validation completed in {result.TotalExecutionTime}ms");
                Debug.WriteLine($"Overall result: {(result.OverallSuccess ? "PASS" : "FAIL")}");

                return result;
            }
            catch (Exception ex)
            {
                result.Tests.Add(new TestResult
                {
                    TestName = "System Validation",
                    Success = false,
                    ErrorMessage = ex.Message,
                    ExecutionTime = stopwatch.ElapsedMilliseconds
                });

                result.OverallSuccess = false;
                return result;
            }
        }

        /// <summary>
        /// Performance test for permission checking
        /// </summary>
        /// <param name="iterations">Number of iterations to test</param>
        /// <returns>Performance metrics</returns>
        public static PerformanceMetrics TestPermissionPerformance(int iterations = 1000)
        {
            var metrics = new PerformanceMetrics();
            var stopwatch = new Stopwatch();

            try
            {
                // Warm up cache
                for (int i = 0; i < 10; i++)
                {
                    PermissionService.HasPermission(1, "UserMasterForm", PermissionType.Read);
                }

                // Test permission checking performance
                stopwatch.Start();
                for (int i = 0; i < iterations; i++)
                {
                    PermissionService.HasPermission(1, "UserMasterForm", PermissionType.Read);
                }
                stopwatch.Stop();

                metrics.PermissionCheckTime = (double)stopwatch.ElapsedMilliseconds / iterations;
                metrics.TotalIterations = iterations;
                metrics.TotalTime = stopwatch.ElapsedMilliseconds;

                Debug.WriteLine($"Permission check performance: {metrics.PermissionCheckTime:F2}ms per check");
                return metrics;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Performance test error: {ex.Message}");
                metrics.ErrorMessage = ex.Message;
                return metrics;
            }
        }

        #endregion

        #region Private Validation Methods

        private static TestResult ValidateBasicPermissionChecking()
        {
            try
            {
                var stopwatch = Stopwatch.StartNew();

                // Test basic permission checking
                var hasPermission = PermissionService.HasPermission(1, "UserMasterForm", PermissionType.Read);
                var hasGlobalPermission = PermissionService.HasGlobalPermission(1, GlobalPermissionType.CanCreateUsers);

                stopwatch.Stop();

                return new TestResult
                {
                    TestName = "Basic Permission Checking",
                    Success = true,
                    ExecutionTime = stopwatch.ElapsedMilliseconds,
                    Details = $"Permission check: {hasPermission}, Global permission check: {hasGlobalPermission}"
                };
            }
            catch (Exception ex)
            {
                return new TestResult
                {
                    TestName = "Basic Permission Checking",
                    Success = false,
                    ErrorMessage = ex.Message
                };
            }
        }

        private static TestResult ValidateRolePermissionInheritance()
        {
            try
            {
                var stopwatch = Stopwatch.StartNew();

                // Test role permission inheritance
                var roles = PermissionService.GetAllRoles();
                var testPassed = roles.Count > 0;

                stopwatch.Stop();

                return new TestResult
                {
                    TestName = "Role Permission Inheritance",
                    Success = testPassed,
                    ExecutionTime = stopwatch.ElapsedMilliseconds,
                    Details = $"Found {roles.Count} roles"
                };
            }
            catch (Exception ex)
            {
                return new TestResult
                {
                    TestName = "Role Permission Inheritance",
                    Success = false,
                    ErrorMessage = ex.Message
                };
            }
        }

        private static TestResult ValidateUserPermissionOverrides()
        {
            try
            {
                var stopwatch = Stopwatch.StartNew();

                // Test user permission overrides
                var users = PermissionService.GetAllUsers();
                var testPassed = users.Count > 0;

                stopwatch.Stop();

                return new TestResult
                {
                    TestName = "User Permission Overrides",
                    Success = testPassed,
                    ExecutionTime = stopwatch.ElapsedMilliseconds,
                    Details = $"Found {users.Count} users"
                };
            }
            catch (Exception ex)
            {
                return new TestResult
                {
                    TestName = "User Permission Overrides",
                    Success = false,
                    ErrorMessage = ex.Message
                };
            }
        }

        private static TestResult ValidateGlobalPermissions()
        {
            try
            {
                var stopwatch = Stopwatch.StartNew();

                // Test global permissions
                var globalPermissions = PermissionService.GetGlobalPermissions(1);
                var testPassed = true; // Global permissions can be null

                stopwatch.Stop();

                return new TestResult
                {
                    TestName = "Global Permissions",
                    Success = testPassed,
                    ExecutionTime = stopwatch.ElapsedMilliseconds,
                    Details = $"Global permissions loaded: {globalPermissions != null}"
                };
            }
            catch (Exception ex)
            {
                return new TestResult
                {
                    TestName = "Global Permissions",
                    Success = false,
                    ErrorMessage = ex.Message
                };
            }
        }

        private static TestResult ValidateCachePerformance()
        {
            try
            {
                var stopwatch = Stopwatch.StartNew();

                // Test cache performance
                for (int i = 0; i < 100; i++)
                {
                    PermissionService.HasPermission(1, "UserMasterForm", PermissionType.Read);
                }

                stopwatch.Stop();
                var avgTime = (double)stopwatch.ElapsedMilliseconds / 100;

                return new TestResult
                {
                    TestName = "Cache Performance",
                    Success = avgTime < 10, // Should be under 10ms average
                    ExecutionTime = stopwatch.ElapsedMilliseconds,
                    Details = $"Average permission check time: {avgTime:F2}ms"
                };
            }
            catch (Exception ex)
            {
                return new TestResult
                {
                    TestName = "Cache Performance",
                    Success = false,
                    ErrorMessage = ex.Message
                };
            }
        }

        private static TestResult ValidateDatabaseIntegrity()
        {
            try
            {
                var stopwatch = Stopwatch.StartNew();

                // Test database integrity
                var summary = PermissionDatabaseService.GetPermissionSummary();
                var testPassed = summary.ActiveRoles > 0 && summary.FormsInSystem > 0;

                stopwatch.Stop();

                return new TestResult
                {
                    TestName = "Database Integrity",
                    Success = testPassed,
                    ExecutionTime = stopwatch.ElapsedMilliseconds,
                    Details = $"Roles: {summary.ActiveRoles}, Forms: {summary.FormsInSystem}, Users: {summary.ActiveUsers}"
                };
            }
            catch (Exception ex)
            {
                return new TestResult
                {
                    TestName = "Database Integrity",
                    Success = false,
                    ErrorMessage = ex.Message
                };
            }
        }

        #endregion
    }

    #region Helper Classes

    /// <summary>
    /// Form permissions structure for testing
    /// </summary>
    public class FormPermissions
    {
        public bool? Read { get; set; }
        public bool? New { get; set; }
        public bool? Edit { get; set; }
        public bool? Delete { get; set; }
        public bool? Print { get; set; }
    }

    /// <summary>
    /// Validation result structure
    /// </summary>
    public class ValidationResult
    {
        public List<TestResult> Tests { get; set; } = new List<TestResult>();
        public bool OverallSuccess { get; set; }
        public long TotalExecutionTime { get; set; }
    }

    /// <summary>
    /// Individual test result
    /// </summary>
    public class TestResult
    {
        public string TestName { get; set; }
        public bool Success { get; set; }
        public long ExecutionTime { get; set; }
        public string ErrorMessage { get; set; }
        public string Details { get; set; }
    }

    /// <summary>
    /// Performance metrics structure
    /// </summary>
    public class PerformanceMetrics
    {
        public double PermissionCheckTime { get; set; }
        public int TotalIterations { get; set; }
        public long TotalTime { get; set; }
        public string ErrorMessage { get; set; }
    }

    #endregion
}
