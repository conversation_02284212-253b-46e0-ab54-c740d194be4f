﻿using DevExpress.XtraBars.Ribbon;
using System;
using System.Drawing;
using System.ComponentModel;
using System.Windows.Forms;

namespace ProManage.Forms
{
    partial class EstimateForm
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(EstimateForm));
            this.RibbonControl = new DevExpress.XtraBars.Ribbon.RibbonControl();
            this.BarButtonItemFirst = new DevExpress.XtraBars.BarButtonItem();
            this.BarButtonItemPrevious = new DevExpress.XtraBars.BarButtonItem();
            this.BarButtonItemNext = new DevExpress.XtraBars.BarButtonItem();
            this.BarButtonItemLast = new DevExpress.XtraBars.BarButtonItem();
            this.BarButtonItemNew = new DevExpress.XtraBars.BarButtonItem();
            this.BarButtonItemEdit = new DevExpress.XtraBars.BarButtonItem();
            this.BarButtonItemSave = new DevExpress.XtraBars.BarButtonItem();
            this.BarButtonItemCancel = new DevExpress.XtraBars.BarButtonItem();
            this.BarButtonItemDelete = new DevExpress.XtraBars.BarButtonItem();
            this.BarButtonItemAddRow = new DevExpress.XtraBars.BarButtonItem();
            this.BarButtonItemPrint = new DevExpress.XtraBars.BarButtonItem();
            this.tglStatus = new DevExpress.XtraBars.BarToggleSwitchItem();
            this.BarButtonPrint = new DevExpress.XtraBars.BarButtonItem();
            this.BarButtonPrintPreview = new DevExpress.XtraBars.BarButtonItem();
            this.RibbonPageEstimate = new DevExpress.XtraBars.Ribbon.RibbonPage();
            this.RibbonPageGroupOperations = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.RibbonPageGroupGrid = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.RibbonPageGroupStatus = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.RibbonPageGroupNavigation = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.ribbonPageGroup1 = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.SplitContainer1 = new System.Windows.Forms.SplitContainer();
            this.gbEstiamateHeader = new System.Windows.Forms.GroupBox();
            this.txtDocRemarks = new DevExpress.XtraEditors.MemoEdit();
            this.labelControl1 = new DevExpress.XtraEditors.LabelControl();
            this.lblEstimate = new DevExpress.XtraEditors.LabelControl();
            this.txtEstimate = new DevExpress.XtraEditors.TextEdit();
            this.lblCustomer = new DevExpress.XtraEditors.LabelControl();
            this.txtCustomer = new DevExpress.XtraEditors.TextEdit();
            this.lblVehicle = new DevExpress.XtraEditors.LabelControl();
            this.txtVehicle = new DevExpress.XtraEditors.TextEdit();
            this.lblVIN = new DevExpress.XtraEditors.LabelControl();
            this.txtVIN = new DevExpress.XtraEditors.TextEdit();
            this.lblBrand = new DevExpress.XtraEditors.LabelControl();
            this.cbBrand = new DevExpress.XtraEditors.ComboBoxEdit();
            this.lblLocation = new DevExpress.XtraEditors.LabelControl();
            this.cbLocation = new DevExpress.XtraEditors.ComboBoxEdit();
            this.lblSalesman = new DevExpress.XtraEditors.LabelControl();
            this.txtSalesman = new DevExpress.XtraEditors.TextEdit();
            this.lblDate = new DevExpress.XtraEditors.LabelControl();
            this.dpDocDate = new DevExpress.XtraEditors.DateEdit();
            this.GridControl1 = new DevExpress.XtraGrid.GridControl();
            this.GridView1 = new DevExpress.XtraGrid.Views.Grid.GridView();
            ((System.ComponentModel.ISupportInitialize)(this.RibbonControl)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.SplitContainer1)).BeginInit();
            this.SplitContainer1.Panel1.SuspendLayout();
            this.SplitContainer1.Panel2.SuspendLayout();
            this.SplitContainer1.SuspendLayout();
            this.gbEstiamateHeader.SuspendLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txtDocRemarks.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtEstimate.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtCustomer.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtVehicle.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtVIN.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbBrand.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbLocation.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtSalesman.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dpDocDate.Properties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.dpDocDate.Properties.CalendarTimeProperties)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.GridControl1)).BeginInit();
            ((System.ComponentModel.ISupportInitialize)(this.GridView1)).BeginInit();
            this.SuspendLayout();
            // 
            // RibbonControl
            // 
            this.RibbonControl.ExpandCollapseItem.Id = 0;
            this.RibbonControl.Items.AddRange(new DevExpress.XtraBars.BarItem[] {
            this.RibbonControl.ExpandCollapseItem,
            this.BarButtonItemFirst,
            this.BarButtonItemPrevious,
            this.BarButtonItemNext,
            this.BarButtonItemLast,
            this.BarButtonItemNew,
            this.BarButtonItemEdit,
            this.BarButtonItemSave,
            this.BarButtonItemCancel,
            this.BarButtonItemDelete,
            this.BarButtonItemAddRow,
            this.BarButtonItemPrint,
            this.tglStatus,
            this.BarButtonPrint,
            this.BarButtonPrintPreview});
            this.RibbonControl.Location = new System.Drawing.Point(0, 0);
            this.RibbonControl.MaxItemId = 16;
            this.RibbonControl.Name = "RibbonControl";
            this.RibbonControl.Pages.AddRange(new DevExpress.XtraBars.Ribbon.RibbonPage[] {
            this.RibbonPageEstimate});
            this.RibbonControl.RibbonStyle = DevExpress.XtraBars.Ribbon.RibbonControlStyle.Office2019;
            this.RibbonControl.ShowApplicationButton = DevExpress.Utils.DefaultBoolean.True;
            this.RibbonControl.ShowExpandCollapseButton = DevExpress.Utils.DefaultBoolean.True;
            this.RibbonControl.ShowPageHeadersInFormCaption = DevExpress.Utils.DefaultBoolean.False;
            this.RibbonControl.ShowPageHeadersMode = DevExpress.XtraBars.Ribbon.ShowPageHeadersMode.ShowOnMultiplePages;
            this.RibbonControl.ShowToolbarCustomizeItem = false;
            this.RibbonControl.Size = new System.Drawing.Size(1195, 167);
            this.RibbonControl.Toolbar.ShowCustomizeItem = false;
            this.RibbonControl.ToolbarLocation = DevExpress.XtraBars.Ribbon.RibbonQuickAccessToolbarLocation.Hidden;
            this.RibbonControl.Click += new System.EventHandler(this.RibbonControl_Click);
            // 
            // BarButtonItemFirst
            // 
            this.BarButtonItemFirst.Caption = "First";
            this.BarButtonItemFirst.Id = 1;
            this.BarButtonItemFirst.ImageOptions.SvgImage = ((DevExpress.Utils.Svg.SvgImage)(resources.GetObject("BarButtonItemFirst.ImageOptions.SvgImage")));
            this.BarButtonItemFirst.Name = "BarButtonItemFirst";
            // 
            // BarButtonItemPrevious
            // 
            this.BarButtonItemPrevious.Caption = "Previous";
            this.BarButtonItemPrevious.Id = 2;
            this.BarButtonItemPrevious.ImageOptions.SvgImage = ((DevExpress.Utils.Svg.SvgImage)(resources.GetObject("BarButtonItemPrevious.ImageOptions.SvgImage")));
            this.BarButtonItemPrevious.Name = "BarButtonItemPrevious";
            // 
            // BarButtonItemNext
            // 
            this.BarButtonItemNext.Caption = "Next";
            this.BarButtonItemNext.Id = 3;
            this.BarButtonItemNext.ImageOptions.SvgImage = ((DevExpress.Utils.Svg.SvgImage)(resources.GetObject("BarButtonItemNext.ImageOptions.SvgImage")));
            this.BarButtonItemNext.Name = "BarButtonItemNext";
            // 
            // BarButtonItemLast
            // 
            this.BarButtonItemLast.Caption = "Last";
            this.BarButtonItemLast.Id = 4;
            this.BarButtonItemLast.ImageOptions.SvgImage = ((DevExpress.Utils.Svg.SvgImage)(resources.GetObject("BarButtonItemLast.ImageOptions.SvgImage")));
            this.BarButtonItemLast.Name = "BarButtonItemLast";
            // 
            // BarButtonItemNew
            // 
            this.BarButtonItemNew.Caption = "New";
            this.BarButtonItemNew.Id = 5;
            this.BarButtonItemNew.ImageOptions.SvgImage = ((DevExpress.Utils.Svg.SvgImage)(resources.GetObject("BarButtonItemNew.ImageOptions.SvgImage")));
            this.BarButtonItemNew.Name = "BarButtonItemNew";
            // 
            // BarButtonItemEdit
            // 
            this.BarButtonItemEdit.Caption = "Edit";
            this.BarButtonItemEdit.Id = 6;
            this.BarButtonItemEdit.ImageOptions.SvgImage = ((DevExpress.Utils.Svg.SvgImage)(resources.GetObject("BarButtonItemEdit.ImageOptions.SvgImage")));
            this.BarButtonItemEdit.Name = "BarButtonItemEdit";
            // 
            // BarButtonItemSave
            // 
            this.BarButtonItemSave.Caption = "Save";
            this.BarButtonItemSave.Id = 7;
            this.BarButtonItemSave.ImageOptions.SvgImage = ((DevExpress.Utils.Svg.SvgImage)(resources.GetObject("BarButtonItemSave.ImageOptions.SvgImage")));
            this.BarButtonItemSave.Name = "BarButtonItemSave";
            // 
            // BarButtonItemCancel
            // 
            this.BarButtonItemCancel.Caption = "Cancel";
            this.BarButtonItemCancel.Id = 8;
            this.BarButtonItemCancel.ImageOptions.SvgImage = ((DevExpress.Utils.Svg.SvgImage)(resources.GetObject("BarButtonItemCancel.ImageOptions.SvgImage")));
            this.BarButtonItemCancel.Name = "BarButtonItemCancel";
            // 
            // BarButtonItemDelete
            // 
            this.BarButtonItemDelete.Caption = "Delete";
            this.BarButtonItemDelete.Id = 9;
            this.BarButtonItemDelete.ImageOptions.SvgImage = ((DevExpress.Utils.Svg.SvgImage)(resources.GetObject("BarButtonItemDelete.ImageOptions.SvgImage")));
            this.BarButtonItemDelete.Name = "BarButtonItemDelete";
            // 
            // BarButtonItemAddRow
            // 
            this.BarButtonItemAddRow.Caption = "Add Row";
            this.BarButtonItemAddRow.Id = 10;
            this.BarButtonItemAddRow.ImageOptions.SvgImage = ((DevExpress.Utils.Svg.SvgImage)(resources.GetObject("BarButtonItemAddRow.ImageOptions.SvgImage")));
            this.BarButtonItemAddRow.Name = "BarButtonItemAddRow";
            // 
            // BarButtonItemPrint
            // 
            this.BarButtonItemPrint.Caption = "Print";
            this.BarButtonItemPrint.Id = 11;
            this.BarButtonItemPrint.Name = "BarButtonItemPrint";
            // 
            // tglStatus
            // 
            this.tglStatus.Caption = "Active";
            this.tglStatus.Id = 12;
            this.tglStatus.Name = "tglStatus";
            // 
            // BarButtonPrint
            // 
            this.BarButtonPrint.Caption = "Print";
            this.BarButtonPrint.Id = 14;
            this.BarButtonPrint.ImageOptions.SvgImage = ((DevExpress.Utils.Svg.SvgImage)(resources.GetObject("BarButtonPrint.ImageOptions.SvgImage")));
            this.BarButtonPrint.Name = "BarButtonPrint";
            // 
            // BarButtonPrintPreview
            // 
            this.BarButtonPrintPreview.Caption = "Print Preview";
            this.BarButtonPrintPreview.Id = 15;
            this.BarButtonPrintPreview.ImageOptions.LargeImage = ((System.Drawing.Image)(resources.GetObject("BarButtonPrintPreview.ImageOptions.LargeImage")));
            this.BarButtonPrintPreview.Name = "BarButtonPrintPreview";
            this.BarButtonPrintPreview.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItem2_ItemClick);
            // 
            // RibbonPageEstimate
            // 
            this.RibbonPageEstimate.Groups.AddRange(new DevExpress.XtraBars.Ribbon.RibbonPageGroup[] {
            this.RibbonPageGroupOperations,
            this.RibbonPageGroupGrid,
            this.RibbonPageGroupStatus,
            this.RibbonPageGroupNavigation,
            this.ribbonPageGroup1});
            this.RibbonPageEstimate.Name = "RibbonPageEstimate";
            this.RibbonPageEstimate.Text = "Estimate";
            // 
            // RibbonPageGroupOperations
            // 
            this.RibbonPageGroupOperations.ItemLinks.Add(this.BarButtonItemNew);
            this.RibbonPageGroupOperations.ItemLinks.Add(this.BarButtonItemEdit, true);
            this.RibbonPageGroupOperations.ItemLinks.Add(this.BarButtonItemSave, true);
            this.RibbonPageGroupOperations.ItemLinks.Add(this.BarButtonItemCancel, true);
            this.RibbonPageGroupOperations.ItemLinks.Add(this.BarButtonItemDelete, true);
            this.RibbonPageGroupOperations.Name = "RibbonPageGroupOperations";
            this.RibbonPageGroupOperations.Text = "Operations";
            // 
            // RibbonPageGroupGrid
            // 
            this.RibbonPageGroupGrid.ItemLinks.Add(this.BarButtonItemAddRow);
            this.RibbonPageGroupGrid.Name = "RibbonPageGroupGrid";
            this.RibbonPageGroupGrid.Text = "Grid";
            // 
            // RibbonPageGroupStatus
            // 
            this.RibbonPageGroupStatus.ItemLinks.Add(this.tglStatus);
            this.RibbonPageGroupStatus.Name = "RibbonPageGroupStatus";
            this.RibbonPageGroupStatus.Text = "Status";
            // 
            // RibbonPageGroupNavigation
            // 
            this.RibbonPageGroupNavigation.Alignment = DevExpress.XtraBars.Ribbon.RibbonPageGroupAlignment.Far;
            this.RibbonPageGroupNavigation.ItemLinks.Add(this.BarButtonItemFirst);
            this.RibbonPageGroupNavigation.ItemLinks.Add(this.BarButtonItemPrevious, true);
            this.RibbonPageGroupNavigation.ItemLinks.Add(this.BarButtonItemNext, true);
            this.RibbonPageGroupNavigation.ItemLinks.Add(this.BarButtonItemLast, true);
            this.RibbonPageGroupNavigation.Name = "RibbonPageGroupNavigation";
            this.RibbonPageGroupNavigation.Text = "Navigation";
            // 
            // ribbonPageGroup1
            // 
            this.ribbonPageGroup1.ItemLinks.Add(this.BarButtonPrint);
            this.ribbonPageGroup1.ItemLinks.Add(this.BarButtonPrintPreview, true);
            this.ribbonPageGroup1.Name = "ribbonPageGroup1";
            this.ribbonPageGroup1.Text = "Print";
            // 
            // SplitContainer1
            // 
            this.SplitContainer1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.SplitContainer1.Location = new System.Drawing.Point(0, 167);
            this.SplitContainer1.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.SplitContainer1.Name = "SplitContainer1";
            // 
            // SplitContainer1.Panel1
            // 
            this.SplitContainer1.Panel1.Controls.Add(this.gbEstiamateHeader);
            this.SplitContainer1.Panel1MinSize = 180;
            // 
            // SplitContainer1.Panel2
            // 
            this.SplitContainer1.Panel2.Controls.Add(this.GridControl1);
            this.SplitContainer1.Panel2MinSize = 200;
            this.SplitContainer1.Size = new System.Drawing.Size(1195, 558);
            this.SplitContainer1.SplitterDistance = 268;
            this.SplitContainer1.SplitterWidth = 5;
            this.SplitContainer1.TabIndex = 1;
            // 
            // gbEstiamateHeader
            // 
            this.gbEstiamateHeader.Controls.Add(this.txtDocRemarks);
            this.gbEstiamateHeader.Controls.Add(this.labelControl1);
            this.gbEstiamateHeader.Controls.Add(this.lblEstimate);
            this.gbEstiamateHeader.Controls.Add(this.txtEstimate);
            this.gbEstiamateHeader.Controls.Add(this.lblCustomer);
            this.gbEstiamateHeader.Controls.Add(this.txtCustomer);
            this.gbEstiamateHeader.Controls.Add(this.lblVehicle);
            this.gbEstiamateHeader.Controls.Add(this.txtVehicle);
            this.gbEstiamateHeader.Controls.Add(this.lblVIN);
            this.gbEstiamateHeader.Controls.Add(this.txtVIN);
            this.gbEstiamateHeader.Controls.Add(this.lblBrand);
            this.gbEstiamateHeader.Controls.Add(this.cbBrand);
            this.gbEstiamateHeader.Controls.Add(this.lblLocation);
            this.gbEstiamateHeader.Controls.Add(this.cbLocation);
            this.gbEstiamateHeader.Controls.Add(this.lblSalesman);
            this.gbEstiamateHeader.Controls.Add(this.txtSalesman);
            this.gbEstiamateHeader.Controls.Add(this.lblDate);
            this.gbEstiamateHeader.Controls.Add(this.dpDocDate);
            this.gbEstiamateHeader.Dock = System.Windows.Forms.DockStyle.Fill;
            this.gbEstiamateHeader.Location = new System.Drawing.Point(0, 0);
            this.gbEstiamateHeader.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.gbEstiamateHeader.Name = "gbEstiamateHeader";
            this.gbEstiamateHeader.Padding = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.gbEstiamateHeader.Size = new System.Drawing.Size(268, 558);
            this.gbEstiamateHeader.TabIndex = 0;
            this.gbEstiamateHeader.TabStop = false;
            this.gbEstiamateHeader.Text = "Estimate Header";
            this.gbEstiamateHeader.Enter += new System.EventHandler(this.gbEstiamateHeader_Enter);
            // 
            // txtDocRemarks
            // 
            this.txtDocRemarks.Location = new System.Drawing.Point(69, 294);
            this.txtDocRemarks.Name = "txtDocRemarks";
            this.txtDocRemarks.Size = new System.Drawing.Size(232, 70);
            this.txtDocRemarks.TabIndex = 18;
            // 
            // labelControl1
            // 
            this.labelControl1.Location = new System.Drawing.Point(6, 294);
            this.labelControl1.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.labelControl1.Name = "labelControl1";
            this.labelControl1.Size = new System.Drawing.Size(48, 15);
            this.labelControl1.TabIndex = 16;
            this.labelControl1.Text = "Remarks:";
            // 
            // lblEstimate
            // 
            this.lblEstimate.Location = new System.Drawing.Point(6, 24);
            this.lblEstimate.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.lblEstimate.Name = "lblEstimate";
            this.lblEstimate.Size = new System.Drawing.Size(48, 15);
            this.lblEstimate.TabIndex = 0;
            this.lblEstimate.Text = "Estimate:";
            // 
            // txtEstimate
            // 
            this.txtEstimate.Location = new System.Drawing.Point(69, 22);
            this.txtEstimate.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.txtEstimate.Name = "txtEstimate";
            this.txtEstimate.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.Simple;
            this.txtEstimate.Size = new System.Drawing.Size(150, 22);
            this.txtEstimate.TabIndex = 1;
            // 
            // lblCustomer
            // 
            this.lblCustomer.Location = new System.Drawing.Point(6, 59);
            this.lblCustomer.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.lblCustomer.Name = "lblCustomer";
            this.lblCustomer.Size = new System.Drawing.Size(55, 15);
            this.lblCustomer.TabIndex = 2;
            this.lblCustomer.Text = "Customer:";
            // 
            // txtCustomer
            // 
            this.txtCustomer.Location = new System.Drawing.Point(69, 56);
            this.txtCustomer.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.txtCustomer.Name = "txtCustomer";
            this.txtCustomer.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.Simple;
            this.txtCustomer.Size = new System.Drawing.Size(232, 22);
            this.txtCustomer.TabIndex = 3;
            // 
            // lblVehicle
            // 
            this.lblVehicle.Location = new System.Drawing.Point(6, 93);
            this.lblVehicle.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.lblVehicle.Name = "lblVehicle";
            this.lblVehicle.Size = new System.Drawing.Size(41, 15);
            this.lblVehicle.TabIndex = 4;
            this.lblVehicle.Text = "Vehicle:";
            // 
            // txtVehicle
            // 
            this.txtVehicle.Location = new System.Drawing.Point(69, 91);
            this.txtVehicle.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.txtVehicle.Name = "txtVehicle";
            this.txtVehicle.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.Simple;
            this.txtVehicle.Size = new System.Drawing.Size(232, 22);
            this.txtVehicle.TabIndex = 5;
            // 
            // lblVIN
            // 
            this.lblVIN.Location = new System.Drawing.Point(6, 128);
            this.lblVIN.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.lblVIN.Name = "lblVIN";
            this.lblVIN.Size = new System.Drawing.Size(22, 15);
            this.lblVIN.TabIndex = 6;
            this.lblVIN.Text = "VIN:";
            // 
            // txtVIN
            // 
            this.txtVIN.Location = new System.Drawing.Point(69, 126);
            this.txtVIN.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.txtVIN.Name = "txtVIN";
            this.txtVIN.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.Simple;
            this.txtVIN.Size = new System.Drawing.Size(232, 22);
            this.txtVIN.TabIndex = 7;
            // 
            // lblBrand
            // 
            this.lblBrand.Location = new System.Drawing.Point(6, 161);
            this.lblBrand.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.lblBrand.Name = "lblBrand";
            this.lblBrand.Size = new System.Drawing.Size(34, 15);
            this.lblBrand.TabIndex = 8;
            this.lblBrand.Text = "Brand:";
            // 
            // cbBrand
            // 
            this.cbBrand.Location = new System.Drawing.Point(68, 159);
            this.cbBrand.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.cbBrand.Name = "cbBrand";
            this.cbBrand.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.Simple;
            this.cbBrand.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.cbBrand.Size = new System.Drawing.Size(176, 22);
            this.cbBrand.TabIndex = 9;
            // 
            // lblLocation
            // 
            this.lblLocation.Location = new System.Drawing.Point(6, 196);
            this.lblLocation.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.lblLocation.Name = "lblLocation";
            this.lblLocation.Size = new System.Drawing.Size(49, 15);
            this.lblLocation.TabIndex = 10;
            this.lblLocation.Text = "Location:";
            // 
            // cbLocation
            // 
            this.cbLocation.Location = new System.Drawing.Point(68, 193);
            this.cbLocation.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.cbLocation.Name = "cbLocation";
            this.cbLocation.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.Simple;
            this.cbLocation.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.cbLocation.Size = new System.Drawing.Size(176, 22);
            this.cbLocation.TabIndex = 11;
            // 
            // lblSalesman
            // 
            this.lblSalesman.Location = new System.Drawing.Point(6, 230);
            this.lblSalesman.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.lblSalesman.Name = "lblSalesman";
            this.lblSalesman.Size = new System.Drawing.Size(53, 15);
            this.lblSalesman.TabIndex = 12;
            this.lblSalesman.Text = "Salesman:";
            // 
            // txtSalesman
            // 
            this.txtSalesman.Location = new System.Drawing.Point(68, 228);
            this.txtSalesman.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.txtSalesman.Name = "txtSalesman";
            this.txtSalesman.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.Simple;
            this.txtSalesman.Size = new System.Drawing.Size(233, 22);
            this.txtSalesman.TabIndex = 13;
            // 
            // lblDate
            // 
            this.lblDate.Location = new System.Drawing.Point(6, 265);
            this.lblDate.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.lblDate.Name = "lblDate";
            this.lblDate.Size = new System.Drawing.Size(27, 15);
            this.lblDate.TabIndex = 14;
            this.lblDate.Text = "Date:";
            // 
            // dpDocDate
            // 
            this.dpDocDate.EditValue = null;
            this.dpDocDate.Location = new System.Drawing.Point(68, 263);
            this.dpDocDate.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.dpDocDate.Name = "dpDocDate";
            this.dpDocDate.Properties.BorderStyle = DevExpress.XtraEditors.Controls.BorderStyles.Simple;
            this.dpDocDate.Properties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.dpDocDate.Properties.CalendarTimeProperties.Buttons.AddRange(new DevExpress.XtraEditors.Controls.EditorButton[] {
            new DevExpress.XtraEditors.Controls.EditorButton(DevExpress.XtraEditors.Controls.ButtonPredefines.Combo)});
            this.dpDocDate.Size = new System.Drawing.Size(176, 22);
            this.dpDocDate.TabIndex = 15;
            // 
            // GridControl1
            // 
            this.GridControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.GridControl1.EmbeddedNavigator.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.GridControl1.Location = new System.Drawing.Point(0, 0);
            this.GridControl1.MainView = this.GridView1;
            this.GridControl1.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.GridControl1.Name = "GridControl1";
            this.GridControl1.Size = new System.Drawing.Size(922, 558);
            this.GridControl1.TabIndex = 0;
            this.GridControl1.ViewCollection.AddRange(new DevExpress.XtraGrid.Views.Base.BaseView[] {
            this.GridView1});
            // 
            // GridView1
            // 
            this.GridView1.DetailHeight = 404;
            this.GridView1.GridControl = this.GridControl1;
            this.GridView1.Name = "GridView1";
            this.GridView1.OptionsEditForm.PopupEditFormWidth = 933;
            this.GridView1.OptionsView.ShowGroupPanel = false;
            // 
            // EstimateForm
            // 
            this.AutoScaleDimensions = new System.Drawing.SizeF(7F, 15F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Font;
            this.ClientSize = new System.Drawing.Size(1195, 725);
            this.Controls.Add(this.SplitContainer1);
            this.Controls.Add(this.RibbonControl);
            this.Margin = new System.Windows.Forms.Padding(4, 3, 4, 3);
            this.Name = "EstimateForm";
            this.Ribbon = this.RibbonControl;
            this.Text = "Estimate Management";
            this.Load += new System.EventHandler(this.EstimateForm_Load);
            ((System.ComponentModel.ISupportInitialize)(this.RibbonControl)).EndInit();
            this.SplitContainer1.Panel1.ResumeLayout(false);
            this.SplitContainer1.Panel2.ResumeLayout(false);
            ((System.ComponentModel.ISupportInitialize)(this.SplitContainer1)).EndInit();
            this.SplitContainer1.ResumeLayout(false);
            this.gbEstiamateHeader.ResumeLayout(false);
            this.gbEstiamateHeader.PerformLayout();
            ((System.ComponentModel.ISupportInitialize)(this.txtDocRemarks.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtEstimate.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtCustomer.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtVehicle.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtVIN.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbBrand.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.cbLocation.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.txtSalesman.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dpDocDate.Properties.CalendarTimeProperties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.dpDocDate.Properties)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.GridControl1)).EndInit();
            ((System.ComponentModel.ISupportInitialize)(this.GridView1)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        // Ribbon Control and Buttons
        private DevExpress.XtraBars.Ribbon.RibbonControl RibbonControl;
        public DevExpress.XtraBars.BarButtonItem BarButtonItemFirst;
        public DevExpress.XtraBars.BarButtonItem BarButtonItemPrevious;
        public DevExpress.XtraBars.BarButtonItem BarButtonItemNext;
        public DevExpress.XtraBars.BarButtonItem BarButtonItemLast;
        public DevExpress.XtraBars.BarButtonItem BarButtonItemNew;
        public DevExpress.XtraBars.BarButtonItem BarButtonItemEdit;
        public DevExpress.XtraBars.BarButtonItem BarButtonItemSave;
        public DevExpress.XtraBars.BarButtonItem BarButtonItemCancel;
        public DevExpress.XtraBars.BarButtonItem BarButtonItemDelete;
        public DevExpress.XtraBars.BarButtonItem BarButtonItemAddRow;
        public DevExpress.XtraBars.BarButtonItem BarButtonItemPrint;
        public DevExpress.XtraBars.BarToggleSwitchItem tglStatus;

        // Ribbon Pages and Groups
        private DevExpress.XtraBars.Ribbon.RibbonPage RibbonPageEstimate;
        private DevExpress.XtraBars.Ribbon.RibbonPageGroup RibbonPageGroupNavigation;
        private DevExpress.XtraBars.Ribbon.RibbonPageGroup RibbonPageGroupOperations;
        private DevExpress.XtraBars.Ribbon.RibbonPageGroup RibbonPageGroupGrid;
        private DevExpress.XtraBars.Ribbon.RibbonPageGroup RibbonPageGroupStatus;

        // Main controls
        private System.Windows.Forms.SplitContainer SplitContainer1;
        public System.Windows.Forms.GroupBox gbEstiamateHeader;
          // Form fields
        public DevExpress.XtraEditors.LabelControl lblEstimate;
        public DevExpress.XtraEditors.TextEdit txtEstimate;
        public DevExpress.XtraEditors.LabelControl lblCustomer;
        public DevExpress.XtraEditors.TextEdit txtCustomer;
        public DevExpress.XtraEditors.LabelControl lblVehicle;
        public DevExpress.XtraEditors.TextEdit txtVehicle;
        public DevExpress.XtraEditors.LabelControl lblVIN;
        public DevExpress.XtraEditors.TextEdit txtVIN;
        public DevExpress.XtraEditors.LabelControl lblBrand;
        public DevExpress.XtraEditors.ComboBoxEdit cbBrand;
        public DevExpress.XtraEditors.LabelControl lblLocation;
        public DevExpress.XtraEditors.ComboBoxEdit cbLocation;
        public DevExpress.XtraEditors.LabelControl lblSalesman;
        public DevExpress.XtraEditors.TextEdit txtSalesman;
        public DevExpress.XtraEditors.LabelControl lblDate;
        public DevExpress.XtraEditors.DateEdit dpDocDate;

        // Grid
        public DevExpress.XtraGrid.GridControl GridControl1;
        public DevExpress.XtraGrid.Views.Grid.GridView GridView1;
        public DevExpress.XtraEditors.LabelControl labelControl1;
        public DevExpress.XtraEditors.MemoEdit txtDocRemarks;
        private DevExpress.XtraBars.BarButtonItem BarButtonPrint;
        private RibbonPageGroup ribbonPageGroup1;
        private DevExpress.XtraBars.BarButtonItem BarButtonPrintPreview;
    }
}