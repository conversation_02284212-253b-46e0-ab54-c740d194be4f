// UnifiedParameterManager - Centralized parameter management service
// Usage: Single service for all parameter types with type-safe access and category organization

using System;
using System.Collections.Concurrent;
using System.Collections.Generic;
using System.Diagnostics;
using System.Globalization;
using ProManage.Modules.Data.ParametersForm;

namespace ProManage.Modules.Services
{
    /// <summary>
    /// Unified parameter manager that centralizes ALL parameter management in the application
    /// Replaces multiple parameter services with a single, efficient, scalable solution
    /// </summary>
    public sealed class UnifiedParameterManager
    {
        #region Singleton Implementation

        private static readonly Lazy<UnifiedParameterManager> _instance = 
            new Lazy<UnifiedParameterManager>(() => new UnifiedParameterManager());

        /// <summary>
        /// Gets the singleton instance of the UnifiedParameterManager
        /// </summary>
        public static UnifiedParameterManager Instance => _instance.Value;

        #endregion

        #region Private Fields

        private readonly ConcurrentDictionary<string, string> _parameterCache;
        private readonly object _lockObject;
        private bool _isInitialized;
        private DateTime _lastCacheUpdate;

        // Category instances
        private CurrencyParameters _currency;
        private CompanyParameters _company;
        private UIParameters _ui;
        private BusinessParameters _business;

        #endregion

        #region Constructor

        /// <summary>
        /// Private constructor for singleton pattern
        /// </summary>
        private UnifiedParameterManager()
        {
            _parameterCache = new ConcurrentDictionary<string, string>();
            _lockObject = new object();
            _isInitialized = false;
            _lastCacheUpdate = DateTime.MinValue;

            // Initialize category instances
            _currency = new CurrencyParameters(this);
            _company = new CompanyParameters(this);
            _ui = new UIParameters(this);
            _business = new BusinessParameters(this);

            Debug.WriteLine("UnifiedParameterManager: Instance created");
        }

        #endregion

        #region Public Properties

        /// <summary>
        /// Gets whether the parameter manager has been initialized
        /// </summary>
        public bool IsInitialized => _isInitialized;

        /// <summary>
        /// Gets the number of parameters currently cached
        /// </summary>
        public int ParameterCount => _parameterCache.Count;

        /// <summary>
        /// Gets the timestamp of the last cache update
        /// </summary>
        public DateTime LastCacheUpdate => _lastCacheUpdate;

        /// <summary>
        /// Gets currency-related parameters
        /// </summary>
        public CurrencyParameters Currency => _currency;

        /// <summary>
        /// Gets company-related parameters
        /// </summary>
        public CompanyParameters Company => _company;

        /// <summary>
        /// Gets UI-related parameters
        /// </summary>
        public UIParameters UI => _ui;

        /// <summary>
        /// Gets business-related parameters
        /// </summary>
        public BusinessParameters Business => _business;

        #endregion

        #region Core Parameter Access Methods

        /// <summary>
        /// Gets a parameter value as string with optional default
        /// </summary>
        /// <param name="code">Parameter code</param>
        /// <param name="defaultValue">Default value if parameter not found</param>
        /// <returns>Parameter value or default</returns>
        public string GetString(string code, string defaultValue = "")
        {
            if (string.IsNullOrWhiteSpace(code))
                return defaultValue;

            EnsureInitialized();

            return _parameterCache.TryGetValue(code.ToUpper(), out string value) ? value : defaultValue;
        }

        /// <summary>
        /// Gets a parameter value as integer with optional default
        /// </summary>
        /// <param name="code">Parameter code</param>
        /// <param name="defaultValue">Default value if parameter not found or invalid</param>
        /// <returns>Parameter value as integer or default</returns>
        public int GetInt(string code, int defaultValue = 0)
        {
            string value = GetString(code);
            if (string.IsNullOrWhiteSpace(value))
                return defaultValue;

            return int.TryParse(value, out int result) ? result : defaultValue;
        }

        /// <summary>
        /// Gets a parameter value as boolean with optional default
        /// </summary>
        /// <param name="code">Parameter code</param>
        /// <param name="defaultValue">Default value if parameter not found or invalid</param>
        /// <returns>Parameter value as boolean or default</returns>
        public bool GetBool(string code, bool defaultValue = false)
        {
            string value = GetString(code);
            if (string.IsNullOrWhiteSpace(value))
                return defaultValue;

            // Handle various boolean representations
            value = value.ToLower().Trim();
            if (value == "true" || value == "1" || value == "yes" || value == "on")
                return true;
            if (value == "false" || value == "0" || value == "no" || value == "off")
                return false;

            return bool.TryParse(value, out bool result) ? result : defaultValue;
        }

        /// <summary>
        /// Gets a parameter value as decimal with optional default
        /// </summary>
        /// <param name="code">Parameter code</param>
        /// <param name="defaultValue">Default value if parameter not found or invalid</param>
        /// <returns>Parameter value as decimal or default</returns>
        public decimal GetDecimal(string code, decimal defaultValue = 0m)
        {
            string value = GetString(code);
            if (string.IsNullOrWhiteSpace(value))
                return defaultValue;

            return decimal.TryParse(value, NumberStyles.Number, CultureInfo.InvariantCulture, out decimal result) 
                ? result : defaultValue;
        }

        /// <summary>
        /// Gets a parameter value as DateTime with optional default
        /// </summary>
        /// <param name="code">Parameter code</param>
        /// <param name="defaultValue">Default value if parameter not found or invalid</param>
        /// <returns>Parameter value as DateTime or default</returns>
        public DateTime GetDateTime(string code, DateTime defaultValue = default)
        {
            string value = GetString(code);
            if (string.IsNullOrWhiteSpace(value))
                return defaultValue;

            return DateTime.TryParse(value, out DateTime result) ? result : defaultValue;
        }

        /// <summary>
        /// Generic parameter access with automatic type conversion
        /// </summary>
        /// <typeparam name="T">Type to convert to</typeparam>
        /// <param name="code">Parameter code</param>
        /// <param name="defaultValue">Default value if parameter not found or conversion fails</param>
        /// <returns>Parameter value converted to type T or default</returns>
        public T GetParameter<T>(string code, T defaultValue = default(T))
        {
            string value = GetString(code);
            if (string.IsNullOrWhiteSpace(value))
                return defaultValue;

            try
            {
                return (T)Convert.ChangeType(value, typeof(T), CultureInfo.InvariantCulture);
            }
            catch
            {
                return defaultValue;
            }
        }

        /// <summary>
        /// Checks if a parameter exists in the cache
        /// </summary>
        /// <param name="code">Parameter code to check</param>
        /// <returns>True if parameter exists, false otherwise</returns>
        public bool HasParameter(string code)
        {
            if (string.IsNullOrWhiteSpace(code))
                return false;

            EnsureInitialized();
            return _parameterCache.ContainsKey(code.ToUpper());
        }

        #endregion

        #region Initialization and Management

        /// <summary>
        /// Initializes the parameter manager by loading all parameters from database
        /// </summary>
        /// <returns>True if initialization successful, false otherwise</returns>
        public bool Initialize()
        {
            try
            {
                Debug.WriteLine("UnifiedParameterManager: Starting initialization");

                lock (_lockObject)
                {
                    if (_isInitialized)
                    {
                        Debug.WriteLine("UnifiedParameterManager: Already initialized");
                        return true;
                    }

                    return RefreshFromDatabaseInternal();
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"UnifiedParameterManager: Initialization error: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Refreshes all parameters from the database
        /// </summary>
        public void RefreshFromDatabase()
        {
            lock (_lockObject)
            {
                RefreshFromDatabaseInternal();
            }
        }

        #endregion

        #region Private Methods

        /// <summary>
        /// Ensures the parameter manager is initialized
        /// </summary>
        private void EnsureInitialized()
        {
            if (!_isInitialized)
            {
                Debug.WriteLine("UnifiedParameterManager: Not initialized, attempting to initialize");
                Initialize();
            }
        }

        /// <summary>
        /// Internal method to refresh parameters from database
        /// </summary>
        /// <returns>True if successful, false otherwise</returns>
        private bool RefreshFromDatabaseInternal()
        {
            try
            {
                Debug.WriteLine("UnifiedParameterManager: Refreshing parameters from database");

                // Get parameters from repository
                var parameters = ParametersFormRepository.GetAllParameters();

                // Clear existing cache
                _parameterCache.Clear();

                // Load parameters if available (even if empty list, that's still successful)
                if (parameters != null)
                {
                    foreach (var parameter in parameters)
                    {
                        if (!string.IsNullOrWhiteSpace(parameter.ParameterCode))
                        {
                            _parameterCache[parameter.ParameterCode.ToUpper()] = parameter.ParameterValue ?? string.Empty;
                        }
                    }
                }

                _lastCacheUpdate = DateTime.Now;
                _isInitialized = true;

                Debug.WriteLine($"UnifiedParameterManager: Successfully initialized with {_parameterCache.Count} parameters loaded from database");

                // Always return true - even 0 parameters is a successful initialization
                // The system is designed to work with default values when parameters are missing
                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"UnifiedParameterManager: Error during initialization: {ex.Message}");

                // Even if there's an error, we can still initialize with an empty cache
                // This allows the system to work with default values
                _parameterCache.Clear();
                _lastCacheUpdate = DateTime.Now;
                _isInitialized = true;

                Debug.WriteLine("UnifiedParameterManager: Initialized with empty cache due to error - will use default values");
                return true; // Return true to prevent startup warnings
            }
        }

        #endregion

        #region Category Classes

        /// <summary>
        /// Currency-related parameters with convenient access methods
        /// </summary>
        public class CurrencyParameters
        {
            private readonly UnifiedParameterManager _manager;

            internal CurrencyParameters(UnifiedParameterManager manager)
            {
                _manager = manager;
            }

            /// <summary>
            /// Gets the currency symbol (e.g., "USD", "EUR")
            /// </summary>
            public string Symbol => _manager.GetString("CURRENCY", "USD");

            /// <summary>
            /// Gets the number of decimal places for currency formatting
            /// </summary>
            public int DecimalPlaces => _manager.GetInt("DECIMALS", 2);

            /// <summary>
            /// Gets a ready-to-use currency format string for DevExpress grids
            /// </summary>
            public string Format => $"{Symbol} {{0:N{DecimalPlaces}}}";

            /// <summary>
            /// Gets the currency format without symbol (just decimal formatting)
            /// </summary>
            public string DecimalFormat => $"N{DecimalPlaces}";
        }

        /// <summary>
        /// Company-related parameters with convenient access methods
        /// </summary>
        public class CompanyParameters
        {
            private readonly UnifiedParameterManager _manager;

            internal CompanyParameters(UnifiedParameterManager manager)
            {
                _manager = manager;
            }

            /// <summary>
            /// Gets the company name
            /// </summary>
            public string Name => _manager.GetString("COMPANY_NAME", "ProManage");

            /// <summary>
            /// Gets the company address
            /// </summary>
            public string Address => _manager.GetString("COMPANY_ADDRESS", "");

            /// <summary>
            /// Gets the company phone number
            /// </summary>
            public string Phone => _manager.GetString("COMPANY_PHONE", "");

            /// <summary>
            /// Gets the company email
            /// </summary>
            public string Email => _manager.GetString("COMPANY_EMAIL", "");

            /// <summary>
            /// Gets the company website
            /// </summary>
            public string Website => _manager.GetString("COMPANY_WEBSITE", "");
        }

        /// <summary>
        /// UI-related parameters with convenient access methods
        /// </summary>
        public class UIParameters
        {
            private readonly UnifiedParameterManager _manager;

            internal UIParameters(UnifiedParameterManager manager)
            {
                _manager = manager;
            }

            /// <summary>
            /// Gets whether to show tooltips in the application
            /// </summary>
            public bool ShowTooltips => _manager.GetBool("SHOW_TOOLTIPS", true);

            /// <summary>
            /// Gets the default page size for grids
            /// </summary>
            public int DefaultPageSize => _manager.GetInt("DEFAULT_PAGE_SIZE", 50);

            /// <summary>
            /// Gets whether to enable debug logging
            /// </summary>
            public bool EnableDebugLogging => _manager.GetBool("ENABLE_DEBUG_LOGGING", false);

            /// <summary>
            /// Gets the application theme name
            /// </summary>
            public string Theme => _manager.GetString("APPLICATION_THEME", "Office 2019 Colorful");

            /// <summary>
            /// Gets the date format for the application
            /// </summary>
            public string DateFormat => _manager.GetString("DATE_FORMAT", "MM/dd/yyyy");
        }

        /// <summary>
        /// Business-related parameters with convenient access methods
        /// </summary>
        public class BusinessParameters
        {
            private readonly UnifiedParameterManager _manager;

            internal BusinessParameters(UnifiedParameterManager manager)
            {
                _manager = manager;
            }

            /// <summary>
            /// Gets the default tax rate as a decimal (e.g., 0.05 for 5%)
            /// </summary>
            public decimal TaxRate => _manager.GetDecimal("TAX_RATE", 0.0m);

            /// <summary>
            /// Gets the maximum discount percentage allowed
            /// </summary>
            public decimal MaxDiscountPercent => _manager.GetDecimal("MAX_DISCOUNT_PERCENT", 10.0m);

            /// <summary>
            /// Gets whether negative amounts are allowed
            /// </summary>
            public bool AllowNegativeAmounts => _manager.GetBool("ALLOW_NEGATIVE_AMOUNTS", false);

            /// <summary>
            /// Gets the session timeout in minutes
            /// </summary>
            public int SessionTimeoutMinutes => _manager.GetInt("SESSION_TIMEOUT_MINUTES", 30);

            /// <summary>
            /// Gets whether GST should be shown
            /// </summary>
            public bool ShowGST => _manager.GetBool("SHOW_GST", false);
        }

        #endregion
    }
}
