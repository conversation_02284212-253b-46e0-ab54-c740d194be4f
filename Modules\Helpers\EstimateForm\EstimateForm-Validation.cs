// Data validation and business rules for EstimateForm operations
// Handles all validation logic to ensure data integrity and business rule compliance

using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.Linq;
using System.Text;
using System.Windows.Forms;
using ProManage.Modules.Models.EstimateForm;
using ProManage.Modules.Data.EstimateForm;

namespace ProManage.Modules.Helpers.EstimateForm
{
    /// <summary>
    /// Validation helper class for EstimateForm data validation and business rules
    /// </summary>
    public static class EstimateFormValidation
    {
        #region Data Validation

        /// <summary>
        /// Validates the complete estimate data before saving
        /// </summary>
        /// <param name="estimate">The estimate to validate</param>
        /// <param name="gridDataTable">The grid data table with details</param>
        /// <returns>True if valid, false otherwise</returns>
        public static bool ValidateEstimateData(EstimateFormHeaderModel estimate, DataTable gridDataTable)
        {
            try
            {
                Debug.WriteLine("=== ValidateEstimateData: Starting validation ===");

                var errors = new List<string>();

                // Validate header data
                ValidateHeaderData(estimate, errors);

                // Validate detail data
                ValidateDetailData(gridDataTable, errors);

                // Special validation for new estimates
                if (estimate.Id <= 0)
                {
                    ValidateNewEstimateRequirements(gridDataTable, errors);
                }

                // Check business rules
                ValidateBusinessRules(estimate, errors);

                if (errors.Count > 0)
                {
                    string errorMessage = "Validation failed:\n\n" + string.Join("\n", errors);
                    MessageBox.Show(errorMessage, "Validation Error", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    Debug.WriteLine($"Validation failed with {errors.Count} errors");
                    return false;
                }

                Debug.WriteLine("=== ValidateEstimateData: Validation passed ===");
                return true;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"ERROR in ValidateEstimateData: {ex.Message}");
                MessageBox.Show($"Error during validation: {ex.Message}", "Validation Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }
        }

        /// <summary>
        /// Validates the estimate header data
        /// </summary>
        /// <param name="estimate">The estimate to validate</param>
        /// <param name="errors">List to collect validation errors</param>
        private static void ValidateHeaderData(EstimateFormHeaderModel estimate, List<string> errors)
        {
            // Required field validations
            if (string.IsNullOrWhiteSpace(estimate.EstimateNo))
            {
                errors.Add("• Estimate number is required");
            }

            if (string.IsNullOrWhiteSpace(estimate.CustomerName))
            {
                errors.Add("• Customer name is required");
            }

            // Format validations
            if (!string.IsNullOrWhiteSpace(estimate.EstimateNo))
            {
                if (estimate.EstimateNo.Length > 50)
                {
                    errors.Add("• Estimate number cannot exceed 50 characters");
                }
            }

            if (!string.IsNullOrWhiteSpace(estimate.CustomerName))
            {
                if (estimate.CustomerName.Length > 100)
                {
                    errors.Add("• Customer name cannot exceed 100 characters");
                }
            }

            // Date validations
            if (estimate.DocDate.HasValue)
            {
                if (estimate.DocDate.Value > DateTime.Now.AddDays(30))
                {
                    errors.Add("• Document date cannot be more than 30 days in the future");
                }

                if (estimate.DocDate.Value < DateTime.Now.AddYears(-5))
                {
                    errors.Add("• Document date cannot be more than 5 years in the past");
                }
            }

            Debug.WriteLine($"Header validation completed with {errors.Count} errors so far");
        }

        /// <summary>
        /// Validates the estimate detail data
        /// </summary>
        /// <param name="gridDataTable">The grid data table to validate</param>
        /// <param name="errors">List to collect validation errors</param>
        private static void ValidateDetailData(DataTable gridDataTable, List<string> errors)
        {
            if (gridDataTable == null || gridDataTable.Rows.Count == 0)
            {
                errors.Add("• At least one detail item is required");
                return;
            }

            int validRowCount = 0;
            for (int i = 0; i < gridDataTable.Rows.Count; i++)
            {
                var row = gridDataTable.Rows[i];

                // Skip completely empty rows
                bool isEmptyRow = string.IsNullOrWhiteSpace(row["PartNumber"]?.ToString()) &&
                                 string.IsNullOrWhiteSpace(row["Description"]?.ToString()) &&
                                 (row["Quantity"] == DBNull.Value || row["Quantity"] == null ||
                                  TryParseDecimal(row["Quantity"]) == 0);

                if (isEmptyRow)
                    continue;

                validRowCount++;

                // Validate individual row
                ValidateDetailRow(row, i + 1, errors);
            }

            if (validRowCount == 0)
            {
                errors.Add("• At least one valid detail item is required");
            }

            Debug.WriteLine($"Detail validation completed for {validRowCount} valid rows with {errors.Count} total errors");
        }

        /// <summary>
        /// Validates a single detail row
        /// </summary>
        /// <param name="row">The data row to validate</param>
        /// <param name="rowNumber">The row number for error reporting</param>
        /// <param name="errors">List to collect validation errors</param>
        private static void ValidateDetailRow(DataRow row, int rowNumber, List<string> errors)
        {
            // Part number validation
            string partNo = row["PartNumber"]?.ToString()?.Trim();
            if (string.IsNullOrWhiteSpace(partNo))
            {
                errors.Add($"• Row {rowNumber}: Part number is required");
            }
            else if (partNo.Length > 50)
            {
                errors.Add($"• Row {rowNumber}: Part number cannot exceed 50 characters");
            }

            // Description validation
            string description = row["Description"]?.ToString()?.Trim();
            if (string.IsNullOrWhiteSpace(description))
            {
                errors.Add($"• Row {rowNumber}: Description is required");
            }
            else if (description.Length > 200)
            {
                errors.Add($"• Row {rowNumber}: Description cannot exceed 200 characters");
            }

            // Quantity validation
            decimal? quantity = TryParseDecimal(row["Quantity"]);
            if (!quantity.HasValue || quantity.Value <= 0)
            {
                errors.Add($"• Row {rowNumber}: Quantity must be greater than 0");
            }
            else if (quantity.Value > 9999)
            {
                errors.Add($"• Row {rowNumber}: Quantity cannot exceed 9999");
            }

            // Price validations
            decimal? oePrice = TryParseDecimal(row["OEPrice"]);
            if (oePrice.HasValue && oePrice.Value < 0)
            {
                errors.Add($"• Row {rowNumber}: OE Price cannot be negative");
            }

            decimal? afmPrice = TryParseDecimal(row["AFMPrice"]);
            if (afmPrice.HasValue && afmPrice.Value < 0)
            {
                errors.Add($"• Row {rowNumber}: AFM Price cannot be negative");
            }
        }

        /// <summary>
        /// Validates specific requirements for new estimates
        /// </summary>
        /// <param name="gridDataTable">The grid data table to validate</param>
        /// <param name="errors">List to collect validation errors</param>
        private static void ValidateNewEstimateRequirements(DataTable gridDataTable, List<string> errors)
        {
            try
            {
                Debug.WriteLine("=== ValidateNewEstimateRequirements: Starting new estimate validation ===");

                // Check if grid has any valid detail rows for new estimates
                if (gridDataTable == null || gridDataTable.Rows.Count == 0)
                {
                    errors.Add("Please add at least one item to the estimate before saving.");
                    Debug.WriteLine("New estimate validation failed: No rows in grid");
                    return;
                }

                // Count valid rows (non-empty rows)
                int validRowCount = 0;
                for (int i = 0; i < gridDataTable.Rows.Count; i++)
                {
                    var row = gridDataTable.Rows[i];

                    // Check if row has any meaningful data
                    bool isEmptyRow = string.IsNullOrWhiteSpace(row["PartNumber"]?.ToString()) &&
                                     string.IsNullOrWhiteSpace(row["Description"]?.ToString()) &&
                                     (row["Quantity"] == DBNull.Value || row["Quantity"] == null ||
                                      TryParseDecimal(row["Quantity"]) == 0);

                    if (!isEmptyRow)
                    {
                        validRowCount++;
                    }
                }

                if (validRowCount == 0)
                {
                    errors.Add("Please add at least one item to the estimate before saving.");
                    Debug.WriteLine("New estimate validation failed: No valid rows found");
                }
                else
                {
                    Debug.WriteLine($"New estimate validation passed: {validRowCount} valid rows found");
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in new estimate validation: {ex.Message}");
                errors.Add("Error validating new estimate requirements");
            }
        }

        /// <summary>
        /// Validates business rules
        /// </summary>
        /// <param name="estimate">The estimate to validate</param>
        /// <param name="errors">List to collect validation errors</param>
        private static void ValidateBusinessRules(EstimateFormHeaderModel estimate, List<string> errors)
        {
            try
            {
                // Check for duplicate estimate number (only for new estimates)
                if (estimate.Id <= 0 && !string.IsNullOrWhiteSpace(estimate.EstimateNo))
                {
                    if (CheckDuplicateEstimateNumber(estimate.EstimateNo))
                    {
                        errors.Add($"• Estimate number '{estimate.EstimateNo}' already exists");
                    }
                }

                Debug.WriteLine("Business rules validation completed");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in business rules validation: {ex.Message}");
                errors.Add("• Error validating business rules");
            }
        }

        /// <summary>
        /// Checks if an estimate number already exists
        /// </summary>
        /// <param name="estimateNo">The estimate number to check</param>
        /// <returns>True if duplicate exists, false otherwise</returns>
        private static bool CheckDuplicateEstimateNumber(string estimateNo)
        {
            try
            {
                var existingEstimate = EstimateFormRepository.GetEstimateByNumber(estimateNo);
                return existingEstimate != null;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error checking duplicate estimate number: {ex.Message}");
                return false; // Assume no duplicate if check fails
            }
        }

        #endregion

        #region Data Conversion Helpers

        /// <summary>
        /// Safely converts a value to decimal
        /// </summary>
        /// <param name="value">The value to convert</param>
        /// <returns>Decimal value or null if conversion fails</returns>
        public static decimal? TryParseDecimal(object value)
        {
            if (value == null || value == DBNull.Value || string.IsNullOrWhiteSpace(value.ToString()))
                return null;

            if (decimal.TryParse(value.ToString(), out decimal result))
                return result;

            return null;
        }

        /// <summary>
        /// Safely converts a value to integer
        /// </summary>
        /// <param name="value">The value to convert</param>
        /// <returns>Integer value or null if conversion fails</returns>
        public static int? TryParseInt(object value)
        {
            if (value == null || value == DBNull.Value || string.IsNullOrWhiteSpace(value.ToString()))
                return null;

            if (int.TryParse(value.ToString(), out int result))
                return result;

            return null;
        }

        /// <summary>
        /// Safely converts a value to DateTime
        /// </summary>
        /// <param name="value">The value to convert</param>
        /// <returns>DateTime value or null if conversion fails</returns>
        public static DateTime? TryParseDateTime(object value)
        {
            if (value == null || value == DBNull.Value || string.IsNullOrWhiteSpace(value.ToString()))
                return null;

            if (DateTime.TryParse(value.ToString(), out DateTime result))
                return result;

            return null;
        }

        #endregion

        #region Validation Utilities

        /// <summary>
        /// Validates required fields and shows appropriate error messages
        /// </summary>
        /// <param name="estimateNo">Estimate number</param>
        /// <param name="customerName">Customer name</param>
        /// <returns>True if all required fields are valid</returns>
        public static bool ValidateRequiredFields(string estimateNo, string customerName)
        {
            var errors = new List<string>();

            if (string.IsNullOrWhiteSpace(estimateNo))
            {
                errors.Add("• Estimate number is required");
            }

            if (string.IsNullOrWhiteSpace(customerName))
            {
                errors.Add("• Customer name is required");
            }

            if (errors.Count > 0)
            {
                string errorMessage = "Required fields missing:\n\n" + string.Join("\n", errors);
                MessageBox.Show(errorMessage, "Required Fields", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }

            return true;
        }

        /// <summary>
        /// Shows a validation error message
        /// </summary>
        /// <param name="message">The error message to show</param>
        /// <param name="title">The title for the message box</param>
        public static void ShowValidationError(string message, string title = "Validation Error")
        {
            MessageBox.Show(message, title, MessageBoxButtons.OK, MessageBoxIcon.Warning);
        }

        /// <summary>
        /// Shows a validation success message
        /// </summary>
        /// <param name="message">The success message to show</param>
        /// <param name="title">The title for the message box</param>
        public static void ShowValidationSuccess(string message, string title = "Success")
        {
            MessageBox.Show(message, title, MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        #endregion
    }
}
