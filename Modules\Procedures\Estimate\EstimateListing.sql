-- EstimateListing.sql
-- Consolidated listing and search queries for estimates
-- Replaces: GetAllEstimates, GetEstimateList, SearchEstimatesByCustomer
--
-- All queries use consistent column naming and proper aggregations

-- [GetAllEstimates] --
-- Retrieve all estimates with summary information
SELECT
    e.id AS estimate_id,
    e.estimate_no AS estimate_number,
    e.customer_name,
    e.vin AS vehicle_vin,
    e.brand AS vehicle_brand,
    e.date AS estimate_date,
    e.location,
    e.vehicle_model,
    e.salesman_name,
    e.status,
    e.created_at AS created_date,
    e.modified_at AS modified_date,
    COALESCE(COUNT(d.id), 0) AS item_count,
    COALESCE(SUM(d.quantity * d.afm_price), 0) AS total_amount
FROM
    estimateheader e
LEFT JOIN
    estimatedetails d ON e.id = d.estimate_id
WHERE
    e.estimate_no IS NOT NULL
    AND LENGTH(TRIM(e.estimate_no)) > 0
    AND e.id > 0
GROUP BY
    e.id, e.estimate_no, e.customer_name, e.vin, e.brand, e.date,
    e.location, e.vehicle_model, e.salesman_name, e.status,
    e.created_at, e.modified_at
ORDER BY
    COALESCE(e.created_at, '1900-01-01'::timestamp) DESC,
    e.estimate_no DESC;
-- [End] --

-- [GetEstimateList] --
-- Retrieve paginated and filtered list of estimates
SELECT
    e.id AS estimate_id,
    e.estimate_no AS estimate_number,
    e.customer_name,
    e.vin AS vehicle_vin,
    e.brand AS vehicle_brand,
    e.date AS estimate_date,
    e.location,
    e.vehicle_model,
    e.salesman_name,
    e.status,
    e.created_at AS created_date,
    e.modified_at AS modified_date,
    COALESCE(COUNT(d.id), 0) AS item_count,
    COALESCE(SUM(d.quantity * d.afm_price), 0) AS total_amount
FROM
    estimateheader e
LEFT JOIN
    estimatedetails d ON e.id = d.estimate_id
WHERE
    (@customer_name IS NULL OR LOWER(e.customer_name) LIKE LOWER('%' || @customer_name || '%'))
    AND (@status IS NULL OR e.status = @status)
    AND (@start_date IS NULL OR e.date >= @start_date)
    AND (@end_date IS NULL OR e.date <= @end_date)
    AND (@search_term IS NULL OR
         LOWER(e.customer_name) LIKE LOWER('%' || @search_term || '%') OR
         LOWER(e.estimate_no) LIKE LOWER('%' || @search_term || '%') OR
         LOWER(e.vin) LIKE LOWER('%' || @search_term || '%'))
GROUP BY
    e.id, e.estimate_no, e.customer_name, e.vin, e.brand, e.date,
    e.location, e.vehicle_model, e.salesman_name, e.status,
    e.created_at, e.modified_at
ORDER BY
    CASE WHEN @sort_by = 'date' AND @sort_direction = 'asc' THEN e.date END ASC,
    CASE WHEN @sort_by = 'date' AND @sort_direction = 'desc' THEN e.date END DESC,
    CASE WHEN @sort_by = 'number' AND @sort_direction = 'asc' THEN e.estimate_no END ASC,
    CASE WHEN @sort_by = 'number' AND @sort_direction = 'desc' THEN e.estimate_no END DESC,
    CASE WHEN @sort_by = 'customer' AND @sort_direction = 'asc' THEN e.customer_name END ASC,
    CASE WHEN @sort_by = 'customer' AND @sort_direction = 'desc' THEN e.customer_name END DESC,
    CASE WHEN @sort_by IS NULL THEN e.estimate_no END DESC
LIMIT COALESCE(@page_size, 50)
OFFSET COALESCE((@page_number - 1) * @page_size, 0);
-- [End] --

-- [GetEstimateCount] --
-- Get total count for pagination
SELECT
    COUNT(DISTINCT e.id) AS total_count
FROM
    estimateheader e
WHERE
    (@customer_name IS NULL OR LOWER(e.customer_name) LIKE LOWER('%' || @customer_name || '%'))
    AND (@status IS NULL OR e.status = @status)
    AND (@start_date IS NULL OR e.date >= @start_date)
    AND (@end_date IS NULL OR e.date <= @end_date)
    AND (@search_term IS NULL OR
         LOWER(e.customer_name) LIKE LOWER('%' || @search_term || '%') OR
         LOWER(e.estimate_no) LIKE LOWER('%' || @search_term || '%') OR
         LOWER(e.vin) LIKE LOWER('%' || @search_term || '%'));
-- [End] --
