using System;
using ProManage.Modules.Models.PermissionManagementForm;

namespace ProManage.Modules.Services
{
    /// <summary>
    /// Event arguments for global permission changes
    /// </summary>
    public class GlobalPermissionChangedEventArgs : EventArgs
    {
        /// <summary>
        /// User ID whose permissions changed
        /// </summary>
        public int UserId { get; set; }

        /// <summary>
        /// Updated global permissions
        /// </summary>
        public GlobalPermissionModel UpdatedPermissions { get; set; }
    }
}
