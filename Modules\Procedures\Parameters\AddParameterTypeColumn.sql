-- Database Migration: Add parameter_type column to parameters table
-- Usage: Run this script to add the parameter_type column to existing parameters table
-- This migration is required for the enhanced parameter type system

-- Add parameter_type column to parameters table
-- Default to 1 (String type) for existing parameters
ALTER TABLE parameters 
ADD COLUMN IF NOT EXISTS parameter_type INTEGER DEFAULT 1 NOT NULL;

-- Add comment to the column for documentation
COMMENT ON COLUMN parameters.parameter_type IS 'Parameter data type: 1=String, 2=Number, 3=Decimal, 4=Date, 5=Boolean';

-- Create index on parameter_type for better query performance
CREATE INDEX IF NOT EXISTS idx_parameters_parameter_type ON parameters(parameter_type);

-- Update existing parameters to have appropriate types based on their values
-- This is a best-effort attempt to infer types from existing data

-- Update boolean-like values
UPDATE parameters 
SET parameter_type = 5 
WHERE parameter_type = 1 
AND LOWER(TRIM(parameter_value)) IN ('true', 'false', '1', '0', 'yes', 'no', 'y', 'n', 'on', 'off', 'enabled', 'disabled');

-- Update integer values (whole numbers)
UPDATE parameters 
SET parameter_type = 2 
WHERE parameter_type = 1 
AND parameter_value ~ '^-?[0-9]+$';

-- Update decimal values (numbers with decimal points)
UPDATE parameters 
SET parameter_type = 3 
WHERE parameter_type = 1 
AND parameter_value ~ '^-?[0-9]+\.[0-9]+$';

-- Update date values (basic date patterns)
UPDATE parameters 
SET parameter_type = 4 
WHERE parameter_type = 1 
AND (
    parameter_value ~ '^[0-9]{4}-[0-9]{2}-[0-9]{2}$' OR  -- YYYY-MM-DD
    parameter_value ~ '^[0-9]{2}/[0-9]{2}/[0-9]{4}$' OR  -- MM/DD/YYYY
    parameter_value ~ '^[0-9]{2}-[0-9]{2}-[0-9]{4}$'     -- MM-DD-YYYY
);

-- Display summary of parameter types after migration
SELECT 
    parameter_type,
    CASE parameter_type
        WHEN 1 THEN 'String'
        WHEN 2 THEN 'Number'
        WHEN 3 THEN 'Decimal'
        WHEN 4 THEN 'Date'
        WHEN 5 THEN 'Boolean'
        ELSE 'Unknown'
    END as type_name,
    COUNT(*) as count
FROM parameters 
GROUP BY parameter_type 
ORDER BY parameter_type;

-- Display any parameters that might need manual review
SELECT 
    id,
    parameter_code,
    parameter_value,
    parameter_type,
    CASE parameter_type
        WHEN 1 THEN 'String'
        WHEN 2 THEN 'Number'
        WHEN 3 THEN 'Decimal'
        WHEN 4 THEN 'Date'
        WHEN 5 THEN 'Boolean'
        ELSE 'Unknown'
    END as type_name
FROM parameters 
WHERE parameter_type NOT IN (1, 2, 3, 4, 5)
ORDER BY parameter_code;
