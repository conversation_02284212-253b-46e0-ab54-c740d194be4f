-- AddUserTableColumns.sql
-- Script to add new columns to the users table for enhanced user management

-- Add new columns to users table
ALTER TABLE users
ADD COLUMN department VARCHAR(50),
ADD COLUMN phone VARCHAR(20),
ADD COLUMN designation VARCHAR(50),
ADD COLUMN short_name VA<PERSON>HAR(50),
ADD COLUMN photo_path TEXT;

-- Add comments for documentation
COMMENT ON COLUMN users.department IS 'User department or division';
COMMENT ON COLUMN users.phone IS 'User phone number';
COMMENT ON COLUMN users.designation IS 'User job title or designation';
COMMENT ON COLUMN users.short_name IS 'User short display name';
COMMENT ON COLUMN users.photo_path IS 'Path to user photo file or base64 encoded image';

-- Verify the changes
SELECT column_name, data_type, character_maximum_length, is_nullable 
FROM information_schema.columns 
WHERE table_schema = 'public' AND table_name = 'users' 
ORDER BY ordinal_position;
