// EstimateForm Repository Helper - Simplified data access wrapper for EstimateForm operations
// Usage: Provides simplified wrapper methods that call the main EstimateFormRepository

using System;
using System.Collections.Generic;
using System.Diagnostics;
using ProManage.Modules.Models.EstimateForm;
using ProManage.Modules.Data.EstimateForm;

namespace ProManage.Modules.Helpers.EstimateForm
{
    public static class EstimateFormRepositoryHelper
    {
        public static EstimateFormHeaderModel GetFirstEstimate()
        {
            try
            {
                Debug.WriteLine("EstimateFormRepositoryHelper.GetFirstEstimate: Starting");
                var result = EstimateFormNavigation.NavigateToFirst();
                if (result != null)
                {
                    EstimateFormNavigation.LoadEstimateDetails(result);
                }
                Debug.WriteLine($"EstimateFormRepositoryHelper.GetFirstEstimate: {(result != null ? $"Found ID={result.Id}" : "No estimates found")}");
                return result;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in EstimateFormRepositoryHelper.GetFirstEstimate: {ex.Message}");
                throw;
            }
        }

        public static EstimateFormHeaderModel GetLastEstimate()
        {
            try
            {
                Debug.WriteLine("EstimateFormRepositoryHelper.GetLastEstimate: Starting");
                var result = EstimateFormNavigation.NavigateToLast();
                if (result != null)
                {
                    EstimateFormNavigation.LoadEstimateDetails(result);
                }
                Debug.WriteLine($"EstimateFormRepositoryHelper.GetLastEstimate: {(result != null ? $"Found ID={result.Id}" : "No estimates found")}");
                return result;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in EstimateFormRepositoryHelper.GetLastEstimate: {ex.Message}");
                throw;
            }
        }

        public static EstimateFormHeaderModel GetPreviousEstimate(int currentId)
        {
            try
            {
                Debug.WriteLine($"EstimateFormRepositoryHelper.GetPreviousEstimate: Looking for estimate before ID {currentId}");
                var result = EstimateFormNavigation.NavigateToPrevious(currentId);
                if (result != null)
                {
                    EstimateFormNavigation.LoadEstimateDetails(result);
                }
                Debug.WriteLine($"EstimateFormRepositoryHelper.GetPreviousEstimate: {(result != null ? $"Found ID={result.Id}" : "No previous estimate found")}");
                return result;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in EstimateFormRepositoryHelper.GetPreviousEstimate: {ex.Message}");
                throw;
            }
        }

        public static EstimateFormHeaderModel GetNextEstimate(int currentId)
        {
            try
            {
                Debug.WriteLine($"EstimateFormRepositoryHelper.GetNextEstimate: Looking for estimate after ID {currentId}");
                var result = EstimateFormNavigation.NavigateToNext(currentId);
                if (result != null)
                {
                    EstimateFormNavigation.LoadEstimateDetails(result);
                }
                Debug.WriteLine($"EstimateFormRepositoryHelper.GetNextEstimate: {(result != null ? $"Found ID={result.Id}" : "No next estimate found")}");
                return result;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in EstimateFormRepositoryHelper.GetNextEstimate: {ex.Message}");
                throw;
            }
        }

        public static EstimateFormHeaderModel GetEstimateById(int id)
        {
            try
            {
                Debug.WriteLine($"EstimateFormRepositoryHelper.GetEstimateById: Looking for estimate ID {id}");
                var result = EstimateFormRepository.GetEstimateById(id);
                Debug.WriteLine($"EstimateFormRepositoryHelper.GetEstimateById: {(result != null ? $"Found estimate {result.EstimateNo}" : "Estimate not found")}");
                return result;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in EstimateFormRepositoryHelper.GetEstimateById: {ex.Message}");
                throw;
            }
        }

        public static List<EstimateFormDetailModel> GetEstimateDetailsById(int estimateId)
        {
            try
            {
                Debug.WriteLine($"EstimateFormRepositoryHelper.GetEstimateDetailsById: Loading details for estimate ID {estimateId}");
                var result = EstimateFormRepository.GetEstimateDetailsById(estimateId);
                Debug.WriteLine($"EstimateFormRepositoryHelper.GetEstimateDetailsById: Found {result.Count} details");
                return result;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in EstimateFormRepositoryHelper.GetEstimateDetailsById: {ex.Message}");
                throw;
            }
        }

        public static bool SaveEstimate(EstimateFormHeaderModel estimate)
        {
            try
            {
                Debug.WriteLine($"EstimateFormRepositoryHelper.SaveEstimate: Saving estimate {estimate.EstimateNo} with {estimate.Details?.Count ?? 0} details");

                var result = EstimateFormRepository.SaveEstimate(estimate);
                Debug.WriteLine($"EstimateFormRepositoryHelper.SaveEstimate: {(result ? "Success" : "Failed")}");
                return result;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in EstimateFormRepositoryHelper.SaveEstimate: {ex.Message}");
                throw;
            }
        }

        public static bool DeleteEstimate(int id)
        {
            try
            {
                Debug.WriteLine($"EstimateFormRepositoryHelper.DeleteEstimate: Deleting estimate ID {id}");
                var result = EstimateFormRepository.DeleteEstimate(id);
                Debug.WriteLine($"EstimateFormRepositoryHelper.DeleteEstimate: {(result ? "Success" : "Failed")}");
                return result;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in EstimateFormRepositoryHelper.DeleteEstimate: {ex.Message}");
                throw;
            }
        }

        public static int GetEstimateCount()
        {
            try
            {
                Debug.WriteLine("EstimateFormRepositoryHelper.GetEstimateCount: Starting");
                var estimates = EstimateFormRepository.GetAllEstimates();
                var result = estimates.Count;
                Debug.WriteLine($"EstimateFormRepositoryHelper.GetEstimateCount: Found {result} estimates");
                return result;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in EstimateFormRepositoryHelper.GetEstimateCount: {ex.Message}");
                throw;
            }
        }

        public static int GetEstimatePosition(int id)
        {
            try
            {
                Debug.WriteLine($"EstimateFormRepositoryHelper.GetEstimatePosition: Getting position for ID {id}");
                // This would need to be implemented in the main repository if needed
                // For now, return 1 as a placeholder
                var result = 1;
                Debug.WriteLine($"EstimateFormRepositoryHelper.GetEstimatePosition: Position is {result}");
                return result;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in EstimateFormRepositoryHelper.GetEstimatePosition: {ex.Message}");
                throw;
            }
        }

        public static string GetNextEstimateNumber()
        {
            try
            {
                Debug.WriteLine("EstimateFormRepositoryHelper.GetNextEstimateNumber: Starting");
                var result = EstimateFormRepository.GetNextEstimateNumber();
                Debug.WriteLine($"EstimateFormRepositoryHelper.GetNextEstimateNumber: Generated {result}");
                return result;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in EstimateFormRepositoryHelper.GetNextEstimateNumber: {ex.Message}");
                throw;
            }
        }

        public static List<EstimateFormHeaderModel> SearchEstimates(string searchTerm)
        {
            try
            {
                Debug.WriteLine($"EstimateFormRepositoryHelper.SearchEstimates: Searching for '{searchTerm}'");
                var result = EstimateFormRepository.SearchEstimatesByCustomer(searchTerm);
                Debug.WriteLine($"EstimateFormRepositoryHelper.SearchEstimates: Found {result.Count} matches");
                return result;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in EstimateFormRepositoryHelper.SearchEstimates: {ex.Message}");
                throw;
            }
        }

        public static bool EstimateNumberExists(string estimateNumber, int? excludeId = null)
        {
            try
            {
                Debug.WriteLine($"EstimateFormRepositoryHelper.EstimateNumberExists: Checking '{estimateNumber}' (exclude ID: {excludeId})");
                var estimate = EstimateFormRepository.GetEstimateByNumber(estimateNumber);
                var result = estimate != null && (excludeId == null || estimate.Id != excludeId);
                Debug.WriteLine($"EstimateFormRepositoryHelper.EstimateNumberExists: {(result ? "Exists" : "Does not exist")}");
                return result;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in EstimateFormRepositoryHelper.EstimateNumberExists: {ex.Message}");
                throw;
            }
        }
    }
}
