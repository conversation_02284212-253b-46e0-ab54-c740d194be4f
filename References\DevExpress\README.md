# DevExpress Dependencies

This folder contains DevExpress UI components and libraries used by the ProManage application.

## Components

DevExpress is the primary UI framework used in ProManage, providing:

- XtraTabbedMdiManager for MDI architecture
- AccordionControl for navigation
- RibbonControl for command interface
- GridControl for data display
- Various editors and controls

## Organization

Components are organized by their package name and version:
- DevExpress.Data.24.1.7
- DevExpress.Drawing.24.1.7
- DevExpress.Win.Grid.24.1.7
- DevExpress.Win.Navigation.24.1.7
- Etc.

## Reference Management

All assemblies are referenced in the project via NuGet package references in the packages.config file, with assembly references in the project file pointing to the packages folder.