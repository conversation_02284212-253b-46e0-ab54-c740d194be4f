{"type":"entity","name":"ProManage Application","entityType":"software_project","observations":["A Windows Forms application for project management","Uses DevExpress and Syncfusion UI components","Implements role-based access control (RBAC)","Built with C# and .NET Framework"]}
{"type":"entity","name":"Memory Tool Test","entityType":"test_case","observations":["Testing memory tool functionality","Created on user request","Verifying entity creation and storage"]}
{"type":"relation","from":"Memory Tool Test","to":"ProManage Application","relationType":"tests_functionality_of"}