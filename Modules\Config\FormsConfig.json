{"lastUpdated": "2024-01-01T00:00:00Z", "forms": [{"formName": "DatabaseForm", "displayName": "Database Management", "category": "System", "isActive": true, "sortOrder": 10, "description": "Database connection and management tools", "requiresSpecialPermission": false}, {"formName": "ParametersForm", "displayName": "System Parameters", "category": "System", "isActive": true, "sortOrder": 20, "description": "System-wide parameter configuration", "requiresSpecialPermission": false}, {"formName": "RoleMasterForm", "displayName": "Role Management", "category": "Security", "isActive": true, "sortOrder": 30, "description": "Manage user roles and permissions", "requiresSpecialPermission": true}, {"formName": "SQLQueryForm", "displayName": "SQL Query Tool", "category": "System", "isActive": true, "sortOrder": 40, "description": "Execute SQL queries and scripts", "requiresSpecialPermission": true}, {"formName": "UserManagementListForm", "displayName": "User List", "category": "Security", "isActive": true, "sortOrder": 50, "description": "View and manage user accounts", "requiresSpecialPermission": false}, {"formName": "UserMasterForm", "displayName": "User Management", "category": "Security", "isActive": true, "sortOrder": 60, "description": "Create and edit user accounts", "requiresSpecialPermission": false}], "categories": [{"categoryName": "System", "displayName": "System Administration", "sortOrder": 10, "isActive": true}, {"categoryName": "Security", "displayName": "Security & Access", "sortOrder": 20, "isActive": true}]}