# Progress Bar Usage Guide

## Overview

The ProManage application uses a centralized progress indicator system built around the `ProgressIndicatorService` singleton class. This service controls the progress bar in the MainFrame status panel, providing visual feedback **specifically during database operations**.

**Key Features:**
- **Centralized Control**: Single service manages the MainFrame progress bar
- **Minimal Form Code**: Simple two-line implementation in forms
- **Thread Safety**: Safe for use in multi-threaded operations
- **Nested Operations**: Supports multiple concurrent operations through reference counting
- **Minimum Display Time**: Ensures progress bar is visible for at least 500ms for better UX

**Architecture:**
```
Database Operation → ProgressIndicatorService → MainFrame StatusProgressBar
```

## Basic Usage Patterns

**Standard Pattern (Use for all database operations):**
```csharp
using ProManage.Modules.UI;

ProgressIndicatorService.Instance.ShowProgress();
try
{
    // Your database operation here
    var result = await DatabaseOperation();
    ProcessResults(result);
}
finally
{
    ProgressIndicatorService.Instance.HideProgress();
}
```

**Synchronous Operations:**
```csharp
ProgressIndicatorService.Instance.ShowProgress();
try
{
    DataTable result = EstimateFormRepository.GetAllEstimates();
    gridControl.DataSource = result;
}
finally
{
    ProgressIndicatorService.Instance.HideProgress();
}
```

**Multiple Sequential Operations:**
```csharp
ProgressIndicatorService.Instance.ShowProgress();
try
{
    // Multiple operations - progress bar stays visible
    var estimates = EstimateFormRepository.GetAllEstimates();
    var customers = CustomerRepository.GetAllCustomers();
    ProcessData(estimates, customers);
}
finally
{
    ProgressIndicatorService.Instance.HideProgress();
}
```

## Advanced Usage

**Nested Operations:**
The service automatically handles nested operations through reference counting:

```csharp
// Outer operation
ProgressIndicatorService.Instance.ShowProgress();
try
{
    ProcessEstimates(); // This will increment the operation count
    ProcessCustomers(); // This will also increment the operation count
}
finally
{
    ProgressIndicatorService.Instance.HideProgress(); // Decrements count
}

private void ProcessEstimates()
{
    ProgressIndicatorService.Instance.ShowProgress(); // Increments count
    try
    {
        var estimates = EstimateFormRepository.GetAllEstimates();
    }
    finally
    {
        ProgressIndicatorService.Instance.HideProgress(); // Decrements count
    }
}
```

**Error Recovery:**
For critical operations, you can reset the progress indicator:
```csharp
ProgressIndicatorService.Instance.ShowProgress();
try
{
    var result = CriticalDatabaseOperation();
}
catch (Exception ex)
{
    // Reset progress indicator if needed
    if (ProgressIndicatorService.Instance.IsVisible)
    {
        ProgressIndicatorService.Instance.Reset();
    }
    MessageBox.Show("Operation failed. Please try again.", "Error");
}
finally
{
    ProgressIndicatorService.Instance.HideProgress();
}
```

## Integration Examples

**Form Load Operations:**
```csharp
private async void EstimateForm_Load(object sender, EventArgs e)
{
    ProgressIndicatorService.Instance.ShowProgress();
    try
    {
        await LoadEstimateData();
        InitializeGridColumns();
        await LoadDropdownData();
    }
    finally
    {
        ProgressIndicatorService.Instance.HideProgress();
    }
}
```

**Save Operations:**
```csharp
private void btnSave_Click(object sender, EventArgs e)
{
    ProgressIndicatorService.Instance.ShowProgress();
    try
    {
        if (!ValidateForm()) return;

        bool success = EstimateFormRepository.SaveEstimate(currentEstimate);
        if (success)
        {
            MessageBox.Show("Estimate saved successfully.", "Success");
            LoadEstimateData();
        }
    }
    finally
    {
        ProgressIndicatorService.Instance.HideProgress();
    }
}
```

## Best Practices

**When to Use (Database Operations Only):**
- Database CRUD operations (Create, Read, Update, Delete)
- Database search and query operations
- Database connection and initialization
- Report generation from database
- Form initialization that loads data from database

**Don't Use For:**
- Simple UI updates
- Client-side validation operations
- Quick calculations
- File I/O operations
- Any operation that doesn't communicate with the database

**Code Organization:**
- Always use try-finally blocks
- Keep ShowProgress/HideProgress calls simple and clear
- Avoid complex nesting patterns
- Use Reset() only for critical error scenarios

## Common Issues and Solutions

**Progress Bar Stuck Visible:**
- **Cause**: Missing `finally` block or unbalanced calls
- **Solution**: Always use try-finally pattern, use `Reset()` for emergency cleanup

**Progress Bar Not Showing:**
- **Cause**: Service not initialized or operation too fast (< 500ms)
- **Solution**: Verify MainFrame initialization, test with longer operations

**Thread Safety Issues:**
- **Solution**: Service handles thread safety automatically through `InvokeRequired` checks

## Debugging

**Check Service Status:**
```csharp
if (!ProgressIndicatorService.Instance.IsInitialized)
{
    Debug.WriteLine("ProgressIndicatorService not initialized!");
}

if (ProgressIndicatorService.Instance.IsVisible)
{
    Debug.WriteLine("Progress indicator is currently visible");
}
```

**Common Debug Messages:**
- `"Warning: ProgressIndicatorService not initialized"` - Check MainFrame initialization
- `"Progress indicator still needed. Remaining operations: X"` - Check for unbalanced calls

## Performance Notes

- **Minimum Display Time**: Service enforces 500ms minimum display for better UX
- **UI Thread Impact**: Uses `Application.DoEvents()` for immediate UI updates
- **Memory Usage**: Singleton pattern ensures minimal overhead
