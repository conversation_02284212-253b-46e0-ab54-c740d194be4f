# Syncfusion Dependencies

This folder contains Syncfusion components and libraries used by the ProManage application.

## Components

Syncfusion provides complementary UI components to DevExpress:

- SfDataGrid: Advanced data grid for specific use cases
- SfPdfViewer: PDF viewing capabilities
- SfChart: Advanced charting
- SfTreeView: Tree view control

## Organization

Components are organized by their package name and version.

## Reference Management

All assemblies are referenced in the project via NuGet package references in the packages.config file, with assembly references in the project file pointing to the packages folder.

## Licensing

Syncfusion components use a Community License (free for small businesses), registered with SyncfusionLicenseProvider.RegisterLicense().