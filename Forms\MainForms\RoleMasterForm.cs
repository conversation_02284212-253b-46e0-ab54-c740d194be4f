using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using DevExpress.XtraGrid.Views.Grid;
using DevExpress.XtraEditors.Repository;
using ProManage.Modules.Services;
using ProManage.Modules.Models;
using ProManage.Modules.Models.PermissionManagementForm;
using ProManage.Modules.Connections;
using ProManage.Modules.Helpers.PermissionManagementForm;

namespace ProManage.Forms
{
    public partial class RoleMasterForm : Form
    {
        #region Private Fields

        private readonly PermissionGridHelper _gridHelper;
        private bool _permissionsChanged = false;
        private bool _isLoading = false;
        private int _currentRoleId = 0;

        #endregion

        #region Constructor

        public RoleMasterForm()
        {
            InitializeComponent();
            _gridHelper = new PermissionGridHelper();
            InitializePermissionEnhancements();
        }

        #endregion

        #region Permission Enhancement Methods

        /// <summary>
        /// Initialize permission-related enhancements
        /// </summary>
        private void InitializePermissionEnhancements()
        {
            try
            {
                // Set form properties for MDI
                this.MdiParent = System.Windows.Forms.Application.OpenForms.OfType<Form>().FirstOrDefault(f => f.IsMdiContainer);
                this.WindowState = FormWindowState.Maximized;

                // Setup permissions grid
                SetupPermissionsGrid();

                // Setup event handlers
                SetupPermissionEventHandlers();

                System.Diagnostics.Debug.WriteLine("RoleMasterForm permission enhancements initialized");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error initializing permission enhancements: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Setup the permissions grid in the second tab
        /// </summary>
        private void SetupPermissionsGrid()
        {
            try
            {
                // Configure the existing grid in xtraTabPage1 for role permissions
                _gridHelper.ConfigureRolePermissionsGrid(UserRoleGrid, gridView1);

                // Update tab text
                xtraTabPage1.Text = "Role Permissions";
                xtraTabPage2.Text = "Additional Settings";

                System.Diagnostics.Debug.WriteLine("Permissions grid setup completed");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error setting up permissions grid: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Setup event handlers for permission functionality
        /// </summary>
        private void SetupPermissionEventHandlers()
        {
            try
            {
                // Grid events for tracking changes
                gridView1.CellValueChanged += GridView1_CellValueChanged;

                // Tab control events
                TabControlResponsiblity.SelectedPageChanged += TabControlResponsiblity_SelectedPageChanged;

                System.Diagnostics.Debug.WriteLine("Permission event handlers setup completed");
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error setting up permission event handlers: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Load role permissions for the specified role
        /// </summary>
        /// <param name="roleId">Role ID to load permissions for</param>
        private void LoadRolePermissions(int roleId)
        {
            if (roleId <= 0) return;

            try
            {
                _isLoading = true;
                _currentRoleId = roleId;

                // Load permissions using the grid helper
                _gridHelper.LoadRolePermissions(UserRoleGrid, roleId);

                // Enable the permissions tab
                xtraTabPage1.PageEnabled = true;
                _permissionsChanged = false;

                System.Diagnostics.Debug.WriteLine($"Loaded permissions for role ID: {roleId}");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading role permissions: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
            finally
            {
                _isLoading = false;
            }
        }

        /// <summary>
        /// Save role permissions changes
        /// </summary>
        /// <returns>True if saved successfully</returns>
        private bool SaveRolePermissions()
        {
            if (_currentRoleId <= 0) return false;

            try
            {
                var permissions = new List<RolePermissionUpdate>();
                var gridView = gridView1;

                // Extract permissions from grid
                for (int i = 0; i < gridView.RowCount; i++)
                {
                    var formName = gridView.GetRowCellValue(i, "FormName")?.ToString();
                    if (string.IsNullOrEmpty(formName)) continue;

                    var permission = new RolePermissionUpdate
                    {
                        RoleId = _currentRoleId,
                        FormName = formName,
                        ReadPermission = (bool)gridView.GetRowCellValue(i, "ReadPermission"),
                        NewPermission = (bool)gridView.GetRowCellValue(i, "NewPermission"),
                        EditPermission = (bool)gridView.GetRowCellValue(i, "EditPermission"),
                        DeletePermission = (bool)gridView.GetRowCellValue(i, "DeletePermission"),
                        PrintPermission = (bool)gridView.GetRowCellValue(i, "PrintPermission")
                    };

                    permissions.Add(permission);
                }

                // Save using permission service
                PermissionService.UpdateRolePermissions(_currentRoleId, permissions);
                PermissionService.ClearCache();

                _permissionsChanged = false;
                return true;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error saving role permissions: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }
        }

        #endregion

        #region Event Handlers

        /// <summary>
        /// Handle grid cell value changes to track modifications
        /// </summary>
        private void GridView1_CellValueChanged(object sender, DevExpress.XtraGrid.Views.Base.CellValueChangedEventArgs e)
        {
            if (!_isLoading)
            {
                _permissionsChanged = true;
                // Enable save functionality (implement based on existing form pattern)
                EnableSaveButton();
            }
        }

        /// <summary>
        /// Handle tab page changes
        /// </summary>
        private void TabControlResponsiblity_SelectedPageChanged(object sender, DevExpress.XtraTab.TabPageChangedEventArgs e)
        {
            try
            {
                if (e.Page == xtraTabPage1 && _currentRoleId > 0)
                {
                    // Refresh permissions when switching to permissions tab
                    LoadRolePermissions(_currentRoleId);
                }
            }
            catch (Exception ex)
            {
                System.Diagnostics.Debug.WriteLine($"Error handling tab change: {ex.Message}");
            }
        }

        #endregion

        #region Helper Methods

        /// <summary>
        /// Enable save button (implement based on existing form pattern)
        /// </summary>
        private void EnableSaveButton()
        {
            // TODO: Implement based on existing form's save button pattern
            // This should enable the save button or mark the form as dirty
        }

        /// <summary>
        /// Load role data and permissions (public method for external calls)
        /// </summary>
        /// <param name="roleId">Role ID to load</param>
        public void LoadRole(int roleId)
        {
            try
            {
                _currentRoleId = roleId;

                // Load role permissions
                LoadRolePermissions(roleId);

                System.Diagnostics.Debug.WriteLine($"Role {roleId} loaded successfully");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error loading role: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Handle new role creation
        /// </summary>
        public void NewRole()
        {
            try
            {
                _currentRoleId = 0;
                _permissionsChanged = false;

                // Disable permissions tab for new role
                xtraTabPage1.PageEnabled = false;

                // Clear permissions grid
                UserRoleGrid.DataSource = null;

                System.Diagnostics.Debug.WriteLine("New role mode activated");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error setting up new role: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// Save all changes (role data and permissions)
        /// </summary>
        /// <returns>True if saved successfully</returns>
        public bool SaveChanges()
        {
            try
            {
                bool success = true;

                // Save role permissions if changed
                if (_permissionsChanged && _currentRoleId > 0)
                {
                    success = SaveRolePermissions();
                }

                return success;
            }
            catch (Exception ex)
            {
                MessageBox.Show($"Error saving changes: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }
        }

        /// <summary>
        /// Check if there are unsaved changes
        /// </summary>
        /// <returns>True if there are unsaved changes</returns>
        public bool HasUnsavedChanges()
        {
            return _permissionsChanged;
        }

        #endregion
    }
}
