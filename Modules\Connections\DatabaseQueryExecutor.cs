using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.Threading.Tasks;
using Npgsql;
using ProManage.Modules.Helpers;

namespace ProManage.Modules.Connections
{
    /// <summary>
    /// Class for executing SQL queries against PostgreSQL database.
    /// Provides methods for executing queries loaded from the Procedures folder.
    ///
    /// This class uses DatabaseConnectionManager for connection management and
    /// SQLQueryLoader for loading SQL queries from files.
    /// </summary>
    public class QueryExecutor
    {
        /// <summary>
        /// Executes a SELECT query and returns the results as a DataTable
        /// </summary>
        /// <param name="query">The SQL SELECT query to execute</param>
        /// <param name="parameters">Optional parameters for the query</param>
        /// <param name="errorMessage">Output parameter that will contain any error message</param>
        /// <returns>DataTable containing query results, or null if an error occurred</returns>
        public static DataTable ExecuteSelectQuery(string query, out string errorMessage, Dictionary<string, object> parameters = null)
        {
            errorMessage = string.Empty;
            var result = new DataTable();
            NpgsqlConnection conn = null;

            try
            {
                // Get a new connection
                conn = DatabaseConnectionManager.Instance.CreateNewConnection();
                conn.Open();
                Debug.WriteLine("Connection opened for SELECT query");

                // Create command and adapter
                using (var cmd = new NpgsqlCommand(query, conn))
                {
                    // Set command timeout to 30 seconds
                    cmd.CommandTimeout = 30;

                    // Add parameters if provided
                    if (parameters != null)
                    {
                        foreach (var param in parameters)
                        {
                            cmd.Parameters.AddWithValue(param.Key, param.Value ?? DBNull.Value);
                        }
                    }

                    // Create adapter and fill DataTable
                    using (var adapter = new NpgsqlDataAdapter(cmd))
                    {
                        adapter.Fill(result);
                        Debug.WriteLine($"Query executed successfully. Rows returned: {result.Rows.Count}");
                    }
                }

                return result;
            }
            catch (Exception ex)
            {
                errorMessage = $"Error executing SELECT query: {ex.Message}";
                if (ex.InnerException != null)
                {
                    errorMessage += Environment.NewLine + $"Inner error: {ex.InnerException.Message}";
                }

                // Add more specific error details for common database issues
                if (ex.Message.Contains("does not exist") || ex.Message.Contains("relation") || ex.Message.Contains("table"))
                {
                    errorMessage += Environment.NewLine + "This usually means the database table doesn't exist. Please check your database setup.";
                }

                Debug.WriteLine(errorMessage);
                return null;
            }
            finally
            {
                // Close and dispose the connection
                if (conn != null)
                {
                    if (conn.State == ConnectionState.Open)
                    {
                        conn.Close();
                    }
                    conn.Dispose();
                    Debug.WriteLine("Connection closed and disposed after SELECT query");
                }
            }
        }

        /// <summary>
        /// Executes a non-SELECT query (INSERT, UPDATE, DELETE, etc.)
        /// </summary>
        /// <param name="query">The SQL non-SELECT query to execute</param>
        /// <param name="parameters">Optional parameters for the query</param>
        /// <param name="errorMessage">Output parameter that will contain any error message</param>
        /// <returns>Number of rows affected, or -1 if an error occurred</returns>
        public static int ExecuteNonQuery(string query, out string errorMessage, Dictionary<string, object> parameters = null)
        {
            errorMessage = string.Empty;
            NpgsqlConnection conn = null;

            try
            {
                // Get a new connection
                conn = DatabaseConnectionManager.Instance.CreateNewConnection();
                conn.Open();
                Debug.WriteLine("Connection opened for non-SELECT query");

                // Create command and execute
                using (var cmd = new NpgsqlCommand(query, conn))
                {
                    // Set command timeout to 30 seconds
                    cmd.CommandTimeout = 30;

                    // Add parameters if provided
                    if (parameters != null)
                    {
                        foreach (var param in parameters)
                        {
                            cmd.Parameters.AddWithValue(param.Key, param.Value ?? DBNull.Value);
                        }
                    }

                    // Execute the command
                    int rowsAffected = cmd.ExecuteNonQuery();
                    Debug.WriteLine($"Non-query executed successfully. Rows affected: {rowsAffected}");
                    return rowsAffected;
                }
            }
            catch (Exception ex)
            {
                errorMessage = $"Error executing non-SELECT query: {ex.Message}";
                if (ex.InnerException != null)
                {
                    errorMessage += Environment.NewLine + $"Inner error: {ex.InnerException.Message}";
                }
                Debug.WriteLine(errorMessage);
                return -1;
            }
            finally
            {
                // Close and dispose the connection
                if (conn != null)
                {
                    if (conn.State == ConnectionState.Open)
                    {
                        conn.Close();
                    }
                    conn.Dispose();
                    Debug.WriteLine("Connection closed and disposed after non-SELECT query");
                }
            }
        }

        /// <summary>
        /// Executes a scalar query and returns a single value
        /// </summary>
        /// <param name="query">The SQL query to execute</param>
        /// <param name="parameters">Optional parameters for the query</param>
        /// <param name="errorMessage">Output parameter that will contain any error message</param>
        /// <returns>The scalar result, or null if an error occurred</returns>
        public static object ExecuteScalar(string query, out string errorMessage, Dictionary<string, object> parameters = null)
        {
            errorMessage = string.Empty;
            NpgsqlConnection conn = null;

            try
            {
                // Get a new connection
                conn = DatabaseConnectionManager.Instance.CreateNewConnection();
                conn.Open();
                Debug.WriteLine("Connection opened for scalar query");

                // Create command and execute
                using (var cmd = new NpgsqlCommand(query, conn))
                {
                    // Set command timeout to 30 seconds
                    cmd.CommandTimeout = 30;

                    // Add parameters if provided
                    if (parameters != null)
                    {
                        foreach (var param in parameters)
                        {
                            cmd.Parameters.AddWithValue(param.Key, param.Value ?? DBNull.Value);
                        }
                    }

                    // Execute the command
                    object result = cmd.ExecuteScalar();
                    Debug.WriteLine($"Scalar query executed successfully. Result: {result}");
                    return result;
                }
            }
            catch (Exception ex)
            {
                errorMessage = $"Error executing scalar query: {ex.Message}";
                if (ex.InnerException != null)
                {
                    errorMessage += Environment.NewLine + $"Inner error: {ex.InnerException.Message}";
                }
                Debug.WriteLine(errorMessage);
                return null;
            }
            finally
            {
                // Close and dispose the connection
                if (conn != null)
                {
                    if (conn.State == ConnectionState.Open)
                    {
                        conn.Close();
                    }
                    conn.Dispose();
                    Debug.WriteLine("Connection closed and disposed after scalar query");
                }
            }
        }

        /// <summary>
        /// Executes a query with a transaction and returns the results
        /// </summary>
        /// <param name="queries">List of SQL queries to execute in the transaction</param>
        /// <param name="parameters">Optional parameters for the queries</param>
        /// <param name="errorMessage">Output parameter that will contain any error message</param>
        /// <returns>True if the transaction was successful, False otherwise</returns>
        public static bool ExecuteTransaction(List<string> queries, out string errorMessage, Dictionary<string, object> parameters = null)
        {
            errorMessage = string.Empty;
            NpgsqlConnection conn = null;
            NpgsqlTransaction transaction = null;

            try
            {
                // Get a new connection
                conn = DatabaseConnectionManager.Instance.CreateNewConnection();
                conn.Open();
                Debug.WriteLine("Connection opened for transaction");

                // Begin transaction
                transaction = conn.BeginTransaction();
                Debug.WriteLine("Transaction started");

                // Execute each query in the transaction
                foreach (string query in queries)
                {
                    using (var cmd = new NpgsqlCommand(query, conn, transaction))
                    {
                        // Set command timeout to 30 seconds
                        cmd.CommandTimeout = 30;

                        // Add parameters if provided
                        if (parameters != null)
                        {
                            foreach (var param in parameters)
                            {
                                cmd.Parameters.AddWithValue(param.Key, param.Value ?? DBNull.Value);
                            }
                        }

                        // Execute the command
                        cmd.ExecuteNonQuery();
                        Debug.WriteLine("Query executed in transaction");
                    }
                }

                // Commit the transaction
                transaction.Commit();
                Debug.WriteLine("Transaction committed successfully");
                return true;
            }
            catch (Exception ex)
            {
                // Rollback the transaction if an error occurs
                if (transaction != null)
                {
                    try
                    {
                        transaction.Rollback();
                        Debug.WriteLine("Transaction rolled back due to error");
                    }
                    catch (Exception rollbackEx)
                    {
                        Debug.WriteLine($"Error rolling back transaction: {rollbackEx.Message}");
                    }
                }

                errorMessage = $"Error executing transaction: {ex.Message}";
                if (ex.InnerException != null)
                {
                    errorMessage += Environment.NewLine + $"Inner error: {ex.InnerException.Message}";
                }
                Debug.WriteLine(errorMessage);
                return false;
            }
            finally
            {
                // Close and dispose the connection
                if (conn != null)
                {
                    if (conn.State == ConnectionState.Open)
                    {
                        conn.Close();
                    }
                    conn.Dispose();
                    Debug.WriteLine("Connection closed and disposed after transaction");
                }
            }
        }

        /// <summary>
        /// Executes a query from a SQL file in the Procedures folder
        /// </summary>
        /// <param name="moduleName">The module name (subfolder in Modules/Procedures)</param>
        /// <param name="queryName">The query file name without extension</param>
        /// <param name="parameters">Optional parameters for the query</param>
        /// <param name="errorMessage">Output parameter that will contain any error message</param>
        /// <returns>DataTable containing query results, or null if an error occurred</returns>
        public static DataTable ExecuteQueryFromFile(string moduleName, string queryName, out string errorMessage, Dictionary<string, object> parameters = null)
        {
            errorMessage = string.Empty;
            try
            {
                // Load the SQL query from file
                string query = SQLQueryLoader.LoadQuery(moduleName, queryName);
                Debug.WriteLine($"Loaded query from file: {moduleName}/{queryName}.sql");

                // Check if the query is a SELECT query
                if (IsSelectQuery(query))
                {
                    // Execute the SELECT query
                    return ExecuteSelectQuery(query, out errorMessage, parameters);
                }
                else
                {
                    // For non-SELECT queries, execute and return an empty DataTable
                    int result = ExecuteNonQuery(query, out errorMessage, parameters);
                    if (result >= 0)
                    {
                        // Return an empty DataTable with a single column to indicate success
                        var dt = new DataTable();
                        dt.Columns.Add("RowsAffected", typeof(int));
                        dt.Rows.Add(result);
                        return dt;
                    }
                    else
                    {
                        return null;
                    }
                }
            }
            catch (Exception ex)
            {
                errorMessage = $"Error executing query from file: {ex.Message}";
                if (ex.InnerException != null)
                {
                    errorMessage += Environment.NewLine + $"Inner error: {ex.InnerException.Message}";
                }
                Debug.WriteLine(errorMessage);
                return null;
            }
        }

        /// <summary>
        /// Executes a named query from a SQL file in the Procedures folder
        /// </summary>
        /// <param name="moduleName">The module name (subfolder in Modules/Procedures)</param>
        /// <param name="queryFileName">The query file name without extension</param>
        /// <param name="queryName">The specific query name to extract</param>
        /// <param name="parameters">Optional parameters for the query</param>
        /// <param name="errorMessage">Output parameter that will contain any error message</param>
        /// <returns>DataTable containing query results, or null if an error occurred</returns>
        public static DataTable ExecuteNamedQueryFromFile(string moduleName, string queryFileName, string queryName, out string errorMessage, Dictionary<string, object> parameters = null)
        {
            errorMessage = string.Empty;
            try
            {
                // Extract the named query from the file
                string query = SQLQueryLoader.ExtractNamedQuery(moduleName, queryFileName, queryName);
                Debug.WriteLine($"Extracted named query '{queryName}' from file: {moduleName}/{queryFileName}.sql");

                // Check if the query is a SELECT query
                if (IsSelectQuery(query))
                {
                    // Execute the SELECT query
                    return ExecuteSelectQuery(query, out errorMessage, parameters);
                }
                else
                {
                    // For non-SELECT queries, execute and return an empty DataTable
                    int result = ExecuteNonQuery(query, out errorMessage, parameters);
                    if (result >= 0)
                    {
                        // Return an empty DataTable with a single column to indicate success
                        var dt = new DataTable();
                        dt.Columns.Add("RowsAffected", typeof(int));
                        dt.Rows.Add(result);
                        return dt;
                    }
                    else
                    {
                        return null;
                    }
                }
            }
            catch (Exception ex)
            {
                errorMessage = $"Error executing named query from file: {ex.Message}";
                if (ex.InnerException != null)
                {
                    errorMessage += Environment.NewLine + $"Inner error: {ex.InnerException.Message}";
                }
                Debug.WriteLine(errorMessage);
                return null;
            }
        }

        /// <summary>
        /// Determines if a query is a SELECT query
        /// </summary>
        /// <param name="query">The SQL query to check</param>
        /// <returns>True if the query is a SELECT query, False otherwise</returns>
        public static bool IsSelectQuery(string query)
        {
            if (string.IsNullOrWhiteSpace(query))
                return false;

            // Split into lines and find the first non-comment, non-empty line
            string[] lines = query.Split(new[] { '\r', '\n' }, StringSplitOptions.RemoveEmptyEntries);

            foreach (string line in lines)
            {
                string trimmedLine = line.Trim();

                // Skip empty lines and comment lines
                if (string.IsNullOrWhiteSpace(trimmedLine) || trimmedLine.StartsWith("--"))
                    continue;

                // Check if this line starts with SELECT
                return trimmedLine.StartsWith("SELECT", StringComparison.OrdinalIgnoreCase);
            }

            return false;
        }

        /// <summary>
        /// Asynchronously executes a SELECT query and returns the results as a DataTable
        /// </summary>
        /// <param name="query">The SQL SELECT query to execute</param>
        /// <param name="parameters">Optional parameters for the query</param>
        /// <returns>Task containing DataTable with query results, or null if an error occurred</returns>
        public static async Task<(DataTable Result, string ErrorMessage)> ExecuteSelectQueryAsync(string query, Dictionary<string, object> parameters = null)
        {
            var result = new DataTable();
            string errorMessage = string.Empty;
            NpgsqlConnection conn = null;

            try
            {
                // Get a new connection
                conn = DatabaseConnectionManager.Instance.CreateNewConnection();
                await conn.OpenAsync();
                Debug.WriteLine("Connection opened asynchronously for SELECT query");

                // Create command and adapter
                using (var cmd = new NpgsqlCommand(query, conn))
                {
                    // Set command timeout to 30 seconds
                    cmd.CommandTimeout = 30;

                    // Add parameters if provided
                    if (parameters != null)
                    {
                        foreach (var param in parameters)
                        {
                            cmd.Parameters.AddWithValue(param.Key, param.Value ?? DBNull.Value);
                        }
                    }

                    // Create adapter and fill DataTable
                    using (var adapter = new NpgsqlDataAdapter(cmd))
                    {
                        adapter.Fill(result);
                        Debug.WriteLine($"Async query executed successfully. Rows returned: {result.Rows.Count}");
                    }
                }

                return (result, errorMessage);
            }
            catch (Exception ex)
            {
                errorMessage = $"Error executing async SELECT query: {ex.Message}";
                if (ex.InnerException != null)
                {
                    errorMessage += Environment.NewLine + $"Inner error: {ex.InnerException.Message}";
                }
                Debug.WriteLine(errorMessage);
                return (null, errorMessage);
            }
            finally
            {
                // Close and dispose the connection
                if (conn != null)
                {
                    if (conn.State == ConnectionState.Open)
                    {
                        await conn.CloseAsync();
                    }
                    await conn.DisposeAsync();
                    Debug.WriteLine("Connection closed and disposed after async SELECT query");
                }
            }
        }

        /// <summary>
        /// Asynchronously executes a non-SELECT query (INSERT, UPDATE, DELETE, etc.)
        /// </summary>
        /// <param name="query">The SQL non-SELECT query to execute</param>
        /// <param name="parameters">Optional parameters for the query</param>
        /// <returns>Task containing number of rows affected and any error message</returns>
        public static async Task<(int RowsAffected, string ErrorMessage)> ExecuteNonQueryAsync(string query, Dictionary<string, object> parameters = null)
        {
            string errorMessage = string.Empty;
            NpgsqlConnection conn = null;

            try
            {
                // Get a new connection
                conn = DatabaseConnectionManager.Instance.CreateNewConnection();
                await conn.OpenAsync();
                Debug.WriteLine("Connection opened asynchronously for non-SELECT query");

                // Create command and execute
                using (var cmd = new NpgsqlCommand(query, conn))
                {
                    // Set command timeout to 30 seconds
                    cmd.CommandTimeout = 30;

                    // Add parameters if provided
                    if (parameters != null)
                    {
                        foreach (var param in parameters)
                        {
                            cmd.Parameters.AddWithValue(param.Key, param.Value ?? DBNull.Value);
                        }
                    }

                    // Execute the command
                    int rowsAffected = await cmd.ExecuteNonQueryAsync();
                    Debug.WriteLine($"Async non-query executed successfully. Rows affected: {rowsAffected}");
                    return (rowsAffected, errorMessage);
                }
            }
            catch (Exception ex)
            {
                errorMessage = $"Error executing async non-SELECT query: {ex.Message}";
                if (ex.InnerException != null)
                {
                    errorMessage += Environment.NewLine + $"Inner error: {ex.InnerException.Message}";
                }
                Debug.WriteLine(errorMessage);
                return (-1, errorMessage);
            }
            finally
            {
                // Close and dispose the connection
                if (conn != null)
                {
                    if (conn.State == ConnectionState.Open)
                    {
                        await conn.CloseAsync();
                    }
                    await conn.DisposeAsync();
                    Debug.WriteLine("Connection closed and disposed after async non-SELECT query");
                }
            }
        }

        /// <summary>
        /// Executes a query with parameters and returns a DataReader
        /// </summary>
        /// <param name="query">The SQL query to execute</param>
        /// <param name="parameters">Parameters for the query</param>
        /// <param name="connection">The connection to use (will be kept open)</param>
        /// <param name="errorMessage">Output parameter that will contain any error message</param>
        /// <returns>NpgsqlDataReader for reading results, or null if an error occurred</returns>
        public static NpgsqlDataReader ExecuteReader(string query, Dictionary<string, object> parameters, ref NpgsqlConnection connection, out string errorMessage)
        {
            errorMessage = string.Empty;
            try
            {
                // Create a new connection if one wasn't provided
                if (connection == null)
                {
                    connection = DatabaseConnectionManager.Instance.CreateNewConnection();
                    connection.Open();
                    Debug.WriteLine("New connection opened for data reader");
                }
                else if (connection.State != ConnectionState.Open)
                {
                    connection.Open();
                    Debug.WriteLine("Existing connection opened for data reader");
                }

                // Create command
                var cmd = new NpgsqlCommand(query, connection);
                cmd.CommandTimeout = 30;

                // Add parameters
                if (parameters != null)
                {
                    foreach (var param in parameters)
                    {
                        cmd.Parameters.AddWithValue(param.Key, param.Value ?? DBNull.Value);
                    }
                }

                // Execute the reader - note that the connection will remain open
                NpgsqlDataReader reader = cmd.ExecuteReader();
                Debug.WriteLine("Data reader created successfully");
                return reader;
            }
            catch (Exception ex)
            {
                errorMessage = $"Error executing reader: {ex.Message}";
                if (ex.InnerException != null)
                {
                    errorMessage += Environment.NewLine + $"Inner error: {ex.InnerException.Message}";
                }
                Debug.WriteLine(errorMessage);

                // Close the connection if we created it and an error occurred
                if (connection != null && connection.State == ConnectionState.Open)
                {
                    connection.Close();
                    connection.Dispose();
                    connection = null;
                    Debug.WriteLine("Connection closed and disposed due to error");
                }

                return null;
            }
        }

        /// <summary>
        /// Asynchronously executes a query from a SQL file in the Procedures folder
        /// </summary>
        /// <param name="moduleName">The module name (subfolder in Modules/Procedures)</param>
        /// <param name="queryName">The query file name without extension</param>
        /// <param name="parameters">Optional parameters for the query</param>
        /// <returns>Task containing DataTable with query results and any error message</returns>
        public static async Task<(DataTable Result, string ErrorMessage)> ExecuteQueryFromFileAsync(string moduleName, string queryName, Dictionary<string, object> parameters = null)
        {
            string errorMessage = string.Empty;
            try
            {
                // Load the SQL query from file
                string query = SQLQueryLoader.LoadQuery(moduleName, queryName);
                Debug.WriteLine($"Loaded query from file for async execution: {moduleName}/{queryName}.sql");

                // Check if the query is a SELECT query
                if (IsSelectQuery(query))
                {
                    // Execute the SELECT query asynchronously
                    return await ExecuteSelectQueryAsync(query, parameters);
                }
                else
                {
                    // For non-SELECT queries, execute asynchronously and return an empty DataTable
                    var result = await ExecuteNonQueryAsync(query, parameters);
                    if (result.RowsAffected >= 0)
                    {
                        // Return an empty DataTable with a single column to indicate success
                        var dt = new DataTable();
                        dt.Columns.Add("RowsAffected", typeof(int));
                        dt.Rows.Add(result.RowsAffected);
                        return (dt, result.ErrorMessage);
                    }
                    else
                    {
                        return (null, result.ErrorMessage);
                    }
                }
            }
            catch (Exception ex)
            {
                errorMessage = $"Error executing query from file asynchronously: {ex.Message}";
                if (ex.InnerException != null)
                {
                    errorMessage += Environment.NewLine + $"Inner error: {ex.InnerException.Message}";
                }
                Debug.WriteLine(errorMessage);
                return (null, errorMessage);
            }
        }

        /// <summary>
        /// Asynchronously executes a named query from a SQL file in the Procedures folder
        /// </summary>
        /// <param name="moduleName">The module name (subfolder in Modules/Procedures)</param>
        /// <param name="queryFileName">The query file name without extension</param>
        /// <param name="queryName">The specific query name to extract</param>
        /// <param name="parameters">Optional parameters for the query</param>
        /// <returns>Task containing DataTable with query results and any error message</returns>
        public static async Task<(DataTable Result, string ErrorMessage)> ExecuteNamedQueryFromFileAsync(string moduleName, string queryFileName, string queryName, Dictionary<string, object> parameters = null)
        {
            string errorMessage = string.Empty;
            try
            {
                // Extract the named query from the file
                string query = SQLQueryLoader.ExtractNamedQuery(moduleName, queryFileName, queryName);
                Debug.WriteLine($"Extracted named query '{queryName}' from file for async execution: {moduleName}/{queryFileName}.sql");

                // Check if the query is a SELECT query
                if (IsSelectQuery(query))
                {
                    // Execute the SELECT query asynchronously
                    return await ExecuteSelectQueryAsync(query, parameters);
                }
                else
                {
                    // For non-SELECT queries, execute asynchronously and return an empty DataTable
                    var result = await ExecuteNonQueryAsync(query, parameters);
                    if (result.RowsAffected >= 0)
                    {
                        // Return an empty DataTable with a single column to indicate success
                        var dt = new DataTable();
                        dt.Columns.Add("RowsAffected", typeof(int));
                        dt.Rows.Add(result.RowsAffected);
                        return (dt, result.ErrorMessage);
                    }
                    else
                    {
                        return (null, result.ErrorMessage);
                    }
                }
            }
            catch (Exception ex)
            {
                errorMessage = $"Error executing named query from file asynchronously: {ex.Message}";
                if (ex.InnerException != null)
                {
                    errorMessage += Environment.NewLine + $"Inner error: {ex.InnerException.Message}";
                }
                Debug.WriteLine(errorMessage);
                return (null, errorMessage);
            }
        }

        /// <summary>
        /// Executes a SELECT query with parameters and returns the results
        /// </summary>
        /// <param name="query">The SQL query to execute</param>
        /// <param name="parameters">Parameters for the query</param>
        /// <param name="errorMessage">Error message if execution fails</param>
        /// <returns>DataTable containing the results, or null if an error occurred</returns>
        public static DataTable ExecuteSelectQueryWithParams(string query, Dictionary<string, object> parameters, out string errorMessage)
        {
            return ExecuteSelectQuery(query, out errorMessage, parameters);
        }
    }
}
