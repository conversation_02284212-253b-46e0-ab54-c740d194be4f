# ProManage Development Environment Setup

## Visual Studio Configuration

### Parallel Development Setup

Visual Studio has been configured to support parallel development of both the VB.NET and C# versions of the ProManage application:

1. **Solution Structure**:
   - ProManage-8.0.sln: Contains the original VB.NET project
   - ProManage.sln: Contains the new C# project

2. **Project Settings**:
   - Both projects target .NET Framework 4.8
   - Both projects use the same output directory structure
   - Both projects reference the same third-party libraries

3. **IDE Configuration**:
   - Code snippets for common conversion patterns
   - Custom tool windows for side-by-side code comparison
   - Syntax highlighting for both VB.NET and C#

## Source Control Setup

Both projects are managed under the same source control repository to ensure synchronized development:

1. **Repository Structure**:
   ```
   /ProManage/
   |-- /ProManage-8.0/     # Original VB.NET project
   |-- /ProManage/         # New C# project
   |-- /Shared/            # Shared resources and documentation
   ```

2. **Branching Strategy**:
   - `main`: Stable, production-ready code
   - `development`: Active development branch
   - `feature/*`: Feature-specific branches
   - `conversion/*`: Conversion-specific branches

3. **Commit Guidelines**:
   - Prefix commits with `[VB]` or `[C#]` to indicate which project is affected
   - Use descriptive commit messages that explain the conversion process
   - Reference task numbers from the conversion task list

## Build Environment Configuration

Both projects have been configured with consistent build environments:

1. **Build Configurations**:
   - Debug: For development and testing
   - Release: For production deployment

2. **Build Process**:
   - Automatic copying of SQL files to output directory
   - Post-build events to synchronize shared resources
   - Conditional compilation symbols for platform-specific code

3. **Output Structure**:
   - Separate output directories for each project
   - Consistent naming conventions for assemblies
   - Shared configuration files

## Testing Framework

A testing framework has been set up to ensure functional equivalence between the VB.NET and C# versions:

1. **Unit Testing**:
   - MSTest for both VB.NET and C# projects
   - Shared test data for consistent testing
   - Parallel test execution for efficiency

2. **Integration Testing**:
   - End-to-end tests for critical workflows
   - Database integration tests with transaction rollback
   - UI automation tests for form functionality

3. **Comparison Testing**:
   - Custom tools to compare outputs between VB.NET and C# versions
   - Automated verification of database operations
   - Performance benchmarking for both versions

## Development Workflow

The development workflow has been established to ensure systematic conversion:

1. **Analysis Phase**:
   - Review VB.NET code for conversion challenges
   - Document patterns and idioms that need special handling
   - Create mapping between VB.NET and C# components

2. **Conversion Phase**:
   - Convert code following the priority list in Tasks.md
   - Implement unit tests for each converted component
   - Document conversion decisions and challenges

3. **Validation Phase**:
   - Run comparison tests to verify functional equivalence
   - Perform manual testing of UI components
   - Address any discrepancies between versions

4. **Optimization Phase**:
   - Refactor C# code to follow best practices
   - Implement C#-specific improvements
   - Optimize performance where possible
