using ProManage.Modules.Models.LoginForm;

namespace ProManage.Modules.Data.LoginForm
{
    /// <summary>
    /// Singleton class to manage the current user throughout the application
    /// </summary>
    public sealed class UserManager
    {
        // Singleton instance
        private static UserManager _instance;
        private static readonly object _lockObject = new object();

        // Current user
        private LoginFormUserModel _currentUser = null;

        /// <summary>
        /// Private constructor to prevent direct instantiation
        /// </summary>
        private UserManager()
        {
        }

        /// <summary>
        /// Gets the singleton instance
        /// </summary>
        public static UserManager Instance
        {
            get
            {
                if (_instance == null)
                {
                    lock (_lockObject)
                    {
                        if (_instance == null)
                        {
                            _instance = new UserManager();
                        }
                    }
                }
                return _instance;
            }
        }

        /// <summary>
        /// Gets the current user
        /// </summary>
        public LoginFormUserModel CurrentUser
        {
            get
            {
                return _currentUser;
            }
        }

        /// <summary>
        /// Sets the current user
        /// </summary>
        public void SetCurrentUser(LoginFormUserModel user)
        {
            _currentUser = user;
        }

        /// <summary>
        /// Clears the current user
        /// </summary>
        public void ClearCurrentUser()
        {
            _currentUser = null;
        }

        /// <summary>
        /// Gets whether a user is currently logged in
        /// </summary>
        public bool IsUserLoggedIn
        {
            get
            {
                return _currentUser != null;
            }
        }

        /// <summary>
        /// Gets the current username or "Guest" if no user is logged in
        /// </summary>
        public string CurrentUsername
        {
            get
            {
                if (_currentUser != null && !string.IsNullOrEmpty(_currentUser.Username))
                {
                    return _currentUser.Username;
                }
                else
                {
                    return "Guest";
                }
            }
        }

        /// <summary>
        /// Gets the current user's full name or username if full name is not available
        /// </summary>
        public string CurrentUserDisplayName
        {
            get
            {
                if (_currentUser != null)
                {
                    if (!string.IsNullOrEmpty(_currentUser.FullName))
                    {
                        return _currentUser.FullName;
                    }
                    else if (!string.IsNullOrEmpty(_currentUser.Username))
                    {
                        return _currentUser.Username;
                    }
                }
                return "Guest";
            }
        }
    }
}
