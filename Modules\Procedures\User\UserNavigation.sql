-- UserNavigation.sql
-- Clean, efficient navigation queries for users
-- Supports all four navigation operations: First, Previous, Next, Last
-- Plus count and position queries for navigation display

-- [GetFirstUser] --
SELECT
    user_id,
    username,
    password_hash,
    password_salt,
    full_name,
    email,
    role,
    is_active,
    last_login_date,
    created_date,
    department,
    phone,
    designation,
    short_name,
    photo_path,
    edit_password
FROM
    users
WHERE
    user_id = (SELECT MIN(user_id) FROM users WHERE user_id > 0)
ORDER BY user_id;
-- [End] --

-- [GetLastUser] --
SELECT
    user_id,
    username,
    password_hash,
    password_salt,
    full_name,
    email,
    role,
    is_active,
    last_login_date,
    created_date,
    department,
    phone,
    designation,
    short_name,
    photo_path,
    edit_password
FROM
    users
WHERE
    user_id = (SELECT MAX(user_id) FROM users WHERE user_id > 0)
ORDER BY user_id;
-- [End] --

-- [GetPreviousUser] --
SELECT
    user_id,
    username,
    password_hash,
    password_salt,
    full_name,
    email,
    role,
    is_active,
    last_login_date,
    created_date,
    department,
    phone,
    designation,
    short_name,
    photo_path,
    edit_password
FROM
    users
WHERE
    user_id < @current_id
ORDER BY user_id DESC
LIMIT 1;
-- [End] --

-- [GetNextUser] --
SELECT
    user_id,
    username,
    password_hash,
    password_salt,
    full_name,
    email,
    role,
    is_active,
    last_login_date,
    created_date,
    department,
    phone,
    designation,
    short_name,
    photo_path,
    edit_password
FROM
    users
WHERE
    user_id > @current_id
ORDER BY user_id ASC
LIMIT 1;
-- [End] --

-- [GetUserCount] --
SELECT COUNT(*) as total_count
FROM users
WHERE user_id > 0;
-- [End] --

-- [GetUserPosition] --
SELECT COUNT(*) as position
FROM users
WHERE user_id <= @user_id
AND user_id > 0;
-- [End] --
