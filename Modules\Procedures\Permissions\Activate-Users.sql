-- Activate-Users.sql
-- Activate inactive users for testing the permission system

-- ============================================================================
-- ACTIVATE USERS
-- ============================================================================

-- [ActivateUsers] --
-- Activate the faraz user for testing
UPDATE users 
SET is_active = true 
WHERE username = 'faraz';

-- Verify the update
SELECT 'UPDATED_USERS' as action, user_id, username, full_name, role_id, is_active 
FROM users 
ORDER BY user_id;
-- [End] --

-- ============================================================================
-- VERIFICATION
-- ============================================================================

-- [VerifyActiveUsers] --
-- Test the GetAllUsers query that the application uses
SELECT u.user_id, u.username, u.full_name, u.role_id, r.role_name, u.is_active
FROM users u
LEFT JOIN roles r ON u.role_id = r.role_id
WHERE u.is_active = true
ORDER BY u.username;
-- [End] --

/*
MANUAL EXECUTION INSTRUCTIONS:

1. Open pgAdmin or your PostgreSQL client
2. Connect to your ProManage database  
3. Execute the UPDATE statement above
4. Verify results with the verification query

EXPECTED RESULTS:
- Both admin and faraz users should be active
- Both should appear in the User Permissions dropdown
- Both should appear in the Global Permissions dropdown
*/
