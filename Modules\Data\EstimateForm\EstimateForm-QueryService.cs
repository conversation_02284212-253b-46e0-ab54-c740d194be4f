using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using Npgsql;
using ProManage.Modules.Helpers;
using ProManage.Modules.Models.EstimateForm;
using ProManage.Modules.Connections;

namespace ProManage.Modules.Data.EstimateForm
{
    /// <summary>
    /// Service class for executing SQL queries related to estimates.
    /// This class loads SQL from the Procedures/Estimate folder and executes them.
    /// </summary>
    public class EstimateFormQueryService
    {
        // Module name constant for SQL query loading
        private const string MODULE_NAME = "Estimate";

        /// <summary>
        /// Gets an estimate by its ID
        /// </summary>
        /// <param name="id">The ID of the estimate to retrieve</param>
        /// <returns>EstimateFormHeaderModel object with details or null if not found</returns>
        public static EstimateFormHeaderModel GetEstimateById(int id)
        {
            Debug.WriteLine($"Getting estimate by ID: {id}");
            EstimateFormHeaderModel estimate = null;

            try
            {
                // Load the SQL query from file for reference (not used directly anymore)
                string sqlQuery = SQLQueryLoader.LoadQuery(MODULE_NAME, SQLQueries.Estimate.GET_ESTIMATE_BY_ID);
                Debug.WriteLine("SQL query loaded successfully, but using hardcoded queries instead of splitting to avoid syntax errors");

                // Define the queries directly to avoid splitting issues
                string headerQuery = "SELECT * FROM estimateheader WHERE id = @id;";
                string detailsQuery = "SELECT * FROM estimatedetails WHERE estimate_id = @estimateId ORDER BY id;";

                // Create a new connection
                using (var conn = DatabaseConnectionManager.Instance.CreateNewConnection())
                {
                    conn.Open();

                    // Get the header information
                    using (var cmd = new NpgsqlCommand(headerQuery, conn))
                    {
                        cmd.Parameters.AddWithValue("@id", id);

                        using (var reader = cmd.ExecuteReader())
                        {
                            if (reader.Read())
                            {
                                estimate = MapEstimateFromReader(reader);
                            }
                        }
                    }

                    // If we found the header, get the details
                    if (estimate != null)
                    {
                        // Get the details
                        using (var cmd = new NpgsqlCommand(detailsQuery, conn))
                        {
                            cmd.Parameters.AddWithValue("@estimateId", id);

                            using (var reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    var detail = MapEstimateDetailFromReader(reader);
                                    estimate.Details.Add(detail);
                                }
                            }
                        }
                    }
                }

                return estimate;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error retrieving estimate by ID: {ex.Message}");
                if (ex.InnerException != null)
                {
                    Debug.WriteLine($"Inner error: {ex.InnerException.Message}");
                }
                return null;
            }
        }

        /// <summary>
        /// Gets all estimates from the database
        /// </summary>
        /// <returns>List of all EstimateFormHeaderModel objects</returns>
        public static List<EstimateFormHeaderModel> GetAllEstimates()
        {
            Debug.WriteLine("Getting all estimates");
            var estimates = new List<EstimateFormHeaderModel>();

            try
            {
                // Load the SQL query from file for reference (not used directly anymore)
                string sqlQuery = SQLQueryLoader.LoadQuery(MODULE_NAME, SQLQueries.Estimate.GET_ALL_ESTIMATES);
                Debug.WriteLine("SQL query loaded successfully, but using hardcoded queries instead of splitting to avoid syntax errors");

                // Define the queries directly to avoid splitting issues
                string headerQuery = "SELECT * FROM estimateheader ORDER BY id;";
                string detailsQuery = "SELECT * FROM estimatedetails WHERE estimate_id = @estimateId ORDER BY id;";

                // Create a new connection
                using (var conn = DatabaseConnectionManager.Instance.CreateNewConnection())
                {
                    conn.Open();

                    // Get all headers
                    using (var cmd = new NpgsqlCommand(headerQuery, conn))
                    {
                        using (var reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                var estimate = MapEstimateFromReader(reader);
                                estimates.Add(estimate);
                            }
                        }
                    }

                    // Get details for each estimate
                    foreach (var estimate in estimates)
                    {
                        // Get the details
                        using (var cmd = new NpgsqlCommand(detailsQuery, conn))
                        {
                            cmd.Parameters.AddWithValue("@estimateId", estimate.Id);

                            using (var reader = cmd.ExecuteReader())
                            {
                                while (reader.Read())
                                {
                                    var detail = MapEstimateDetailFromReader(reader);
                                    estimate.Details.Add(detail);
                                }
                            }
                        }
                    }
                }

                return estimates;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error retrieving all estimates: {ex.Message}");
                if (ex.InnerException != null)
                {
                    Debug.WriteLine($"Inner error: {ex.InnerException.Message}");
                }
                return new List<EstimateFormHeaderModel>();
            }
        }

        /// <summary>
        /// Gets the next estimate number using the SQL query from the Procedures folder
        /// </summary>
        /// <returns>The next estimate number</returns>
        public static string GetNextEstimateNumber()
        {
            Debug.WriteLine("Getting next estimate number");

            try
            {
                // Load SQL query from file
                string query = SQLQueryLoader.LoadQuery(MODULE_NAME, SQLQueries.Estimate.GET_NEXT_ESTIMATE_NUMBER);

                // Create a new connection
                using (var conn = DatabaseConnectionManager.Instance.CreateNewConnection())
                {
                    conn.Open();

                    using (var cmd = new NpgsqlCommand(query, conn))
                    {
                        var result = cmd.ExecuteScalar();

                        // If we have a result and it's not DBNull, return it
                        if (result != null && !Convert.IsDBNull(result))
                        {
                            return result.ToString();
                        }

                        // Generate a number in the format EST-YY-NNNNN
                        int lastId = GetLastEstimateId(conn);
                        int nextId = lastId + 1;
                        string currentYear = DateTime.Now.Year.ToString().Substring(2);
                        return $"EST-{currentYear}-{nextId:00000}";
                    }
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error getting next estimate number: {ex.Message}");
                if (ex.InnerException != null)
                {
                    Debug.WriteLine($"Inner error: {ex.InnerException.Message}");
                }

                // Fallback to the old method if an exception occurs
                return GenerateFallbackEstimateNumber();
            }
        }

        /// <summary>
        /// Gets the details for an estimate directly from the database
        /// </summary>
        /// <param name="estimateId">The ID of the estimate</param>
        /// <returns>List of EstimateFormDetailModel objects</returns>
        public static List<EstimateFormDetailModel> GetEstimateDetailsById(int estimateId)
        {
            Debug.WriteLine($"Getting estimate details by ID: {estimateId}");
            var details = new List<EstimateFormDetailModel>();

            try
            {
                // Query to get the estimate details
                string detailsQuery = "SELECT * FROM estimatedetails WHERE estimate_id = @estimateId ORDER BY id";

                // Create a new connection
                using (var conn = DatabaseConnectionManager.Instance.CreateNewConnection())
                {
                    conn.Open();

                    // Get the details
                    using (var cmd = new NpgsqlCommand(detailsQuery, conn))
                    {
                        cmd.Parameters.AddWithValue("@estimateId", estimateId);

                        using (var reader = cmd.ExecuteReader())
                        {
                            while (reader.Read())
                            {
                                var detail = MapEstimateDetailFromReader(reader);
                                details.Add(detail);
                                Debug.WriteLine($"Loaded detail: ID={detail.Id}, PartNo={detail.PartNo}, Description={detail.Description}, Qty={detail.Qty}");
                            }
                        }
                    }
                }

                return details;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error getting estimate details: {ex.Message}");
                if (ex.InnerException != null)
                {
                    Debug.WriteLine($"Inner error: {ex.InnerException.Message}");
                }
                return new List<EstimateFormDetailModel>();
            }
        }

        /// <summary>
        /// Maps a data reader to an EstimateFormHeaderModel object
        /// </summary>
        /// <param name="reader">The data reader positioned at the record to map</param>
        /// <returns>Populated EstimateFormHeaderModel object</returns>
        private static EstimateFormHeaderModel MapEstimateFromReader(NpgsqlDataReader reader)
        {
            try
            {
                Debug.WriteLine("Mapping estimate from database reader");

                // Get the ID first to help with debugging
                int id = Convert.ToInt32(reader["id"]);
                Debug.WriteLine($"Mapping estimate ID: {id}");

                // Create a new estimate object
                var estimate = new EstimateFormHeaderModel
                {
                    Id = id,
                    EstimateNo = reader["estimate_no"] != DBNull.Value ? reader["estimate_no"].ToString() : null,
                    CustomerName = reader["customer_name"] != DBNull.Value ? reader["customer_name"].ToString() : null,
                    VIN = reader["vin"] != DBNull.Value ? reader["vin"].ToString() : null,
                    Brand = reader["brand"] != DBNull.Value ? reader["brand"].ToString() : null,
                    DocDate = reader["date"] != DBNull.Value ? Convert.ToDateTime(reader["date"]) : (DateTime?)null,
                    Location = reader["location"] != DBNull.Value ? reader["location"].ToString() : null,
                    VehicleModel = reader["vehicle_model"] != DBNull.Value ? reader["vehicle_model"].ToString() : null,
                    SalesmanName = reader["salesman_name"] != DBNull.Value ? reader["salesman_name"].ToString() : null,
                    Status = reader["status"] != DBNull.Value && Convert.ToBoolean(reader["status"]),
                    CreatedAt = reader["created_at"] != DBNull.Value ? Convert.ToDateTime(reader["created_at"]) : (DateTime?)null,
                    CreatedBy = reader["created_by"] != DBNull.Value ? reader["created_by"].ToString() : null
                };

                // Log the mapped estimate details
                Debug.WriteLine($"Successfully mapped estimate: ID={estimate.Id}, Number={estimate.EstimateNo}, Customer={estimate.CustomerName}");

                return estimate;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"ERROR mapping estimate from reader: {ex.Message}");
                if (ex.InnerException != null)
                {
                    Debug.WriteLine($"Inner exception: {ex.InnerException.Message}");
                }

                // Rethrow the exception to be handled by the caller
                throw new Exception($"Failed to map estimate from database: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Maps a data reader to an EstimateFormDetailModel object
        /// </summary>
        /// <param name="reader">The data reader positioned at the record to map</param>
        /// <returns>Populated EstimateFormDetailModel object</returns>
        private static EstimateFormDetailModel MapEstimateDetailFromReader(NpgsqlDataReader reader)
        {
            try
            {
                // Create a new detail object
                var detail = new EstimateFormDetailModel
                {
                    Id = Convert.ToInt32(reader["id"]),
                    EstimateId = Convert.ToInt32(reader["estimate_id"]),
                    PartNo = reader["part_no"] != DBNull.Value ? reader["part_no"].ToString() : null,
                    Description = reader["description"] != DBNull.Value ? reader["description"].ToString() : null,
                    Qty = reader["qty"] != DBNull.Value ? Convert.ToInt32(reader["qty"]) : (int?)null,
                    OEPrice = reader["oe_price"] != DBNull.Value ? Convert.ToDecimal(reader["oe_price"]) : (decimal?)null,
                    AFMPrice = reader["afm_price"] != DBNull.Value ? Convert.ToDecimal(reader["afm_price"]) : (decimal?)null,
                    Remarks = reader["remarks"] != DBNull.Value ? reader["remarks"].ToString() : null
                };

                return detail;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"ERROR mapping detail from reader: {ex.Message}");
                if (ex.InnerException != null)
                {
                    Debug.WriteLine($"Inner exception: {ex.InnerException.Message}");
                }

                // Rethrow the exception to be handled by the caller
                throw new Exception($"Failed to map detail from database: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Gets the last estimate ID from the database
        /// </summary>
        /// <param name="conn">An open database connection</param>
        /// <returns>The last estimate ID or 0 if no estimates exist</returns>
        private static int GetLastEstimateId(NpgsqlConnection conn)
        {
            int lastId = 0;
            bool closeConnection = false;

            try
            {
                // Use the provided connection or create a new one
                if (conn == null)
                {
                    conn = DatabaseConnectionManager.Instance.CreateNewConnection();
                    conn.Open();
                    closeConnection = true;
                }

                // Get the last ID
                using (var cmd = new NpgsqlCommand("SELECT MAX(id) FROM estimateheader", conn))
                {
                    var result = cmd.ExecuteScalar();

                    // If we have a result and it's not DBNull, convert it to Integer
                    if (result != null && !Convert.IsDBNull(result))
                    {
                        lastId = Convert.ToInt32(result);
                    }
                }

                return lastId;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error getting last estimate ID: {ex.Message}");
                return 0;
            }
            finally
            {
                // Close the connection if we created it
                if (closeConnection && conn != null)
                {
                    conn.Close();
                }
            }
        }

        /// <summary>
        /// Generates a fallback estimate number when the database query fails
        /// </summary>
        /// <returns>A generated estimate number</returns>
        private static string GenerateFallbackEstimateNumber()
        {
            try
            {
                // Create a new connection
                using (var conn = DatabaseConnectionManager.Instance.CreateNewConnection())
                {
                    conn.Open();

                    // Get the last ID
                    int lastId = GetLastEstimateId(conn);
                    int nextId = lastId + 1;
                    string currentYear = DateTime.Now.Year.ToString().Substring(2);
                    return $"EST-{currentYear}-{nextId:00000}";
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error generating fallback estimate number: {ex.Message}");

                // Last resort fallback
                string currentYear = DateTime.Now.Year.ToString().Substring(2);
                string randomPart = DateTime.Now.Ticks.ToString().Substring(0, 5);
                return $"EST-{currentYear}-{randomPart}";
            }
        }

        /// <summary>
        /// Deletes an estimate by ID
        /// </summary>
        /// <param name="estimateId">The ID of the estimate to delete</param>
        /// <returns>True if successful, False otherwise</returns>
        public static bool DeleteEstimate(int estimateId)
        {
            Debug.WriteLine($"Deleting estimate with ID: {estimateId}");
            NpgsqlConnection conn = null;
            NpgsqlTransaction transaction = null;

            try
            {
                // Define the SQL queries
                string detailDeleteSql = "DELETE FROM estimatedetails WHERE estimate_id = @estimateId;";
                string headerDeleteSql = "DELETE FROM estimateheader WHERE id = @id;";

                // Create a new connection
                conn = DatabaseConnectionManager.Instance.CreateNewConnection();
                conn.Open();

                // Begin transaction
                transaction = conn.BeginTransaction();

                // Delete details first (foreign key constraint)
                using (var cmd = new NpgsqlCommand(detailDeleteSql, conn, transaction))
                {
                    cmd.Parameters.AddWithValue("@estimateId", estimateId);
                    cmd.ExecuteNonQuery();
                }

                // Delete header
                using (var cmd = new NpgsqlCommand(headerDeleteSql, conn, transaction))
                {
                    cmd.Parameters.AddWithValue("@id", estimateId);
                    cmd.ExecuteNonQuery();
                }

                // Commit transaction
                transaction.Commit();
                Debug.WriteLine($"Estimate with ID {estimateId} deleted successfully");
                return true;
            }
            catch (Exception ex)
            {
                // Rollback transaction on error
                if (transaction != null)
                {
                    try
                    {
                        transaction.Rollback();
                    }
                    catch (Exception rollbackEx)
                    {
                        Debug.WriteLine($"Error rolling back transaction: {rollbackEx.Message}");
                    }
                }

                Debug.WriteLine($"Error deleting estimate: {ex.Message}");
                if (ex.InnerException != null)
                {
                    Debug.WriteLine($"Inner error: {ex.InnerException.Message}");
                }
                return false;
            }
            finally
            {
                // Close connection
                if (conn != null && conn.State == ConnectionState.Open)
                {
                    conn.Close();
                }
            }
        }

        /// <summary>
        /// Saves an estimate (insert or update)
        /// </summary>
        /// <param name="estimate">The estimate to save</param>
        /// <returns>True if successful, False otherwise</returns>
        public static bool SaveEstimate(EstimateFormHeaderModel estimate)
        {
            Debug.WriteLine($"Saving estimate: {estimate.EstimateNo}, Customer: {estimate.CustomerName}");
            Debug.WriteLine($"Details count: {(estimate.Details == null ? 0 : estimate.Details.Count)}");

            bool isUpdate = estimate.Id > 0;
            NpgsqlConnection conn = null;
            NpgsqlTransaction transaction = null;

            try
            {
                // Load the SQL query from file for reference (not used directly anymore)
                string sqlQuery = SQLQueryLoader.LoadQuery(MODULE_NAME, SQLQueries.Estimate.ESTIMATE_CRUD);
                Debug.WriteLine("SQL query loaded successfully, but using hardcoded queries instead of splitting to avoid syntax errors");

                // Define the SQL queries directly to avoid splitting issues
                string headerUpdateSql = "UPDATE estimateheader SET estimate_no = @estimateNo, customer_name = @customerName, vin = @vin, brand = @brand, date = @date, location = @location, vehicle_model = @vehicleModel, salesman_name = @salesmanName, status = @status WHERE id = @id;";

                string detailDeleteSql = "DELETE FROM estimatedetails WHERE estimate_id = @estimateId;";

                string detailInsertSql = "INSERT INTO estimatedetails (id, estimate_id, part_no, description, qty, oe_price, afm_price, remarks, approve_status) VALUES (@id, @estimateId, @partNo, @description, @qty, @oePrice, @afmPrice, @remarks, @approveStatus);";

                string headerInsertWithIdSql = "INSERT INTO estimateheader (id, estimate_no, customer_name, vin, brand, date, location, vehicle_model, salesman_name, status) VALUES (@id, @estimateNo, @customerName, @vin, @brand, @date, @location, @vehicleModel, @salesmanName, @status);";

                string getNextDetailIdSql = "SELECT COALESCE(MAX(id), 0) + 1 FROM estimatedetails;";

                // Create a new connection
                conn = DatabaseConnectionManager.Instance.CreateNewConnection();
                conn.Open();

                // Begin transaction
                transaction = conn.BeginTransaction();

                // Update or insert header
                if (isUpdate)
                {
                    // Update existing header
                    using (var cmd = new NpgsqlCommand(headerUpdateSql, conn, transaction))
                    {
                        cmd.Parameters.AddWithValue("@id", estimate.Id);
                        cmd.Parameters.AddWithValue("@estimateNo", estimate.EstimateNo);
                        cmd.Parameters.AddWithValue("@customerName", estimate.CustomerName ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@vin", estimate.VIN ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@brand", estimate.Brand ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@date", estimate.DocDate.HasValue ? (object)estimate.DocDate : DBNull.Value);
                        cmd.Parameters.AddWithValue("@location", estimate.Location ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@vehicleModel", estimate.VehicleModel ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@salesmanName", estimate.SalesmanName ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@status", estimate.Status);

                        cmd.ExecuteNonQuery();
                    }

                    // Delete existing details
                    using (var cmd = new NpgsqlCommand(detailDeleteSql, conn, transaction))
                    {
                        cmd.Parameters.AddWithValue("@estimateId", estimate.Id);
                        cmd.ExecuteNonQuery();
                    }
                }
                else
                {
                    // Get the next ID using our workaround method
                    int nextId;

                    // Use the workaround method directly to avoid sequence permission errors
                    Debug.WriteLine("Using MAX(id)+1 method to generate next ID and avoid sequence permission errors");
                    using (var cmd = new NpgsqlCommand("SELECT COALESCE(MAX(id), 0) + 1 FROM estimateheader", conn, transaction))
                    {
                        nextId = Convert.ToInt32(cmd.ExecuteScalar());
                    }

                    // Insert new header with explicit ID
                    using (var cmd = new NpgsqlCommand(headerInsertWithIdSql, conn, transaction))
                    {
                        cmd.Parameters.AddWithValue("@id", nextId);
                        cmd.Parameters.AddWithValue("@estimateNo", estimate.EstimateNo);
                        cmd.Parameters.AddWithValue("@customerName", estimate.CustomerName ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@vin", estimate.VIN ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@brand", estimate.Brand ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@date", estimate.DocDate.HasValue ? (object)estimate.DocDate : DBNull.Value);
                        cmd.Parameters.AddWithValue("@location", estimate.Location ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@vehicleModel", estimate.VehicleModel ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@salesmanName", estimate.SalesmanName ?? (object)DBNull.Value);
                        cmd.Parameters.AddWithValue("@status", estimate.Status);

                        cmd.ExecuteNonQuery();
                        estimate.Id = nextId;
                    }
                }

                // Insert details
                if (estimate.Details != null && estimate.Details.Count > 0)
                {
                    // Get the next ID for estimatedetails using the workaround method
                    int nextDetailId;
                    Debug.WriteLine("Using MAX(id)+1 method to generate next detail ID and avoid sequence permission errors");
                    using (var cmd = new NpgsqlCommand(getNextDetailIdSql, conn, transaction))
                    {
                        nextDetailId = Convert.ToInt32(cmd.ExecuteScalar());
                    }

                    // Insert each detail
                    foreach (var detail in estimate.Details)
                    {
                        // Set the estimate ID and detail ID
                        detail.EstimateId = estimate.Id;

                        // If the detail has no ID or ID is 0, assign a new ID
                        if (detail.Id <= 0)
                        {
                            detail.Id = nextDetailId++;
                        }

                        // Insert the detail
                        using (var cmd = new NpgsqlCommand(detailInsertSql, conn, transaction))
                        {
                            cmd.Parameters.AddWithValue("@id", detail.Id);
                            cmd.Parameters.AddWithValue("@estimateId", detail.EstimateId);
                            cmd.Parameters.AddWithValue("@partNo", detail.PartNo ?? (object)DBNull.Value);
                            cmd.Parameters.AddWithValue("@description", detail.Description ?? (object)DBNull.Value);
                            cmd.Parameters.AddWithValue("@qty", detail.Qty);
                            cmd.Parameters.AddWithValue("@oePrice", detail.OEPrice);
                            cmd.Parameters.AddWithValue("@afmPrice", detail.AFMPrice);
                            cmd.Parameters.AddWithValue("@remarks", detail.Remarks ?? (object)DBNull.Value);
                            cmd.Parameters.AddWithValue("@approveStatus", detail.ApproveStatus ?? false);

                            cmd.ExecuteNonQuery();
                        }
                    }
                }

                // Commit the transaction
                transaction.Commit();
                Debug.WriteLine("Transaction committed successfully");

                return true;
            }
            catch (Exception ex)
            {
                // Rollback the transaction if it exists
                if (transaction != null)
                {
                    try
                    {
                        transaction.Rollback();
                        Debug.WriteLine("Transaction rolled back due to error");
                    }
                    catch (Exception rollbackEx)
                    {
                        Debug.WriteLine($"Error rolling back transaction: {rollbackEx.Message}");
                    }
                }

                Debug.WriteLine($"Error saving estimate: {ex.Message}");
                if (ex.InnerException != null)
                {
                    Debug.WriteLine($"Inner error: {ex.InnerException.Message}");
                }
                return false;
            }
            finally
            {
                // Close and dispose the connection
                if (conn != null)
                {
                    if (conn.State == ConnectionState.Open)
                    {
                        conn.Close();
                    }
                    conn.Dispose();
                }
            }
        }
    }
}
