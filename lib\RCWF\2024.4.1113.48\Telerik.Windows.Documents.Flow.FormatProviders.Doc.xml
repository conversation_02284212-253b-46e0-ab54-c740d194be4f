<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Telerik.Windows.Documents.Flow.FormatProviders.Doc</name>
    </assembly>
    <members>
        <member name="F:DIaLOGIKa.b2xtranslator.OfficeDrawing.BitmapBlip.m_pvBits">
            <summary>
            Raster bits of the blip
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OfficeDrawing.BlipStoreEntry.btWin32">
            <summary>
            Required type on Win32 
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OfficeDrawing.ChildAnchor.rcgBounds">
            <summary>
            Rectangle that describe sthe bounds of the anchor
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OfficeDrawing.DrawingRecord.csp">
            <summary>
            The number of shapes in this drawing
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OfficeDrawing.DrawingRecord.spidCur">
            <summary>
            The last MSOSPID given to an SP in this DG
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OfficeDrawing.MetafilePictBlip.m_cb">
            <summary>
            Cache of the metafile size
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OfficeDrawing.MetafilePictBlip.m_cbSave">
            <summary>
            Cache of saved size (size of m_pvBits)
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OfficeDrawing.MetafilePictBlip.m_fCompression">
            <summary>
            Compression
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OfficeDrawing.MetafilePictBlip.m_pvBits">
            <summary>
            Compressed bits of metafile.
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.OfficeDrawing.MetafilePictBlip.Decrompress">
            <summary>
            Decompresses the bits of the picture if the picture is decompressed.<br/>
            If the picture is not compressed, it returns original byte array.
            </summary>
        </member>
        <member name="T:DIaLOGIKa.b2xtranslator.OfficeDrawing.OfficeRecordAttribute">
            <summary>
            Used for mapping Office record TypeCodes to the classes implementing them.
            </summary>
        </member>
        <member name="T:DIaLOGIKa.b2xtranslator.OfficeDrawing.OfficeShapeTypeAttribute">
            <summary>
            Used for mapping Office shape types to the classes implementing them.
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OfficeDrawing.Record.SiblingIdx">
            <summary>
            Index of sibling, 0 for first child in container, 1 for second child and so on...
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.OfficeDrawing.Record.FirstAncestorWithType``1">
            <summary>
            Finds the first ancestor of the given type.
            </summary>
            <typeparam name="T">Type of ancestor to search for</typeparam>
            <returns>First ancestor with appropriate type or null if none was found</returns>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.OfficeDrawing.Record.UpdateTypeToRecordClassMapping(System.Reflection.Assembly,System.String)">
            <summary>
            Updates the Dictionary used for mapping Office record TypeCodes to Office record classes.
            This is done by querying all classes in the specified assembly filtered by the specified
            namespace and looking for attributes of type OfficeRecordAttribute.
            </summary>
            
            <param name="assembly">Assembly to scan</param>
            <param name="ns">Namespace to scan or null for all namespaces</param>
        </member>
        <member name="T:DIaLOGIKa.b2xtranslator.OfficeDrawing.RegularContainer">
            <summary>
            Regular containers are containers with Record children.<br/>
            (There also is containers that only have a zipped XML payload.
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.OfficeDrawing.RegularContainer.AllChildrenWithType``1">
            <summary>
            Finds all children of the given type.
            </summary>
            <typeparam name="T">Type of child to search for</typeparam>
            <returns>List of children with appropriate type or null if none were found</returns>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.OfficeDrawing.RegularContainer.FirstChildWithType``1">
            <summary>
            Finds the first child of the given type.
            </summary>
            <typeparam name="T">Type of child to search for</typeparam>
            <returns>First child with appropriate type or null if none was found</returns>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OfficeDrawing.Shape.fGroup">
            <summary>
            This shape is a group shape 
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OfficeDrawing.Shape.fChild">
            <summary>
            Not a top-level shape 
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OfficeDrawing.Shape.fPatriarch">
            <summary>
            This is the topmost group shape.<br/>
            Exactly one of these per drawing. 
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OfficeDrawing.Shape.fFlipH">
            <summary>
            Shape is flipped horizontally 
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OfficeDrawing.Shape.fFlipV">
            <summary>
            Shape is flipped vertically 
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OfficeDrawing.Shape.ShapeType">
            <summary>
            The shape type of the shape
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.OfficeDrawing.ShapeContainer.ExtractOptions">
            <summary>
            Searches all OptionEntry in the ShapeContainer and puts them into a list.
            </summary>
            <returns>A List containing all OptionEntry of the ShapeContainer</returns>
        </member>
        <member name="T:DIaLOGIKa.b2xtranslator.OfficeDrawing.Shapetypes.RoundedRectangleType">
            <summary>
            interim solution<br/>
            OOX uses an additional attribute: arcsize
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OfficeDrawing.Shapetypes.ShapeType.Path">
            <summary>
            This string describes a sequence of commands that define the shape’s path.<br/>
            This string describes both the pSegmentInfo array and pVertices array in the shape’s geometry properties.
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OfficeDrawing.Shapetypes.ShapeType.Formulas">
            <summary>
            This specifies a list of formulas whose calculated values are referenced by other properties. <br/>
            Each formula is listed on a separate line. Formulas are ordered, with the first formula having index 0. <br/>
            This section can be omitted if the shape doesn’t need any guides.
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OfficeDrawing.Shapetypes.ShapeType.AdjustmentValues">
            <summary>
            Specifies a comma-delimited list of parameters, or adjustment values, 
            used to define values for a parameterized formula. <br/>
            These values represent the location of an adjust handle and may be 
            referenced by the geometry of an adjust handle or as a parameter guide function.
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OfficeDrawing.Shapetypes.ShapeType.ConnectorLocations">
            <summary>
            These values specify the location of connection points on the shape’s path. <br/>
            The connection points are defined by a string consisting of pairs of x and y values, delimited by commas.
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OfficeDrawing.Shapetypes.ShapeType.Handles">
            <summary>
            This section specifies the properties of each adjust handle on the shape. <br/>
            One adjust handle is specified per line. <br/>
            The properties for each handle correspond to values of the ADJH structure 
            contained in the pAdjustHandles array in the shape’s geometry properties.
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OfficeDrawing.Shapetypes.ShapeType.TextboxRectangle">
            <summary>
            Specifies one or more text boxes inscribed inside the shape. <br/>
            A textbox is defined by one or more sets of numbers specifying (in order) the left, top, right, and bottom points of the rectangle. <br/>
            Multiple sets are delimited by a semicolon. <br/>
            If omitted, the text box is the same as the geometry’s bounding box.
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OfficeDrawing.Shapetypes.ShapeType.Joins">
            <summary>
            Specifies what join style the shape has. <br/>
            Since there is no UI for changing the join style, 
            all shapes of this type will always have the specified join style.
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OfficeDrawing.Shapetypes.ShapeType.Limo">
            <summary>
            Specifies the (x,y) coordinates of the limo stretch point.<br/>
            Some shapes that have portions that should be constrained to a fixed aspect ratio, are designed with limo-stretch to keep those portions at the fixed aspect ratio.<br/>
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OfficeDrawing.Shapetypes.ShapeType.ConnectorAngles">
            <summary>
            Associated with each connection site, there is a direction which specifies at what angle elbow and curved connectors should attach to it<br/>
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OfficeDrawing.Shapetypes.ShapeType.Filled">
            <summary>
            Specifies if a shape of this type is filled by default
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OfficeDrawing.Shapetypes.ShapeType.Stroked">
            <summary>
            Specifies if a shape of this type is stroked by default
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OfficeDrawing.Shapetypes.ShapeType.Lock">
            <summary>
            Speicfies the locked properties of teh shape.
            By default nothing is locked.
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.OfficeDrawing.Shapetypes.ShapeType.UpdateTypeToShapeClassMapping(System.Reflection.Assembly,System.String)">
            <summary>
            Updates the Dictionary used for mapping Office shape type codes to Office ShapeType classes.
            This is done by querying all classes in the specified assembly filtered by the specified
            namespace and looking for attributes of type OfficeShapeTypeAttribute.
            </summary>
            
            <param name="assembly">Assembly to scan</param>
            <param name="ns">Namespace to scan or null for all namespaces</param>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.AudioVideo.ElExtLst">
            <summary>
            
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.AudioVideo.ElSt">
            <summary>
            Audio Start Time
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.AudioVideo.ElEnd">
            <summary>
            Audio End Time
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.AudioVideo.ElAudioCd">
            <summary>
            Audio from CD
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.AudioVideo.ElWavAudioFile">
            <summary>
            Audio from WAV File
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.AudioVideo.ElAudioFile">
            <summary>
            Audio from File
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.AudioVideo.ElVideoFile">
            <summary>
            Video from File
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.AudioVideo.ElQuickTimeFile">
            <summary>
            QuickTime from File
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.AudioVideo.AttrTrack">
            <summary>
            Track
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.AudioVideo.AttrTime">
            <summary>
            Time
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramStyleDefinition.ElCat">
            <summary>
            Category
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramStyleDefinition.ElScene3d">
            <summary>
            3-D Scene
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramStyleDefinition.ElSp3d">
            <summary>
            3-D Shape Properties
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramStyleDefinition.ElTxPr">
            <summary>
            Text Properties
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramStyleDefinition.ElStyle">
            <summary>
            Shape Style
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramStyleDefinition.ElExtLst">
            <summary>
            
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramStyleDefinition.ElTitle">
            <summary>
            Title
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramStyleDefinition.ElDesc">
            <summary>
            Style Label Description
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramStyleDefinition.ElCatLst">
            <summary>
            Category List
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramStyleDefinition.ElStyleLbl">
            <summary>
            Style Label
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramStyleDefinition.ElStyleDef">
            <summary>
            Style Definition
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramStyleDefinition.ElStyleDefHdr">
            <summary>
            Style Definition Header
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramStyleDefinition.ElStyleDefHdrLst">
            <summary>
            List of Style Definition Headers
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramStyleDefinition.AttrLang">
            <summary>
            Natural Language
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramStyleDefinition.AttrVal">
            <summary>
            Description Value
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramStyleDefinition.AttrType">
            <summary>
            Category Type
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramStyleDefinition.AttrPri">
            <summary>
            Priority
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramStyleDefinition.AttrName">
            <summary>
            Style Name
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramStyleDefinition.AttrUniqueId">
            <summary>
            Unique Style ID
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramStyleDefinition.AttrMinVer">
            <summary>
            Minimum Version
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramStyleDefinition.AttrResId">
            <summary>
            Resource ID
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Shape3DScene.ElCamera">
            <summary>
            Camera
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Shape3DScene.ElLightRig">
            <summary>
            Light Rig
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Shape3DScene.ElBackdrop">
            <summary>
            Backdrop Plane
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Shape3DScene.ElExtLst">
            <summary>
            
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Stylesheet.ElExtLst">
            <summary>
            
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Stylesheet.ElMasterClrMapping">
            <summary>
            Master Color Mapping
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Stylesheet.ElOverrideClrMapping">
            <summary>
            Override Color Mapping
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Stylesheet.ElClrScheme">
            <summary>
            
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Stylesheet.ElClrMap">
            <summary>
            
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Stylesheet.ElExtraClrScheme">
            <summary>
            Extra Color Scheme
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Stylesheet.ElThemeElements">
            <summary>
            Theme Elements
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Stylesheet.ElObjectDefaults">
            <summary>
            Object Defaults
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Stylesheet.ElExtraClrSchemeLst">
            <summary>
            Extra Color Scheme List
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Stylesheet.ElCustClrLst">
            <summary>
            Custom Color List
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Stylesheet.ElFontScheme">
            <summary>
            
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Stylesheet.ElFmtScheme">
            <summary>
            
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Stylesheet.ElTheme">
            <summary>
            Theme
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Stylesheet.ElThemeOverride">
            <summary>
            Theme Override
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Stylesheet.ElThemeManager">
            <summary>
            Theme Manager
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Stylesheet.AttrBg1">
            <summary>
            Background 1
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Stylesheet.AttrTx1">
            <summary>
            Text 1
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Stylesheet.AttrBg2">
            <summary>
            Background 2
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Stylesheet.AttrTx2">
            <summary>
            Text 2
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Stylesheet.AttrAccent1">
            <summary>
            Accent 1
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Stylesheet.AttrAccent2">
            <summary>
            Accent 2
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Stylesheet.AttrAccent3">
            <summary>
            Accent 3
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Stylesheet.AttrAccent4">
            <summary>
            Accent 4
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Stylesheet.AttrAccent5">
            <summary>
            Accent 5
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Stylesheet.AttrAccent6">
            <summary>
            Accent 6
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Stylesheet.AttrHlink">
            <summary>
            Hyperlink
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Stylesheet.AttrFolHlink">
            <summary>
            Followed Hyperlink
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Stylesheet.AttrName">
            <summary>
            Name
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.BaseStylesheet.ElDk1">
            <summary>
            Dark 1
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.BaseStylesheet.ElLt1">
            <summary>
            Light 1
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.BaseStylesheet.ElDk2">
            <summary>
            Dark 2
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.BaseStylesheet.ElLt2">
            <summary>
            Light 2
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.BaseStylesheet.ElAccent1">
            <summary>
            Accent 1
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.BaseStylesheet.ElAccent2">
            <summary>
            Accent 2
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.BaseStylesheet.ElAccent3">
            <summary>
            Accent 3
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.BaseStylesheet.ElAccent4">
            <summary>
            Accent 4
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.BaseStylesheet.ElAccent5">
            <summary>
            Accent 5
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.BaseStylesheet.ElAccent6">
            <summary>
            Accent 6
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.BaseStylesheet.ElHlink">
            <summary>
            Hyperlink
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.BaseStylesheet.ElFolHlink">
            <summary>
            Followed Hyperlink
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.BaseStylesheet.ElExtLst">
            <summary>
            
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.BaseStylesheet.ElCustClr">
            <summary>
            Custom color
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.BaseStylesheet.ElLatin">
            <summary>
            Latin Font
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.BaseStylesheet.ElEa">
            <summary>
            East Asian Font
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.BaseStylesheet.ElCs">
            <summary>
            Complex Script Font
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.BaseStylesheet.ElFont">
            <summary>
            Font
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.BaseStylesheet.ElScene3d">
            <summary>
            3D Scene Properties
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.BaseStylesheet.ElSp3d">
            <summary>
            3D properties
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.BaseStylesheet.ElMajorFont">
            <summary>
            Major Font
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.BaseStylesheet.ElMinorFont">
            <summary>
            Minor fonts
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.BaseStylesheet.ElLn">
            <summary>
            
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.BaseStylesheet.ElEffectStyle">
            <summary>
            Effect Style
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.BaseStylesheet.ElFillStyleLst">
            <summary>
            Fill Style List
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.BaseStylesheet.ElLnStyleLst">
            <summary>
            Line Style List
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.BaseStylesheet.ElEffectStyleLst">
            <summary>
            Effect Style List
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.BaseStylesheet.ElBgFillStyleLst">
            <summary>
            Background Fill Style List
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.BaseStylesheet.ElClrScheme">
            <summary>
            
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.BaseStylesheet.ElFontScheme">
            <summary>
            Font Scheme
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.BaseStylesheet.ElFmtScheme">
            <summary>
            Format Scheme
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.BaseStylesheet.AttrName">
            <summary>
            Name
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.BaseStylesheet.AttrScript">
            <summary>
            Script
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.BaseStylesheet.AttrTypeface">
            <summary>
            Typeface
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Shape3DScenePlane.ElAnchor">
            <summary>
            Anchor Point
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Shape3DScenePlane.ElNorm">
            <summary>
            Normal
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Shape3DScenePlane.ElUp">
            <summary>
            Up Vector
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Shape3DScenePlane.ElExtLst">
            <summary>
            
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Table.ElLnL">
            <summary>
            Left Border Line Properties
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Table.ElLnR">
            <summary>
            Right Border Line Properties
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Table.ElLnT">
            <summary>
            Top Border Line Properties
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Table.ElLnB">
            <summary>
            Bottom Border Line Properties
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Table.ElLnTlToBr">
            <summary>
            Top-Left to Bottom-Right Border Line Properties
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Table.ElLnBlToTr">
            <summary>
            Bottom-Left to Top-Right Border Line Properties
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Table.ElCell3D">
            <summary>
            Cell 3-D
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Table.ElExtLst">
            <summary>
            
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Table.ElGridCol">
            <summary>
            Table Grid Column
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Table.ElTxBody">
            <summary>
            Text Body
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Table.ElTcPr">
            <summary>
            Table Cell Properties
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Table.ElTc">
            <summary>
            Table Cell
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Table.ElTableStyle">
            <summary>
            Table Style
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Table.ElTableStyleId">
            <summary>
            Table Style ID
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Table.ElTblPr">
            <summary>
            Table Properties
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Table.ElTblGrid">
            <summary>
            Table Grid
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Table.ElTr">
            <summary>
            Table Row
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Table.ElTbl">
            <summary>
            Table
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Table.AttrMarL">
            <summary>
            Left Margin
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Table.AttrMarR">
            <summary>
            Right Margin
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Table.AttrMarT">
            <summary>
            Top Margin
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Table.AttrMarB">
            <summary>
            Bottom Margin
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Table.AttrVert">
            <summary>
            Text Direction
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Table.AttrAnchor">
            <summary>
            Anchor
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Table.AttrAnchorCtr">
            <summary>
            Anchor Center
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Table.AttrHorzOverflow">
            <summary>
            Horizontal Overflow
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Table.AttrW">
            <summary>
            Width
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Table.AttrRowSpan">
            <summary>
            Row Span
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Table.AttrGridSpan">
            <summary>
            Grid Span
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Table.AttrHMerge">
            <summary>
            Horizontal Merge
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Table.AttrVMerge">
            <summary>
            Vertical Merge
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Table.AttrH">
            <summary>
            Height
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Table.AttrRtl">
            <summary>
            Right-to-Left
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Table.AttrFirstRow">
            <summary>
            First Row
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Table.AttrFirstCol">
            <summary>
            First Column
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Table.AttrLastRow">
            <summary>
            Last Row
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Table.AttrLastCol">
            <summary>
            Last Column
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Table.AttrBandRow">
            <summary>
            Banded Rows
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Table.AttrBandCol">
            <summary>
            Banded Columns
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.BaseTypes.ElTint">
            <summary>
            Tint
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.BaseTypes.ElShade">
            <summary>
            Shade
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.BaseTypes.ElComp">
            <summary>
            Complement
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.BaseTypes.ElInv">
            <summary>
            Inverse
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.BaseTypes.ElGray">
            <summary>
            Gray
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.BaseTypes.ElAlpha">
            <summary>
            Alpha
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.BaseTypes.ElAlphaOff">
            <summary>
            Alpha Offset
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.BaseTypes.ElAlphaMod">
            <summary>
            Alpha Modulation
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.BaseTypes.ElHue">
            <summary>
            Hue
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.BaseTypes.ElHueOff">
            <summary>
            Hue Offset
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.BaseTypes.ElHueMod">
            <summary>
            Hue Modulate
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.BaseTypes.ElSat">
            <summary>
            Saturation
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.BaseTypes.ElSatOff">
            <summary>
            Saturation Offset
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.BaseTypes.ElSatMod">
            <summary>
            Saturation Modulation
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.BaseTypes.ElLum">
            <summary>
            Luminance
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.BaseTypes.ElLumOff">
            <summary>
            Luminance Offset
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.BaseTypes.ElLumMod">
            <summary>
            Luminance Modulation
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.BaseTypes.ElRed">
            <summary>
            Red
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.BaseTypes.ElRedOff">
            <summary>
            Red Offset
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.BaseTypes.ElRedMod">
            <summary>
            Red Modulation
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.BaseTypes.ElGreen">
            <summary>
            Green
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.BaseTypes.ElGreenOff">
            <summary>
            Green Offset
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.BaseTypes.ElGreenMod">
            <summary>
            Green Modification
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.BaseTypes.ElBlue">
            <summary>
            Blue
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.BaseTypes.ElBlueOff">
            <summary>
            Blue Offset
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.BaseTypes.ElBlueMod">
            <summary>
            Blue Modification
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.BaseTypes.ElGamma">
            <summary>
            Gamma
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.BaseTypes.ElInvGamma">
            <summary>
            Inverse Gamma
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.BaseTypes.ElExt">
            <summary>
            Extension
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.BaseTypes.ElSx">
            <summary>
            Horizontal Ratio
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.BaseTypes.ElSy">
            <summary>
            Vertical Ratio
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.BaseTypes.ElOff">
            <summary>
            Offset
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.BaseTypes.ElChOff">
            <summary>
            Child Offset
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.BaseTypes.ElChExt">
            <summary>
            Child Extents
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.BaseTypes.ElScrgbClr">
            <summary>
            RGB Color Model - Percentage Variant
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.BaseTypes.ElSrgbClr">
            <summary>
            RGB Color Model - Hex Variant
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.BaseTypes.ElHslClr">
            <summary>
            Hue, Saturation, Luminance Color Model
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.BaseTypes.ElSysClr">
            <summary>
            System Color
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.BaseTypes.ElSchemeClr">
            <summary>
            Scheme Color
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.BaseTypes.ElPrstClr">
            <summary>
            Preset Color
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.BaseTypes.ElSnd">
            <summary>
            Hyperlink Sound
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.BaseTypes.ElExtLst">
            <summary>
            
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.BaseTypes.AttrUri">
            <summary>
            Uniform Resource Identifier
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.BaseTypes.AttrVal">
            <summary>
            Value
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.BaseTypes.AttrN">
            <summary>
            Numerator
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.BaseTypes.AttrD">
            <summary>
            Denominator
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.BaseTypes.AttrX">
            <summary>
            X-Axis Coordinate
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.BaseTypes.AttrY">
            <summary>
            Y-Axis Coordinate
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.BaseTypes.AttrCx">
            <summary>
            Extent Length
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.BaseTypes.AttrCy">
            <summary>
            Extent Width
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.BaseTypes.AttrR">
            <summary>
            Red
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.BaseTypes.AttrG">
            <summary>
            Green
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.BaseTypes.AttrB">
            <summary>
            Blue
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.BaseTypes.AttrLastClr">
            <summary>
            Last Color
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.BaseTypes.AttrRot">
            <summary>
            Rotation
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.BaseTypes.AttrFlipH">
            <summary>
            Horizontal Flip
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.BaseTypes.AttrFlipV">
            <summary>
            Vertical Flip
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.BaseTypes.AttrZ">
            <summary>
            Z-Coordinate in 3D
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.BaseTypes.AttrDx">
            <summary>
            Distance along X-axis in 3D
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.BaseTypes.AttrDy">
            <summary>
            Distance along Y-axis in 3D
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.BaseTypes.AttrDz">
            <summary>
            Distance along Z-axis in 3D
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.BaseTypes.AttrLat">
            <summary>
            Latitude
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.BaseTypes.AttrLon">
            <summary>
            Longitude
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.BaseTypes.AttrRev">
            <summary>
            Revolution
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.BaseTypes.AttrL">
            <summary>
            Left Offset
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.BaseTypes.AttrT">
            <summary>
            Top Offset
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.BaseTypes.AttrName">
            <summary>
            Sound Name
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.BaseTypes.AttrBuiltIn">
            <summary>
            Recognized Built-In Sound
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.BaseTypes.AttrInvalidUrl">
            <summary>
            Invalid URL
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.BaseTypes.AttrAction">
            <summary>
            Action Setting
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.BaseTypes.AttrTgtFrame">
            <summary>
            Target Frame
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.BaseTypes.AttrTooltip">
            <summary>
            Hyperlink Tooltip
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.BaseTypes.AttrHistory">
            <summary>
            Add Hyperlink to Page History
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.BaseTypes.AttrHighlightClick">
            <summary>
            Highlight Click
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.BaseTypes.AttrEndSnd">
            <summary>
            End Sounds
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DocumentProperties.ElExtLst">
            <summary>
            
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DocumentProperties.ElHlinkClick">
            <summary>
            Drawing Element On Click Hyperlink
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DocumentProperties.ElHlinkHover">
            <summary>
            Hyperlink for Hover
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DocumentProperties.ElSpLocks">
            <summary>
            Shape Locks
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DocumentProperties.ElCxnSpLocks">
            <summary>
            Connection Shape Locks
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DocumentProperties.ElStCxn">
            <summary>
            Connection Start
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DocumentProperties.ElEndCxn">
            <summary>
            Connection End
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DocumentProperties.ElPicLocks">
            <summary>
            Picture Locks
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DocumentProperties.ElGrpSpLocks">
            <summary>
            Group Shape Locks
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DocumentProperties.ElGraphicFrameLocks">
            <summary>
            Graphic Frame Locks
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DocumentProperties.AttrNoGrp">
            <summary>
            Disallow Shape Grouping
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DocumentProperties.AttrNoSelect">
            <summary>
            Disallow Shape Selection
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DocumentProperties.AttrNoRot">
            <summary>
            Disallow Shape Rotation
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DocumentProperties.AttrNoChangeAspect">
            <summary>
            Disallow Aspect Ratio Change
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DocumentProperties.AttrNoMove">
            <summary>
            Disallow Shape Movement
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DocumentProperties.AttrNoResize">
            <summary>
            Disallow Shape Resize
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DocumentProperties.AttrNoEditPoints">
            <summary>
            Disallow Shape Point Editing
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DocumentProperties.AttrNoAdjustHandles">
            <summary>
            Disallow Showing Adjust Handles
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DocumentProperties.AttrNoChangeArrowheads">
            <summary>
            Disallow Arrowhead Changes
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DocumentProperties.AttrNoChangeShapeType">
            <summary>
            Disallow Shape Type Change
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DocumentProperties.AttrNoTextEdit">
            <summary>
            Disallow Shape Text Editing
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DocumentProperties.AttrNoCrop">
            <summary>
            Disallow Crop Changes
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DocumentProperties.AttrNoUngrp">
            <summary>
            Disallow Shape Ungrouping
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DocumentProperties.AttrNoDrilldown">
            <summary>
            Disallow Selection of Child Shapes
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DocumentProperties.AttrId">
            <summary>
            Unique Identifier
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DocumentProperties.AttrName">
            <summary>
            Name
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DocumentProperties.AttrDescr">
            <summary>
            Alternative Text for Object
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DocumentProperties.AttrHidden">
            <summary>
            Hidden
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DocumentProperties.AttrTxBox">
            <summary>
            Text Box
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DocumentProperties.AttrPreferRelativeResize">
            <summary>
            Relative Resize Preferred
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Shape3DStyles.ElBevelT">
            <summary>
            Top Bevel
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Shape3DStyles.ElBevelB">
            <summary>
            Bottom Bevel
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Shape3DStyles.ElExtrusionClr">
            <summary>
            Extrusion Color
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Shape3DStyles.ElContourClr">
            <summary>
            Contour Color
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Shape3DStyles.ElExtLst">
            <summary>
            
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Shape3DStyles.ElSp3d">
            <summary>
            Apply 3D shape properties
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Shape3DStyles.ElFlatTx">
            <summary>
            No text in 3D scene
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Shape3DStyles.AttrW">
            <summary>
            Width
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Shape3DStyles.AttrH">
            <summary>
            Height
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Shape3DStyles.AttrPrst">
            <summary>
            Preset Bevel
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Shape3DStyles.AttrZ">
            <summary>
            Shape Depth
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Shape3DStyles.AttrExtrusionH">
            <summary>
            Extrusion Height
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Shape3DStyles.AttrContourW">
            <summary>
            Contour Width
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Shape3DStyles.AttrPrstMaterial">
            <summary>
            Preset Material Type
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TableStyle.ElBevel">
            <summary>
            Bevel
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TableStyle.ElLightRig">
            <summary>
            Light Rig
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TableStyle.ElExtLst">
            <summary>
            
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TableStyle.ElFill">
            <summary>
            Fill
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TableStyle.ElFillRef">
            <summary>
            Fill Reference
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TableStyle.ElLn">
            <summary>
            
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TableStyle.ElLnRef">
            <summary>
            Line Reference
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TableStyle.ElEffect">
            <summary>
            Effect
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TableStyle.ElEffectRef">
            <summary>
            Effect Reference
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TableStyle.ElFont">
            <summary>
            Font
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TableStyle.ElFontRef">
            <summary>
            
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TableStyle.ElLeft">
            <summary>
            Left Border
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TableStyle.ElRight">
            <summary>
            Right Border
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TableStyle.ElTop">
            <summary>
            Top Border
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TableStyle.ElBottom">
            <summary>
            Bottom Border
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TableStyle.ElInsideH">
            <summary>
            Inside Horizontal Border
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TableStyle.ElInsideV">
            <summary>
            Inside Vertical Border
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TableStyle.ElTl2br">
            <summary>
            Top Left to Bottom Right Border
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TableStyle.ElTr2bl">
            <summary>
            Top Right to Bottom Left Border
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TableStyle.ElTcBdr">
            <summary>
            Table Cell Borders
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TableStyle.ElCell3D">
            <summary>
            
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TableStyle.ElTcTxStyle">
            <summary>
            Table Cell Text Style
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TableStyle.ElTcStyle">
            <summary>
            Table Cell Style
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TableStyle.ElTblBg">
            <summary>
            Table Background
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TableStyle.ElWholeTbl">
            <summary>
            Whole Table
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TableStyle.ElBand1H">
            <summary>
            Band 1 Horizontal
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TableStyle.ElBand2H">
            <summary>
            Band 2 Horizontal
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TableStyle.ElBand1V">
            <summary>
            Band 1 Vertical
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TableStyle.ElBand2V">
            <summary>
            Band 2 Vertical
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TableStyle.ElLastCol">
            <summary>
            Last Column
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TableStyle.ElFirstCol">
            <summary>
            First Column
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TableStyle.ElLastRow">
            <summary>
            Last Row
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TableStyle.ElSeCell">
            <summary>
            Southeast Cell
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TableStyle.ElSwCell">
            <summary>
            Southwest Cell
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TableStyle.ElFirstRow">
            <summary>
            First Row
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TableStyle.ElNeCell">
            <summary>
            Northeast Cell
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TableStyle.ElNwCell">
            <summary>
            Northwest Cell
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TableStyle.ElTblStyle">
            <summary>
            Table Style
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TableStyle.ElTblStyleLst">
            <summary>
            Table Style List
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TableStyle.AttrPrstMaterial">
            <summary>
            Preset Material
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TableStyle.AttrB">
            <summary>
            Bold
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TableStyle.AttrI">
            <summary>
            Italic
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TableStyle.AttrStyleId">
            <summary>
            Style ID
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TableStyle.AttrStyleName">
            <summary>
            Name
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TableStyle.AttrDef">
            <summary>
            Default
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElExt">
            <summary>
            Extension
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElV">
            <summary>
            Numeric Value
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElFormatCode">
            <summary>
            Format Code
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElPtCount">
            <summary>
            Point Count
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElPt">
            <summary>
            Numeric Point
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElExtLst">
            <summary>
            
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElF">
            <summary>
            Formula
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElNumCache">
            <summary>
            Number Cache
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElNumRef">
            <summary>
            Number Reference
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElNumLit">
            <summary>
            Number Literal
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElStrCache">
            <summary>
            String Cache
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElStrRef">
            <summary>
            String Reference
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElRich">
            <summary>
            Rich Text
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElLvl">
            <summary>
            Level
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElMultiLvlStrCache">
            <summary>
            Multi Level String Cache
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElMultiLvlStrRef">
            <summary>
            Multi Level String Reference
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElStrLit">
            <summary>
            String Literal
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElLayoutTarget">
            <summary>
            Layout Target
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElXMode">
            <summary>
            Left Mode
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElYMode">
            <summary>
            Top Mode
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElWMode">
            <summary>
            Width Mode
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElHMode">
            <summary>
            Height Mode
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElX">
            <summary>
            Left
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElY">
            <summary>
            Top
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElW">
            <summary>
            Width
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElH">
            <summary>
            Height
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElManualLayout">
            <summary>
            Manual Layout
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElTx">
            <summary>
            Chart Text
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElLayout">
            <summary>
            Layout
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElOverlay">
            <summary>
            Overlay
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElSpPr">
            <summary>
            
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElTxPr">
            <summary>
            
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElRotX">
            <summary>
            X Rotation
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElHPercent">
            <summary>
            Height Percent
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElRotY">
            <summary>
            Y Rotation
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElDepthPercent">
            <summary>
            Depth Percent
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElRAngAx">
            <summary>
            Right Angle Axes
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElPerspective">
            <summary>
            Perspective
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElThickness">
            <summary>
            Thickness
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElPictureOptions">
            <summary>
            Picture Options
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElShowHorzBorder">
            <summary>
            Show Horizontal Border
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElShowVertBorder">
            <summary>
            Show Vertical Border
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElShowOutline">
            <summary>
            Show Outline Border
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElShowKeys">
            <summary>
            Show Legend Keys
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElSecondPiePt">
            <summary>
            Second Pie Point
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElNumFmt">
            <summary>
            Number Format
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElDLblPos">
            <summary>
            Data Label Position
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElShowLegendKey">
            <summary>
            Show Legend Key
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElShowVal">
            <summary>
            Show Value
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElShowCatName">
            <summary>
            Show Category Name
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElShowSerName">
            <summary>
            Show Series Name
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElShowPercent">
            <summary>
            Show Percent
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElShowBubbleSize">
            <summary>
            Show Bubble Size
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElSeparator">
            <summary>
            Separator
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElIdx">
            <summary>
            Index
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElDelete">
            <summary>
            Delete
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElShowLeaderLines">
            <summary>
            Show Leader Lines
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElLeaderLines">
            <summary>
            Leader Lines
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElDLbl">
            <summary>
            Data Label
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElSymbol">
            <summary>
            Symbol
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElSize">
            <summary>
            Size
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElInvertIfNegative">
            <summary>
            Invert if Negative
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElMarker">
            <summary>
            Marker
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElBubble3D">
            <summary>
            3D Bubble
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElExplosion">
            <summary>
            Explosion
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElName">
            <summary>
            Trendline Name
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElTrendlineType">
            <summary>
            Trendline Type
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElOrder">
            <summary>
            Polynomial Trendline Order
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElPeriod">
            <summary>
            Period
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElForward">
            <summary>
            Forward
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElBackward">
            <summary>
            Backward
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElIntercept">
            <summary>
            Intercept
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElDispRSqr">
            <summary>
            Display R Squared Value
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElDispEq">
            <summary>
            Display Equation
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElTrendlineLbl">
            <summary>
            Trendline Label
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElErrDir">
            <summary>
            Error Bar Direction
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElErrBarType">
            <summary>
            Error Bar Type
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElErrValType">
            <summary>
            Error Bar Value Type
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElNoEndCap">
            <summary>
            No End Cap
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElPlus">
            <summary>
            Plus
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElMinus">
            <summary>
            Minus
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElVal">
            <summary>
            Error Bar Value
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElGapWidth">
            <summary>
            Gap Width
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElUpBars">
            <summary>
            Up Bars
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElDownBars">
            <summary>
            Down Bars
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElDPt">
            <summary>
            Data Point
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElDLbls">
            <summary>
            Data Labels
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElTrendline">
            <summary>
            
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElErrBars">
            <summary>
            Error Bars
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElCat">
            <summary>
            Category Axis Data
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElSmooth">
            <summary>
            
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElXVal">
            <summary>
            
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElYVal">
            <summary>
            
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElShape">
            <summary>
            Shape
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElBubbleSize">
            <summary>
            Bubble Size
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElGrouping">
            <summary>
            Grouping
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElVaryColors">
            <summary>
            
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElSer">
            <summary>
            
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElDropLines">
            <summary>
            Drop Lines
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElHiLowLines">
            <summary>
            High Low Lines
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElUpDownBars">
            <summary>
            
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElAxId">
            <summary>
            Axis ID
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElGapDepth">
            <summary>
            Gap Depth
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElScatterStyle">
            <summary>
            Scatter Style
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElRadarStyle">
            <summary>
            Radar Style
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElBarDir">
            <summary>
            Bar Direction
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElOverlap">
            <summary>
            Overlap
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElSerLines">
            <summary>
            Series Lines
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElFirstSliceAng">
            <summary>
            First Slice Angle
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElHoleSize">
            <summary>
            Hole Size
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElOfPieType">
            <summary>
            Pie of Pie or Bar of Pie Type
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElSplitType">
            <summary>
            Split Type
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElSplitPos">
            <summary>
            Split Position
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElCustSplit">
            <summary>
            Custom Split
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElSecondPieSize">
            <summary>
            Second Pie Size
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElBubbleScale">
            <summary>
            Bubble Scale
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElShowNegBubbles">
            <summary>
            Show Negative Bubbles
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElSizeRepresents">
            <summary>
            Size Represents
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElBandFmt">
            <summary>
            Band Format
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElWireframe">
            <summary>
            Wireframe
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElBandFmts">
            <summary>
            Band Formats
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElApplyToFront">
            <summary>
            Apply To Front
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElApplyToSides">
            <summary>
            Apply To Sides
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElApplyToEnd">
            <summary>
            Apply to End
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElPictureFormat">
            <summary>
            Picture Format
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElPictureStackUnit">
            <summary>
            Picture Stack Unit
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElCustUnit">
            <summary>
            Custom Display Unit
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElBuiltInUnit">
            <summary>
            Built in Display Unit Value
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElDispUnitsLbl">
            <summary>
            Display Units Label
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElLogBase">
            <summary>
            Logarithmic Base
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElOrientation">
            <summary>
            Axis Orientation
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElMax">
            <summary>
            Maximum
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElMin">
            <summary>
            Minimum
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElScaling">
            <summary>
            Scaling
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElAxPos">
            <summary>
            Axis Position
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElMajorGridlines">
            <summary>
            Major Gridlines
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElMinorGridlines">
            <summary>
            Minor Gridlines
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElTitle">
            <summary>
            Title
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElMajorTickMark">
            <summary>
            Major Tick Mark
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElMinorTickMark">
            <summary>
            Minor Tick Mark
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElTickLblPos">
            <summary>
            Tick Label Position
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElCrossAx">
            <summary>
            Crossing Axis ID
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElCrosses">
            <summary>
            Crosses
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElCrossesAt">
            <summary>
            Crossing Value
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElAuto">
            <summary>
            Automatic Category Axis
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElLblAlgn">
            <summary>
            Label Alignment
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElLblOffset">
            <summary>
            Label Offset
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElTickLblSkip">
            <summary>
            
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElTickMarkSkip">
            <summary>
            Tick Mark Skip
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElNoMultiLvlLbl">
            <summary>
            No Multi-level Labels
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElBaseTimeUnit">
            <summary>
            Base Time Unit
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElMajorUnit">
            <summary>
            Major Unit
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElMajorTimeUnit">
            <summary>
            Major Time Unit
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElMinorUnit">
            <summary>
            Minor Unit
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElMinorTimeUnit">
            <summary>
            Minor Time Unit
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElCrossBetween">
            <summary>
            Cross Between
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElDispUnits">
            <summary>
            Display Units
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElAreaChart">
            <summary>
            Area Charts
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElArea3DChart">
            <summary>
            3D Area Charts
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElLineChart">
            <summary>
            Line Charts
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElLine3DChart">
            <summary>
            3D Line Charts
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElStockChart">
            <summary>
            Stock Charts
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElRadarChart">
            <summary>
            Radar Charts
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElScatterChart">
            <summary>
            Scatter Charts
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElPieChart">
            <summary>
            Pie Charts
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElPie3DChart">
            <summary>
            3D Pie Charts
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElDoughnutChart">
            <summary>
            Doughnut Charts
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElBarChart">
            <summary>
            Bar Charts
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElBar3DChart">
            <summary>
            3D Bar Charts
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElOfPieChart">
            <summary>
            Pie of Pie or Bar of Pie Charts
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElSurfaceChart">
            <summary>
            Surface Charts
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElSurface3DChart">
            <summary>
            3D Surface Charts
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElBubbleChart">
            <summary>
            Bubble Charts
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElValAx">
            <summary>
            Value Axis
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElCatAx">
            <summary>
            Category Axis Data
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElDateAx">
            <summary>
            Date Axis
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElSerAx">
            <summary>
            Series Axis
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElDTable">
            <summary>
            Data Table
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElPivotFmt">
            <summary>
            Pivot Format
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElLegendPos">
            <summary>
            Legend Position
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElLegendEntry">
            <summary>
            Legend Entry
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElAutoTitleDeleted">
            <summary>
            Auto Title Is Deleted
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElPivotFmts">
            <summary>
            Pivot Formats
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElView3D">
            <summary>
            View In 3D
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElFloor">
            <summary>
            Floor
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElSideWall">
            <summary>
            Side Wall
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElBackWall">
            <summary>
            Back Wall
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElPlotArea">
            <summary>
            Plot Area
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElLegend">
            <summary>
            Legend
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElPlotVisOnly">
            <summary>
            Plot Visible Only
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElDispBlanksAs">
            <summary>
            Display Blanks As
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElShowDLblsOverMax">
            <summary>
            Show Data Labels over Maximum
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElFmtId">
            <summary>
            Format ID
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElChartObject">
            <summary>
            Chart Object
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElData">
            <summary>
            Data Cannot Be Changed
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElFormatting">
            <summary>
            Formatting
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElSelection">
            <summary>
            Selection
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElUserInterface">
            <summary>
            User Interface
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElOddHeader">
            <summary>
            Odd Header
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElOddFooter">
            <summary>
            Odd Footer
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElEvenHeader">
            <summary>
            Even Header
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElEvenFooter">
            <summary>
            Even Footer
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElFirstHeader">
            <summary>
            First Header
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElFirstFooter">
            <summary>
            First Footer
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElAutoUpdate">
            <summary>
            Update Automatically
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElHeaderFooter">
            <summary>
            Header and Footer
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElPageMargins">
            <summary>
            Page Margins
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElPageSetup">
            <summary>
            Page Setup
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElLegacyDrawingHF">
            <summary>
            Legacy Drawing for Headers and Footers
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElDate1904">
            <summary>
            1904 Date System
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElLang">
            <summary>
            Editing Language
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElRoundedCorners">
            <summary>
            Rounded Corners
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElStyle">
            <summary>
            Style
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElClrMapOvr">
            <summary>
            Color Map Override
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElPivotSource">
            <summary>
            Pivot Source
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElProtection">
            <summary>
            Protection
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElChart">
            <summary>
            Chart
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElExternalData">
            <summary>
            External Data Relationship
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElPrintSettings">
            <summary>
            Print Settings
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElUserShapes">
            <summary>
            Reference to Chart Drawing Part
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.ElChartSpace">
            <summary>
            Chart Space
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.AttrUri">
            <summary>
            Uniform Resource Identifier
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.AttrSourceLinked">
            <summary>
            Linked to Source
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.AttrAlignWithMargins">
            <summary>
            Align With Margins
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.AttrDifferentOddEven">
            <summary>
            Different Odd Even
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.AttrDifferentFirst">
            <summary>
            Different First
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.AttrIdx">
            <summary>
            Index
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.AttrL">
            <summary>
            Left
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.AttrR">
            <summary>
            Right
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.AttrT">
            <summary>
            Top
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.AttrB">
            <summary>
            Bottom
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.AttrHeader">
            <summary>
            Header
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.AttrFooter">
            <summary>
            Footer
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.AttrPaperSize">
            <summary>
            Page Size
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.AttrFirstPageNumber">
            <summary>
            First Page Number
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.AttrBlackAndWhite">
            <summary>
            Black and White
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.AttrDraft">
            <summary>
            Draft
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.AttrUseFirstPageNumber">
            <summary>
            Use First Page Number
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.AttrHorizontalDpi">
            <summary>
            Horizontal DPI
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.AttrVerticalDpi">
            <summary>
            Vertical DPI
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Chart.AttrCopies">
            <summary>
            Copies
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.GraphicalObject.ElGraphicData">
            <summary>
            Graphic Object Data
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.GraphicalObject.ElGraphic">
            <summary>
            Graphic Object
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.GraphicalObject.AttrUri">
            <summary>
            Uniform Resource Identifier
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeEffects.ElClrFrom">
            <summary>
            Change Color From
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeEffects.ElClrTo">
            <summary>
            Change Color To
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeEffects.ElFillToRect">
            <summary>
            Fill To Rectangle
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeEffects.ElLin">
            <summary>
            Linear Gradient Fill
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeEffects.ElPath">
            <summary>
            Path Gradient
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeEffects.ElGs">
            <summary>
            Gradient stops
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeEffects.ElGsLst">
            <summary>
            Gradient Stop List
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeEffects.ElTileRect">
            <summary>
            Tile Rectangle
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeEffects.ElFillRect">
            <summary>
            Fill Rectangle
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeEffects.ElTile">
            <summary>
            Tile
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeEffects.ElStretch">
            <summary>
            Stretch
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeEffects.ElAlphaBiLevel">
            <summary>
            Alpha Bi-Level Effect
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeEffects.ElAlphaCeiling">
            <summary>
            
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeEffects.ElAlphaFloor">
            <summary>
            
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeEffects.ElAlphaInv">
            <summary>
            
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeEffects.ElAlphaMod">
            <summary>
            
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeEffects.ElAlphaModFix">
            <summary>
            Alpha Modulate Fixed Effect
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeEffects.ElAlphaRepl">
            <summary>
            
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeEffects.ElBiLevel">
            <summary>
            Bi-Level (Black/White) Effect
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeEffects.ElBlur">
            <summary>
            
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeEffects.ElClrChange">
            <summary>
            
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeEffects.ElClrRepl">
            <summary>
            Solid Color Replacement
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeEffects.ElDuotone">
            <summary>
            
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeEffects.ElFillOverlay">
            <summary>
            
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeEffects.ElGrayscl">
            <summary>
            
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeEffects.ElHsl">
            <summary>
            
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeEffects.ElLum">
            <summary>
            Luminance Effect
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeEffects.ElTint">
            <summary>
            Tint Effect
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeEffects.ElExtLst">
            <summary>
            
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeEffects.ElBlip">
            <summary>
            
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeEffects.ElSrcRect">
            <summary>
            Source Rectangle
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeEffects.ElFgClr">
            <summary>
            Foreground color
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeEffects.ElBgClr">
            <summary>
            Background color
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeEffects.ElNoFill">
            <summary>
            
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeEffects.ElSolidFill">
            <summary>
            
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeEffects.ElGradFill">
            <summary>
            
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeEffects.ElBlipFill">
            <summary>
            
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeEffects.ElPattFill">
            <summary>
            Pattern Fill
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeEffects.ElGrpFill">
            <summary>
            Group Fill
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeEffects.ElCont">
            <summary>
            Effect Container
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeEffects.ElEffect">
            <summary>
            Effect
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeEffects.ElAlphaOutset">
            <summary>
            Alpha Inset/Outset Effect
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeEffects.ElBlend">
            <summary>
            Blend Effect
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeEffects.ElFill">
            <summary>
            Fill
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeEffects.ElGlow">
            <summary>
            Glow Effect
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeEffects.ElInnerShdw">
            <summary>
            Inner Shadow Effect
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeEffects.ElOuterShdw">
            <summary>
            Outer Shadow Effect
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeEffects.ElPrstShdw">
            <summary>
            Preset Shadow
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeEffects.ElReflection">
            <summary>
            Reflection Effect
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeEffects.ElRelOff">
            <summary>
            Relative Offset Effect
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeEffects.ElSoftEdge">
            <summary>
            Soft Edge Effect
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeEffects.ElXfrm">
            <summary>
            Transform Effect
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeEffects.ElEffectLst">
            <summary>
            Effect Container
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeEffects.ElEffectDag">
            <summary>
            Effect Container
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeEffects.AttrThresh">
            <summary>
            Threshold
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeEffects.AttrAmt">
            <summary>
            Amount
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeEffects.AttrRad">
            <summary>
            Radius
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeEffects.AttrA">
            <summary>
            Alpha
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeEffects.AttrGrow">
            <summary>
            Grow Bounds
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeEffects.AttrUseA">
            <summary>
            Consider Alpha Values
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeEffects.AttrHue">
            <summary>
            Hue
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeEffects.AttrSat">
            <summary>
            Saturation
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeEffects.AttrBlurRad">
            <summary>
            Blur Radius
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeEffects.AttrDist">
            <summary>
            Distance
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeEffects.AttrDir">
            <summary>
            Direction
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeEffects.AttrBright">
            <summary>
            Brightness
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeEffects.AttrContrast">
            <summary>
            Contrast
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeEffects.AttrSx">
            <summary>
            Horizontal Scaling Factor
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeEffects.AttrSy">
            <summary>
            Vertical Scaling Factor
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeEffects.AttrKx">
            <summary>
            Horizontal Skew
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeEffects.AttrKy">
            <summary>
            Vertical Skew
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeEffects.AttrAlgn">
            <summary>
            Shadow Alignment
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeEffects.AttrRotWithShape">
            <summary>
            Rotate With Shape
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeEffects.AttrPrst">
            <summary>
            Preset Shadow
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeEffects.AttrStA">
            <summary>
            Start Opacity
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeEffects.AttrStPos">
            <summary>
            Start Position
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeEffects.AttrEndA">
            <summary>
            End Alpha
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeEffects.AttrEndPos">
            <summary>
            End Position
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeEffects.AttrFadeDir">
            <summary>
            Fade Direction
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeEffects.AttrTx">
            <summary>
            Offset X
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeEffects.AttrTy">
            <summary>
            Offset Y
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeEffects.AttrAng">
            <summary>
            Angle
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeEffects.AttrScaled">
            <summary>
            Scaled
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeEffects.AttrPos">
            <summary>
            Position
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeEffects.AttrFlip">
            <summary>
            Tile Flip
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeEffects.AttrCstate">
            <summary>
            Compression State
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeEffects.AttrDpi">
            <summary>
            DPI Setting
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeEffects.AttrRef">
            <summary>
            Reference
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeEffects.AttrType">
            <summary>
            Effect Container Type
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeEffects.AttrName">
            <summary>
            Name
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Text.ElPPr">
            <summary>
            Text Paragraph Properties
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Text.ElEndParaRPr">
            <summary>
            End Paragraph Run Properties
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Text.ElDefPPr">
            <summary>
            Default Paragraph Style
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Text.ElLvl1pPr">
            <summary>
            List Level 1 Text Style
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Text.ElLvl2pPr">
            <summary>
            List Level 2 Text Style
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Text.ElLvl3pPr">
            <summary>
            List Level 3 Text Style
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Text.ElLvl4pPr">
            <summary>
            List Level 4 Text Style
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Text.ElLvl5pPr">
            <summary>
            List Level 5 Text Style
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Text.ElLvl6pPr">
            <summary>
            List Level 6 Text Style
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Text.ElLvl7pPr">
            <summary>
            List Level 7 Text Style
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Text.ElLvl8pPr">
            <summary>
            List Level 8 Text Style
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Text.ElLvl9pPr">
            <summary>
            List Level 9 Text Style
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Text.ElExtLst">
            <summary>
            
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Text.ElNoAutofit">
            <summary>
            No AutoFit
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Text.ElNormAutofit">
            <summary>
            Normal AutoFit
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Text.ElSpAutoFit">
            <summary>
            Shape AutoFit
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Text.ElPrstTxWarp">
            <summary>
            Preset Text Shape
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Text.ElScene3d">
            <summary>
            3D Scene Properties
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Text.ElBodyPr">
            <summary>
            Body Properties
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Text.ElLstStyle">
            <summary>
            Text List Styles
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Text.ElP">
            <summary>
            Text Paragraphs
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Text.AttrFontScale">
            <summary>
            Font Scale
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Text.AttrLnSpcReduction">
            <summary>
            Line Space Reduction
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Text.AttrRot">
            <summary>
            Rotation
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Text.AttrSpcFirstLastPara">
            <summary>
            Paragraph Spacing
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Text.AttrVertOverflow">
            <summary>
            Text Vertical Overflow
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Text.AttrHorzOverflow">
            <summary>
            Text Horizontal Overflow
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Text.AttrVert">
            <summary>
            Vertical Text
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Text.AttrWrap">
            <summary>
            Text Wrapping Type
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Text.AttrLIns">
            <summary>
            Left Inset
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Text.AttrTIns">
            <summary>
            Top Inset
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Text.AttrRIns">
            <summary>
            Right Inset
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Text.AttrBIns">
            <summary>
            Bottom Inset
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Text.AttrNumCol">
            <summary>
            Number of Columns
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Text.AttrSpcCol">
            <summary>
            Space Between Columns
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Text.AttrRtlCol">
            <summary>
            Columns Right-To-Left
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Text.AttrFromWordArt">
            <summary>
            From WordArt
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Text.AttrAnchor">
            <summary>
            Anchor
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Text.AttrAnchorCtr">
            <summary>
            Anchor Center
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Text.AttrForceAA">
            <summary>
            Force Anti-Alias
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Text.AttrUpright">
            <summary>
            Text Upright
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Text.AttrCompatLnSpc">
            <summary>
            Compatible Line Spacing
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ChartDrawing.ElCNvPr">
            <summary>
            Chart Non Visual Properties
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ChartDrawing.ElCNvSpPr">
            <summary>
            Non-Visual Shape Drawing Properties
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ChartDrawing.ElNvSpPr">
            <summary>
            Non-Visual Shape Properties
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ChartDrawing.ElSpPr">
            <summary>
            Shape Properties
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ChartDrawing.ElStyle">
            <summary>
            Shape Style
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ChartDrawing.ElTxBody">
            <summary>
            Shape Text Body
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ChartDrawing.ElCNvCxnSpPr">
            <summary>
            Non-Visual Connection Shape Drawing Properties
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ChartDrawing.ElNvCxnSpPr">
            <summary>
            Connector Non Visual Properties
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ChartDrawing.ElCNvPicPr">
            <summary>
            Non-Visual Picture Drawing Properties
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ChartDrawing.ElNvPicPr">
            <summary>
            Non-Visual Picture Properties
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ChartDrawing.ElBlipFill">
            <summary>
            Picture Fill
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ChartDrawing.ElCNvGraphicFramePr">
            <summary>
            Non-Visual Graphic Frame Drawing Properties
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ChartDrawing.ElNvGraphicFramePr">
            <summary>
            Non-Visual Graphic Frame Properties
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ChartDrawing.ElXfrm">
            <summary>
            Graphic Frame Transform
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ChartDrawing.ElCNvGrpSpPr">
            <summary>
            Non-Visual Group Shape Drawing Properties
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ChartDrawing.ElNvGrpSpPr">
            <summary>
            Non-Visual Group Shape Properties
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ChartDrawing.ElGrpSpPr">
            <summary>
            Group Shape Properties
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ChartDrawing.ElSp">
            <summary>
            Shape
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ChartDrawing.ElGrpSp">
            <summary>
            Group Shape
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ChartDrawing.ElGraphicFrame">
            <summary>
            Graphic Frame
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ChartDrawing.ElCxnSp">
            <summary>
            Connector Shape
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ChartDrawing.ElPic">
            <summary>
            Picture
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ChartDrawing.ElX">
            <summary>
            Relative X Coordinate
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ChartDrawing.ElY">
            <summary>
            Relative Y Coordinate
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ChartDrawing.ElFrom">
            <summary>
            Starting Anchor Point
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ChartDrawing.ElTo">
            <summary>
            Ending Anchor Point
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ChartDrawing.ElExt">
            <summary>
            Shape Extent
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ChartDrawing.ElRelSizeAnchor">
            <summary>
            Relative Anchor Shape Size
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ChartDrawing.ElAbsSizeAnchor">
            <summary>
            Absolute Anchor Shape Size
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ChartDrawing.AttrMacro">
            <summary>
            Reference to Custom Function
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ChartDrawing.AttrTextlink">
            <summary>
            Text Link
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ChartDrawing.AttrFLocksText">
            <summary>
            Lock Text
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ChartDrawing.AttrFPublished">
            <summary>
            Publish to Server
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.GraphicalObjectAnimation.ElDgm">
            <summary>
            Diagram to Animate
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.GraphicalObjectAnimation.ElChart">
            <summary>
            Chart to Animate
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.GraphicalObjectAnimation.ElBldDgm">
            <summary>
            Build Diagram
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.GraphicalObjectAnimation.ElBldChart">
            <summary>
            Build Chart
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.GraphicalObjectAnimation.AttrId">
            <summary>
            Identifier
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.GraphicalObjectAnimation.AttrBldStep">
            <summary>
            Animation Build Step
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.GraphicalObjectAnimation.AttrSeriesIdx">
            <summary>
            Series Index
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.GraphicalObjectAnimation.AttrCategoryIdx">
            <summary>
            Category Index
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.GraphicalObjectAnimation.AttrBld">
            <summary>
            Build
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.GraphicalObjectAnimation.AttrRev">
            <summary>
            Reverse Animation
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.GraphicalObjectAnimation.AttrAnimBg">
            <summary>
            Animate Background
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeGeometry.ElGd">
            <summary>
            Shape Guide
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeGeometry.ElPos">
            <summary>
            Position
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeGeometry.ElAhXY">
            <summary>
            XY Adjust Handle
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeGeometry.ElAhPolar">
            <summary>
            Polar Adjust Handle
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeGeometry.ElCxn">
            <summary>
            Shape Connection Site
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeGeometry.ElPt">
            <summary>
            Move end point
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeGeometry.ElClose">
            <summary>
            Close Shape Path
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeGeometry.ElMoveTo">
            <summary>
            Move Path To
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeGeometry.ElLnTo">
            <summary>
            Draw Line To
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeGeometry.ElArcTo">
            <summary>
            Draw Arc To
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeGeometry.ElQuadBezTo">
            <summary>
            Draw Quadratic Bezier Curve To
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeGeometry.ElCubicBezTo">
            <summary>
            Draw Cubic Bezier Curve To
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeGeometry.ElPath">
            <summary>
            Shape Path
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeGeometry.ElAvLst">
            <summary>
            List of Shape Adjust Values
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeGeometry.ElGdLst">
            <summary>
            List of Shape Guides
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeGeometry.ElAhLst">
            <summary>
            List of Shape Adjust Handles
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeGeometry.ElCxnLst">
            <summary>
            List of Shape Connection Sites
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeGeometry.ElRect">
            <summary>
            Shape Text Rectangle
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeGeometry.ElPathLst">
            <summary>
            List of Shape Paths
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeGeometry.ElCustGeom">
            <summary>
            Custom geometry
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeGeometry.ElPrstGeom">
            <summary>
            Preset geometry
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeGeometry.ElPrstTxWarp">
            <summary>
            Preset Text Warp
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeGeometry.AttrName">
            <summary>
            Shape Guide Name
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeGeometry.AttrFmla">
            <summary>
            Shape Guide Formula
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeGeometry.AttrX">
            <summary>
            X-Coordinate
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeGeometry.AttrY">
            <summary>
            Y-Coordinate
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeGeometry.AttrL">
            <summary>
            Left
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeGeometry.AttrT">
            <summary>
            Top
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeGeometry.AttrR">
            <summary>
            Right
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeGeometry.AttrB">
            <summary>
            Bottom Position
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeGeometry.AttrGdRefX">
            <summary>
            Horizontal Adjustment Guide
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeGeometry.AttrMinX">
            <summary>
            Minimum Horizontal Adjustment
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeGeometry.AttrMaxX">
            <summary>
            Maximum Horizontal Adjustment
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeGeometry.AttrGdRefY">
            <summary>
            Vertical Adjustment Guide
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeGeometry.AttrMinY">
            <summary>
            Minimum Vertical Adjustment
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeGeometry.AttrMaxY">
            <summary>
            Maximum Vertical Adjustment
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeGeometry.AttrGdRefR">
            <summary>
            Radial Adjustment Guide
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeGeometry.AttrMinR">
            <summary>
            Minimum Radial Adjustment
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeGeometry.AttrMaxR">
            <summary>
            Maximum Radial Adjustment
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeGeometry.AttrGdRefAng">
            <summary>
            Angle Adjustment Guide
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeGeometry.AttrMinAng">
            <summary>
            Minimum Angle Adjustment
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeGeometry.AttrMaxAng">
            <summary>
            Maximum Angle Adjustment
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeGeometry.AttrAng">
            <summary>
            Connection Site Angle
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeGeometry.AttrId">
            <summary>
            Identifier
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeGeometry.AttrIdx">
            <summary>
            Index
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeGeometry.AttrWR">
            <summary>
            Shape Arc Width Radius
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeGeometry.AttrHR">
            <summary>
            Shape Arc Height Radius
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeGeometry.AttrStAng">
            <summary>
            Shape Arc Start Angle
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeGeometry.AttrSwAng">
            <summary>
            Shape Arc Swing Angle
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeGeometry.AttrW">
            <summary>
            Path Width
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeGeometry.AttrH">
            <summary>
            Path Height
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeGeometry.AttrFill">
            <summary>
            Path Fill
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeGeometry.AttrStroke">
            <summary>
            Path Stroke
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeGeometry.AttrExtrusionOk">
            <summary>
            3D Extrusion Allowed
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeGeometry.AttrPrst">
            <summary>
            Preset Shape
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TextBullet.ElBuClrTx">
            <summary>
            Follow Text
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TextBullet.ElBuClr">
            <summary>
            Color Specified
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TextBullet.ElBuSzTx">
            <summary>
            Bullet Size Follows Text
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TextBullet.ElBuSzPct">
            <summary>
            Bullet Size Percentage
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TextBullet.ElBuSzPts">
            <summary>
            Bullet Size Points
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TextBullet.ElBuFontTx">
            <summary>
            Follow text
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TextBullet.ElBuFont">
            <summary>
            Specified
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TextBullet.ElBlip">
            <summary>
            Blip
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TextBullet.ElBuNone">
            <summary>
            No Bullet
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TextBullet.ElBuAutoNum">
            <summary>
            Auto-Numbered Bullet
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TextBullet.ElBuChar">
            <summary>
            Character Bullet
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TextBullet.ElBuBlip">
            <summary>
            Picture Bullet
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TextBullet.AttrVal">
            <summary>
            Value
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TextBullet.AttrType">
            <summary>
            Bullet Autonumbering Type
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TextBullet.AttrStartAt">
            <summary>
            Start Numbering At
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TextBullet.AttrChar">
            <summary>
            Bullet Character
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Compatibility.ElLegacyDrawing">
            <summary>
            Legacy Drawing Object
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Compatibility.AttrSpid">
            <summary>
            Shape ID
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.GraphicalObjectFormat.ElLn">
            <summary>
            Outline
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeLineProperties.ElNoFill">
            <summary>
            No Fill
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeLineProperties.ElSolidFill">
            <summary>
            Solid Fill
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeLineProperties.ElGradFill">
            <summary>
            Gradient Fill
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeLineProperties.ElPattFill">
            <summary>
            
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeLineProperties.ElRound">
            <summary>
            Round Line Join
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeLineProperties.ElBevel">
            <summary>
            Line Join Bevel
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeLineProperties.ElMiter">
            <summary>
            Miter Line Join
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeLineProperties.ElDs">
            <summary>
            Dash Stop
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeLineProperties.ElPrstDash">
            <summary>
            Preset Dash
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeLineProperties.ElCustDash">
            <summary>
            Custom Dash
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeLineProperties.ElHeadEnd">
            <summary>
            Line Head/End Style
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeLineProperties.ElTailEnd">
            <summary>
            Tail line end style
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeLineProperties.ElExtLst">
            <summary>
            
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeLineProperties.AttrType">
            <summary>
            Line Head/End Type
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeLineProperties.AttrW">
            <summary>
            Width of Head/End
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeLineProperties.AttrLen">
            <summary>
            Length of Head/End
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeLineProperties.AttrLim">
            <summary>
            Miter Join Limit
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeLineProperties.AttrVal">
            <summary>
            Value
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeLineProperties.AttrD">
            <summary>
            Dash Length
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeLineProperties.AttrSp">
            <summary>
            Space Length
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeLineProperties.AttrCap">
            <summary>
            Line Ending Cap Type
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeLineProperties.AttrCmpd">
            <summary>
            Compound Line Type
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeLineProperties.AttrAlgn">
            <summary>
            Stroke Alignment
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TextCharacter.ElULnTx">
            <summary>
            Underline Follows Text
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TextCharacter.ElULn">
            <summary>
            Underline Stroke
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TextCharacter.ElUFillTx">
            <summary>
            Underline Fill Properties Follow Text
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TextCharacter.ElUFill">
            <summary>
            Underline Fill
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TextCharacter.ElLn">
            <summary>
            Line
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TextCharacter.ElHighlight">
            <summary>
            Highlight Color
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TextCharacter.ElLatin">
            <summary>
            Latin Font
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TextCharacter.ElEa">
            <summary>
            East Asian Font
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TextCharacter.ElCs">
            <summary>
            Complex Script Font
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TextCharacter.ElSym">
            <summary>
            Symbol Font
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TextCharacter.ElHlinkClick">
            <summary>
            Click Hyperlink
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TextCharacter.ElHlinkMouseOver">
            <summary>
            Mouse-Over Hyperlink
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TextCharacter.ElExtLst">
            <summary>
            
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TextCharacter.AttrTypeface">
            <summary>
            Text Typeface
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TextCharacter.AttrPanose">
            <summary>
            Panose Setting
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TextCharacter.AttrPitchFamily">
            <summary>
            Similar Font Family
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TextCharacter.AttrCharset">
            <summary>
            Similar Character Set
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TextCharacter.AttrKumimoji">
            <summary>
            Kumimoji
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TextCharacter.AttrLang">
            <summary>
            Language ID
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TextCharacter.AttrAltLang">
            <summary>
            Alternative Language
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TextCharacter.AttrSz">
            <summary>
            Font Size
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TextCharacter.AttrB">
            <summary>
            Bold
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TextCharacter.AttrI">
            <summary>
            Italics
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TextCharacter.AttrU">
            <summary>
            Underline
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TextCharacter.AttrStrike">
            <summary>
            Strikethrough
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TextCharacter.AttrKern">
            <summary>
            Kerning
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TextCharacter.AttrCap">
            <summary>
            Capitalization
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TextCharacter.AttrSpc">
            <summary>
            Spacing
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TextCharacter.AttrNormalizeH">
            <summary>
            Normalize Heights
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TextCharacter.AttrBaseline">
            <summary>
            Baseline
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TextCharacter.AttrNoProof">
            <summary>
            No Proofing
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TextCharacter.AttrDirty">
            <summary>
            Dirty
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TextCharacter.AttrErr">
            <summary>
            Spelling Error
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TextCharacter.AttrSmtClean">
            <summary>
            SmartTag Clean
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TextCharacter.AttrSmtId">
            <summary>
            SmartTag ID
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TextCharacter.AttrBmk">
            <summary>
            Bookmark Link Target
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramColorTransform.ElCat">
            <summary>
            Color Transform Category
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramColorTransform.ElFillClrLst">
            <summary>
            Fill Color List
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramColorTransform.ElLinClrLst">
            <summary>
            Line Color List
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramColorTransform.ElEffectClrLst">
            <summary>
            Effect Color List
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramColorTransform.ElTxLinClrLst">
            <summary>
            Text Line Color List
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramColorTransform.ElTxFillClrLst">
            <summary>
            Text Fill Color List
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramColorTransform.ElTxEffectClrLst">
            <summary>
            Text Effect Color List
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramColorTransform.ElExtLst">
            <summary>
            
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramColorTransform.ElTitle">
            <summary>
            Title
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramColorTransform.ElDesc">
            <summary>
            Description
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramColorTransform.ElCatLst">
            <summary>
            Color Transform Category List
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramColorTransform.ElStyleLbl">
            <summary>
            Style Label
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramColorTransform.ElColorsDef">
            <summary>
            Color Transform Definitions
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramColorTransform.ElColorsDefHdr">
            <summary>
            Color Transform Header
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramColorTransform.ElColorsDefHdrLst">
            <summary>
            Color Transform Header List
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramColorTransform.AttrLang">
            <summary>
            Language
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramColorTransform.AttrVal">
            <summary>
            Description Value
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramColorTransform.AttrType">
            <summary>
            Category Type
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramColorTransform.AttrPri">
            <summary>
            Priority
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramColorTransform.AttrMeth">
            <summary>
            Color Application Method Type
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramColorTransform.AttrHueDir">
            <summary>
            Hue Direction
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramColorTransform.AttrName">
            <summary>
            Name
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramColorTransform.AttrUniqueId">
            <summary>
            Unique ID
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramColorTransform.AttrMinVer">
            <summary>
            Minimum Version
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramColorTransform.AttrResId">
            <summary>
            Resource ID
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Gvml.ElTxBody">
            <summary>
            Shape Text Body
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Gvml.ElUseSpRect">
            <summary>
            Use Shape Text Rectangle
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Gvml.ElXfrm">
            <summary>
            
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Gvml.ElExtLst">
            <summary>
            
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Gvml.ElCNvPr">
            <summary>
            
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Gvml.ElCNvSpPr">
            <summary>
            Non-Visual Shape Drawing Properties
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Gvml.ElNvSpPr">
            <summary>
            Non-Visual Properties for a Shape
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Gvml.ElSpPr">
            <summary>
            Visual Properties
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Gvml.ElTxSp">
            <summary>
            Text Shape
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Gvml.ElStyle">
            <summary>
            Style
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Gvml.ElCNvCxnSpPr">
            <summary>
            Non-Visual Connector Shape Drawing Properties
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Gvml.ElNvCxnSpPr">
            <summary>
            Non-Visual Properties for a Connection Shape
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Gvml.ElCNvPicPr">
            <summary>
            Non-Visual Picture Drawing Properties
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Gvml.ElNvPicPr">
            <summary>
            Non-Visual Properties for a Picture
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Gvml.ElBlipFill">
            <summary>
            Picture Fill
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Gvml.ElCNvGraphicFramePr">
            <summary>
            Non-Visual Graphic Frame Drawing Properties
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Gvml.ElNvGraphicFramePr">
            <summary>
            Non-Visual Properties for a Graphic Frame
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Gvml.ElCNvGrpSpPr">
            <summary>
            Non-Visual Group Shape Drawing Properties
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Gvml.ElNvGrpSpPr">
            <summary>
            Non-Visual Properties for a Group Shape
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Gvml.ElGrpSpPr">
            <summary>
            Visual Group Shape Properties
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Gvml.ElSp">
            <summary>
            Shape
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Gvml.ElCxnSp">
            <summary>
            Connection Shape
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Gvml.ElPic">
            <summary>
            Picture
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Gvml.ElGraphicFrame">
            <summary>
            Graphic Frame
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Gvml.ElGrpSp">
            <summary>
            Group shape
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TextParagraph.ElTab">
            <summary>
            Tab Stop
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TextParagraph.ElRPr">
            <summary>
            Text Run Properties
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TextParagraph.ElSpcPct">
            <summary>
            Spacing Percent
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TextParagraph.ElSpcPts">
            <summary>
            Spacing Points
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TextParagraph.ElLnSpc">
            <summary>
            Line Spacing
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TextParagraph.ElSpcBef">
            <summary>
            Space Before
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TextParagraph.ElSpcAft">
            <summary>
            Space After
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TextParagraph.ElTabLst">
            <summary>
            Tab List
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TextParagraph.ElDefRPr">
            <summary>
            Default Text Run Properties
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TextParagraph.ElExtLst">
            <summary>
            
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TextParagraph.ElPPr">
            <summary>
            Text Paragraph Properties
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TextParagraph.ElT">
            <summary>
            
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TextParagraph.ElR">
            <summary>
            Text Run
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TextParagraph.ElBr">
            <summary>
            Text Line Break
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TextParagraph.ElFld">
            <summary>
            Text Field
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TextParagraph.AttrVal">
            <summary>
            Value
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TextParagraph.AttrPos">
            <summary>
            Tab Position
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TextParagraph.AttrAlgn">
            <summary>
            Tab Alignment
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TextParagraph.AttrMarL">
            <summary>
            Left Margin
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TextParagraph.AttrMarR">
            <summary>
            Right Margin
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TextParagraph.AttrLvl">
            <summary>
            Level
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TextParagraph.AttrIndent">
            <summary>
            Indent
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TextParagraph.AttrDefTabSz">
            <summary>
            Default Tab Size
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TextParagraph.AttrRtl">
            <summary>
            Right To Left
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TextParagraph.AttrEaLnBrk">
            <summary>
            East Asian Line Break
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TextParagraph.AttrFontAlgn">
            <summary>
            Font Alignment
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TextParagraph.AttrLatinLnBrk">
            <summary>
            Latin Line Break
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TextParagraph.AttrHangingPunct">
            <summary>
            Hanging Punctuation
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TextParagraph.AttrId">
            <summary>
            Field ID
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TextParagraph.AttrType">
            <summary>
            Field Type
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramDataModel.ElPrSet">
            <summary>
            Property Set
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramDataModel.ElSpPr">
            <summary>
            Shape Properties
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramDataModel.ElT">
            <summary>
            Text Body
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramDataModel.ElExtLst">
            <summary>
            
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramDataModel.ElPt">
            <summary>
            Point
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramDataModel.ElCxn">
            <summary>
            Connection
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramDataModel.ElPtLst">
            <summary>
            Point List
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramDataModel.ElCxnLst">
            <summary>
            Connection List
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramDataModel.ElBg">
            <summary>
            Background Formatting
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramDataModel.ElWhole">
            <summary>
            Whole E2O Formatting
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramDataModel.ElDataModel">
            <summary>
            Data Model
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramDataModel.AttrModelId">
            <summary>
            Model Identifier
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramDataModel.AttrType">
            <summary>
            Point Type
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramDataModel.AttrCxnId">
            <summary>
            Connection Identifier
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramDataModel.AttrSrcId">
            <summary>
            Source Identifier
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramDataModel.AttrDestId">
            <summary>
            Destination Identifier
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramDataModel.AttrSrcOrd">
            <summary>
            Source Position
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramDataModel.AttrDestOrd">
            <summary>
            Destination Position
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramDataModel.AttrParTransId">
            <summary>
            Parent Transition Identifier
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramDataModel.AttrSibTransId">
            <summary>
            Sibling Transition Identifier
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramDataModel.AttrPresId">
            <summary>
            Presentation Identifier
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.LockedCanvas.ElLockedCanvas">
            <summary>
            Locked Canvas Container
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeProperties.ElXfrm">
            <summary>
            2D Transform for Individual Objects
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeProperties.ElLn">
            <summary>
            
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeProperties.ElScene3d">
            <summary>
            
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeProperties.ElSp3d">
            <summary>
            
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeProperties.ElExtLst">
            <summary>
            
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeProperties.AttrBwMode">
            <summary>
            Black and White Mode
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TextRun.ElRPr">
            <summary>
            Text Character Properties
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.TextRun.ElT">
            <summary>
            Text String
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramDefinition.ElExtLst">
            <summary>
            
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramDefinition.ElConstr">
            <summary>
            Constraint
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramDefinition.ElRule">
            <summary>
            Rule
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramDefinition.ElAdj">
            <summary>
            Shape Adjust
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramDefinition.ElAdjLst">
            <summary>
            Shape Adjust List
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramDefinition.ElParam">
            <summary>
            Parameter
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramDefinition.ElAlg">
            <summary>
            Algorithm
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramDefinition.ElShape">
            <summary>
            Shape
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramDefinition.ElPresOf">
            <summary>
            Presentation Of
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramDefinition.ElConstrLst">
            <summary>
            Constraint List
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramDefinition.ElRuleLst">
            <summary>
            Rule List
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramDefinition.ElVarLst">
            <summary>
            Variable List
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramDefinition.ElForEach">
            <summary>
            For Each
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramDefinition.ElLayoutNode">
            <summary>
            Layout Node
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramDefinition.ElChoose">
            <summary>
            Choose Element
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramDefinition.ElIf">
            <summary>
            If
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramDefinition.ElElse">
            <summary>
            Else
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramDefinition.ElDataModel">
            <summary>
            Data Model
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramDefinition.ElCat">
            <summary>
            Category
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramDefinition.ElTitle">
            <summary>
            Title
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramDefinition.ElDesc">
            <summary>
            Description
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramDefinition.ElCatLst">
            <summary>
            Category List
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramDefinition.ElSampData">
            <summary>
            Sample Data
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramDefinition.ElStyleData">
            <summary>
            Style Data
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramDefinition.ElClrData">
            <summary>
            Color Transform Sample Data
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramDefinition.ElLayoutDef">
            <summary>
            Layout Definition
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramDefinition.ElLayoutDefHdr">
            <summary>
            Layout Definition Header
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramDefinition.ElLayoutDefHdrLst">
            <summary>
            Diagram Layout Header List
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramDefinition.ElRelIds">
            <summary>
            Explicit Relationships to Diagram Parts
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramDefinition.AttrAxis">
            <summary>
            Axis
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramDefinition.AttrPtType">
            <summary>
            Data Point Type
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramDefinition.AttrHideLastTrans">
            <summary>
            Hide Last Transition
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramDefinition.AttrSt">
            <summary>
            Start
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramDefinition.AttrCnt">
            <summary>
            Count
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramDefinition.AttrStep">
            <summary>
            Step
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramDefinition.AttrType">
            <summary>
            Constraint Type
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramDefinition.AttrFor">
            <summary>
            For
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramDefinition.AttrForName">
            <summary>
            For Name
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramDefinition.AttrRefType">
            <summary>
            Reference Type
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramDefinition.AttrRefFor">
            <summary>
            Reference For
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramDefinition.AttrRefForName">
            <summary>
            Reference For Name
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramDefinition.AttrRefPtType">
            <summary>
            Reference Point Type
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramDefinition.AttrOp">
            <summary>
            Operator
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramDefinition.AttrVal">
            <summary>
            Value
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramDefinition.AttrFact">
            <summary>
            Factor
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramDefinition.AttrMax">
            <summary>
            Max Value
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramDefinition.AttrIdx">
            <summary>
            Adjust Handle Index
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramDefinition.AttrRot">
            <summary>
            Rotation
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramDefinition.AttrZOrderOff">
            <summary>
            Z-Order Offset
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramDefinition.AttrHideGeom">
            <summary>
            Hide Geometry
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramDefinition.AttrLkTxEntry">
            <summary>
            Prevent Text Editing
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramDefinition.AttrBlipPhldr">
            <summary>
            Image Placeholder
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramDefinition.AttrRev">
            <summary>
            Revision Number
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramDefinition.AttrName">
            <summary>
            Name
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramDefinition.AttrStyleLbl">
            <summary>
            Style Label
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramDefinition.AttrChOrder">
            <summary>
            Child Order
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramDefinition.AttrMoveWith">
            <summary>
            Move With
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramDefinition.AttrRef">
            <summary>
            Reference
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramDefinition.AttrFunc">
            <summary>
            Function
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramDefinition.AttrArg">
            <summary>
            Argument
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramDefinition.AttrUseDef">
            <summary>
            Use Default
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramDefinition.AttrPri">
            <summary>
            Priority
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramDefinition.AttrLang">
            <summary>
            Language
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramDefinition.AttrUniqueId">
            <summary>
            Unique Identifier
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramDefinition.AttrMinVer">
            <summary>
            Minimum Version
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramDefinition.AttrDefStyle">
            <summary>
            Default Style
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramDefinition.AttrResId">
            <summary>
            Resource Identifier
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Picture.ElCNvPr">
            <summary>
            Non-Visual Drawing Properties
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Picture.ElCNvPicPr">
            <summary>
            Non-Visual Picture Drawing Properties
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Picture.ElNvPicPr">
            <summary>
            Non-Visual Picture Properties
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Picture.ElBlipFill">
            <summary>
            Picture Fill
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Picture.ElSpPr">
            <summary>
            Shape Properties
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Picture.ElPic">
            <summary>
            Picture
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeStyle.ElLnRef">
            <summary>
            
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeStyle.ElFillRef">
            <summary>
            
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeStyle.ElEffectRef">
            <summary>
            
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeStyle.ElFontRef">
            <summary>
            Font Reference
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.ShapeStyle.AttrIdx">
            <summary>
            Style Matrix Index
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.WordprocessingDrawing.ElExtent">
            <summary>
            Drawing Object Size
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.WordprocessingDrawing.ElEffectExtent">
            <summary>
            Inline Wrapping Extent
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.WordprocessingDrawing.ElDocPr">
            <summary>
            Drawing Object Non-Visual Properties
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.WordprocessingDrawing.ElCNvGraphicFramePr">
            <summary>
            Common DrawingML Non-Visual Properties
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.WordprocessingDrawing.ElStart">
            <summary>
            Wrapping Polygon Start
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.WordprocessingDrawing.ElLineTo">
            <summary>
            Wrapping Polygon Line End Position
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.WordprocessingDrawing.ElWrapPolygon">
            <summary>
            Tight Wrapping Extents Polygon
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.WordprocessingDrawing.ElWrapNone">
            <summary>
            No Text Wrapping
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.WordprocessingDrawing.ElWrapSquare">
            <summary>
            Square Wrapping
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.WordprocessingDrawing.ElWrapTight">
            <summary>
            Tight Wrapping
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.WordprocessingDrawing.ElWrapThrough">
            <summary>
            Through Wrapping
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.WordprocessingDrawing.ElWrapTopAndBottom">
            <summary>
            Top and Bottom Wrapping
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.WordprocessingDrawing.ElAlign">
            <summary>
            Relative Horizontal Alignment
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.WordprocessingDrawing.ElPosOffset">
            <summary>
            Absolute Position Offset
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.WordprocessingDrawing.ElSimplePos">
            <summary>
            Simple Positioning Coordinates
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.WordprocessingDrawing.ElPositionH">
            <summary>
            Horizontal Positioning
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.WordprocessingDrawing.ElPositionV">
            <summary>
            Vertical Positioning
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.WordprocessingDrawing.ElInline">
            <summary>
            Inline DrawingML Object
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.WordprocessingDrawing.ElAnchor">
            <summary>
            Anchor for Floating DrawingML Object
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.WordprocessingDrawing.AttrL">
            <summary>
            Additional Extent on Left Edge
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.WordprocessingDrawing.AttrT">
            <summary>
            Additional Extent on Top Edge
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.WordprocessingDrawing.AttrR">
            <summary>
            Additional Extent on Right Edge
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.WordprocessingDrawing.AttrB">
            <summary>
            Additional Extent on Bottom Edge
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.WordprocessingDrawing.AttrDistT">
            <summary>
            Distance From Text on Top Edge
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.WordprocessingDrawing.AttrDistB">
            <summary>
            Distance From Text on Bottom Edge
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.WordprocessingDrawing.AttrDistL">
            <summary>
            Distance From Text on Left Edge
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.WordprocessingDrawing.AttrDistR">
            <summary>
            Distance From Text on Right Edge
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.WordprocessingDrawing.AttrEdited">
            <summary>
            Wrapping Points Modified
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.WordprocessingDrawing.AttrWrapText">
            <summary>
            Text Wrapping Location
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.WordprocessingDrawing.AttrRelativeFrom">
            <summary>
            Horizontal Position Relative Base
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.WordprocessingDrawing.AttrRelativeHeight">
            <summary>
            Relative Z-Ordering Position
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.WordprocessingDrawing.AttrBehindDoc">
            <summary>
            Display Behind Document Text
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.WordprocessingDrawing.AttrLocked">
            <summary>
            Lock Anchor
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.WordprocessingDrawing.AttrLayoutInCell">
            <summary>
            Layout In Table Cell
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.WordprocessingDrawing.AttrHidden">
            <summary>
            Hidden
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.WordprocessingDrawing.AttrAllowOverlap">
            <summary>
            Allow Objects to Overlap
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramElementPropertySet.ElPresLayoutVars">
            <summary>
            Presentation Layout Variables
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramElementPropertySet.ElStyle">
            <summary>
            Shape Style
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramElementPropertySet.AttrPresAssocID">
            <summary>
            Presentation Element Identifier
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramElementPropertySet.AttrPresName">
            <summary>
            Presentation Name
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramElementPropertySet.AttrPresStyleLbl">
            <summary>
            Presentation Style Label
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramElementPropertySet.AttrPresStyleIdx">
            <summary>
            Presentation Style Index
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramElementPropertySet.AttrPresStyleCnt">
            <summary>
            Presentation Style Count
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramElementPropertySet.AttrLoTypeId">
            <summary>
            Current Diagram Type
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramElementPropertySet.AttrLoCatId">
            <summary>
            Current Diagram Category
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramElementPropertySet.AttrQsTypeId">
            <summary>
            Current Style Type
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramElementPropertySet.AttrQsCatId">
            <summary>
            Current Style Category
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramElementPropertySet.AttrCsTypeId">
            <summary>
            Color Transform Type Identifier
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramElementPropertySet.AttrCsCatId">
            <summary>
            Color Transform Category
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramElementPropertySet.AttrCoherent3DOff">
            <summary>
            Coherent 3D Behavior
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramElementPropertySet.AttrPhldrT">
            <summary>
            Placeholder Text
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramElementPropertySet.AttrPhldr">
            <summary>
            Placeholder
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramElementPropertySet.AttrCustAng">
            <summary>
            Custom Rotation
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramElementPropertySet.AttrCustFlipVert">
            <summary>
            Custom Vertical Flip
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramElementPropertySet.AttrCustFlipHor">
            <summary>
            Custom Horizontal Flip
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramElementPropertySet.AttrCustSzX">
            <summary>
            Fixed Width Override
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramElementPropertySet.AttrCustSzY">
            <summary>
            Fixed Height Override
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramElementPropertySet.AttrCustScaleX">
            <summary>
            Width Scale
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramElementPropertySet.AttrCustScaleY">
            <summary>
            Height Scale
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramElementPropertySet.AttrCustT">
            <summary>
            Text Changed
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramElementPropertySet.AttrCustLinFactX">
            <summary>
            Custom Factor Width
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramElementPropertySet.AttrCustLinFactY">
            <summary>
            Custom Factor Height
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramElementPropertySet.AttrCustLinFactNeighborX">
            <summary>
            Neighbor Offset Width
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramElementPropertySet.AttrCustLinFactNeighborY">
            <summary>
            Neighbor Offset Height
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramElementPropertySet.AttrCustRadScaleRad">
            <summary>
            Radius Scale
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramElementPropertySet.AttrCustRadScaleInc">
            <summary>
            Include Angle Scale
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Shape3DCamera.ElRot">
            <summary>
            Rotation
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Shape3DCamera.AttrPrst">
            <summary>
            Preset Camera Type
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Shape3DCamera.AttrFov">
            <summary>
            Field of View
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Shape3DCamera.AttrZoom">
            <summary>
            Zoom
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.SpreadsheetDrawing.ElCNvPr">
            <summary>
            Non-Visual Drawing Properties
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.SpreadsheetDrawing.ElCNvSpPr">
            <summary>
            Connection Non-Visual Shape Properties
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.SpreadsheetDrawing.ElNvSpPr">
            <summary>
            Non-Visual Properties for a Shape
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.SpreadsheetDrawing.ElSpPr">
            <summary>
            Shape Properties
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.SpreadsheetDrawing.ElStyle">
            <summary>
            
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.SpreadsheetDrawing.ElTxBody">
            <summary>
            Shape Text Body
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.SpreadsheetDrawing.ElCNvCxnSpPr">
            <summary>
            Non-Visual Connector Shape Drawing Properties
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.SpreadsheetDrawing.ElNvCxnSpPr">
            <summary>
            Non-Visual Properties for a Connection Shape
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.SpreadsheetDrawing.ElCNvPicPr">
            <summary>
            Non-Visual Picture Drawing Properties
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.SpreadsheetDrawing.ElNvPicPr">
            <summary>
            Non-Visual Properties for a Picture
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.SpreadsheetDrawing.ElBlipFill">
            <summary>
            Picture Fill
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.SpreadsheetDrawing.ElCNvGraphicFramePr">
            <summary>
            Non-Visual Graphic Frame Drawing Properties
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.SpreadsheetDrawing.ElNvGraphicFramePr">
            <summary>
            Non-Visual Properties for a Graphic Frame
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.SpreadsheetDrawing.ElXfrm">
            <summary>
            2D Transform for Graphic Frames
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.SpreadsheetDrawing.ElCNvGrpSpPr">
            <summary>
            Non-Visual Group Shape Drawing Properties
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.SpreadsheetDrawing.ElNvGrpSpPr">
            <summary>
            Non-Visual Properties for a Group Shape
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.SpreadsheetDrawing.ElGrpSpPr">
            <summary>
            Group Shape Properties
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.SpreadsheetDrawing.ElSp">
            <summary>
            Shape
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.SpreadsheetDrawing.ElGrpSp">
            <summary>
            Group Shape
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.SpreadsheetDrawing.ElGraphicFrame">
            <summary>
            
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.SpreadsheetDrawing.ElCxnSp">
            <summary>
            Connection Shape
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.SpreadsheetDrawing.ElPic">
            <summary>
            Picture
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.SpreadsheetDrawing.ElCol">
            <summary>
            Column)
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.SpreadsheetDrawing.ElColOff">
            <summary>
            Column Offset
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.SpreadsheetDrawing.ElRow">
            <summary>
            Row
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.SpreadsheetDrawing.ElRowOff">
            <summary>
            Row Offset
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.SpreadsheetDrawing.ElFrom">
            <summary>
            Starting Anchor Point
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.SpreadsheetDrawing.ElTo">
            <summary>
            Ending Anchor Point
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.SpreadsheetDrawing.ElClientData">
            <summary>
            Client Data
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.SpreadsheetDrawing.ElExt">
            <summary>
            
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.SpreadsheetDrawing.ElPos">
            <summary>
            Position
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.SpreadsheetDrawing.ElTwoCellAnchor">
            <summary>
            Two Cell Anchor Shape Size
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.SpreadsheetDrawing.ElOneCellAnchor">
            <summary>
            One Cell Anchor Shape Size
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.SpreadsheetDrawing.ElAbsoluteAnchor">
            <summary>
            Absolute Anchor Shape Size
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.SpreadsheetDrawing.ElWsDr">
            <summary>
            Worksheet Drawing
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.SpreadsheetDrawing.AttrFLocksWithSheet">
            <summary>
            Locks With Sheet Flag
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.SpreadsheetDrawing.AttrFPrintsWithSheet">
            <summary>
            Prints With Sheet Flag
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.SpreadsheetDrawing.AttrMacro">
            <summary>
            Reference to Custom Function
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.SpreadsheetDrawing.AttrTextlink">
            <summary>
            Text Link
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.SpreadsheetDrawing.AttrFLocksText">
            <summary>
            Lock Text Flag
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.SpreadsheetDrawing.AttrFPublished">
            <summary>
            Publish to Server Flag
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.SpreadsheetDrawing.AttrEditAs">
            <summary>
            Positioning and Resizing Behaviors
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramLayoutVariables.ElOrgChart">
            <summary>
            Show Organization Chart User Interface
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramLayoutVariables.ElChMax">
            <summary>
            Maximum Children
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramLayoutVariables.ElChPref">
            <summary>
            Preferred Number of Children
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramLayoutVariables.ElBulletEnabled">
            <summary>
            Show Insert Bullet
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramLayoutVariables.ElDir">
            <summary>
            Diagram Direction
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramLayoutVariables.ElHierBranch">
            <summary>
            Organization Chart Branch Style
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramLayoutVariables.ElAnimOne">
            <summary>
            One by One Animation String
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramLayoutVariables.ElAnimLvl">
            <summary>
            Level Animation
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramLayoutVariables.ElResizeHandles">
            <summary>
            Shape Resize Style
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.DiagramLayoutVariables.AttrVal">
            <summary>
            Show Organization Chart User Interface Value
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Shape3DLighting.ElRot">
            <summary>
            Rotation
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Shape3DLighting.AttrRig">
            <summary>
            Rig Preset
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.Shape3DLighting.AttrDir">
            <summary>
            Direction
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.StyleDefaults.ElSpPr">
            <summary>
            Visual Properties
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.StyleDefaults.ElBodyPr">
            <summary>
            
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.StyleDefaults.ElLstStyle">
            <summary>
            
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.StyleDefaults.ElStyle">
            <summary>
            
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.StyleDefaults.ElExtLst">
            <summary>
            
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.StyleDefaults.ElSpDef">
            <summary>
            Shape Default
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.StyleDefaults.ElLnDef">
            <summary>
            Line Default
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.OpenXmlLib.DrawingML.Dml.StyleDefaults.ElTxDef">
            <summary>
            Text Default
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.OpenXmlLib.OpenXmlPartContainer.ReferencePart``1(``0)">
            <summary>
            Add a part reference without actually managing the part.
            </summary>
        </member>
        <member name="T:DIaLOGIKa.b2xtranslator.StructuredStorage.Common.AbstractDirectoryEntry">
            <summary>
            Abstract class for a directory entry in a structured storage.
            Athor: math
            </summary>
        </member>
        <member name="T:DIaLOGIKa.b2xtranslator.StructuredStorage.Common.AbstractHeader">
            <summary>
            Abstract class fo the header of a compound file.
            Author: math
            </summary>
        </member>
        <member name="T:DIaLOGIKa.b2xtranslator.StructuredStorage.Common.AbstractIOHandler">
            <summary>
            Abstract class for input and putput handlers.
            Author: math
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Common.AbstractIOHandler.InitBitConverter(System.Boolean)">
            <summary>
            Initializes the internal bit converter
            </summary>
            <param name="isLittleEndian">flag whether big endian or little endian is used</param>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Common.AbstractIOHandler.SetHeaderReference(DIaLOGIKa.b2xtranslator.StructuredStorage.Common.AbstractHeader)">
            <summary>
            Initializes the reference to the header
            </summary>
            <param name="header"></param>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Common.AbstractIOHandler.CloseStream">
            <summary>
            Closes the file associated with this handler
            </summary>
        </member>
        <member name="T:DIaLOGIKa.b2xtranslator.StructuredStorage.Common.MagicNumberException">
            <summary>
            Exceptions used
            Author: math
            </summary>
        </member>
        <member name="T:DIaLOGIKa.b2xtranslator.StructuredStorage.Common.SectorId">
            <summary>
            Constants used to identify sectors in fat, minifat and directory
            </summary>
        </member>
        <member name="T:DIaLOGIKa.b2xtranslator.StructuredStorage.Common.Measures">
            <summary>
            Size constants 
            </summary>
        </member>
        <member name="T:DIaLOGIKa.b2xtranslator.StructuredStorage.Common.DirectoryEntryType">
            <summary>
            Type of a directory entry
            </summary>
        </member>
        <member name="T:DIaLOGIKa.b2xtranslator.StructuredStorage.Common.DirectoryEntryColor">
            <summary>
            Color of a directory entry in the red-black-tree
            </summary>
        </member>
        <member name="T:DIaLOGIKa.b2xtranslator.StructuredStorage.Common.InternalBitConverter">
            <summary>
            Wrapper of the class BitConverter in order to support big endian
            Author: math
            </summary>
        </member>
        <member name="T:DIaLOGIKa.b2xtranslator.StructuredStorage.Common.MaskingHandler">
            <summary>
            Provides methods for masking/unmasking strings in a path
            Author: math
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Common.MaskingHandler.Mask(System.String)">
            <summary>
            Masks the given string
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Common.MaskingHandler.UnMask(System.String)">
            <summary>
            Unmasks the given string
            </summary>
        </member>
        <member name="T:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.AbstractFat">
            <summary>
            Abstract class of a Fat in a compound file
            Author: math
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.AbstractFat.#ctor(DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.Header,DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.InputHandler)">
            <summary>
            Constructor
            </summary>
            <param name="header">Handle to the header of the compound file</param>
            <param name="fileHandler">Handle to the file handler of the compound file</param>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.AbstractFat.GetSectorChain(System.UInt32,System.UInt64,System.String)">
            <summary>
            Returns the sectors in a chain which starts at a given sector
            </summary>
            <param name="startSector">The start sector of the chain</param>
            <param name="maxCount">The maximum count of sectors in a chain</param>
            <param name="name">The name of a chain</param>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.AbstractFat.GetSectorChain(System.UInt32,System.UInt64,System.String,System.Boolean)">
            <summary>
            Returns the sectors in a chain which starts at a given sector
            </summary>
            <param name="startSector">The start sector of the chain</param>
            <param name="maxCount">The maximum count of sectors in a chain</param>
            <param name="name">The name of a chain</param>
            <param name="immediateCycleCheck">Flag whether to check for cycles in every loop</param>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.AbstractFat.UncheckedRead(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Reads bytes into an array
            </summary>
            <param name="array">The array to read to</param>
            <param name="offset">The offset in the array to read to</param>
            <param name="count">The number of bytes to read</param>
            <returns>The number of bytes read</returns>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.AbstractFat.UncheckedReadByte">
            <summary>
            Reads a byte at the current position of the file stream.
            Advances the stream pointer accordingly.
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.AbstractFat.GetNextSectorInChain(System.UInt32)">
            <summary>
            Returns the next sector in a chain
            </summary>
            <param name="currentSector">The current sector in the chain</param>
            <returns>The next sector in the chain</returns>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.AbstractFat.SeekToPositionInSector(System.Int64,System.Int64)">
            <summary>
            Seeks to a given position in a sector
            </summary>
            <param name="sector">The sector to seek to</param>
            <param name="position">The position in the sector to seek to</param>
            <returns></returns>
        </member>
        <member name="T:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.DirectoryEntry">
            <summary>
            Encapsulates a directory entry
            Author: math
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.DirectoryEntry.#ctor(DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.Header,DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.InputHandler,System.UInt32,System.String)">
            <summary>
            Constructor
            </summary>
            <param name="header">Handle to the header of the compound file</param>
            <param name="fileHandler">Handle to the file handler of the compound file</param>
            <param name="sid">The sid of the directory entry</param>
            <param name="path"></param>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.DirectoryEntry.ReadDirectoryEntry">
            <summary>
            Reads the values of the directory entry. The position of the file handler must be at the start of a directory entry.
            </summary>
        </member>
        <member name="T:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.DirectoryTree">
            <summary>
            Represents the directory structure of a compound file
            Author: math
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.DirectoryTree.#ctor(DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.Fat,DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.Header,DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.InputHandler)">
            <summary>
            Constructor
            </summary>
            <param name="fat">Handle to the Fat of the compound file</param>
            <param name="header">Handle to the header of the compound file</param>
            <param name="fileHandler">Handle to the file handler of the compound file</param>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.DirectoryTree.Init(System.UInt32)">
            <summary>
            Inits the directory
            </summary>
            <param name="startSector">The sector containing the root of the directory</param>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.DirectoryTree.GetAllDirectoryEntriesRecursive(System.UInt32,System.String)">
            <summary>
            Determines the directory _entries in a compound file recursively
            </summary>
            <param name="sid">start sid</param>
            <param name="path"></param>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.DirectoryTree.ReadDirectoryEntry(System.UInt32,System.String)">
            <summary>
            Returns a directory entry for a given sid
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.DirectoryTree.SeekToDirectoryEntry(System.UInt32)">
            <summary>
            Seeks to the start sector of the directory entry of the given sid
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.DirectoryTree.GetDirectoryEntry(System.String,System.Boolean)">
            <summary>
            Returns the directory entry with the given name/path
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.DirectoryTree.GetDirectoryEntry(System.UInt32)">
            <summary>
            Returns the directory entry with the given sid
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.DirectoryTree.GetMiniStreamStart">
            <summary>
            Returns the start sector of the mini stream
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.DirectoryTree.GetSizeOfMiniStream">
            <summary>
            Returns the size of the mini stream
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.DirectoryTree.GetNamesOfAllEntries">
            <summary>
            Returns all entry names contained in a compound file
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.DirectoryTree.GetPathsOfAllEntries">
            <summary>
            Returns all entry paths contained in a compound file
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.DirectoryTree.GetNamesOfAllStreamEntries">
            <summary>
            Returns all stream entry names contained in a compound file
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.DirectoryTree.GetPathsOfAllStreamEntries">
            <summary>
            Returns all stream entry paths contained in a compound file
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.DirectoryTree.GetAllEntries">
            <summary>
            Returns all _entries contained in a compound file
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.DirectoryTree.GetAllStreamEntries">
            <summary>
            Returns all stream _entries contained in a compound file
            </summary>
        </member>
        <member name="T:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.Fat">
            <summary>
            Represents the Fat in a compound file
            Author: math
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.Fat.#ctor(DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.Header,DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.InputHandler)">
            <summary>
            Constructor
            </summary>
            <param name="header">Handle to the header of the compound file</param>
            <param name="fileHandler">Handle to the file handler of the compound file</param>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.Fat.SeekToPositionInSector(System.Int64,System.Int64)">
            <summary>
            Seeks to a given position in a sector
            </summary>
            <param name="sector">The sector to seek to</param>
            <param name="position">The position in the sector to seek to</param>
            <returns>The new position in the stream.</returns>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.Fat.GetNextSectorInChain(System.UInt32)">
            <summary>
            Returns the next sector in a chain
            </summary>
            <param name="currentSector">The current sector in the chain</param>
            <returns>The next sector in the chain</returns>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.Fat.Init">
            <summary>
            Initalizes the Fat
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.Fat.ReadFirst109SectorsUsedByFAT">
            <summary>
            Reads the first 109 sectors of the Fat stored in the header
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.Fat.ReadSectorsUsedByFatFromDiFat">
            <summary>
            Reads the sectors of the Fat which are stored in the DiFat
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.Fat.CheckConsistency">
            <summary>
            Checks whether the sizes specified in the header matches the actual sizes
            </summary>
        </member>
        <member name="T:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.Header">
            <summary>
            Encapsulates the header of a compound file
            Author: math
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.Header.#ctor(DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.InputHandler)">
            <summary>
            Constructor
            </summary>
            <param name="fileHandler">The Handle to the file handler of the compound file</param>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.Header.ReadHeader">
            <summary>
            Reads the header from the file stream
            </summary>
        </member>
        <member name="T:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.InputHandler">
            <summary>
            Provides methods for accessing the file stream
            Author: math
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.InputHandler.#ctor(System.String)">
            <summary>
            Constructor, opens the given file
            </summary>        
        </member>
        <member name="P:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.InputHandler.IOStreamSize">
            <summary>
            The size of the associated stream in bytes
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.InputHandler.RelativeSeek(System.Int64)">
            <summary>
            Seeks relative to the current position by the given offset
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.InputHandler.SeekToSector(System.Int64)">
            <summary>
            Seeks to a given sector in the compound file.
            May only be used after SetHeaderReference() is called.
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.InputHandler.SeekToPositionInSector(System.Int64,System.Int64)">
            <summary>
            Seeks to a given sector and position in the compound file.
            May only be used after SetHeaderReference() is called.
            </summary>
            <returns>The new position in the stream.</returns>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.InputHandler.ReadByte">
            <summary>
            Reads a byte at the current position of the file stream.
            Advances the stream pointer accordingly.
            </summary>
            <returns>The byte value read from the stream. </returns>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.InputHandler.Read(System.Byte[])">
            <summary>
            Reads bytes at the current position of the file stream into a byte array.
            The array size determines the number of bytes to read.
            Advances the stream pointer accordingly.
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.InputHandler.Read(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Reads bytes at the current position of the file stream into a byte array.
            Advances the stream pointer accordingly.
            </summary>
            <param name="array">The array to read to</param>
            <param name="offset">The offset in the array to read to</param>
            <param name="count">The number of bytes to read</param>        
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.InputHandler.UncheckedReadByte">
            <summary>
            Reads a byte at the current position of the file stream.
            Advances the stream pointer accordingly.
            </summary>
            <returns>The byte cast to an int, or -1 if reading from the end of the stream.</returns>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.InputHandler.UncheckedRead(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Reads bytes at the current position of the file stream into a byte array.
            Advances the stream pointer accordingly.
            </summary>
            <param name="array">The array to read to</param>
            <param name="offset">The offset in the array to read to</param>
            <param name="count">The number of bytes to read</param>    
            <returns>The total number of bytes read into the buffer. 
            This might be less than the number of bytes requested if that number 
            of bytes are not currently available, or zero if the end of the stream is reached.</returns>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.InputHandler.ReadPosition(System.Byte[],System.Int64)">
            <summary>
            Reads bytes at the given position of the file stream into a byte array.
            The array size determines the number of bytes to read.
            Advances the stream pointer accordingly.
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.InputHandler.ReadUInt16">
            <summary>
            Reads a UInt16 at the current position of the file stream.
            May only be used after InitBitConverter() is called.
            Advances the stream pointer accordingly.
            </summary>
            <returns>The UInt16 value read from the stream.</returns>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.InputHandler.ReadUInt32">
            <summary>
            Reads a UInt32 at the current position of the file stream.
            May only be used after InitBitConverter() is called.
            Advances the stream pointer accordingly.
            </summary>
            <returns>The UInt32 value read from the stream.</returns>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.InputHandler.ReadUInt64">
            <summary>
            Reads a UInt64 at the current position of the file stream.
            May only be used after InitBitConverter() is called.
            Advances the stream pointer accordingly.
            </summary>
            <returns>The UInt64 value read from the stream.</returns>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.InputHandler.ReadUInt16(System.Int64)">
            <summary>
            Reads a UInt16 at the given position of the file stream.
            May only be used after InitBitConverter() is called.
            Advances the stream pointer accordingly.
            </summary>
            <returns>The UInt16 value read at the given position.</returns>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.InputHandler.ReadUInt32(System.Int64)">
            <summary>
            Reads a UInt32 at the given position of the file stream.
            May only be used after InitBitConverter() is called.
            Advances the stream pointer accordingly.
            </summary>
            <returns>The UInt32 value read at the given position.</returns>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.InputHandler.ReadUInt64(System.Int64)">
            <summary>
            Reads a UInt64 at the given position of the file stream.
            May only be used after InitBitConverter() is called.
            Advances the stream pointer accordingly.
            </summary>
            <returns>The UInt64 value read at the given position.</returns>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.InputHandler.ReadString(System.Int32)">
            <summary>
            Reads a UTF-16 encoded unicode string at the current position of the file stream.
            May only be used after InitBitConverter() is called.
            Advances the stream pointer accordingly.
            </summary>
            <param name="size">The maximum size of the string in bytes (1 char = 2 bytes) including the Unicode NULL.</param>
            <returns>The string read from the stream.</returns>
        </member>
        <member name="P:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.IStreamReader.BaseStream">
            <summary>
            Exposes access to the underlying stream of type IStreamReader.
            </summary>
            <returns>The underlying stream associated with the IStreamReader</returns>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.IStreamReader.Close">
            <summary>
            Closes the current reader and the underlying stream.
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.IStreamReader.PeekChar">
            <summary>
            Returns the next available character and does not advance the byte or character position.
            </summary>
            <returns>
            The next available character, or -1 if no more characters are available or 
            the stream does not support seeking.
            </returns>
            <exception cref="T:System.IO.IOException">An I/O error occurs.</exception>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.IStreamReader.Read">
            <summary>
            Reads characters from the underlying stream and advances the current position
            of the stream in accordance with the Encoding used and the specific character
            being read from the stream.
            </summary>
            <returns>
            The next character from the input stream, or -1 if no characters are currently available.
            </returns>
            <exception cref="T:System.IO.IOException">An I/O error occurs.</exception>
            <exception cref="T:System.ObjectDisposedException">The stream is closed.</exception>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.IStreamReader.Read(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Reads count bytes from the stream with index as the starting point in the byte array.
            </summary>
            <param name="buffer">The buffer to read data into.</param>
            <param name="index">The starting point in the buffer at which to begin reading into the buffer.</param>
            <param name="count">The number of characters to read.</param>
            <returns>The number of characters read into buffer. This might be less than the number 
            of bytes requested if that many bytes are not available, or it might be zero 
            if the end of the stream is reached.</returns>
            <exception cref="T:System.ArgumentException">The buffer length minus index is less than count.</exception>
            <exception cref="T:System.ArgumentNullException">buffer is null.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">index or count is negative.</exception>
            <exception cref="T:System.ObjectDisposedException">The stream is closed.</exception>
            <exception cref="T:System.IO.IOException">An I/O error occurs.</exception>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.IStreamReader.Read(System.Char[],System.Int32,System.Int32)">
            <summary>
            Reads count characters from the stream with index as the starting point in the character array.
            </summary>
            <param name="buffer">The buffer to read data into.</param>
            <param name="index">The starting point in the buffer at which to begin reading into the buffer.</param>
            <param name="count">The number of characters to read.</param>
            <returns>The total number of characters read into the buffer. This might be less than
                the number of characters requested if that many characters are not currently
                available, or it might be zero if the end of the stream is reached.</returns>
            <exception cref="T:System.ArgumentException">The buffer length minus index is less than count.</exception>
            <exception cref="T:System.ArgumentNullException">buffer is null.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">index or count is negative.</exception>
            <exception cref="T:System.ObjectDisposedException">The stream is closed.</exception>
            <exception cref="T:System.IO.IOException">An I/O error occurs.</exception>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.IStreamReader.ReadBoolean">
            <summary>
            Reads a Boolean value from the current stream and advances the current position
                of the stream by one byte.
            </summary>
            <returns>true if the byte is nonzero; otherwise, false.</returns>
            <exception cref="T:System.IO.EndOfStreamException">The end of the stream is reached.</exception>
            <exception cref="T:System.ObjectDisposedException">The stream is closed.</exception>
            <exception cref="T:System.IO.IOException">An I/O error occurs.</exception>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.IStreamReader.ReadByte">
            <summary>
            Reads the next byte from the current stream and advances the current position
                of the stream by one byte.
            </summary>
            <returns>The next byte read from the current stream.</returns>
            <exception cref="T:System.IO.EndOfStreamException">The end of the stream is reached.</exception>
            <exception cref="T:System.ObjectDisposedException">The stream is closed.</exception>
            <exception cref="T:System.IO.IOException">An I/O error occurs.</exception>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.IStreamReader.ReadBytes(System.Int32)">
            <summary>
            Reads count bytes from the current stream into a byte array and advances
                the current position by count bytes.
            </summary>
            <param name="count">The number of bytes to read.</param>
            <returns>A byte array containing data read from the underlying stream. This might
                be less than the number of bytes requested if the end of the stream is reached.</returns>
            <exception cref="T:System.IO.IOException">An I/O error occurs.</exception>
            <exception cref="T:System.ObjectDisposedException">The stream is closed.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">count is negative.</exception>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.IStreamReader.ReadBytes(System.Int64,System.Int32)">
            <summary>
            Reads count bytes from the current stream into a byte array and advances
                the current position by count bytes.
            </summary>
            <param name="position">The absolute byte offset where to read.</param>
            <param name="count">The number of bytes to read.</param>
            <returns>A byte array containing data read from the underlying stream. This might
                be less than the number of bytes requested if the end of the stream is reached.</returns>
            <exception cref="T:System.IO.IOException">An I/O error occurs.</exception>
            <exception cref="T:System.ObjectDisposedException">The stream is closed.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">count is negative.</exception>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.IStreamReader.ReadChar">
            <summary>
            Reads the next character from the current stream and advances the current
                position of the stream in accordance with the Encoding used and the specific
                character being read from the stream.
            </summary>
            <returns>A character read from the current stream.</returns>
            <exception cref="T:System.IO.EndOfStreamException">The end of the stream is reached.</exception>
            <exception cref="T:System.ObjectDisposedException">The stream is closed.</exception>
            <exception cref="T:System.IO.IOException">An I/O error occurs.</exception>
            <exception cref="T:System.ArgumentException">A surrogate character was read.</exception>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.IStreamReader.ReadChars(System.Int32)">
            <summary>
            Reads count characters from the current stream, returns the data in a character
                array, and advances the current position in accordance with the Encoding
                used and the specific character being read from the stream.
            </summary>
            <param name="count">The number of characters to read.</param>
            <returns>A character array containing data read from the underlying stream. This might
                be less than the number of characters requested if the end of the stream
                is reached.</returns>
            <exception cref="T:System.ObjectDisposedException">The stream is closed.</exception>    
            <exception cref="T:System.IO.IOException">An I/O error occurs.</exception>    
            <exception cref="T:System.ArgumentOutOfRangeException">count is negative.</exception>    
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.IStreamReader.ReadDecimal">
            <summary>
            Reads a decimal value from the current stream and advances the current position
                of the stream by sixteen bytes.
            </summary>
            <returns>A decimal value read from the current stream.</returns>
            <exception cref="T:System.IO.EndOfStreamException">The end of the stream is reached.</exception>
            <exception cref="T:System.ObjectDisposedException">The stream is closed.</exception>
            <exception cref="T:System.IO.IOException">An I/O error occurs.</exception>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.IStreamReader.ReadDouble">
            <summary>
            Reads an 8-byte floating point value from the current stream and advances
                the current position of the stream by eight bytes.
            </summary>
            <returns>An 8-byte floating point value read from the current stream.</returns>
            <exception cref="T:System.IO.EndOfStreamException">The end of the stream is reached.</exception>
            <exception cref="T:System.ObjectDisposedException">The stream is closed.</exception>
            <exception cref="T:System.IO.IOException">An I/O error occurs.</exception>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.IStreamReader.ReadInt16">
            <summary>
            Reads a 2-byte signed integer from the current stream and advances the current
                position of the stream by two bytes.
            </summary>
            <returns>A 2-byte signed integer read from the current stream.</returns>
            <exception cref="T:System.IO.EndOfStreamException">The end of the stream is reached.</exception>
            <exception cref="T:System.ObjectDisposedException">The stream is closed.</exception>
            <exception cref="T:System.IO.IOException">An I/O error occurs.</exception>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.IStreamReader.ReadInt32">
            <summary>
            Reads a 4-byte signed integer from the current stream and advances the current
               position of the stream by four bytes.
            </summary>
            <returns>A 4-byte signed integer read from the current stream.</returns>
            <exception cref="T:System.IO.EndOfStreamException">The end of the stream is reached.</exception>
            <exception cref="T:System.ObjectDisposedException">The stream is closed.</exception>
            <exception cref="T:System.IO.IOException">An I/O error occurs.</exception>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.IStreamReader.ReadInt64">
            <summary>
            Reads an 8-byte signed integer from the current stream and advances the current
                position of the stream by eight bytes.
            </summary>
            <returns>An 8-byte signed integer read from the current stream.</returns>
            <exception cref="T:System.IO.EndOfStreamException">The end of the stream is reached.</exception>
            <exception cref="T:System.ObjectDisposedException">The stream is closed.</exception>
            <exception cref="T:System.IO.IOException">An I/O error occurs.</exception>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.IStreamReader.ReadSByte">
            <summary>
            Reads a signed byte from this stream and advances the current position of
                the stream by one byte.
            </summary>
            <returns>A signed byte read from the current stream.</returns>
            <exception cref="T:System.IO.EndOfStreamException">The end of the stream is reached.</exception>
            <exception cref="T:System.ObjectDisposedException">The stream is closed.</exception>
            <exception cref="T:System.IO.IOException">An I/O error occurs.</exception>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.IStreamReader.ReadSingle">
            <summary>
            Reads a 4-byte floating point value from the current stream and advances
                the current position of the stream by four bytes.
            </summary>
            <returns>A 4-byte floating point value read from the current stream.</returns>
            <exception cref="T:System.IO.EndOfStreamException">The end of the stream is reached.</exception>
            <exception cref="T:System.ObjectDisposedException">The stream is closed.</exception>
            <exception cref="T:System.IO.IOException">An I/O error occurs.</exception>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.IStreamReader.ReadString">
            <summary>
            Reads a string from the current stream. The string is prefixed with the length,
                encoded as an integer seven bits at a time.
            </summary>
            <returns>The string being read.</returns>
            <exception cref="T:System.IO.EndOfStreamException">The end of the stream is reached.</exception>
            <exception cref="T:System.ObjectDisposedException">The stream is closed.</exception>
            <exception cref="T:System.IO.IOException">An I/O error occurs.</exception>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.IStreamReader.ReadUInt16">
            <summary>
            Reads a 2-byte unsigned integer from the current stream using little-endian
                encoding and advances the position of the stream by two bytes.
            </summary>
            <returns>A 2-byte unsigned integer read from this stream.</returns>
            <exception cref="T:System.IO.EndOfStreamException">The end of the stream is reached.</exception>
            <exception cref="T:System.ObjectDisposedException">The stream is closed.</exception>
            <exception cref="T:System.IO.IOException">An I/O error occurs.</exception>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.IStreamReader.ReadUInt32">
            <summary>
            Reads a 4-byte unsigned integer from the current stream and advances the
                position of the stream by four bytes.
            </summary>
            <returns>A 4-byte unsigned integer read from this stream.</returns>
            <exception cref="T:System.IO.EndOfStreamException">The end of the stream is reached.</exception>
            <exception cref="T:System.ObjectDisposedException">The stream is closed.</exception>
            <exception cref="T:System.IO.IOException">An I/O error occurs.</exception>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.IStreamReader.ReadUInt64">
            <summary>
            Reads an 8-byte unsigned integer from the current stream and advances the
                position of the stream by eight bytes.
            </summary>
            <returns>An 8-byte unsigned integer read from this stream.</returns>
            <exception cref="T:System.IO.EndOfStreamException">The end of the stream is reached.</exception>
            <exception cref="T:System.ObjectDisposedException">The stream is closed.</exception>
            <exception cref="T:System.IO.IOException">An I/O error occurs.</exception>
        </member>
        <member name="P:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.IStructuredStorageReader.AllEntries">
            <summary>
            Collection of all _entries contained in a compound file
            </summary> 
        </member>
        <member name="P:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.IStructuredStorageReader.AllStreamEntries">
            <summary> 
            Collection of all stream _entries contained in a compound file
            </summary> 
        </member>
        <member name="P:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.IStructuredStorageReader.FullNameOfAllEntries">
            <summary>
            Collection of all entry names contained in a compound file
            </summary>        
        </member>
        <member name="P:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.IStructuredStorageReader.FullNameOfAllStreamEntries">
            <summary>
            Collection of all stream entry names contained in a compound file
            </summary>        
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.IStructuredStorageReader.Close">
            <summary>
            Closes the file handle
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.IStructuredStorageReader.GetStream(System.String)">
            <summary>
            Returns a handle to a stream with the given name/path.
            If a path is used, it must be preceeded by '\'.
            The characters '\' ( if not separators in the path) and '%' must be masked by '%XXXX'
            where 'XXXX' is the unicode in hex of '\' and '%', respectively
            </summary>
            <param name="path">The path of the virtual stream.</param>
            <returns>An object which enables access to the virtual stream.</returns>
        </member>
        <member name="T:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.MiniFat">
            <summary>
            Represents the MiniFat in a compound file
            Author: math
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.MiniFat.#ctor(DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.Fat,DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.Header,DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.InputHandler,System.UInt32,System.UInt64)">
            <summary>
            Constructor
            </summary>
            <param name="fat">Handle to the Fat of the compound file</param>
            <param name="header">Handle to the header of the compound file</param>
            <param name="fileHandler">Handle to the file handler of the compound file</param>
            <param name="miniStreamStart">Address of the sector where the mini stream starts</param>
            <param name="sizeOfMiniStream"></param>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.MiniFat.SeekToPositionInSector(System.Int64,System.Int64)">
            <summary>
            Seeks to a given position in a sector of the mini stream
            </summary>
            <param name="sector">The sector to seek to</param>
            <param name="position">The position in the sector to seek to</param>
            <returns>The new position in the stream.</returns>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.MiniFat.GetNextSectorInChain(System.UInt32)">
            <summary>
            Returns the next sector in a chain
            </summary>
            <param name="currentSector">The current sector in the chain</param>
            <returns>The next sector in the chain</returns>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.MiniFat.Init">
            <summary>
            Initalizes the Fat
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.MiniFat.ReadSectorsUsedByMiniFAT">
            <summary>
            Reads the sectors used by the MiniFat
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.MiniFat.ReadSectorsUsedByMiniStream">
            <summary>
            Reads the sectors used by the MiniFat
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.MiniFat.CheckConsistency">
            <summary>
            Checks whether the size specified in the header matches the actual size
            </summary>
        </member>
        <member name="T:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.StructuredStorageReader">
            <summary>
            Provides methods for accessing a compound file.
            Author: math
            </summary>
        </member>
        <member name="P:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.StructuredStorageReader.FullNameOfAllEntries">
            <summary>
            Collection of all entry names contained in a compound file
            </summary>
        </member>
        <member name="P:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.StructuredStorageReader.FullNameOfAllStreamEntries">
            <summary>
            Collection of all stream entry names contained in a compound file
            </summary>
        </member>
        <member name="P:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.StructuredStorageReader.AllEntries">
            <summary>
            Collection of all _entries contained in a compound file
            </summary>
        </member>
        <member name="P:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.StructuredStorageReader.AllStreamEntries">
            <summary>
            Collection of all stream _entries contained in a compound file
            </summary>
        </member>
        <member name="P:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.StructuredStorageReader.RootDirectoryEntry">
            <summary>
            Returns a handle to the RootDirectoryEntry.
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.StructuredStorageReader.#ctor(System.IO.Stream)">
            <summary>
            Initalizes a handle to a compound file based on a stream
            </summary>
            <param name="stream">The stream to the storage</param>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.StructuredStorageReader.#ctor(System.String)">
            <summary>
            Initalizes a handle to a compound file with the given name
            </summary>
            <param name="fileName">The name of the file including its path</param>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.StructuredStorageReader.GetStream(System.String)">
            <summary>
            Returns a handle to a stream with the given name/path.
            If a path is used, it must be preceeded by '\'.
            The characters '\' ( if not separators in the path) and '%' must be masked by '%XXXX'
            where 'XXXX' is the unicode in hex of '\' and '%', respectively
            </summary>
            <param name="path">The path of the virtual stream.</param>
            <returns>An object which enables access to the virtual stream.</returns>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.StructuredStorageReader.GetEntry(System.String)">
            <summary>
            Returns a handle to a directory entry with the given name/path.
            If a path is used, it must be preceeded by '\'.
            The characters '\' ( if not separators in the path) and '%' must be masked by '%XXXX'
            where 'XXXX' is the unicode in hex of '\' and '%', respectively
            </summary>
            <param name="path">The path of the directory entry.</param>
            <returns>An object which enables access to the directory entry.</returns>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.StructuredStorageReader.Close">
            <summary>
            Closes the file handle
            </summary>
        </member>
        <member name="T:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.VirtualStream">
            <summary>
            Encapsulates a virtual stream in a compound file 
            Author: math
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.VirtualStream.#ctor(DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.AbstractFat,System.UInt32,System.Int64,System.String)">
            <summary>
            Initializes a virtual stream
            </summary>
            <param name="fat">Handle to the fat of the respective file</param>        
            <param name="startSector">Start sector of the stream (sector 0 is sector immediately following the header)</param>
            <param name="sizeOfStream">Size of the stream in bytes</param>
            <param name="name">Name of the stream</param>
        </member>
        <member name="P:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.VirtualStream.Position">
            <summary>
            The current position within the stream. 
            The supported range is from 0 to 2^31 - 1 = 2147483647 = 2GB
            </summary>
        </member>
        <member name="P:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.VirtualStream.Length">
            <summary>
            A long value representing the length of the stream in bytes. 
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.VirtualStream.Read(System.Byte[])">
            <summary>
            Reads bytes from the current position in the virtual stream.
            The number of bytes to read is determined by the length of the array.
            </summary>
            <param name="array">Array which will contain the read bytes after successful execution.</param>
            <returns>The total number of bytes read into the buffer. 
            This might be less than the length of the array if that number 
            of bytes are not currently available, or zero if the end of the stream is reached.</returns>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.VirtualStream.Read(System.Byte[],System.Int32)">
            <summary>
            Reads bytes from the current position in the virtual stream.
            </summary>
            <param name="array">Array which will contain the read bytes after successful execution.</param>
            <param name="count">Number of bytes to read.</param>
            <returns>The total number of bytes read into the buffer. 
            This might be less than the number of bytes requested if that number 
            of bytes are not currently available, or zero if the end of the stream is reached.</returns>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.VirtualStream.Read(System.Byte[],System.Int32,System.Int32)">
            <summary>
            Reads bytes from a virtual stream.
            </summary>
            <param name="array">Array which will contain the read bytes after successful execution.</param>
            <param name="offset">Offset in the array.</param>
            <param name="count">Number of bytes to read.</param>
            <returns>The total number of bytes read into the buffer. 
            This might be less than the number of bytes requested if that number 
            of bytes are not currently available, or zero if the end of the stream is reached.</returns>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.VirtualStream.Read(System.Byte[],System.Int32,System.Int32,System.Int64)">
            <summary>
            Reads bytes from the virtual stream.
            </summary>
            <param name="array">Array which will contain the read bytes after successful execution.</param>
            <param name="offset">Offset in the array.</param>
            <param name="count">Number of bytes to read.</param>
            <param name="position">Start position in the stream.</param>
            <returns>The total number of bytes read into the buffer. 
            This might be less than the number of bytes requested if that number 
            of bytes are not currently available, or zero if the end of the stream is reached.</returns>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.VirtualStream.Skip(System.UInt32)">
            <summary>
            Skips bytes in the virtual stream.
            </summary>
            <param name="count">Number of bytes to skip.</param>
            <returns>The total number of bytes skipped. 
            This might be less than the number of bytes requested if that number 
            of bytes are not currently available, or zero if the end of the stream is reached.</returns>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.VirtualStream.Init(System.UInt32)">
            <summary>
            Initalizes the stream.
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.VirtualStream.CheckConsistency">
            <summary>
            Checks whether the size specified in the header matches the actual size
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.VirtualStreamReader.#ctor(DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.VirtualStream)">
            <summary>
            Ctor 
            
            </summary>
            <param name="stream"></param>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.VirtualStreamReader.#ctor(System.IO.MemoryStream)">
            <summary>
            Second constructor to create a StreamReader with a MemoryStream. 
            </summary>
            <param name="stream"></param>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.VirtualStreamReader.Read(System.Byte[])">
            <summary>
            Reads bytes from the current position in the virtual stream.
            The number of bytes to read is determined by the length of the array.
            </summary>
            <param name="buffer">Array which will contain the read bytes after successful execution.</param>
            <returns>The total number of bytes read into the buffer. 
            This might be less than the length of the array if that number 
            of bytes are not currently available, or zero if the end of the stream is reached.</returns>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.VirtualStreamReader.Read(System.Byte[],System.Int32)">
            <summary>
            Reads bytes from the current position in the virtual stream.
            </summary>
            <param name="buffer">Array which will contain the read bytes after successful execution.</param>
            <param name="count">Number of bytes to read.</param>
            <returns>The total number of bytes read into the buffer. 
            This might be less than the number of bytes requested if that number 
            of bytes are not currently available, or zero if the end of the stream is reached.</returns>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.VirtualStreamReader.ReadBytes(System.Int64,System.Int32)">
            <summary>
            Reads count bytes from the current stream into a byte array and advances
                the current position by count bytes.
            </summary>
            <param name="position">The absolute byte offset where to read.</param>
            <param name="count">The number of bytes to read.</param>
            <returns>A byte array containing data read from the underlying stream. This might
                be less than the number of bytes requested if the end of the stream is reached.</returns>
            <exception cref="T:System.IO.IOException">An I/O error occurs.</exception>
            <exception cref="T:System.ObjectDisposedException">The stream is closed.</exception>
            <exception cref="T:System.ArgumentOutOfRangeException">count is negative.</exception>
        </member>
        <member name="T:DIaLOGIKa.b2xtranslator.StructuredStorage.Writer.AbstractFat">
            <summary>
            Abstract class of a Fat in a compound file
            Author: math
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Writer.AbstractFat.#ctor(DIaLOGIKa.b2xtranslator.StructuredStorage.Writer.StructuredStorageContext)">
            <summary>
            Constructor
            <param name="context">the current context</param>
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Writer.AbstractFat.writeChain(System.UInt32)">
            <summary>
            Write a chain to the fat.
            </summary>
            <param name="entryCount">number of entries in the chain</param>
            <returns></returns>
        </member>
        <member name="T:DIaLOGIKa.b2xtranslator.StructuredStorage.Writer.BaseDirectoryEntry">
            <summary>
            Common base class for stream and storage directory entries
            Author: math
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Writer.BaseDirectoryEntry.#ctor(System.String,DIaLOGIKa.b2xtranslator.StructuredStorage.Writer.StructuredStorageContext)">
            <summary>
            
            </summary>
            <param name="name">Name of the directory entry.</param>
            <param name="context">the current context</param>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Writer.BaseDirectoryEntry.setInitialValues">
            <summary>
            Set the initial values
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Writer.BaseDirectoryEntry.write">
            <summary>
            Writes the directory entry to the directory stream of the current context
            </summary>
        </member>
        <member name="T:DIaLOGIKa.b2xtranslator.StructuredStorage.Writer.EmptyDirectoryEntry">
            <summary>
            Empty directory entry used to pad out directory stream.
            Author: math
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Writer.EmptyDirectoryEntry.#ctor(DIaLOGIKa.b2xtranslator.StructuredStorage.Writer.StructuredStorageContext)">
            <summary>
            Constructor
            </summary>
            <param name="context">the current context</param>
        </member>
        <member name="T:DIaLOGIKa.b2xtranslator.StructuredStorage.Writer.Fat">
            <summary>
            Class which represents the fat of a structured storage.
            Author: math
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Writer.Fat.#ctor(DIaLOGIKa.b2xtranslator.StructuredStorage.Writer.StructuredStorageContext)">
            <summary>
            Constructor
            </summary>
            <param name="context">the current context</param>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Writer.Fat.writeDiFatEntriesToFat(System.UInt32)">
            <summary>
            Writes the difat entries to the fat
            </summary>
            <param name="sectorCount">Number of difat sectors.</param>
            <returns>Start sector of the difat.</returns>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Writer.Fat.writeDiFatSectorsToStream(System.UInt32)">
            <summary>
            Writes the difat sectors to the output stream of the current context
            </summary>
            <param name="fatStartSector"></param>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Writer.Fat.write">
            <summary>
            Marks the difat and fat sectors in the fat and writes the difat and fat data to the output stream of the current context.
            </summary>
        </member>
        <member name="T:DIaLOGIKa.b2xtranslator.StructuredStorage.Writer.Header">
            <summary>
            Class which represents the header of a structured storage.
            Author: math
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Writer.Header.#ctor(DIaLOGIKa.b2xtranslator.StructuredStorage.Writer.StructuredStorageContext)">
            <summary>
            Constructor.
            </summary>
            <param name="context">the current context</param>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Writer.Header.setHeaderDefaults">
            <summary>
            Initializes header defaults.
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Writer.Header.writeNextDiFatSector(System.UInt32)">
            <summary>
            Writes the next difat sector (which is one of the first 109) to the header.
            </summary>
            <param name="sector"></param>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Writer.Header.write">
            <summary>
            Writes the header to the internal stream.
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Writer.Header.writeToStream(System.IO.Stream)">
            <summary>
            Writes the internal header stream to the given stream.
            </summary>
            <param name="stream">The stream to which is written to.</param>
        </member>
        <member name="T:DIaLOGIKa.b2xtranslator.StructuredStorage.Writer.MiniFat">
            <summary>
            Represents the minifat of a structured storage.
            Author: math
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Writer.MiniFat.#ctor(DIaLOGIKa.b2xtranslator.StructuredStorage.Writer.StructuredStorageContext)">
            <summary>
            Constructor.
            </summary>
            <param name="context">the current context</param>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Writer.MiniFat.write">
            <summary>
            Writes minifat chain to fat and writes the minifat data to the output stream of the current context.
            </summary>
        </member>
        <member name="T:DIaLOGIKa.b2xtranslator.StructuredStorage.Writer.OutputHandler">
            <summary>
            Class which encapsulates methods which ease writing structured storage components to a stream.
            Author: math
            </summary>
        </member>
        <member name="P:DIaLOGIKa.b2xtranslator.StructuredStorage.Writer.OutputHandler.IOStreamSize">
            <summary>
            Returns UInt64.MaxValue because size of stream is not defined yet.
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Writer.OutputHandler.#ctor(System.IO.MemoryStream)">
            <summary>
            Constructor.
            </summary>
            <param name="memoryStream">The target memory stream.</param>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Writer.OutputHandler.writeByte(System.Byte)">
            <summary>
            Writes a byte to the stream.
            </summary>
            <param name="value">The byte to write.</param>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Writer.OutputHandler.writeUInt16(System.UInt16)">
            <summary>
            Writes a UInt16 to the stream.
            </summary>
            <param name="value">The UInt16 to write.</param>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Writer.OutputHandler.writeUInt32(System.UInt32)">
            <summary>
            Writes a UInt32 to the stream.
            </summary>
            <param name="value">The UInt32 to write.</param>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Writer.OutputHandler.writeUInt64(System.UInt64)">
            <summary>
            Writes a UInt64 to the stream.
            </summary>
            <param name="value">The UInt64 to write.</param>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Writer.OutputHandler.write(System.Byte[])">
            <summary>
            Writes a byte array to the stream.
            </summary>
            <param name="data">The byte array to write.</param>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Writer.OutputHandler.writeSectors(System.Byte[],System.UInt16,System.Byte)">
            <summary>
            Writes sectors to the stream and padding the sector with the given byte.
            </summary>
            <param name="data">The data to write.</param>
            <param name="sectorSize">The size of a sector.</param>
            <param name="padding">The byte which is used for padding</param>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Writer.OutputHandler.writeSectors(System.Byte[],System.UInt16,System.UInt32)">
            <summary>
            Writes sectors to the stream and padding the sector with the given UInt32.
            </summary>
            <param name="data">The data to write.</param>
            <param name="sectorSize">The size of a sector.</param>
            <param name="padding">The UInt32 which is used for padding</param>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Writer.OutputHandler.writeToStream(System.IO.Stream)">
            <summary>
            Writes the internal memory stream to a given stream.
            </summary>
            <param name="stream">The output stream.</param>
        </member>
        <member name="T:DIaLOGIKa.b2xtranslator.StructuredStorage.Writer.RootDirectoryEntry">
            <summary>
            Class which represents the root directory entry of a structured storage.
            Author: math
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Writer.RootDirectoryEntry.#ctor(DIaLOGIKa.b2xtranslator.StructuredStorage.Writer.StructuredStorageContext)">
            <summary>
            Constructor.
            </summary>
            <param name="context">the current context</param>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Writer.RootDirectoryEntry.writeReferencedStream">
            <summary>
            Writes the mini stream chain to the fat and the mini stream data to the output stream of the current context.
            </summary>
        </member>
        <member name="T:DIaLOGIKa.b2xtranslator.StructuredStorage.Writer.StorageDirectoryEntry">
            <summary>
            Represents a storage directory entry in a structured storage.
            Author: math
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Writer.StorageDirectoryEntry.#ctor(System.String,DIaLOGIKa.b2xtranslator.StructuredStorage.Writer.StructuredStorageContext)">
            <summary>
            Constructor.
            </summary>
            <param name="name">The name of the directory entry.</param>
            <param name="context">The current context.</param>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Writer.StorageDirectoryEntry.AddStreamDirectoryEntry(System.String,System.IO.Stream)">
            <summary>
            Adds a stream directory entry to this storage directory entry.
            </summary>
            <param name="name">The name of the stream directory entry to add.</param>
            <param name="stream">The stream referenced by the stream directory entry</param>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Writer.StorageDirectoryEntry.AddStorageDirectoryEntry(System.String)">
            <summary>
            Adds a storage directory entry to this storage directory entry.
            </summary>
            <param name="name">The name of the storage directory entry to add.</param>
            <returns>The storage directory entry whic hahs been added.</returns>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Writer.StorageDirectoryEntry.setClsId(System.Guid)">
            <summary>
            Sets the clsID.
            </summary>
            <param name="clsId">The clsId to set.</param>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Writer.StorageDirectoryEntry.RecursiveGetAllDirectoryEntries">
            <summary>
            Recursively gets all storage directory entries starting at this directory entry.
            </summary>
            <returns>A list of directory entries.</returns>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Writer.StorageDirectoryEntry.RecursiveGetAllDirectoryEntries(System.Collections.Generic.List{DIaLOGIKa.b2xtranslator.StructuredStorage.Writer.BaseDirectoryEntry})">
            <summary>
            The recursive implementation of the method RecursiveGetAllDirectoryEntries().
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Writer.StorageDirectoryEntry.RecursiveCreateRedBlackTrees">
            <summary>
            Creates the red-black-tree recursively
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Writer.StorageDirectoryEntry.CreateRedBlackTree">
            <summary>
            Creates the red-black-tree for this directory entry
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Writer.StorageDirectoryEntry.setRelationsAndColorRecursive(System.Collections.Generic.List{DIaLOGIKa.b2xtranslator.StructuredStorage.Writer.BaseDirectoryEntry},System.Int32,System.Int32)">
            <summary>
            Helper function for the method CreateRedBlackTree()
            </summary>
            <param name="entryList">The list of directory entries</param>
            <param name="treeHeight">The height of the balanced red-black-tree</param>
            <param name="treeLevel">The current tree level</param>
            <returns>The root of this red-black-tree</returns>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Writer.StorageDirectoryEntry.getMiddleIndex(System.Collections.Generic.List{DIaLOGIKa.b2xtranslator.StructuredStorage.Writer.BaseDirectoryEntry})">
            <summary>
            Calculation of the middle index of a list of directory entries.
            </summary>
            <param name="list">The input list.</param>
            <returns>The result</returns>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Writer.StorageDirectoryEntry.DirectoryEntryComparison(DIaLOGIKa.b2xtranslator.StructuredStorage.Writer.BaseDirectoryEntry,DIaLOGIKa.b2xtranslator.StructuredStorage.Writer.BaseDirectoryEntry)">
            <summary>
            Method for comparing directory entries (used in the red-black-tree).
            </summary>
            <param name="a">The 1st directory entry.</param>
            <param name="b">The 2nd directory entry.</param>
            <returns>Comparison result.</returns>
        </member>
        <member name="T:DIaLOGIKa.b2xtranslator.StructuredStorage.Writer.StreamDirectoryEntry">
            <summary>
            Represents a stream directory entry in a structured storage.
            Author: math
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Writer.StreamDirectoryEntry.#ctor(System.String,System.IO.Stream,DIaLOGIKa.b2xtranslator.StructuredStorage.Writer.StructuredStorageContext)">
            <summary>
            Constructor.
            </summary>
            <param name="name">Name of the stream directory entry.</param>
            <param name="stream">The stream referenced by the stream directory entry.</param>
            <param name="context">The current context.</param>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Writer.StreamDirectoryEntry.writeReferencedStream">
            <summary>
            Writes the referenced stream chain to the fat and the referenced stream data to the output stream of the current context.
            </summary>
        </member>
        <member name="T:DIaLOGIKa.b2xtranslator.StructuredStorage.Writer.StructuredStorageContext">
            <summary>
            Class which pools the different elements of a structured storage in a context.
            Author math.
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Writer.StructuredStorageContext.#ctor">
            <summary>
            Constructor.
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Writer.StructuredStorageContext.getNewSid">
            <summary>
             Returns a new sid for directory entries in this context.
            </summary>
            <returns>The new sid.</returns>
        </member>
        <member name="T:DIaLOGIKa.b2xtranslator.StructuredStorage.Writer.StructuredStorageWriter">
            <summary>
            The root class for creating a structured storage
            Author: math
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Writer.StructuredStorageWriter.#ctor">
            <summary>
            Constructor.
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Writer.StructuredStorageWriter.write(System.IO.Stream)">
            <summary>
            Writes the structured storage to a given stream.
            </summary>
            <param name="outputStream">The output stream.</param>
        </member>
        <member name="T:DIaLOGIKa.b2xtranslator.StructuredStorage.Writer.VirtualStream">
            <summary>
            Class which represents a virtual stream in a structured storage.
            Author: math
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Writer.VirtualStream.#ctor(System.IO.Stream,DIaLOGIKa.b2xtranslator.StructuredStorage.Writer.AbstractFat,System.UInt16,DIaLOGIKa.b2xtranslator.StructuredStorage.Writer.OutputHandler)">
            <summary>
            Constructor.
            </summary>
            <param name="stream">The input stream.</param>
            <param name="fat">The fat which is used by this stream.</param>
            <param name="sectorSize">The sector size.</param>
            <param name="outputHander"></param>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.StructuredStorage.Writer.VirtualStream.write">
            <summary>
            Writes the virtual stream chain to the fat and the virtual stream data to the output stream of the current context.
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.Tools.EmuValue.#ctor(System.Int32)">
            <summary>
            Creates a new EmuValue for the given value.
            </summary>
            <param name="value"></param>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.Tools.EmuValue.ToPoints">
            <summary>
            Converts the EMU to pt
            </summary>
            <returns></returns>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.Tools.EmuValue.ToTwips">
            <summary>
            Converts the EMU to twips
            </summary>
            <returns></returns>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.Tools.EmuValue.ToString">
            <summary>
            returns the original value as string 
            </summary>
            <returns></returns>
        </member>
        <member name="T:DIaLOGIKa.b2xtranslator.Tools.FixedPointNumber">
            <summary>
            Specifies an approximation of a real number, where the approximation has a fixed number of digits after the radix point. 
            
            This type is specified in [MS-OSHARED] section *******.
            
            Value of the real number = Integral + ( Fractional / 65536.0 ) 
            
            Integral (2 bytes): A signed integer that specifies the integral part of the real number. 
            Fractional (2 bytes): An unsigned integer that specifies the fractional part of the real number.
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.Tools.PtValue.#ctor(System.Double)">
            <summary>
            Creates a new PtValue for the given value.
            </summary>
            <param name="value"></param>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.Tools.PtValue.ToPoints">
            <summary>
            Converts the EMU to pt
            </summary>
            <returns></returns>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.Tools.PtValue.ToEmu">
            <summary>
            Converts the pt value to EMU
            </summary>
            <returns></returns>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.Tools.PtValue.ToCm">
            <summary>
            Converts the pt value to cm
            </summary>
            <returns></returns>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.Tools.PtValue.ToString">
            <summary>
            returns the original value as string 
            </summary>
            <returns></returns>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.Tools.TraceLogger.Simple(System.String,System.Object[])">
            <summary>
            Write a line on error level (is written if level != none)
            </summary>
            <param name="msg"></param>
            <param name="objs"></param>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.Tools.TwipsValue.Dpi">
            <summary>
            The dots per inch value that should be used.
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.Tools.TwipsValue.#ctor(System.Double)">
            <summary>
            Creates a new TwipsValue for the given value.
            </summary>
            <param name="value"></param>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.Tools.TwipsValue.ToPoints">
            <summary>
            Converts the twips to pt
            </summary>
            <returns></returns>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.Tools.TwipsValue.ToInch">
            <summary>
            Converts the twips to inch
            </summary>
            <returns></returns>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.Tools.TwipsValue.ToMm">
            <summary>
            Converts the twips to mm
            </summary>
            <returns></returns>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.Tools.TwipsValue.ToCm">
            <summary>
            Converts the twips to cm
            </summary>
            <returns></returns>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.Tools.Utils.ReadLengthPrefixedUnicodeString(System.IO.Stream)">
            <summary>
            Read a length prefixed Unicode string from the given stream.
            The string must have the following structure:<br/>
            byte 1 - 4:         Character count (cch)<br/>
            byte 5 - (cch*2)+4: Unicode characters terminated by \0
            </summary>
            <param name="stream"></param>
            <returns></returns>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.Tools.Utils.ReadLengthPrefixedAnsiString(System.IO.Stream)">
            <summary>
            Read a length prefixed ANSI string from the given stream.
            The string must have the following structure:<br/>
            byte 1-4:       Character count (cch)<br/>
            byte 5-cch+4:   ANSI characters terminated by \0
            </summary>
            <param name="stream"></param>
            <returns></returns>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.AnnotationReferenceDescriptor.UserInitials">
            <summary>
            The initials of the user who left the annotation.
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.AnnotationReferenceDescriptor.AuthorIndex">
            <summary>
            An index into the string table of comment author names.
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.DocFileFormat.AuthorTable.#ctor(DIaLOGIKa.b2xtranslator.DocFileFormat.FileInformationBlock,DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.VirtualStream)">
            <summary>
            Parses the bytes to retrieve a AuthorTable
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.AutoNumberedListDataDescriptor.rgxch">
            <summary>
            Characters displayed before/after auto number
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.DocFileFormat.AutoNumberedListDataDescriptor.#ctor">
            <summary>
            Creates a new AutoNumberedListDataDescriptor with defaut values
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.DocFileFormat.AutoNumberedListDataDescriptor.#ctor(System.Byte[])">
            <summary>
            Parses the bytes to retrieve a AutoNumberedListDataDescriptor
            </summary>
            <param name="bytes">The bytes</param>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.DocFileFormat.AutoNumberLevelDescriptor.#ctor">
            <summary>
            Creates a new AutoNumberedListDataDescriptor with default values
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.DocFileFormat.AutoNumberLevelDescriptor.#ctor(System.Byte[])">
            <summary>
            Parses the bytes to retrieve a AutoNumberLevelDescriptor
            </summary>
            <param name="bytes">The bytes</param>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.DocFileFormat.AutoSummaryInfo.#ctor(System.Byte[])">
            <summary>
            Parses the bytes to retrieve a AutoSummaryInfo
            </summary>
            <param name="bytes">The bytes</param>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.BookmarkFirst.ibkl">
            <summary>
            An unsigned integer that specifies a zero-based index into the PlcfBkl or PlcfBkld 
            that is paired with the PlcfBkf  or PlcfBkfd containing this FBKF. <br/>
            The entry found at said index specifies the location of the end of the bookmark associated with this FBKF. <br/>
            Ibkl MUST be unique for all FBKFs inside a given PlcfBkf or PlcfBkfd.
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.BorderCode.cv">
            <summary>
            24-bit border color
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.BorderCode.dptLineWidth">
            <summary>
            Width of a single line in 1/8pt, max of 32pt
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.BorderCode.brcType">
            <summary>
            Border type code:
            0 none
            1 single
            2 thick
            3 double
            5 hairline
            6 dot
            7 dash large gap
            8 dot dash
            9 dot dot dash
            10 triple
            11 thin-thick small gap
            12 tick-thin small gap
            13 thin-thick-thin small gap
            14 thin-thick medium gap
            15 thick-thin medium gap
            16 thin-thick-thin medium gap
            17 thin-thick large gap
            18 thick-thin large gap
            19 thin-thick-thin large gap
            20 wave
            21 double wave
            22 dash small gap
            23 dash dot stroked
            24 emboss 3D
            25 engrave 3D
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.BorderCode.dptSpace">
            <summary>
            Width of space to maintain between border and text within border
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.BorderCode.fShadow">
            <summary>
            When true, border is drawn with shadow. Must be false when BRC is substructure of the TC
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.BorderCode.fNil">
            <summary>
            It's a nil BRC, bytes are FFFF.
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.DocFileFormat.BorderCode.#ctor">
            <summary>
            Creates a new BorderCode with default values
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.DocFileFormat.BorderCode.#ctor(System.Byte[])">
            <summary>
            Parses the byte for a BRC
            </summary>
            <param name="bytes"></param>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.DocFileFormat.CharacterProperties.#ctor">
            <summary>
            Creates a CHP with default properties
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.DocFileFormat.CharacterProperties.#ctor(DIaLOGIKa.b2xtranslator.DocFileFormat.StyleSheet,DIaLOGIKa.b2xtranslator.DocFileFormat.CharacterPropertyExceptions,DIaLOGIKa.b2xtranslator.DocFileFormat.ParagraphPropertyExceptions)">
            <summary>
            Builds a CHP based on a CHPX
            </summary>
            <param name="styleSheet">The stylesheet</param>
            <param name="chpx">The CHPX</param>
            <param name="parentPapx"></param>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.DocFileFormat.CharacterPropertyExceptions.#ctor">
            <summary>
            Creates a CHPX wich doesn't modify anything.<br/>
            The grpprl list is empty
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.DocFileFormat.CharacterPropertyExceptions.#ctor(System.Byte[])">
            <summary>
            Parses the bytes to retrieve a CHPX
            </summary>
            <param name="bytes">The bytes starting with the istd</param>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.CustomToolbar.cbTBData">
            <summary>
            Signed integer that specifies the size, in bytes, of this structure excluding the name, cCtls, and rTBC fields. 
            Value is given by the following formula: cbTBData = sizeof(tb) + sizeof(rVisualData) + 12
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.CustomToolbar.cCtls">
            <summary>
            Signed integer that specifies the number of toolbar controls in this toolbar.
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.CustomToolbar.rTBC">
            <summary>
            Zero-based index array of TBC structures. <br/>
            The number of elements in this array MUST equal cCtls.
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.CustomToolbarWrapper.cCust">
            <summary>
            Signed integer that specifies the number of elements in the rCustomizations array. <br/>
            MUST be greater than 0x0000.
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.CustomToolbarWrapper.cbDTBC">
            <summary>
            Signed integer that specifies the size, in bytes, of the rtbdc array.<br/> 
            MUST be greater or equal to 0x00000000.
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.CustomToolbarWrapper.rTBDC">
            <summary>
            
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.CustomToolbarWrapper.rCustomizations">
            <summary>
            
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.DateAndTime.mint">
            <summary>
            minutes (0-59)
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.DateAndTime.hr">
            <summary>
            hours (0-23)
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.DateAndTime.dom">
            <summary>
            day of month (1-31)
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.DateAndTime.mon">
            <summary>
            month (1-12)
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.DateAndTime.yr">
            <summary>
            year (1900-2411)-1900
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.DocFileFormat.DateAndTime.#ctor">
            <summary>
            Creates a new DateAndTime with default values
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.DocFileFormat.DateAndTime.#ctor(System.Byte[])">
            <summary>
            Parses the byte sto retrieve a DateAndTime
            </summary>
            <param name="bytes">The bytes</param>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.DocumentProperties.fFacingPages">
            <summary>
            True when facing pages should be printed
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.DocumentProperties.Fpc">
            <summary>
            Footnote position code:<br/>
            0 print as endnotes<br/>
            1 print as bottom of page<br/>
            2 print immediately beneath text
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.DocumentProperties.rncFtn">
            <summary>
            Restart index for footnotes:<br/>
            0 don't restart note numbering<br/>
            1 restart for each section<br/>
            2 restart for each page
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.DocumentProperties.nFtn">
            <summary>
            Initial footnote number for document
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.DocumentProperties.fMirrorMargins">
            <summary>
            When true, swap margins on left/right pages
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.DocumentProperties.fNoTabForInd">
            <summary>
            Compatibility option: when true, don't add automatic tab 
            stops for hanging indent
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.DocumentProperties.fNoSpaceRaiseLower">
            <summary>
            Compatibility option: when true, don't add extra space 
            for raised or lowered characters
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.DocumentProperties.fSuppressSpbfAfterPageBreak">
            <summary>
            Compatibility option: when true, suppress the paragraph 
            Space Before and Space After options after a page break
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.DocumentProperties.fWrapTrailSpaces">
            <summary>
            Compatibility option: when true, wrap trailing spaces 
            at the end of a line to the next line
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.DocumentProperties.fMapPrintTextColor">
            <summary>
            Compatibility option: when true, print colors as black 
            on non-color printer
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.DocumentProperties.fNoColumnBalance">
            <summary>
            Compatibility option: when true, don't balance columns 
            for Continuous Section starts
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.DocumentProperties.fConvMailMergeEsc">
            <summary>
            
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.DocumentProperties.fSuppressTopSpacing">
            <summary>
            Compatibility option: when true, suppress extra line 
            spacing at top of page
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.DocumentProperties.fShowBreaksInFrames">
            <summary>
            Compatibility option: when true, show hard page or 
            column breaks in frames
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.DocumentProperties.fSwapBordersFacingPgs">
            <summary>
            Compatibility option: when true, swap left and right 
            pages on odd facing pages
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.DocumentProperties.dxaTab">
            <summary>
            Default tab width
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.DocumentProperties.wScaleSaved">
            <summary>
            Zoom percentage
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.DocumentProperties.zkSaved">
            <summary>
            Zoom type:<br/>
            0 None<br/>
            1 Full page<br/>
            2 Page width
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.DocumentProperties.fTruncDxaExpand">
            <summary>
            Expand/Codense by whole number of points
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.DocumentProperties.fPrintBodyBeforeHdr">
            <summary>
            Print body text before header/footer
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.DocumentProperties.fNoLeading">
            <summary>
            Don't add leading (extra space) between rows of text
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.DocumentProperties.fMWSmallCaps">
            <summary>
            USer larger small caps like Word 5.x for the Macintosh
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.DocumentProperties.cCh">
            <summary>
            Count of characters tallied by the last Word Count execution
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.DocumentProperties.cLines">
            <summary>
            Count of lines tallied by last Word Count operation
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.DocumentProperties.cChWS">
            <summary>
            Count of characters with spaces
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.DocumentProperties.cPg">
            <summary>
            Count of pages tallied by the last Word Count execution
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.DocumentProperties.cParas">
            <summary>
            Count of paragraphs tallied by the last Word Count execution
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.DocumentProperties.cWords">
            <summary>
            Count of words tallied by last Word Count execution
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.DocumentProperties.doptypography">
            <summary>
            
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.DocumentProperties.dogrid">
            <summary>
            
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.DocumentProperties.fGramAllClean">
            <summary>
            No grammar errors exist in document
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.DocumentProperties.fLeaveBackslashAlone">
            <summary>
            Compatibility option: when set to true, do not convert 
            backslash characters into yen signs
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.DocumentProperties.fExpShRtn">
            <summary>
            Compatibility option: when set to true, expand character 
            spaces on the line ending SHIFT+RETURN
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.DocumentProperties.fDntULTrlSpc">
            <summary>
            Compatibility option: when set to true, don‘t underline trailing spaces
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.DocumentProperties.fDntBlnSbDbWid">
            <summary>
            Compatibility option: when set to true, don't balance SBCS and DBCS characters
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.DocumentProperties.fMakeSpaceForUL">
            <summary>
            Compatibility option: when set to true, add space for underlines.
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.DocumentProperties.fSubOnSize">
            <summary>
            Compatibility option: when set to true, substitute fonts based on size.
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.DocumentProperties.fLineWrapLikeWord6">
            <summary>
            Compatibility option: when set to true, lines wrap like Word 6.0
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.DocumentProperties.fWPSpace">
            <summary>
            Compatibility option: when set to true, set the width of a space like WordPerfect 5
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.DocumentProperties.fWPJust">
            <summary>
            Compatibility option: when set to true, do full justification like WordPerfect 6.x
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.DocumentProperties.fPrintMet">
            <summary>
            Compatibility option: when set to true, use printer metrics to lay out the document
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.DocumentProperties.fSpLayoutLikeWW8">
            <summary>
            Compatibility option: when set to true, lay AutoShapes like Word 97
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.DocumentProperties.fFtnLayoutLikeWW8">
            <summary>
            Compatibility option: when set to true, lay footnotes like Word 6.x/95/97.
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.DocumentProperties.fDontUseHTMLParagraphAutoSpacing">
            <summary>
            Compatibility option: when set to true, don't use HTML paragraph auto spacing
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.DocumentProperties.fDontAdjustLineHeightInTable">
            <summary>
            Compatibility option: when set to true, don't adjust line height in tables
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.DocumentProperties.fForgetLastTabAlign">
            <summary>
            Compatibility option: when set to 1, forget last tab alignment
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.DocumentProperties.fUseAutoSpaceForFullWidthAlpha">
            <summary>
            Compatibility option: when set to 1, use auto space like Word 95
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.DocumentProperties.fAlignTablesRowByRow">
            <summary>
            Compatibility option: when set to 1, align table rows independently
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.DocumentProperties.fLayoutRawTableWidth">
            <summary>
            Compatibility option: when set to 1, lay out tables with raw width
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.DocumentProperties.fLayoutTableRowsApart">
            <summary>
            Compatibility option: when set to 1, allow table rows to lay out apart
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.DocumentProperties.fUserWord97LineBreakingRules">
            <summary>
            Compatibility option: when set to 1, use Word 97 line breaking rules for East Asian text
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.DocumentProperties.fDontBreakWrappedTables">
            <summary>
            Compatibility option: Do not break wrapped tables across pages.
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.DocumentProperties.fDontSnapToGridInCell">
            <summary>
            Compatibility option: Do not snap text to grid while in a table with inline objects.
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.DocumentProperties.fDontAllowFieldEndSelect">
            <summary>
            Compatibility option: Select the entire field with the first or last character
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.DocumentProperties.fApplyBreakingRules">
            <summary>
            Compatibility option: Apply breaking rules
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.DocumentProperties.fDontWrapTextWithPunct">
            <summary>
            Compatibility option: Do not allow hanging punctuation with character grid
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.DocumentProperties.fDontUseAsianBreakRules">
            <summary>
            Compatibility option: Do not use Asian break rules for line breaks with character grid.
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.DocumentProperties.fUseWord2002TableStyleRules">
            <summary>
            Compatibility option: Use the Word 2002 table style rules. <br/>
            Word 2002 places the top border of a column under the heading row, 
            rather than above it as Word 2003 does. <br/>
            Word 2003 applies the top border of a column in a more intuitive place when 
            there is a header row in the table. This new behavior also fixes an issue with 
            shading not displaying correctly for cells using conditional formatting.
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.DocumentProperties.fGrowAutofit">
            <summary>
            Compatibility option:
            Allow tables set to ―autofit to contents‖ to extend into the margins when in Print Layout.<br/>
            Word 2003 does not allow this by default.
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.DocumentProperties.fDoNotEmbedSystemFont">
            <summary>
            Do not embed system fonts in this document
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.DocumentProperties.grfFmtFilter">
            <summary>
            Internal: filter state for the Styles and Formatting Pane.
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.DocFileFormat.DocumentProperties.#ctor(DIaLOGIKa.b2xtranslator.DocFileFormat.FileInformationBlock,DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.VirtualStream)">
            <summary>
            Parses the bytes to retrieve a DocumentProperties
            </summary>
            <param name="fib"></param>
            <param name="tableStream"></param>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.DocumentTypographyInfo.fKerningPunct">
            <summary>
            True if we're kerning punctation
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.DocFileFormat.DocumentTypographyInfo.#ctor(System.Byte[])">
            <summary>
            Parses the bytes to retrieve a DocumentTypographyInfo
            </summary>
            <param name="bytes">The bytes</param>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.DrawingObjectGrid.dyGridDisplay">
            <summary>
            The number of grid squares (in the y direction) between each 
            gridline drawn on the screen. 0 means don‘t display any 
            gridlines in the y direction.
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.DrawingObjectGrid.dxGridDisplay">
            <summary>
            The number of grid squares (in the x direction) between each 
            gridline drawn on the screen. 0 means don‘t display any 
            gridlines in the y direction.
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.DrawingObjectGrid.fFollowMargins">
            <summary>
            If true, the grid will start at the left and top margins and 
            ignore xaGrid and yaGrid
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.DocFileFormat.DrawingObjectGrid.#ctor(System.Byte[])">
            <summary>
            Parses the bytes to retrieve a DrawingObjectGrid
            </summary>
            <param name="bytes"></param>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.DocFileFormat.DropCapSpecifier.#ctor">
            <summary>
            Creates a new DropCapSpecifier with default values
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.DocFileFormat.DropCapSpecifier.#ctor(System.Byte[])">
            <summary>
            Parses the bytes to retrieve a DropCapSpecifier
            </summary>
            <param name="bytes"></param>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.FileShapeAddress.spid">
            <summary>
            Shape Identifier. Used in conjunction with the office art data 
            (found via fcDggInfo in the FIB) to find the actual data for this shape.
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.FileShapeAddress.xaLeft">
            <summary>
            Left of rectangle enclosing shape relative to the origin of the shape
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.FileShapeAddress.yaTop">
            <summary>
            Top of rectangle enclosing shape relative to the origin of the shape
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.FileShapeAddress.xaRight">
            <summary>
            Right of rectangle enclosing shape relative to the origin of the shape
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.FileShapeAddress.yaBottom">
            <summary>
            Bottom of the rectangle enclosing shape relative to the origin of the shape
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.FileShapeAddress.wr">
            <summary>
            Text wrapping mode <br/>
            0 like 2, but doesn‘t require absolute object <br/>
            1 no text next to shape <br/>
            2 wrap around absolute object <br/>
            3 wrap as if no object present <br/>
            4 wrap tightly around object <br/>
            5 wrap tightly, but allow holes <br/>
            6-15 reserved for future use
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.FontFamilyName.fTrueType">
            <summary>
            When true, font is a TrueType font
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.FontFamilyName.ff">
            <summary>
            Font family id
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.FontFamilyName.chs">
            <summary>
            Character set identifier
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.FontFamilyName.prq">
            <summary>
            Pitch request
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.FontFamilyName.xszFtn">
            <summary>
            Name of font
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.FontFamilyName.xszAlt">
            <summary>
            Alternative name of the font
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.FontFamilyName.panose">
            <summary>
            Panose
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.FontFamilyName.fs">
            <summary>
            Font sinature
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.FormattedDiskPage.crun">
            <summary>
            Count of runs for that FKP
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.FormattedDiskPage.rgfc">
            <summary>
            Each value is the limit of a paragraph or run of exception text
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.FormattedDiskPageCHPX.rgb">
            <summary>
            An array of bytes where each byte is the word offset of a CHPX.
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.FormattedDiskPageCHPX.grpchpx">
            <summary>
            Consists all of the CHPXs stored in this FKP.
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.DocFileFormat.FormattedDiskPageCHPX.GetAllCHPXFKPs(DIaLOGIKa.b2xtranslator.DocFileFormat.FileInformationBlock,DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.VirtualStream,DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.VirtualStream)">
            <summary>
            Parses the 0Table (or 1Table) for FKP _entries containing CHPX
            </summary>
            <param name="fib">The FileInformationBlock</param>
            <param name="wordStream">The WordDocument stream</param>
            <param name="tableStream">The 0Table stream</param>
            <returns></returns>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.FormattedDiskPagePAPX.rgbx">
            <summary>
            An array of the BX data structure.<br/>
            BX is a 13 byte data structure. The first byte of each is the word offset of the PAPX.
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.FormattedDiskPagePAPX.grppapx">
            <summary>
            grppapx consists of all of the PAPXs stored in FKP concatenated end to end. 
            Each PAPX begins with a count of words which records its length padded to a word boundary.
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.DocFileFormat.FormattedDiskPagePAPX.GetAllPAPXFKPs(DIaLOGIKa.b2xtranslator.DocFileFormat.FileInformationBlock,DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.VirtualStream,DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.VirtualStream,DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.VirtualStream)">
            <summary>
            Parses the 0Table (or 1Table) for FKP _entries containing PAPX
            </summary>
            <param name="fib">The FileInformationBlock</param>
            <param name="wordStream">The WordDocument stream</param>
            <param name="tableStream">The 0Table stream</param>
            <param name="dataStream"></param>
            <returns></returns>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.DocFileFormat.FormattedDiskPagePAPX.GetFileCharacterPositions(System.Int32,System.Int32,DIaLOGIKa.b2xtranslator.DocFileFormat.FileInformationBlock,DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.VirtualStream,DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.VirtualStream,DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.VirtualStream)">
            <summary>
            Returns a list of all PAPX FCs between they given boundaries.
            </summary>
            <param name="fcMin">The lower boundary</param>
            <param name="fcMax">The upper boundary</param>
            <param name="fib">The FileInformationBlock</param>
            <param name="wordStream">The VirtualStream "WordStream"</param>
            <param name="tableStream">The VirtualStream "0Table" or "1Table"</param>
            <param name="dataStream"></param>
            <returns>The FCs</returns>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.DocFileFormat.FormattedDiskPagePAPX.GetParagraphPropertyExceptions(System.Int32,System.Int32,DIaLOGIKa.b2xtranslator.DocFileFormat.FileInformationBlock,DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.VirtualStream,DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.VirtualStream,DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.VirtualStream)">
            <summary>
            Returnes a list of all ParagraphPropertyExceptions which correspond to text 
            between the given offsets.
            </summary>
            <param name="fcMin">The lower boundary</param>
            <param name="fcMax">The upper boundary</param>
            <param name="fib">The FileInformationBlock</param>
            <param name="wordStream">The VirtualStream "WordStream"</param>
            <param name="tableStream">The VirtualStream "0Table" or "1Table"</param>
            <param name="dataStream"></param>
            <returns>The FCs</returns>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.FormFieldData.version">
            <summary>
            An unsigned integer that MUST be 0xFFFFFFFF.
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.FormFieldData.iType">
            <summary>
            Specifies the type of the form field.
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.FormFieldData.iRes">
            <summary>
            An unsigned integer.
            If iType is iTypeText (0), then iRes MUST be 0.<br/>
            If iType is iTypeChck (1), then iRes specifies the state of the checkbox and 
            MUST be 0 (unchecked), 1 (checked), or 25 (undefined).<br/> 
            Undefined checkboxes are treated as unchecked.<br/> 
            If iType is iTypeDrop (2), then iRes specifies the current selected list box item.<br/>
            A value of 25 specifies the selection is undefined. 
            Otherwise, iRes is a zero-based index into FFData.hsttbDropList.
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.FormFieldData.iTypeTxt">
            <summary>
            Specifies the type of the textbox
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.FormFieldData.fRecalc">
            <summary>
            A bool that specifies whether the field‘s value is automatically calculated after the field is modified.
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.FormFieldData.cch">
            <summary>
            An unsigned integer that specifies the maximum length, in characters, 
            of the value of the textbox.<br/><br/>
            
            MUST NOT exceed 32767.<br/> 
            A value of 0 means there is no maximum length of the value of the textbox.<br/> 
            MUST be 0 if iType is not iTypeText (0).<br/>
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.FormFieldData.hps">
            <summary>
            An unsigned integer.<br/><br/>
            
            If iType is iTypeChck (1), then hps specifies the size, in half-points, 
            of the checkbox and MUST be between 2 and 3168, inclusive.<br/> 
            If bitiType is not iTypeChck (1), then hps is undefined and MUST be ignored.
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.FormFieldData.xstzName">
            <summary>
            An string that specifies the name of this form field.<br/><br/>
            
            The length MUST NOT exceed 20.
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.FormFieldData.xstzTextDef">
            <summary>
            An optional Xstz that specifies the default text of this textbox.<br/><br/>
            
            This structure MUST exist if and only if iType is iTypeTxt (0).<br/> 
            The length MUST NOT exceed 255.<br/>
            If iTypeTxt is either iTypeTxtCurDate (3) or iTypeTxtCurTime (4), 
            then xstzTextDef MUST be an empty string.<br/> 
            If iTypeTxt is iTypeTxtCalc (5), then xstzTextDef specifies an expression to calculate.
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.FormFieldData.wDef">
            <summary>
            An optional unsigned integer that specifies the default state of the checkbox or dropdown list box.<br/><br/>
            
            MUST exist if and only if iType is iTypeChck (1) or iTypeDrop (2).<br/> 
            If iType is iTypeChck (1), then wDef MUST be 0 or 1 and specify 
            the default state of the checkbox as unchecked or checked, respectively.<br/> 
            If iType is iTypeDrop (2), then wDef MUST be less than the number of 
            items in the dropdown list box and specify the default item selected (zero-based index).
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.FormFieldData.xstzTextFormat">
            <summary>
            An string that specifies the string format of the textbox. <br/><br/>
            
            MUST be an empty string if iType is not iTypeTxt (0).<br/> 
            The length MUST NOT exceed 64.<br/> 
            Valid formatting strings are specified in [ECMA-376] part 4, section 2.16.22 format (Text Box Form Field Formatting).
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.FormFieldData.xstzHelpText">
            <summary>
            An string that specifies the help text for the form field.<br/><br/>
            
            The length MUST NOT exceed 255.
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.FormFieldData.xstzStatText">
            <summary>
            An string that specifies the status bar text for the form field.<br/><br/>
            
            The length MUST NOT exceed 138.
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.FormFieldData.xstzEntryMcr">
            <summary>
            An string that specifies a macro to run upon entry of the form field.<br/><br/>
            
            The length MUST NOT exceed 32.
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.FormFieldData.xstzExitMcr">
            <summary>
            An string that specifies a macro to run after the value of the form field has changed. <br/><br/>
            
            The length MUST NOT exceed 32.
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.DocFileFormat.FormFieldData.#ctor(System.Byte[])">
            <summary>
            Creates a new FFData by reading the data from the given stream.<br/>
            The position must already be set.
            </summary>
            <param name="bytes"></param>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.FormFieldData.FormFieldType.iTypeText">
            <summary>
            Specifies that the form field is a textbox.
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.FormFieldData.FormFieldType.iTypeChck">
            <summary>
            Specifies that the form field is a checkbox.
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.FormFieldData.FormFieldType.iTypeDrop">
            <summary>
            Specifies that the form field is a dropdown list box.
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.FormFieldData.TextboxType.regular">
            <summary>
            Specifies that the textbox value is regular text.
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.FormFieldData.TextboxType.number">
            <summary>
            Specifies that the textbox value is a number.
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.FormFieldData.TextboxType.date">
            <summary>
            Specifies that the textbox value is a date or time.
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.FormFieldData.TextboxType.currentDate">
            <summary>
            Specifies that the textbox value is the current date.
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.FormFieldData.TextboxType.currentTime">
            <summary>
            Specifies that the textbox value is the current time.
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.FormFieldData.TextboxType.calculated">
            <summary>
            Specifies that the textbox value is calculated from an expression. 
            The expression is given by xstzTextDef.
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.KeyMapEntry.kcm1">
            <summary>
            
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.KeyMapEntry.kt">
            <summary>
            
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.LineSpacingDescriptor.dyaLine">
            <summary>
            
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.LineSpacingDescriptor.fMultLinespace">
            <summary>
            
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.DocFileFormat.LineSpacingDescriptor.#ctor">
            <summary>
            Creates a new LineSpacingDescriptor with empty values
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.DocFileFormat.LineSpacingDescriptor.#ctor(System.Byte[])">
            <summary>
            Parses the bytes to retrieve a LineSpacingDescriptor
            </summary>
            <param name="bytes"></param>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.ListData.lsid">
            <summary>
            Unique List ID
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.ListData.tplc">
            <summary>
            Unique template code
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.ListData.rgistd">
            <summary>
            Array of shorts containing the istd‘s linked to each level of the list, 
            or ISTD_NIL (4095) if no style is linked.
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.ListData.fSimpleList">
            <summary>
            True if this is a simple (one-level) list.<br/>
            False if this is a multilevel (nine-level) list.
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.ListData.fHybrid">
            <summary>
            When true, list is a hybrid multilevel/simple (UI=simple, internal=multilevel)
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.ListData.rglvl">
            <summary>
            Array of ListLevel describing the several levels of the list.
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.DocFileFormat.ListData.#ctor(DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.VirtualStreamReader,System.Int32)">
            <summary>
            Parses the StreamReader to retrieve a ListData
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.ListFormatOverride.lsid">
            <summary>
            List ID of corresponding ListData
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.ListFormatOverride.clfolvl">
            <summary>
            Count of levels whose format is overridden
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.ListFormatOverride.rgLfoLvl">
            <summary>
            Array of all levels whose format is overridden
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.DocFileFormat.ListFormatOverride.#ctor(DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.VirtualStreamReader,System.Int32)">
            <summary>
            Parses the given Stream Reader to retrieve a ListFormatOverride
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.DocFileFormat.ListFormatOverrideLevel.#ctor(DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.VirtualStreamReader,System.Int32)">
            <summary>
            Parses the bytes to retrieve a ListFormatOverrideLevel
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.ListLevel.iStartAt">
            <summary>
            Start at value for this list level
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.ListLevel.nfc">
            <summary>
            Number format code (see anld.nfc for a list of options)
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.ListLevel.jc">
            <summary>
            Alignment (left, right, or centered) of the paragraph number.
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.ListLevel.rgbxchNums">
            <summary>
            Contains the character offsets into the LVL’s XST of the inherited numbers of previous levels. <br/>
            The XST contains place holders for any paragraph numbers contained in the text of the number, 
            and the place holder contains the ilvl of the inherited number, 
            so lvl.xst[lvl.rgbxchNums[0]] == the level of the first inherited number in this level.
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.ListLevel.ixchFollow">
            <summary>
            The type of character following the number text for the paragraph.
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.ListLevel.cbGrpprlChpx">
            <summary>
            Length, in bytes, of the LVL‘s grpprlChpx.
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.ListLevel.cbGrpprlPapx">
            <summary>
            Length, in bytes, of the LVL‘s grpprlPapx.
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.ListLevel.grpprlPapx">
            <summary>
            
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.ListLevel.grpprlChpx">
            <summary>
            
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.ListLevel.xst">
            <summary>
            
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.DocFileFormat.ListLevel.#ctor(DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.VirtualStreamReader,System.Int32)">
            <summary>
            Parses the given StreamReader to retrieve a LVL struct
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.MacroData.ibst">
            <summary>
            Unsigned integer that specifies the name of the macro.<br/>
            Macro name is specified by MacroName.xstz of the MacroName entry in 
            the MacroNames such that MacroName.ibst equals ibst. <br/>
            MacroNames MUST contain such an entry.
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.MacroData.ibstName">
            <summary>
            An unsigned integer that specifies the index into the 
            CommandStringTable (CommandTable.CommandStringTable)
            where the macro‘s name and arguments are specified.
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.NilPicfAndBinData.lcb">
            <summary>
            A signed integer that specifies the size, in bytes, of this structure.
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.NilPicfAndBinData.cbHeader">
            <summary>
            An unsigned integer that specifies the number of bytes from the beginning of this structure to the beginning of binData. 
            MUST be 0x44. 
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.NilPicfAndBinData.binData">
            <summary>
            The interpretation of binData depends on the field type of the field containing the 
            picture character and is given by the following table:<br/><br/>
            
            REF: HyperlinkFieldData<br/>
            PAGEREF: HyperlinkFieldData<br/>
            NOTEREF: HyperlinkFieldData<br/><br/>
            
            FORMTEXT: FormFieldData<br/>
            FORMCHECKBOX: FormFieldData<br/>
            FORMDROPDOWN: FormFieldData<br/><br/>
            
            PRIVATE: Custom binary data that is specified by the add-in that inserted this field.<br/>
            ADDIN: Custom binary data that is specified by the add-in that inserted this field.<br/>
            HYPERLINK: HyperlinkFieldData<br/>
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.NumberRevisionMarkData.rgbxchNums">
            <summary>
            Index into xst of the locations of paragraph number 
            place holders for each level
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.NumberRevisionMarkData.rgnfc">
            <summary>
            Number format code for the paragraph number 
            place holders for each level
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.NumberRevisionMarkData.PNBR">
            <summary>
            Numeric value for each place holder in xst
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.NumberRevisionMarkData.xst">
            <summary>
            The text string for the paragraph number, 
            containing level place holders
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.DocFileFormat.NumberRevisionMarkData.#ctor">
            <summary>
            Creates a new NumberRevisionMarkData with default values
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.DocFileFormat.NumberRevisionMarkData.#ctor(System.Byte[])">
            <summary>
            Parses the bytes to retrieve a NumberRevisionMarkData
            </summary>
            <param name="bytes">The bytes</param>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.DocFileFormat.OfficeArtContent.GetShapeContainer(System.Int32)">
            <summary>
            Searches the matching shape
            </summary>
            <param name="spid">The shape ID</param>
            <returns>The ShapeContainer</returns>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.OutlineLiSTData.rganlv">
            <summary>
            An array of  ANLV structures describing how heading numbers 
            should be displayed fpr each of Word's 0 outline heading levels
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.OutlineLiSTData.rgxch">
            <summary>
            Text before/after number
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.DocFileFormat.OutlineLiSTData.#ctor">
            <summary>
            Creates a new OutlineLiSTData with default values
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.DocFileFormat.OutlineLiSTData.#ctor(System.Byte[])">
            <summary>
            Parses the bytes to retrieve a OutlineLiSTData
            </summary>
            <param name="bytes">The bytes</param>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.DocFileFormat.ParagraphHeight.#ctor(System.Byte[])">
            <summary>
            Parses the bytes to retrieve a ParagraphHeight
            </summary>
            <param name="bytes">The bytes</param>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.ParagraphPropertyExceptions.istd">
            <summary>
            Index to style descriptor of the style from which the 
            paragraph inherits its paragraph and character properties
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.DocFileFormat.ParagraphPropertyExceptions.#ctor">
            <summary>
            Creates a PAPX wich doesn't modify anything.<br/>
            The grpprl list is empty
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.DocFileFormat.ParagraphPropertyExceptions.#ctor(System.Byte[],DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.VirtualStream)">
            <summary>
            Parses the bytes to retrieve a PAPX
            </summary>
            <param name="bytes">The bytes starting with the istd</param>
            <param name="dataStream"></param>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.PictureDescriptor.MetafilePicture.mm">
            <summary>
            Specifies the mapping mode in which the picture is drawn.
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.PictureDescriptor.MetafilePicture.xExt">
            <summary>
            Specifies the size of the metafile picture for all modes except the MM_ISOTROPIC and MM_ANISOTROPIC modes.<br/>
            (For more information about these modes, see the yExt member.) <br/>
            The x-extent specifies the width of the rectangle within which the picture is drawn.<br/>
            The coordinates are in units that correspond to the mapping mode.<br/>
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.PictureDescriptor.MetafilePicture.yExt">
            <summary>
            Specifies the size of the metafile picture for all modes except the MM_ISOTROPIC and MM_ANISOTROPIC modes.<br/>
            The y-extent specifies the height of the rectangle within which the picture is drawn.<br/>
            The coordinates are in units that correspond to the mapping mode. <br/>
            For MM_ISOTROPIC and MM_ANISOTROPIC modes, which can be scaled, the xExt and yExt members
            contain an optional suggested size in MM_HIMETRIC units.<br/>
            For MM_ANISOTROPIC pictures, xExt and yExt can be zero when no suggested size is supplied.<br/>
            For MM_ISOTROPIC pictures, an aspect ratio must be supplied even when no suggested size is given.<br/>
            (If a suggested size is given, the aspect ratio is implied by the size.)<br/>
            To give an aspect ratio without implying a suggested size, set xExt and yExt to negative values
            whose ratio is the appropriate aspect ratio.<br/>
            The magnitude of the negative xExt and yExt values is ignored; only the ratio is used.
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.PictureDescriptor.MetafilePicture.hMf">
            <summary>
            Handle to a memory metafile.
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.PictureDescriptor.dxaGoal">
            <summary>
            Horizontal measurement in twips of the rectangle the picture should be imaged within.
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.PictureDescriptor.dyaGoal">
            <summary>
            Vertical measurement in twips of the rectangle the picture should be imaged within.
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.PictureDescriptor.mx">
            <summary>
            Horizontal scaling factor supplied by user expressed in .001% units
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.PictureDescriptor.my">
            <summary>
            Vertical scaling factor supplied by user expressed in .001% units
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.PictureDescriptor.mfp">
            <summary>
            The data of the windows metafile picture (WMF)
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.PictureDescriptor.brcTop">
            <summary>
            Border above picture
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.PictureDescriptor.brcLeft">
            <summary>
            Border to the left of the picture
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.PictureDescriptor.brcBottom">
            <summary>
            Border below picture
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.PictureDescriptor.brcRight">
            <summary>
            Border to the right of the picture
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.PictureDescriptor.dxaOrigin">
            <summary>
            Horizontal offset of hand annotation origin
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.PictureDescriptor.dyaOrigin">
            <summary>
            vertical offset of hand annotation origin
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.DocFileFormat.PictureDescriptor.#ctor(DIaLOGIKa.b2xtranslator.DocFileFormat.CharacterPropertyExceptions,DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.VirtualStream)">
            <summary>
            Parses the CHPX for a fcPic an loads the PictureDescriptor at this offset
            </summary>
            <param name="chpx">The CHPX that holds a SPRM for fcPic</param>
            <param name="stream"></param>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.DocFileFormat.PictureDescriptor.GetFcPic(DIaLOGIKa.b2xtranslator.DocFileFormat.CharacterPropertyExceptions)">
            <summary>
            Returns the fcPic into the "data" stream, where the PIC begins.
            Returns -1 if the CHPX has no fcPic.
            </summary>
            <param name="chpx">The CHPX</param>
            <returns></returns>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.PieceDescriptor.fc">
            <summary>
            File offset of beginning of piece. <br/>
            This is relative to the beginning of the WordDocument stream.
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.PieceDescriptor.encoding">
            <summary>
            The encoding of the piece
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.DocFileFormat.PieceDescriptor.#ctor(System.Byte[])">
            <summary>
            Parses the bytes to retrieve a PieceDescriptor
            </summary>
            <param name="bytes">The bytes</param>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.PieceTable.Pieces">
            <summary>
            A list of PieceDescriptor standing for each piece of text.
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.PieceTable.FileCharacterPositions">
            <summary>
            A dictionary with character positions as keys and the matching FCs as values
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.PieceTable.CharacterPositions">
            <summary>
            A dictionary with file character positions as keys and the matching CPs as values
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.DocFileFormat.PieceTable.#ctor(DIaLOGIKa.b2xtranslator.DocFileFormat.FileInformationBlock,DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.VirtualStream)">
            <summary>
            Parses the pice table and creates a list of PieceDescriptors.
            </summary>
            <param name="fib">The FIB</param>
            <param name="tableStream">The 0Table or 1Table stream</param>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.DocFileFormat.Plex`1.GetStruct(System.Int32)">
            <summary>
            Retruns the struct that matches the given character position.
            </summary>
            <param name="cp">The character position</param>
            <returns>The matching struct</returns>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.PropertyExceptions.grpprl">
            <summary>
            A list of the sprms that encode the differences between 
            CHP for a character and the PAP for the paragraph style used.
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.SectionDescriptor.fcSepx">
            <summary>
            A signed integer that specifies the position in the WordDocument Stream where a Sepx structure is located.
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.DocFileFormat.SectionPropertyExceptions.#ctor(System.Byte[])">
            <summary>
            Parses the bytes to retrieve a SectionPropertyExceptions
            </summary>
            <param name="bytes">The bytes starting with the grpprl</param>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.ShadingDescriptor.cvFore">
            <summary>
            24-bit foreground color
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.ShadingDescriptor.cvBack">
            <summary>
            24-bit background color
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.ShadingDescriptor.ipat">
            <summary>
            Shading pattern
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.DocFileFormat.ShadingDescriptor.#ctor">
            <summary>
            Creates a new ShadingDescriptor with default values
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.DocFileFormat.ShadingDescriptor.#ctor(System.Byte[])">
            <summary>
            Parses the bytes to retrieve a ShadingDescriptor.
            </summary>
            <param name="bytes">The bytes</param>
        </member>
        <member name="T:DIaLOGIKa.b2xtranslator.DocFileFormat.SinglePropertyModifier.SprmType">
            <summary>
            Identifies the type of a SPRM
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.SinglePropertyModifier.OpCode">
            <summary>
            The operation code identifies the property of the 
            PAP/CHP/PIC/SEP/TAP which sould be modified
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.SinglePropertyModifier.Type">
            <summary>
            The type of the SPRM
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.SinglePropertyModifier.Arguments">
            <summary>
            The arguments which is applied to the property
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.DocFileFormat.SinglePropertyModifier.#ctor(System.Byte[])">
            <summary>
            parses the byte to retrieve a SPRM
            </summary>
            <param name="bytes">The bytes</param>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.DocFileFormat.SinglePropertyModifier.GetOperandSize(System.Byte)">
            <summary>
            Get be used to get the size of the sprm's operand.
            Returns 0 if the Operation failed and 255 if the size is variable
            </summary>
            <param name="spra">the 3 bits for spra (as byte)</param>
            <returns>the size (as byte)</returns>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.SprmTDefTable.rgdxaCenter">
            <summary>
            An array of 16-bit signed integer that specifies horizontal distance in twips. <br/>
            MUST be greater than or equal to -31680 and less than or equal to 31680.
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.SprmTDefTable.rgTc80">
            <summary>
            An array of TC80 that specifies the default formatting for a cell in the table. <br/>
            Each TC80 in the array corresponds to the equivalent column in the table.<br/>
            If there are fewer TC80s than columns, the remaining columns are formatted with the default TC80 formatting. <br/>
            If there are more TC80s than columns, the excess TC80s MUST be ignored.
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.TC80.horzMerge">
            <summary>
            A value from the following table that specifies how this cell merges horizontally with the neighboring cells in the same row. <br/>
            MUST be one of the following values:<br/>
            0        The cell is not merged with the cells on either side of it.
            1        The cell is one of a set of horizontally merged cells. It contributes its layout region to the set and its own contents are not rendered.
            2, 3     The cell is the first cell in a set of horizontally merged cells. The contents and formatting of this cell extend into any consecutive cells following it that are designated as part of the merged set.
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.TC80.textFlow">
            <summary>
            A value from the TextFlow enumeration that specifies rotation settings for the text in the cell.
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.TC80.vertMerge">
            <summary>
            A value from the VerticalMergeFlag enumeration that specifies how this cell merges vertically with the cells above or below it.
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.TC80.vertAlign">
            <summary>
            A value from the VerticalAlign enumeration that specifies how contents inside this cell are aligned.
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.TC80.ftsWidth">
            <summary>
            An Fts that specifies the unit of measurement for the wWidth field in the TC80 structure.
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.TC80.fFitText">
            <summary>
            Specifies whether the contents of the cell are to be stretched out such that the full cell width is used.
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.TC80.fNoWrap">
            <summary>
            When set, specifies that the preferred layout of the contents of this cell are as a single line, 
            and cell widths can be adjusted to accommodate long lines. <br/>
            This preference is ignored when the preferred width of this cell is set to ftsDxa.
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.TC80.fHideMark">
            <summary>
            When set, specifies that this cell is rendered with no height if all cells in the row are empty.
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.TC80.wWidth">
            <summary>
            An integer that specifies the preferred width of the cell. 
            The width includes cell margins, but does not include cell spacing. MUST be non-negative.<br/>
            The unit of measurement depends on ftsWidth.
            If ftsWidth is set to ftsPercent, the value is a fraction of the width of the entire table.
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.StyleSheet.stshi">
            <summary>
            The StyleSheetInformation of the stylesheet.
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.StyleSheet.Styles">
            <summary>
            The list contains all styles.
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.DocFileFormat.StyleSheet.#ctor(DIaLOGIKa.b2xtranslator.DocFileFormat.FileInformationBlock,DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.VirtualStream,DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.VirtualStream)">
            <summary>
            Parses the streams to retrieve a StyleSheet.
            </summary>
            <param name="fib">The FileInformationBlock</param>
            <param name="tableStream">The 0Table or 1Table stream</param>
            <param name="dataStream"></param>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.StyleSheetDescription.xstzName">
            <summary>
            The name of the style
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.StyleSheetDescription.sti">
            <summary>
            Invariant style identifier 
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.StyleSheetDescription.stk">
            <summary>
            style kind 
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.StyleSheetDescription.istdBase">
            <summary>
            base style 
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.StyleSheetDescription.cupx">
            <summary>
            number of UPXs (and UPEs) 
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.StyleSheetDescription.istdNext">
            <summary>
            next style
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.StyleSheetDescription.fHidden">
            <summary>
            hidden from UI? 
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.StyleSheetDescription.fSemiHidden">
            <summary>
            Do not show this style in long style lists 
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.StyleSheetDescription.fLocked">
            <summary>
            Locked style? 
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.StyleSheetDescription.istdLink">
            <summary>
            Is this style linked to another?
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.StyleSheetDescription.papx">
            <summary>
            A StyleSheetDescription can have a PAPX. <br/>
            If the style doesn't modify paragraph properties, papx is null.
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.StyleSheetDescription.chpx">
            <summary>
            A StyleSheetDescription can have a CHPX. <br/>
            If the style doesn't modify character properties, chpx is null.
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.StyleSheetDescription.tapx">
            <summary>
            A StyleSheetDescription can have a TAPX. <br/>
            If the style doesn't modify table properties, tapx is null.
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.DocFileFormat.StyleSheetDescription.#ctor">
            <summary>
            Creates an empty STD object
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.DocFileFormat.StyleSheetDescription.#ctor(System.Byte[],System.Int32,DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.VirtualStream)">
            <summary>
            Parses the bytes to retrieve a StyleSheetDescription
            </summary>
            <param name="bytes">The bytes</param>
            <param name="cbStdBase"></param>
            <param name="dataStream">The "Data" stream (optional, can be null)</param>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.StyleSheetInformation.cstd">
            <summary>
            Count of styles in stylesheet
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.StyleSheetInformation.cbSTDBaseInFile">
            <summary>
            Length of STD Base as stored in a file
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.StyleSheetInformation.stiMaxWhenSaved">
            <summary>
            Max sti known when this file was written
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.StyleSheetInformation.rgftcStandardChpStsh">
            <summary>
            This is a list of the default fonts for this style sheet.<br/>
            The first is for ASCII characters (0-127), the second is for East Asian characters,
            and the third is the default font for non-East Asian, non-ASCII text.
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.StyleSheetInformation.cbLSD">
            <summary>
            Size of each lsd in mpstilsd<br/>
            The count of lsd's is stiMaxWhenSaved
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.StyleSheetInformation.mpstilsd">
            <summary>
            latent style data (size == stiMaxWhenSaved upon save!)
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.DocFileFormat.StyleSheetInformation.#ctor(System.Byte[])">
            <summary>
            Parses the bytes to retrieve a StyleSheetInformation
            </summary>
            <param name="bytes"></param>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.TabDescriptor.jc">
            <summary>
            Justification code:<br/>
            0 left tab<br/>
            1 centered tab<br/>
            2 right tab<br/>
            3 decimal tab<br/>
            4 bar
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.TabDescriptor.tlc">
            <summary>
            Tab leader code:<br/>
            0 no leader<br/>
            1 dotted leader<br/>
            2 hyphenated leader<br/>
            3 single line leader<br/>
            4 heavy line leader<br/>
            5 middle dot
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.DocFileFormat.TabDescriptor.#ctor(System.Byte)">
            <summary>
            Parses the bytes to retrieve a TabDescriptor
            </summary>
            <param name="b">The byte</param>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.DocFileFormat.TablePropertyExceptions.#ctor">
            <summary>
            Creates a TAPX wich doesn't modify anything.<br/>
            The grpprl list is empty
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.DocFileFormat.TablePropertyExceptions.#ctor(System.Byte[])">
            <summary>
            Parses the bytes to retrieve a TAPX
            </summary>
            <param name="bytes">The bytes starting with the istd</param>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.DocFileFormat.TablePropertyExceptions.#ctor(DIaLOGIKa.b2xtranslator.DocFileFormat.ParagraphPropertyExceptions,DIaLOGIKa.b2xtranslator.StructuredStorage.Reader.VirtualStream)">
            <summary>
            Extracts the TAPX SPRMs out of a PAPX
            </summary>
            <param name="papx"></param>
            <param name="dataStream"></param>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.ToolbarControl.tct">
            <summary>
            
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.ToolbarControl.tcid">
            <summary>
            Unsigned integer that specifies the toolbar control identifier for this toolbar control.<br/> 
            MUST be 0x0001 when the toolbar control is a custom toolbar control or MUST be equal 
            to one of the values listed in [MS-CTDOC] section 2.2 or in [MS-CTXLS] section 2.2 
            when the toolbar control is not a custom toolbar control.
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.ToolbarControlBitmap.cbDIB">
            <summary>
            Signed integer that specifies the count of total bytes, excluding this field, 
            in the TBCBitmap structure plus 10. Value is given by the following formula: <br/>
            cbDIB = sizeOf(biHeader) + sizeOf(colors) + sizeOf(bitmapData) + 10<br/>
            MUST be greater or equal to 40, and MUST be less or equal to 65576.
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.ToolbarCustomization.tbidForTBD">
            <summary>
            Signed integer that specifies if customizationData contains a CTB structure or an array of TBDelta structures.  <br/>
            MUST be greater than or equal to 0x00000000.  <br/>
            If value equals 0x00000000, customizationData MUST contain a CTB structure. <br/>
            If value does not equal 0x00000000, customizationData MUST contain an array of TBDelta structures 
            and the value of this field specifies the toolbar identifier of the toolbar affected by 
            the TBDelta structures contained in the array.
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.ToolbarCustomization.ctbds">
            <summary>
            Signed integer that specifies, if tbidForTBD is not equal to 0x00000000, the number of TBDelta 
            structures contained in the customizationData array. <br/>
            MUST be 0x0000 if tbidForTBD equals 0x00000000.
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.WordDocument.AllSepx">
            <summary>
            A dictionary that contains all SEPX of the document.<br/>
            The key is the CP at which sections ends.<br/>
            The value is the SEPX that formats the section.
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.WordDocument.AllPapx">
            <summary>
            A dictionary that contains all PAPX of the document.<br/>
            The key is the FC at which the paragraph starts.<br/>
            The value is the PAPX that formats the paragraph.
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.WordDocument.PieceTable">
            <summary>
            
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.WordDocument.SectionPlex">
            <summary>
            A Plex containing all section descriptors
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.WordDocument.RevisionAuthorTable">
            <summary>
            Contains the names of all author who revised something in the document
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.WordDocument.WordDocumentStream">
            <summary>
            The stream "WordDocument"
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.WordDocument.TableStream">
            <summary>
            The stream "0Table" or "1Table"
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.WordDocument.DataStream">
            <summary>
            The stream called "Data"
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.WordDocument.Storage">
            <summary>
            The StructuredStorageFile itself
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.WordDocument.FIB">
            <summary>
            The file information block of the word document
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.WordDocument.Text">
            <summary>
            All text of the Word document
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.WordDocument.Styles">
            <summary>
            The style sheet of the document
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.WordDocument.FontTable">
            <summary>
            A list of all font names, used in the doucument
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.WordDocument.AnnotationsReferencePlex">
            <summary>
            A plex with all ATRDPre10 structs
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.WordDocument.AnnotationReferenceExtraTable">
            <summary>
            An array with all ATRDPost10 structs
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.WordDocument.ListTable">
            <summary>
            A list that contains all formatting information of 
            the lists and numberings in the document.
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.WordDocument.OfficeArtContent">
            <summary>
            The drawing object table ....
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.WordDocument.OfficeDrawingPlex">
            <summary>
            
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.WordDocument.OfficeDrawingPlexHeader">
            <summary>
            
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.WordDocument.AutoTextPlex">
            <summary>
            Each character position specifies the beginning of a range of text 
            that constitutes the contents of an AutoText item.
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.WordDocument.TextboxBreakPlex">
            <summary>
            Describes the breaks inside the textbox subdocument
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.WordDocument.TextboxBreakPlexHeader">
            <summary>
            Describes the breaks inside the header textbox subdocument
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.WordDocument.DocumentProperties">
            <summary>
            The DocumentProperties of the word document
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.WordDocument.ListFormatOverrideTable">
            <summary>
            A list that contains all overriding formatting information
            of the lists and numberings in the document.
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.WordDocument.AllPapxFkps">
            <summary>
            A list of all FKPs that contain PAPX
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.WordDocument.AllChpxFkps">
            <summary>
            A list of all FKPs that contain CHPX
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.DocFileFormat.WordDocument.HeaderAndFooterTable">
            <summary>
            A table that contains the positions of the headers and footer in the text.
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.DocFileFormat.WordDocument.GetFileCharacterPositions(System.Int32,System.Int32)">
            <summary>
            Returns a list of all CHPX which are valid for the given FCs.
            </summary>
            <param name="fcMin">The lower boundary</param>
            <param name="fcMax">The upper boundary</param>
            <returns>The FCs</returns>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.DocFileFormat.WordDocument.GetCharacterPropertyExceptions(System.Int32,System.Int32)">
            <summary>
            Returnes a list of all CharacterPropertyExceptions which correspond to text 
            between the given boundaries.
            </summary>
            <param name="fcMin">The lower boundary</param>
            <param name="fcMax">The upper boundary</param>
            <returns>The FCs</returns>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.WordprocessingMLMapping.OleObject.Path">
            <summary>
            The path of the object in the storage
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.WordprocessingMLMapping.OleObject.fLinked">
            <summary>
            The the value is true, the object is a linked object
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.WordprocessingMLMapping.CharacterPropertiesMapping.appendFlagElement(System.Xml.XmlElement,DIaLOGIKa.b2xtranslator.DocFileFormat.SinglePropertyModifier,System.String,System.Boolean)">
            <summary>
            CHPX flags are special flags because the can be 0,1,128 and 129,
            so this method overrides the appendFlagElement method.
            </summary>
        </member>
        <member name="P:DIaLOGIKa.b2xtranslator.WordprocessingMLMapping.ConversionContext.Doc">
            <summary>
            The source of the conversion.
            </summary>
        </member>
        <member name="P:DIaLOGIKa.b2xtranslator.WordprocessingMLMapping.ConversionContext.Docx">
            <summary>
            This is the target of the conversion.<br/>
            The result will be written to the parts of this document.
            </summary>
        </member>
        <member name="P:DIaLOGIKa.b2xtranslator.WordprocessingMLMapping.ConversionContext.WriterSettings">
            <summary>
            The settings of the XmlWriter which writes to the part
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.WordprocessingMLMapping.ConversionContext.AllRsids">
            <summary>
            A list thta contains all revision ids.
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.WordprocessingMLMapping.ConversionContext.AddRsid(System.String)">
            <summary>
            Adds a new RSID to the list
            </summary>
            <param name="rsid"></param>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.WordprocessingMLMapping.DateMapping.#ctor(System.Xml.XmlWriter)">
            <summary>
            Writes a date attribute to the given writer
            </summary>
            <param name="writer"></param>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.WordprocessingMLMapping.DateMapping.#ctor(System.Xml.XmlElement)">
            <summary>
            Appends a date attribute to the given Element
            </summary>
            <param name="parent"></param>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.WordprocessingMLMapping.DocumentMapping.#ctor(DIaLOGIKa.b2xtranslator.WordprocessingMLMapping.ConversionContext,DIaLOGIKa.b2xtranslator.OpenXmlLib.ContentPart,System.Xml.XmlWriter)">
            <summary>
            Creates a new DocumentMapping that writes to the given XmlWriter
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.WordprocessingMLMapping.DocumentMapping.#ctor(DIaLOGIKa.b2xtranslator.WordprocessingMLMapping.ConversionContext,DIaLOGIKa.b2xtranslator.OpenXmlLib.ContentPart)">
            <summary>
            Creates a new DocumentMapping that creates a new XmLWriter on to the given ContentPart
            </summary>
            <param name="ctx"></param>
            <param name="targetPart"></param>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.WordprocessingMLMapping.DocumentMapping.writeTable(System.Int32,System.UInt32)">
            <summary>
            Writes the table starts at the given cp value
            </summary>
            <param name="initialCp">The cp at where the table begins</param>
            <param name="nestingLevel"></param>
            <returns>The character pointer to the first character after this table</returns>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.WordprocessingMLMapping.DocumentMapping.writeTableRow(System.Int32,System.Collections.Generic.List{System.Int16},System.UInt32)">
            <summary>
            Writes the table row that starts at the given cp value and ends at the next row end mark
            </summary>
            <param name="initialCp">The cp at where the row begins</param>
            <param name="grid"></param>
            <param name="nestingLevel"></param>
            <returns>The character pointer to the first character after this row</returns>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.WordprocessingMLMapping.DocumentMapping.writeTableCell(System.Int32,DIaLOGIKa.b2xtranslator.DocFileFormat.TablePropertyExceptions,System.Collections.Generic.List{System.Int16},System.Int32@,System.Int32,System.UInt32)">
            <summary>
            Writes the table cell that starts at the given cp value and ends at the next cell end mark
            </summary>
            <param name="initialCp">The cp at where the cell begins</param>
            <param name="tapx">The TAPX that formats the row to which the cell belongs</param>
            <param name="grid"></param>
            <param name="gridIndex">The index of this cell in the grid</param>
            <param name="cellIndex">The grid</param>
            <param name="nestingLevel">The grid</param>
            <returns>The character pointer to the first character after this cell</returns>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.WordprocessingMLMapping.DocumentMapping.buildTableGrid(System.Int32,System.UInt32)">
            <summary>
            Builds a list that contains the width of the several columns of the table.
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.WordprocessingMLMapping.DocumentMapping.findRowEndFc(System.Int32,System.Int32@,System.UInt32)">
            <summary>
            Finds the FC of the next row end mark.
            </summary>
            <param name="initialCp">Some CP before the row end</param>
            <param name="rowEndCp">The CP of the next row end mark</param>
            <param name="nestingLevel"></param>
            <returns>The FC of the next row end mark</returns>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.WordprocessingMLMapping.DocumentMapping.findRowEndFc(System.Int32,System.UInt32)">
            <summary>
            Finds the FC of the next row end mark.
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.WordprocessingMLMapping.DocumentMapping.writeParagraph(System.Int32)">
            <summary>
            Writes a Paragraph that starts at the given cp and 
            ends at the next paragraph end mark or section end mark
            </summary>
            <param name="cp"></param>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.WordprocessingMLMapping.DocumentMapping.writeParagraph(System.Int32,System.Int32,System.Boolean)">
            <summary>
            Writes a Paragraph that starts at the given cpStart and 
            ends at the given cpEnd
            </summary>
            <param name="initialCp"></param>
            <param name="cpEnd"></param>
            <param name="sectionEnd">Set if this paragraph is the last paragraph of a section</param>
            <returns></returns>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.WordprocessingMLMapping.DocumentMapping.writeRun(System.Collections.Generic.List{System.Char},DIaLOGIKa.b2xtranslator.DocFileFormat.CharacterPropertyExceptions,System.Int32)">
            <summary>
            Writes a run with the given characters and CHPX
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.WordprocessingMLMapping.DocumentMapping.writeText(System.Collections.Generic.List{System.Char},System.Int32,DIaLOGIKa.b2xtranslator.DocFileFormat.CharacterPropertyExceptions,System.Boolean)">
            <summary>
            Writes the given text to the document
            </summary>
            <param name="chars"></param>
            <param name="initialCp"></param>
            <param name="chpx"></param>
            <param name="writeDeletedText"></param>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.WordprocessingMLMapping.DocumentMapping.writeBookmarkStarts(System.Int32)">
            <summary>
            Writes a bookmark start element at the given position
            </summary>
            <param name="cp"></param>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.WordprocessingMLMapping.DocumentMapping.writeBookmarkEnds(System.Int32)">
            <summary>
            Writes a bookmark end element at the given position
            </summary>
            <param name="cp"></param>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.WordprocessingMLMapping.DocumentMapping.splitCharList(System.Collections.Generic.List{System.Char},System.Collections.Generic.List{System.Int32})">
            <summary>
            Splits a list of characters into several lists
            </summary>
            <returns></returns>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.WordprocessingMLMapping.DocumentMapping.searchBookmarks(System.Collections.Generic.List{System.Char},System.Int32)">
            <summary>
            Searches for bookmarks in the list of characters.
            </summary>
            <returns>A List with all bookmarks indices in the given character list</returns>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.WordprocessingMLMapping.DocumentMapping.searchNextTextMark(System.Collections.Generic.List{System.Char},System.Int32,System.Char)">
            <summary>
            Searches the given List for the next FieldEnd character.
            </summary>
            <param name="chars">The List of chars</param>
            <param name="initialCp">The position where the search should start</param>
            <param name="mark">The TextMark</param>
            <returns>The position of the next FieldEnd mark</returns>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.WordprocessingMLMapping.DocumentMapping.isOld(DIaLOGIKa.b2xtranslator.DocFileFormat.ParagraphPropertyExceptions)">
            <summary>
            Checks if the PAPX is old
            </summary>
            <param name="papx">The PAPX</param>
            <returns></returns>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.WordprocessingMLMapping.DocumentMapping.isSpecial(DIaLOGIKa.b2xtranslator.DocFileFormat.CharacterPropertyExceptions)">
            <summary>
            Checks if the CHPX is special
            </summary>
            <param name="chpx">The CHPX</param>
            <returns></returns>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.WordprocessingMLMapping.DocumentMapping.getSymbol(DIaLOGIKa.b2xtranslator.DocFileFormat.CharacterPropertyExceptions)">
            <summary>
            
            </summary>
            <param name="chpx"></param>
            <returns></returns>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.WordprocessingMLMapping.DocumentMapping.isSectionEnd(System.Int32)">
            <summary>
            Looks into the section table to find out if this CP is the end of a section
            </summary>
            <param name="cp"></param>
            <returns></returns>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.WordprocessingMLMapping.DocumentMapping.findValidPapx(System.Int32)">
            <summary>
            Finds the PAPX that is valid for the given FC.
            </summary>
            <param name="fc"></param>
            <returns></returns>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.WordprocessingMLMapping.DocumentMapping.findValidSepx(System.Int32)">
            <summary>
            Finds the SEPX that is valid for the given CP.
            </summary>
            <param name="cp"></param>
            <returns></returns>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.WordprocessingMLMapping.NumberingMapping.getLvlText(System.String)">
            <summary>
            Converts the number text of the binary format to the number text of OOXML.
            OOXML uses different placeholders for the numbers.
            </summary>
            <param name="numberText">The number text of the binary format</param>
            <returns></returns>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.WordprocessingMLMapping.NumberingMapping.GetNumberFormat(System.Int32)">
            <summary>
            Converts the number format code of the binary format.
            </summary>
            <param name="nfc">The number format code</param>
            <returns>The OOXML attribute value</returns>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.WordprocessingMLMapping.OleObjectMapping.copyEmbeddedObject(DIaLOGIKa.b2xtranslator.WordprocessingMLMapping.OleObject,DIaLOGIKa.b2xtranslator.OpenXmlLib.EmbeddedObjectPart)">
            <summary>
            Writes the embedded OLE object from the ObjectPool of the binary file to the OpenXml Package.
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.WordprocessingMLMapping.RevisionData.#ctor(DIaLOGIKa.b2xtranslator.DocFileFormat.CharacterPropertyExceptions)">
            <summary>
            Collects the revision data of a CHPX
            </summary>
            <param name="chpx"></param>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.WordprocessingMLMapping.SectionPropertiesMapping.#ctor(System.Xml.XmlWriter,DIaLOGIKa.b2xtranslator.WordprocessingMLMapping.ConversionContext,System.Int32)">
            <summary>
            Creates a new SectionPropertiesMapping which writes the
            properties to the given writer
            </summary>
            <param name="writer">The XmlWriter</param>
            <param name="ctx"></param>
            <param name="sectionNr"></param>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.WordprocessingMLMapping.SectionPropertiesMapping.#ctor(System.Xml.XmlElement,DIaLOGIKa.b2xtranslator.WordprocessingMLMapping.ConversionContext,System.Int32)">
            <summary>
            Creates a new SectionPropertiesMapping which appends
            the properties to a given node.
            </summary>
            <param name="sectPr">The sectPr node</param>
            <param name="ctx"></param>
            <param name="sectionNr"></param>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.WordprocessingMLMapping.SectionPropertiesMapping.Apply(DIaLOGIKa.b2xtranslator.DocFileFormat.SectionPropertyExceptions)">
            <summary>
            Converts the given SectionPropertyExceptions
            </summary>
            <param name="sepx"></param>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.WordprocessingMLMapping.StyleSheetMapping.MakeStyleId(DIaLOGIKa.b2xtranslator.DocFileFormat.StyleSheetDescription)">
            <summary>
            Generates a style id for custom style names or returns the build-in identifier for build-in styles.
            </summary>
            <param name="std">The StyleSheetDescription</param>
            <returns></returns>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.WordprocessingMLMapping.StyleSheetMapping.getStyleName(DIaLOGIKa.b2xtranslator.DocFileFormat.StyleSheetDescription)">
            <summary>
            Chooses the correct style name.
            Word 2007 needs the identifier instead of the stylename for translating it into the UI language.
            </summary>
            <param name="std">The StyleSheetDescription</param>
            <returns></returns>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.WordprocessingMLMapping.StyleSheetMapping.writeNormalTableStyle">
            <summary>
            Writes the "NormalTable" default style
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.WordprocessingMLMapping.TableCellPropertiesMapping._gridSpan">
            <summary>
            The grind span of this cell
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.WordprocessingMLMapping.TableCellPropertiesMapping.getLastTabelBorderOccurrence(System.Collections.Generic.List{DIaLOGIKa.b2xtranslator.DocFileFormat.SinglePropertyModifier})">
            <summary>
            Returns the index of the last occurence of an sprmTTableBorders or sprmTTableBorders80 sprm.
            </summary>
            <param name="grpprl">The grpprl of sprms</param>
            <returns>The index or -1 if no sprm is in the list</returns>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.WordprocessingMLMapping.TableInfo.fInTable">
            <summary>
            
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.WordprocessingMLMapping.TableInfo.fTtp">
            <summary>
            
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.WordprocessingMLMapping.TableInfo.fInnerTtp">
            <summary>
            
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.WordprocessingMLMapping.TableInfo.fInnerTableCell">
            <summary>
            
            </summary>
        </member>
        <member name="F:DIaLOGIKa.b2xtranslator.WordprocessingMLMapping.TableInfo.iTap">
            <summary>
            
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.WordprocessingMLMapping.VMLPictureMapping.writePictureBorder(System.String,DIaLOGIKa.b2xtranslator.DocFileFormat.BorderCode)">
            <summary>
            Writes a border element
            </summary>
            <param name="name">The name of the element</param>
            <param name="brc">The BorderCode object</param>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.WordprocessingMLMapping.VMLPictureMapping.copyPicture(DIaLOGIKa.b2xtranslator.OfficeDrawing.BlipStoreEntry)">
            <summary>
            Copies the picture from the binary stream to the zip archive 
            and creates the relationships for the image.
            </summary>
            <param name="bse">The PictureDescriptor</param>
            <returns>The created ImagePart</returns>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.WordprocessingMLMapping.VMLShapeMapping.convertGroup(DIaLOGIKa.b2xtranslator.OfficeDrawing.GroupContainer)">
            <summary>
            Converts a group of shapes
            </summary>
            <param name="container"></param>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.WordprocessingMLMapping.VMLShapeMapping.convertShape(DIaLOGIKa.b2xtranslator.OfficeDrawing.ShapeContainer)">
            <summary>
            Converts a single shape
            </summary>
            <param name="container"></param>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.WordprocessingMLMapping.VMLShapeMapping.getFillType(System.UInt32)">
            <summary>
            Returns the OpenXML fill type of a fill effect
            </summary>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.WordprocessingMLMapping.VMLShapeMapping.getWrapType(DIaLOGIKa.b2xtranslator.DocFileFormat.FileShapeAddress)">
            <summary>
            Returns the OpenXML wrap type of the shape
            </summary>
            <param name="fspa"></param>
            <returns></returns>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.WordprocessingMLMapping.VMLShapeMapping.getWrapCoords(DIaLOGIKa.b2xtranslator.OfficeDrawing.ShapeOptions.OptionEntry)">
            <summary>
            Build the VML wrapcoords string for a given pWrapPolygonVertices
            </summary>
            <param name="pWrapPolygonVertices"></param>
            <returns></returns>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.WordprocessingMLMapping.VMLShapeMapping.copyPicture(DIaLOGIKa.b2xtranslator.OfficeDrawing.BlipStoreEntry)">
            <summary>
            Copies the picture from the binary stream to the zip archive 
            and creates the relationships for the image.
            </summary>
            <param name="bse">The PictureDescriptor</param>
            <returns>The created ImagePart</returns>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.WordprocessingMLMapping.VMLShapeMapping.getShapeId(DIaLOGIKa.b2xtranslator.OfficeDrawing.Shape)">
            <summary>
            Generates a string id for the given shape
            </summary>
            <param name="shape"></param>
            <returns></returns>
        </member>
        <member name="M:DIaLOGIKa.b2xtranslator.WordprocessingMLMapping.VMLShapeTypeMapping.GenerateTypeId(DIaLOGIKa.b2xtranslator.OfficeDrawing.Shapetypes.ShapeType)">
            <summary>
            Returns the id of the referenced type
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Flow.FormatProviders.Doc.DocFormatProvider">
            <summary>
            Represents Doc format provider.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Flow.FormatProviders.Doc.DocFormatProvider.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Flow.FormatProviders.Doc.DocFormatProvider" /> class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Flow.FormatProviders.Doc.DocFormatProvider.SupportedExtensions">
            <summary>
            Gets the supported extensions.
            </summary>
            <value>The supported extensions.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Flow.FormatProviders.Doc.DocFormatProvider.CanImport">
            <summary>
            Gets a value indicating whether can import.
            </summary>
            <value>The value indicating whether can import.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Flow.FormatProviders.Doc.DocFormatProvider.CanExport">
            <summary>
            Gets a value indicating whether can export.
            </summary>
            <value>The value indicating whether can export.</value>
        </member>
        <member name="M:Telerik.Windows.Documents.Flow.FormatProviders.Doc.DocFormatProvider.ImportOverride(System.IO.Stream)">
            <summary>
            Imports the specified input.
            </summary>
            <param name="input">The input stream containing the doc file.</param>
            <returns>The imported document.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Flow.FormatProviders.Doc.DocFormatProvider.ImportOverride(System.IO.Stream,System.Threading.CancellationToken)">
            <summary>
            Imports the specified input.
            </summary>
            <param name="input">The input stream containing the doc file.</param>
            <param name="cancellationToken">The cancellation token used to cancel the operation.</param>
            <returns>The imported document.</returns>
        </member>
    </members>
</doc>
