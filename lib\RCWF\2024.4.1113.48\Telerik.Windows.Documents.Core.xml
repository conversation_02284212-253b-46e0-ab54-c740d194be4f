<?xml version="1.0"?>
<doc>
    <assembly>
        <name>Telerik.Windows.Documents.Core</name>
    </assembly>
    <members>
        <member name="T:Telerik.Windows.Documents.Common.FormatProviders.BinaryFormatProviderBase`1">
            <summary>
            Represents base type for binary format provider.
            </summary>
            <typeparam name="T">The type of the document.</typeparam>
        </member>
        <member name="M:Telerik.Windows.Documents.Common.FormatProviders.BinaryFormatProviderBase`1.Import(System.Byte[])">
            <summary>
            Imports the specified input.
            </summary>
            <param name="input">The input.</param>
            <returns>The document.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Common.FormatProviders.BinaryFormatProviderBase`1.Import(System.Byte[],System.Nullable{System.TimeSpan})">
            <summary>
            Imports the specified input.
            </summary>
            <param name="input">The input.</param>
            <param name="timeout">The timeout after which the operation will be cancelled.</param>
            <returns>The document.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Common.FormatProviders.BinaryFormatProviderBase`1.Export(`0)">
            <summary>
            Exports the specified document.
            </summary>
            <param name="document">The document.</param>
            <returns>The result byte array.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Common.FormatProviders.BinaryFormatProviderBase`1.Export(`0,System.Nullable{System.TimeSpan})">
            <summary>
            Exports the specified document.
            </summary>
            <param name="document">The document.</param>
            <param name="timeout">The timeout after which the operation will be cancelled.</param>
            <returns></returns>
        </member>
        <member name="T:Telerik.Windows.Documents.Common.FormatProviders.FormatProviderBase`1">
            <summary>
            Represents base binary format provider.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Common.FormatProviders.FormatProviderBase`1.SupportedExtensions">
            <summary>
            Gets the supported extensions.
            </summary>
            <value>
            The supported extensions.
            </value>
        </member>
        <member name="P:Telerik.Windows.Documents.Common.FormatProviders.FormatProviderBase`1.CanImport">
            <summary>
            Gets a value indicating whether format provider can import.
            </summary>
            <value>The value indicating whether can import.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Common.FormatProviders.FormatProviderBase`1.CanExport">
            <summary>
            Gets a value indicating whether format provider can export.
            </summary>
            <value>The value indicating whether can export.</value>
        </member>
        <member name="M:Telerik.Windows.Documents.Common.FormatProviders.FormatProviderBase`1.Import(System.IO.Stream)">
            <summary>
            Imports the specified input stream.
            </summary>
            <param name="input">The input stream.</param>
            <returns>The imported document.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Common.FormatProviders.FormatProviderBase`1.Import(System.IO.Stream,System.Nullable{System.TimeSpan})">
            <summary>
            Imports the specified input stream.
            </summary>
            <param name="input">The input stream.</param>
            <param name="timeout">The timeout after which the operation will be cancelled.</param>
            <returns>The imported document.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Common.FormatProviders.FormatProviderBase`1.Export(`0,System.IO.Stream)">
            <summary>
            Exports the specified document to the output stream.
            </summary>
            <param name="document">The document.</param>
            <param name="output">The output stream.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Common.FormatProviders.FormatProviderBase`1.Export(`0,System.IO.Stream,System.Nullable{System.TimeSpan})">
            <summary>
            Exports the specified document to the output stream.
            </summary>
            <param name="document">The document.</param>
            <param name="output">The output stream.</param>
            <param name="timeout">The timeout after which the operation will be cancelled.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Common.FormatProviders.FormatProviderBase`1.ImportOverride(System.IO.Stream)">
            <summary>
            Imports the specified input.
            </summary>
            <param name="input">The input.</param>
            <returns>The imported document.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Common.FormatProviders.FormatProviderBase`1.ImportOverride(System.IO.Stream,System.Threading.CancellationToken)">
            <summary>
            Imports the specified input.
            </summary>
            <param name="input">The input.</param>
            <param name="cancellationToken">The cancellation token used to cancel the operation.</param>
            <returns>The imported document.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Common.FormatProviders.FormatProviderBase`1.ExportOverride(`0,System.IO.Stream)">
            <summary>
            Exports the specified document.
            </summary>
            <param name="document">The document.</param>
            <param name="output">The output.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Common.FormatProviders.FormatProviderBase`1.ExportOverride(`0,System.IO.Stream,System.Threading.CancellationToken)">
            <summary>
            Exports the specified document.
            </summary>
            <param name="document">The document.</param>
            <param name="output">The output.</param>
            <param name="cancellationToken">The cancellation token used to cancel the operation.</param>
        </member>
        <member name="T:Telerik.Windows.Documents.Common.FormatProviders.IBinaryFormatProvider`1">
            <summary>
            Represents binary format provider.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Common.FormatProviders.IBinaryFormatProvider`1.Import(System.Byte[])">
            <summary>
            Imports the specified input.
            </summary>
            <param name="input">The input.</param>
            <returns>The document.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Common.FormatProviders.IBinaryFormatProvider`1.Import(System.Byte[],System.Nullable{System.TimeSpan})">
            <summary>
            Imports the specified input.
            </summary>
            <param name="input">The input.</param>
            <param name="timeout">The timeout after which the operation will be cancelled.</param>
            <returns>The document.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Common.FormatProviders.IBinaryFormatProvider`1.Export(`0)">
            <summary>
            Exports the specified document.
            </summary>
            <param name="document">The document.</param>
            <returns>The result byte array.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Common.FormatProviders.IBinaryFormatProvider`1.Export(`0,System.Nullable{System.TimeSpan})">
            <summary>
            Exports the specified document.
            </summary>
            <param name="document">The document.</param>
            <param name="timeout">The timeout after which the operation will be cancelled.</param>
            <returns>The result byte array.</returns>
        </member>
        <member name="T:Telerik.Windows.Documents.Common.FormatProviders.IFormatProvider`1">
            <summary>
            Represents interface for format provider.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Common.FormatProviders.IFormatProvider`1.SupportedExtensions">
            <summary>
            Gets the supported extensions.
            </summary>
            <value>The supported extensions.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Common.FormatProviders.IFormatProvider`1.CanImport">
            <summary>
            Gets a value indicating whether format provider can import.
            </summary>
            <value>The value indicating whether can import.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Common.FormatProviders.IFormatProvider`1.CanExport">
            <summary>
            Gets a value indicating whether format provider can export.
            </summary>
            <value>The value indicating whether can export.</value>
        </member>
        <member name="M:Telerik.Windows.Documents.Common.FormatProviders.IFormatProvider`1.Import(System.IO.Stream)">
            <summary>
            Imports the specified input stream.
            </summary>
            <param name="input">The input stream.</param>
            <returns>The imported document.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Common.FormatProviders.IFormatProvider`1.Import(System.IO.Stream,System.Nullable{System.TimeSpan})">
            <summary>
            Imports the specified input stream.
            </summary>
            <param name="input">The input stream.</param>
            <param name="timeout">The timeout after which the operation will be cancelled.</param>
            <returns>The imported document.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Common.FormatProviders.IFormatProvider`1.Export(`0,System.IO.Stream)">
            <summary>
            Exports the specified document to the output stream.
            </summary>
            <param name="document">The document.</param>
            <param name="output">The output stream.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Common.FormatProviders.IFormatProvider`1.Export(`0,System.IO.Stream,System.Nullable{System.TimeSpan})">
            <summary>
            Exports the specified document to the output stream.
            </summary>
            <param name="document">The document.</param>
            <param name="output">The output stream.</param>
            <param name="timeout">The timeout after which the operation will be cancelled.</param>
        </member>
        <member name="T:Telerik.Windows.Documents.Common.FormatProviders.ITextBasedFormatProvider`1">
            <summary>
            Represents interface for text base format provider.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Common.FormatProviders.ITextBasedFormatProvider`1.Import(System.String)">
            <summary>
            Imports the specified input.
            </summary>
            <param name="input">The input.</param>
            <returns>The document.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Common.FormatProviders.ITextBasedFormatProvider`1.Import(System.String,System.Nullable{System.TimeSpan})">
            <summary>
            Imports the specified input.
            </summary>
            <param name="input">The input.</param>
            <param name="timeout">The timeout after which the operation will be cancelled.</param>
            <returns>The document.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Common.FormatProviders.ITextBasedFormatProvider`1.Export(`0)">
            <summary>
            Exports the specified document.
            </summary>
            <param name="document">The document.</param>
            <returns>The result string.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Common.FormatProviders.ITextBasedFormatProvider`1.Export(`0,System.Nullable{System.TimeSpan})">
            <summary>
            Exports the specified document.
            </summary>
            <param name="document">The document.</param>
            <param name="timeout">The timeout after which the operation will be cancelled.</param>
            <returns>The result string.</returns>
        </member>
        <member name="T:Telerik.Windows.Documents.Common.FormatProviders.TextBasedFormatProviderBase`1">
            <summary>
            Represents base class for text base format provider.
            </summary>
            <typeparam name="T">The type of the T.</typeparam>
        </member>
        <member name="M:Telerik.Windows.Documents.Common.FormatProviders.TextBasedFormatProviderBase`1.Import(System.String)">
            <summary>
            Imports the specified input.
            </summary>
            <param name="input">The input.</param>
            <returns>The result document.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Common.FormatProviders.TextBasedFormatProviderBase`1.Import(System.String,System.Nullable{System.TimeSpan})">
            <summary>
            Imports the specified input.
            </summary>
            <param name="input">The input.</param>
            <param name="timeout">The timeout after which the operation will be cancelled.</param>
            <returns>The result document.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Common.FormatProviders.TextBasedFormatProviderBase`1.Export(`0)">
            <summary>
            Exports the specified document.
            </summary>
            <param name="document">The document.</param>
            <returns>The result string.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Common.FormatProviders.TextBasedFormatProviderBase`1.Export(`0,System.Nullable{System.TimeSpan})">
            <summary>
            Exports the specified document.
            </summary>
            <param name="document">The document.</param>
            <param name="timeout">The timeout after which the operation will be cancelled.</param>
            <returns>The result string.</returns>
        </member>
        <member name="F:Telerik.Windows.Documents.Common.Model.Graphics.DashCap.Flat">
            <summary>
            Specifies a square cap that squares off both ends of each dash.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Common.Model.Graphics.DashCap.Round">
            <summary>
            Specifies a circular cap that rounds off both ends of each dash.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Common.Model.Graphics.DashCap.Triangle">
            <summary>
            Specifies a triangular cap that points both ends of each dash.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Common.Model.Graphics.Region.GetRegionScans(System.Windows.Media.Matrix)">
            <summary>
            Returns an array of Rect structures that approximate this
            Region after the specified matrix transformation is applied.
            </summary>
            <param name="matrix">A Matrix that represents a geometric transformation to apply to the region.</param>
            <returns>An array of Rect structures that approximate this Region after the specified matrix transformation is applied.</returns>
        </member>
        <member name="T:Telerik.Windows.Documents.Common.Model.Protection.HashingAlgorithmsProvider">
            <summary>
            Represents a hash algorithms provider.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Common.Model.Protection.HashingAlgorithmsProvider.EnforceFips1402">
            <summary>
            Enforces Federal Information Processing Standard (FIPS) Publication 140-2.
            </summary>
            <remarks>
            Calling this method will cause some hashing algorithms to be excluded from the supported set of algorithms.
            This method must be call before using any hashing algorithms.
            </remarks>
        </member>
        <member name="M:Telerik.Windows.Documents.Common.Model.Protection.HexEncoder.Encode(System.Byte[],System.Int32,System.Int32,System.IO.Stream)">
             encode the input data producing a Hex output stream.
            
             @return the number of bytes produced.
        </member>
        <member name="M:Telerik.Windows.Documents.Common.Model.Protection.HexEncoder.Decode(System.Byte[],System.Int32,System.Int32,System.IO.Stream)">
             decode the Hex encoded byte data writing it to the given output stream,
             whitespace characters will be ignored.
            
             @return the number of bytes produced.
        </member>
        <member name="M:Telerik.Windows.Documents.Common.Model.Protection.HexEncoder.DecodeString(System.String,System.IO.Stream)">
             decode the Hex encoded string data writing it to the given output stream,
             whitespace characters will be ignored.
            
             @return the number of bytes produced.
        </member>
        <member name="P:Telerik.Windows.Documents.Common.Model.Protection.IDigest.AlgorithmName">
             return the algorithm name
            
             @return the algorithm name
        </member>
        <member name="M:Telerik.Windows.Documents.Common.Model.Protection.IDigest.GetDigestSize">
             return the size, in bytes, of the digest produced by this message digest.
            
             @return the size, in bytes, of the digest produced by this message digest.
        </member>
        <member name="M:Telerik.Windows.Documents.Common.Model.Protection.IDigest.GetByteLength">
             return the size, in bytes, of the internal buffer used by this digest.
            
             @return the size, in bytes, of the internal buffer used by this digest.
        </member>
        <member name="M:Telerik.Windows.Documents.Common.Model.Protection.IDigest.Update(System.Byte)">
             update the message digest with a single byte.
            
             @param inByte the input byte to be entered.
        </member>
        <member name="M:Telerik.Windows.Documents.Common.Model.Protection.IDigest.BlockUpdate(System.Byte[],System.Int32,System.Int32)">
             update the message digest with a block of bytes.
            
             @param input the byte array containing the data.
             @param inOff the offset into the byte array where the data starts.
             @param len the length of the data.
        </member>
        <member name="M:Telerik.Windows.Documents.Common.Model.Protection.IDigest.DoFinal(System.Byte[],System.Int32)">
             Close the digest, producing the final digest value. The doFinal
             call leaves the digest reset.
            
             @param output the array the digest is to be copied into.
             @param outOff the offset into the out array the digest is to start at.
        </member>
        <member name="M:Telerik.Windows.Documents.Common.Model.Protection.IDigest.Reset">
            reset the digest back to it's initial state.
        </member>
        <member name="T:Telerik.Windows.Documents.Common.Model.Protection.IProtectionAlgorithm">
            <summary>
            Defines the members of protection algorithm.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Common.Model.Protection.IProtectionAlgorithm.ComputeHash(System.Byte[])">
            <summary>
            Computes the hash value for the specified byte array.
            </summary>
            <param name="buffer">The input to compute the hash code for.</param>
            <returns>The computed hash code.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Common.Model.Protection.LongDigest.#ctor">
            Constructor for variable length word
        </member>
        <member name="M:Telerik.Windows.Documents.Common.Model.Protection.LongDigest.#ctor(Telerik.Windows.Documents.Common.Model.Protection.LongDigest)">
            Copy constructor.  We are using copy constructors in place
            of the object.Clone() interface as this interface is not
            supported by J2ME.
        </member>
        <member name="M:Telerik.Windows.Documents.Common.Model.Protection.LongDigest.AdjustByteCounts">
            adjust the byte counts so that byteCount2 represents the
            upper long (less 3 bits) word of the byte count.
        </member>
        <member name="M:Telerik.Windows.Documents.Common.Model.Protection.ProtectionHelperBase.GenerateSaltBase64">
            <summary>
            Generates base64 salt.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Common.Model.Protection.ProtectionHelperBase.GenerateHashBase64(System.String,System.String,System.String,System.Int32)">
            <summary>
            Generates base64 hash.
            </summary>
            <param name="salt">The salt.</param>
            <param name="password">The password.</param>
            <param name="algorithmName">Name of the algorithm.</param>
            <param name="spinCount">The spin count.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Common.Model.Protection.ProtectionHelperBase.IsPasswordCorrect(System.String,System.String,System.String,System.String,System.Int32)">
            <summary>
            Determines whether [is password correct] [the specified password].
            </summary>
            <param name="password">The password.</param>
            <param name="hash">The hash.</param>
            <param name="salt">The salt.</param>
            <param name="algorithmName">Name of the algorithm.</param>
            <param name="spinCount">The spin count.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Common.Model.Protection.RIPEMD160.#ctor">
            Standard constructor
        </member>
        <member name="M:Telerik.Windows.Documents.Common.Model.Protection.RIPEMD160.#ctor(Telerik.Windows.Documents.Common.Model.Protection.RIPEMD160)">
            Copy constructor.  This will copy the state of the provided
            message digest.
        </member>
        <member name="M:Telerik.Windows.Documents.Common.Model.Protection.RIPEMD160.Reset">
            reset the chaining variables to the IV values.
        </member>
        <member name="M:Telerik.Windows.Documents.Common.Model.Protection.SHA384.#ctor(Telerik.Windows.Documents.Common.Model.Protection.SHA384)">
            Copy constructor.  This will copy the state of the provided
            message digest.
        </member>
        <member name="M:Telerik.Windows.Documents.Common.Model.Protection.SHA384.Reset">
            reset the chaining variables
        </member>
        <member name="M:Telerik.Windows.Documents.Common.Model.Protection.SHA512.#ctor(Telerik.Windows.Documents.Common.Model.Protection.SHA512)">
            Copy constructor.  This will copy the state of the provided
            message digest.
        </member>
        <member name="M:Telerik.Windows.Documents.Common.Model.Protection.SHA512.Reset">
            reset the chaining variables
        </member>
        <member name="M:Telerik.Windows.Documents.Common.Model.Protection.Whirlpool.#ctor(Telerik.Windows.Documents.Common.Model.Protection.Whirlpool)">
            Copy constructor. This will copy the state of the provided message
            digest.
        </member>
        <member name="M:Telerik.Windows.Documents.Common.Model.Protection.Whirlpool.Reset">
            Reset the chaining variables
        </member>
        <member name="T:Telerik.Windows.Documents.FormatProviders.OpenXml.Export.OpenXmlExportSettings">
            <summary>
            Represents OpenXml export settings.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.OpenXml.Export.OpenXmlExportSettings.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.FormatProviders.OpenXml.OpenXmlImportSettings" /> class.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.FormatProviders.OpenXml.OpenXmlImportSettings">
            <summary>
            Represents OpenXml import settings.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.FormatProviders.OpenXml.OpenXmlImportSettings.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.FormatProviders.OpenXml.OpenXmlImportSettings" /> class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.OpenXml.Model.Elements.OpenXmlElementBase.UniquePoolId">
            <summary>
            There is property with the same name in OpenXmlChildElement of T class. This property is introduced to fix a bug caused by the other property.
            When creating a new element the UniquePoolId is used which causes a check if there are elements with the same name in the object pool.
            When the element is released and is returned to the pool the ElementName property is used. This way the create method never finds the already
            created objects and the pool for the ElementName objects becomes larger and larger and the objects are never leaving it. In order to remove this
            hack all UniquePoolId properties must be removed and the ElementsFactory should be refactored so instead of matching the element factory method not only by name,
            to match them by name and namespace. Refactoring of the ElementsFactory might not be enough for removing the need for UniuqePoolId property.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.FormatProviders.OpenXml.ShapesSettingsBase">
            <summary>
            Represents base class incorporating shape Import/Export related settings.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.FormatProviders.OpenXml.ShapesSettingsBase.SkipShapes">
            <summary>
            Get or sets whether or not to try and import/export the shapes.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Fixed.Model.Collections.PathFigureCollection">
            <summary>
            Represents path figures collection.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Fixed.Model.Collections.PathFigureCollection.AddPathFigure">
            <summary>
            Adds the path figure.
            </summary>
            <returns></returns>
        </member>
        <member name="T:Telerik.Windows.Documents.Fixed.Model.Collections.PathSegmentCollection">
            <summary>
            Represents path segment collection.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Fixed.Model.Collections.PathSegmentCollection.AddLineSegment">
            <summary>
            Adds the line segment.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Fixed.Model.Collections.PathSegmentCollection.AddLineSegment(System.Windows.Point)">
            <summary>
            Adds the line segment.
            </summary>
            <param name="point">The point.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Fixed.Model.Collections.PathSegmentCollection.AddQuadraticBezierSegment">
            <summary>
            Adds the quadratic bezier segment.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Fixed.Model.Collections.PathSegmentCollection.AddQuadraticBezierSegment(System.Windows.Point,System.Windows.Point)">
            <summary>
            Adds the quadratic bezier segment.
            </summary>
            <param name="point1">The first point.</param>
            <param name="point2">The second point.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Fixed.Model.Collections.PathSegmentCollection.AddBezierSegment">
            <summary>
            Adds the bezier segment.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Fixed.Model.Collections.PathSegmentCollection.AddBezierSegment(System.Windows.Point,System.Windows.Point,System.Windows.Point)">
            <summary>
            Adds the bezier segment.
            </summary>
            <param name="point1">The first point.</param>
            <param name="point2">The second point.</param>
            <param name="point3">The third point.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Fixed.Model.Collections.PathSegmentCollection.AddBezierSegment(System.Single,System.Single,System.Single,System.Single,System.Single,System.Single)">
            <summary>
            Adds the bezier segment.
            </summary>
            <param name="x1">The x-coordinate of the starting point of the curve.</param>
            <param name="y1">The y-coordinate of the starting point of the curve.</param>
            <param name="x2">The x-coordinate of the first control point for the curve.</param>
            <param name="y2">The y-coordinate of the first control point for the curve.</param>
            <param name="x3">The x-coordinate of the endpoint of the curve.</param>
            <param name="y3">The y-coordinate of the endpoint of the curve.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Fixed.Model.Collections.PathSegmentCollection.AddArcSegment">
            <summary>
            Adds the arc segment.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Fixed.Model.Collections.PathSegmentCollection.AddArcSegment(System.Windows.Point,System.Double,System.Double)">
            <summary>
            Adds the arc segment.
            </summary>
            <param name="point">The point.</param>
            <param name="radiusX">The radius X.</param>
            <param name="radiusY">The radius Y.</param>
            <returns></returns>
        </member>
        <member name="T:Telerik.Windows.Documents.Fixed.Model.Graphics.ArcSegment">
            <summary>
            Represents arc segment.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Fixed.Model.Graphics.ArcSegment.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Fixed.Model.Graphics.ArcSegment" /> class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Fixed.Model.Graphics.ArcSegment.Point">
            <summary>
            Gets or sets the point.
            </summary>
            <value>The point.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Fixed.Model.Graphics.ArcSegment.RadiusX">
            <summary>
            Gets or sets the radius X.
            </summary>
            <value>The radius X.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Fixed.Model.Graphics.ArcSegment.RadiusY">
            <summary>
            Gets or sets the radius Y.
            </summary>
            <value>The radius Y.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Fixed.Model.Graphics.ArcSegment.IsLargeArc">
            <summary>
            Gets or sets the is large arc.
            </summary>
            <value>The is large arc.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Fixed.Model.Graphics.ArcSegment.SweepDirection">
            <summary>
            Gets or sets the sweep direction.
            </summary>
            <value>The sweep direction.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Fixed.Model.Graphics.ArcSegment.RotationAngle">
            <summary>
            Gets or sets the x-axis rotation angle.
            </summary>
            <value>The x-axis rotation angle.</value>
        </member>
        <member name="T:Telerik.Windows.Documents.Fixed.Model.Graphics.BezierSegment">
            <summary>
            Represents bezier segment.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Fixed.Model.Graphics.BezierSegment.Point1">
            <summary>
            Gets or sets the first point.
            </summary>
            <value>The first point.</value>
        </member>
        <member name="F:Telerik.Windows.Documents.Fixed.Model.Graphics.BezierSegment.Point2">
            <summary>
            Gets or sets the second point.
            </summary>
            <value>The second point.</value>
        </member>
        <member name="F:Telerik.Windows.Documents.Fixed.Model.Graphics.BezierSegment.Point3">
            <summary>
            Gets or sets the third point.
            </summary>
            <value>The third point.</value>
        </member>
        <member name="T:Telerik.Windows.Documents.Fixed.Model.Graphics.FillRule">
            <summary>
            Represents fill rule types.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Fixed.Model.Graphics.FillRule.EvenOdd">
            <summary>
            Even odd fill rule.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Fixed.Model.Graphics.FillRule.Nonzero">
            <summary>
            Non zero fill rule.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Fixed.Model.Graphics.GeometryBase">
            <summary>
            Represents base class for geometry objects.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Fixed.Model.Graphics.GeometryBase.Bounds">
            <summary>
            Gets the bounds of the geometry.
            </summary>
            <value>The bounds.</value>
        </member>
        <member name="M:Telerik.Windows.Documents.Fixed.Model.Graphics.GeometryBase.GetBounds">
            <summary>
            Gets the geometry bounds.
            </summary>
            <returns></returns>
        </member>
        <member name="T:Telerik.Windows.Documents.Fixed.Model.Graphics.LineCap">
            <summary>
            Represents line cap types.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Fixed.Model.Graphics.LineCap.Flat">
            <summary>
            Flat line cap.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Fixed.Model.Graphics.LineCap.Round">
            <summary>
            Round line cap.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Fixed.Model.Graphics.LineCap.Square">
            <summary>
            Square line cap.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Fixed.Model.Graphics.LineJoin">
            <summary>
            Represents line join types.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Fixed.Model.Graphics.LineJoin.Miter">
            <summary>
            Miter line join.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Fixed.Model.Graphics.LineJoin.Round">
            <summary>
            Round line join.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Fixed.Model.Graphics.LineJoin.Bevel">
            <summary>
            Bevel line join.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Fixed.Model.Graphics.LineSegment">
            <summary>
            Represents line segment.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Fixed.Model.Graphics.LineSegment.Point">
            <summary>
            Gets or sets the point.
            </summary>
            <value>The point.</value>
        </member>
        <member name="T:Telerik.Windows.Documents.Fixed.Model.Graphics.PathFigure">
            <summary>
            Represents path figure.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Fixed.Model.Graphics.PathFigure.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Fixed.Model.Graphics.PathFigure" /> class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Fixed.Model.Graphics.PathFigure.Segments">
            <summary>
            Gets the path segments collection.
            </summary>
            <value>The path segments collection.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Fixed.Model.Graphics.PathFigure.StartPoint">
            <summary>
            Gets or sets the start point.
            </summary>
            <value>The start point.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Fixed.Model.Graphics.PathFigure.IsClosed">
            <summary>
            Gets or sets if the path figure is closed.
            </summary>
            <value>If the path figure is closed.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Fixed.Model.Graphics.PathFigure.LastPoint">
            <summary>
            Gets or sets the start point.
            </summary>
            <value>The start point.</value>
        </member>
        <member name="T:Telerik.Windows.Documents.Fixed.Model.Graphics.PathGeometry">
            <summary>
            Represents path geometry.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Fixed.Model.Graphics.PathGeometry.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Fixed.Model.Graphics.PathGeometry" /> class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Fixed.Model.Graphics.PathGeometry.Figures">
            <summary>
            Gets the path figures collection.
            </summary>
            <value>The path figures collection.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Fixed.Model.Graphics.PathGeometry.FillRule">
            <summary>
            Gets or sets the fill rule.
            </summary>
            <value>The fill rule.</value>
        </member>
        <member name="M:Telerik.Windows.Documents.Fixed.Model.Graphics.PathGeometry.GetBounds">
            <summary>
            Gets the geometry bounds.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Fixed.Model.Graphics.PathGeometry.AddPath(Telerik.Windows.Documents.Fixed.Model.Graphics.PathGeometry,System.Boolean)">
            <summary>
            Appends the specified PathGeometry to this PathGeometry.
            </summary>
            <param name="child">The PathGeometry to add.</param>
            <param name="connect">
            A Boolean value that specifies whether the first figure in the added path is
            part of the last figure in this path. A value of true specifies that (if possible)
            the first figure in the added path is part of the last figure in this path. 
            A value of false specifies that the first figure in the added path is separate
            from the last figure in this path.
            </param>
            <exception cref="T:System.NotImplementedException"></exception>
        </member>
        <member name="T:Telerik.Windows.Documents.Fixed.Model.Graphics.PathSegment">
            <summary>
            Represents base class for path segments.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Fixed.Model.Graphics.QuadraticBezierSegment">
            <summary>
            Represents bezier segment.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Fixed.Model.Graphics.QuadraticBezierSegment.Point1">
            <summary>
            Gets or sets the first point.
            </summary>
            <value>The first point.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Fixed.Model.Graphics.QuadraticBezierSegment.Point2">
            <summary>
            Gets or sets the second point.
            </summary>
            <value>The second point.</value>
        </member>
        <member name="T:Telerik.Windows.Documents.Fixed.Model.Graphics.SweepDirection">
            <summary>
            Represents sweep direction.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Fixed.Model.Graphics.SweepDirection.Clockwise">
            <summary>
            Represents clockwise sweep direction.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Fixed.Model.Graphics.SweepDirection.Counterclockwise">
            <summary>
            Represents counterclockwise sweep direction.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Spreadsheet.Core.INamedObject">
            <summary>
            Represents named objects. 
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Spreadsheet.Core.INamedObject.Name">
            <summary>
            Gets the name.
            </summary>
            <value>The name.</value>
        </member>
        <member name="T:Telerik.Windows.Documents.Spreadsheet.Core.NamedObjectBase">
            <summary>
            Provides the base class from which the classes that represent named objects are derived
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Spreadsheet.Core.NamedObjectBase.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Spreadsheet.Core.NamedObjectBase" /> class.
            </summary>
            <param name="name">The name.</param>
        </member>
        <member name="P:Telerik.Windows.Documents.Spreadsheet.Core.NamedObjectBase.Name">
            <summary>
            Gets the name value.
            </summary>
            <value>The name value.</value>
        </member>
        <member name="T:Telerik.Windows.Documents.Spreadsheet.Utilities.ThemableColorTypeConverter">
            <summary>
            A converter for the ThemableColor class. 
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Spreadsheet.Utilities.ThemableColorTypeConverter.CanConvertFrom(System.ComponentModel.ITypeDescriptorContext,System.Type)">
            <summary>
            Returns whether this converter can convert an object of the given type
            to the type of this converter, using the specified context.
            </summary>
            <param name="context">An <see cref="T:System.ComponentModel.ITypeDescriptorContext" />
            that provides a format context.</param>
            <param name="sourceType">A <see cref="T:System.Type" /> that represents the type
            you want to convert from.</param>
            <returns>
            true if this converter can perform the conversion; otherwise, false.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Spreadsheet.Utilities.ThemableColorTypeConverter.CanConvertTo(System.ComponentModel.ITypeDescriptorContext,System.Type)">
            <summary>
            Returns whether this converter can convert the object to the specified
            type, using the specified context.
            </summary>
            <param name="context">An <see cref="T:System.ComponentModel.ITypeDescriptorContext" />
            that provides a format context.</param>
            <param name="destinationType">A <see cref="T:System.Type" /> that represents the
            type you want to convert to.</param>
            <returns>
            true if this converter can perform the conversion; otherwise, false.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Spreadsheet.Utilities.ThemableColorTypeConverter.ConvertFrom(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object)">
            <summary>
            Converts the given object to the type of this converter, using the specified
            context and culture information.
            </summary>
            <param name="context">An <see cref="T:System.ComponentModel.ITypeDescriptorContext" />
            that provides a format context.</param>
            <param name="culture">The <see cref="T:System.Globalization.CultureInfo" /> to
            use as the current culture.</param>
            <param name="value">The <see cref="T:System.Object" /> to convert.</param>
            <exception cref="T:System.NotSupportedException">The conversion cannot be performed.
            </exception>
            <returns>
            An <see cref="T:System.Object" /> that represents the converted value.
            </returns>
        </member>
        <member name="T:Telerik.Windows.Documents.Spreadsheet.Model.ColorShadeType">
            <summary>
            Defines the types of color shade.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Spreadsheet.Model.ColorShadeType.Shade1">
            <summary>
            Represents shade 1 shade type.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Spreadsheet.Model.ColorShadeType.Shade2">
            <summary>
            Represents shade 2 shade type.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Spreadsheet.Model.ColorShadeType.Shade3">
            <summary>
            Represents shade 3 shade type.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Spreadsheet.Model.ColorShadeType.Shade4">
            <summary>
            Represents shade 4 shade type.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Spreadsheet.Model.ColorShadeType.Shade5">
            <summary>
            Represents shade 5 shade type.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Spreadsheet.Model.FontLanguageType">
            <summary>
            Describes the types of font languages.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Spreadsheet.Model.FontLanguageType.Latin">
            <summary>
            Represents latin font language type.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Spreadsheet.Model.FontLanguageType.EastAsian">
            <summary>
            Represents east asian font language type.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Spreadsheet.Model.FontLanguageType.ComplexScript">
            <summary>
            Represents complex script font language type.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Spreadsheet.Model.ThemableColor">
            <summary>
            Represents a color which can be used in a theme.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Spreadsheet.Model.ThemableColor.#ctor(System.Windows.Media.Color)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Spreadsheet.Model.ThemableColor" /> class.
            </summary>
            <param name="color">The color.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Spreadsheet.Model.ThemableColor.#ctor(System.Windows.Media.Color,System.Boolean)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Spreadsheet.Model.ThemableColor" /> class.
            </summary>
            <param name="color">The color.</param>
            <param name="isAutomatic">The is automatic.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Spreadsheet.Model.ThemableColor.#ctor(Telerik.Windows.Documents.Spreadsheet.Theming.ThemeColorType,System.Nullable{Telerik.Windows.Documents.Spreadsheet.Model.ColorShadeType})">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Spreadsheet.Model.ThemableColor" /> class.
            </summary>
            <param name="themeColorType">The theme color type.</param>
            <param name="colorShadeType">The color shade type.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Spreadsheet.Model.ThemableColor.#ctor(Telerik.Windows.Documents.Spreadsheet.Theming.ThemeColorType,System.Double)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Spreadsheet.Model.ThemableColor" /> class.
            </summary>
            <param name="themeColorType">Theme color type.</param>
            <param name="tintAndShade">The tint and shade.</param>
        </member>
        <member name="P:Telerik.Windows.Documents.Spreadsheet.Model.ThemableColor.LocalValue">
            <summary>
            Gets the local value of the color.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Spreadsheet.Model.ThemableColor.IsAutomatic">
            <summary>
            Gets the value indicating if the color is automatic. Automatic colors may be modified by a consumer as appropriate.
            </summary>
            <value>Value indicating if the color is automatic.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Spreadsheet.Model.ThemableColor.ThemeColorType">
            <summary>
            Gets the theme color type.
            </summary>
            <value>The theme color type.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Spreadsheet.Model.ThemableColor.ColorShadeType">
            <summary>
            Gets the color shade type.
            </summary>
            <value>The color shade type.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Spreadsheet.Model.ThemableColor.TintAndShade">
            <summary>
            Gets the tint and shade.
            </summary>
            <value>The tint and shade.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Spreadsheet.Model.ThemableColor.IsFromTheme">
            <summary>
            Gets the value indicating if the instance is from a theme.
            </summary>
            <value>The value indicating if the instance is from a theme.</value>
        </member>
        <member name="M:Telerik.Windows.Documents.Spreadsheet.Model.ThemableColor.FromArgb(System.Byte,System.Byte,System.Byte,System.Byte)">
            <summary>
            Creates new themable color from Argb.
            </summary>
            <param name="alfa">The alfa.</param>
            <param name="red">The red.</param>
            <param name="green">The green.</param>
            <param name="blue">The blue.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Spreadsheet.Model.ThemableColor.op_Equality(Telerik.Windows.Documents.Spreadsheet.Model.ThemableColor,Telerik.Windows.Documents.Spreadsheet.Model.ThemableColor)">
            <summary>
            Compares two themable colors.
            </summary>
            <param name="first">The first themable color.</param>
            <param name="second">The second themable color.</param>
            <returns>If the two themable colors are equal.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Spreadsheet.Model.ThemableColor.op_Inequality(Telerik.Windows.Documents.Spreadsheet.Model.ThemableColor,Telerik.Windows.Documents.Spreadsheet.Model.ThemableColor)">
            <summary>
            Compares two themable colors.
            </summary>
            <param name="first">The first themable color.</param>
            <param name="second">The second themable color.</param>
            <returns>If the two themable colors are equal.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Spreadsheet.Model.ThemableColor.op_Explicit(System.Windows.Media.Color)~Telerik.Windows.Documents.Spreadsheet.Model.ThemableColor">
            <summary>
            Explicitly cast color to themable color.
            </summary>
            <param name="value">The color.</param>
            <returns>Themable color.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Spreadsheet.Model.ThemableColor.FromColor(System.Windows.Media.Color)">
            <summary>
            Converts <see cref="T:System.Windows.Media.Color" /> to <see cref="T:Telerik.Windows.Documents.Spreadsheet.Model.ThemableColor" />.
            </summary>
            <param name="value">The <see cref="T:System.Windows.Media.Color" />.</param>
            <returns>The <see cref="T:Telerik.Windows.Documents.Spreadsheet.Model.ThemableColor" />.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Spreadsheet.Model.ThemableColor.GetActualValue(Telerik.Windows.Documents.Spreadsheet.Theming.DocumentTheme)">
            <summary>
            Gets the actual value.
            </summary>
            <param name="theme">The theme.</param>
            <returns>The actual value.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Spreadsheet.Model.ThemableColor.GetActualValue(Telerik.Windows.Documents.Spreadsheet.Theming.ThemeColorScheme)">
            <summary>
            Gets the actual value.
            </summary>
            <param name="colorScheme">The color scheme.</param>
            <returns>The actual color.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Spreadsheet.Model.ThemableColor.Equals(System.Object)">
            <summary>
            Determines whether the specified <see cref="T:System.Object" /> is equal
            to the current <see cref="T:System.Object" />.
            </summary>
            <param name="obj">The <see cref="T:System.Object" /> to compare with the current
            <see cref="T:System.Object" />.</param>
            <returns>
            True if the specified <see cref="T:System.Object" /> is equal to the
            current <see cref="T:System.Object" />; otherwise, false.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Spreadsheet.Model.ThemableColor.GetHashCode">
            <summary>
            Serves as a hash function for a particular type.
            </summary>
            <returns>A hash code for the current <see cref="T:System.Object" />.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Spreadsheet.Model.ThemableColor.ToString">
            <summary>
            Returns a <see cref="T:System.String" /> that represents the current <see cref="T:System.Object" />.
            </summary>
            <returns>
            A <see cref="T:System.String" /> that represents the current <see cref="T:System.Object" />.
            </returns>
        </member>
        <member name="T:Telerik.Windows.Documents.Spreadsheet.Model.ThemableFontFamily">
            <summary>
            Represents a font family which can be used in a theme.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Spreadsheet.Model.ThemableFontFamily.#ctor(System.Windows.Media.FontFamily)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Spreadsheet.Model.ThemableFontFamily" /> class.
            </summary>
            <param name="fontFamily">The font family.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Spreadsheet.Model.ThemableFontFamily.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Spreadsheet.Model.ThemableFontFamily" /> class.
            </summary>
            <param name="familyName">Name of the family.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Spreadsheet.Model.ThemableFontFamily.#ctor(Telerik.Windows.Documents.Spreadsheet.Theming.ThemeFontType)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Spreadsheet.Model.ThemableFontFamily" /> class.
            </summary>
            <param name="themeFontType">Type of the theme font.</param>
        </member>
        <member name="P:Telerik.Windows.Documents.Spreadsheet.Model.ThemableFontFamily.LocalValue">
            <summary>
            Gets the local value.
            </summary>
            <value>The local value.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Spreadsheet.Model.ThemableFontFamily.ThemeFontType">
            <summary>
            Gets the theme font type.
            </summary>
            <value>The theme font type.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Spreadsheet.Model.ThemableFontFamily.IsFromTheme">
            <summary>
            Gets the value indicating if the instance is from a theme.
            </summary>
            <value>The value indicating if the instance is from a theme.</value>
        </member>
        <member name="M:Telerik.Windows.Documents.Spreadsheet.Model.ThemableFontFamily.op_Equality(Telerik.Windows.Documents.Spreadsheet.Model.ThemableFontFamily,Telerik.Windows.Documents.Spreadsheet.Model.ThemableFontFamily)">
            <summary>
            Compares two themable font families.
            </summary>
            <param name="first">The first themable font family.</param>
            <param name="second">The second themable font family.</param>
            <returns>If the two themable font families are equal.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Spreadsheet.Model.ThemableFontFamily.op_Inequality(Telerik.Windows.Documents.Spreadsheet.Model.ThemableFontFamily,Telerik.Windows.Documents.Spreadsheet.Model.ThemableFontFamily)">
            <summary>
            Compares two themable font families.
            </summary>
            <param name="first">The first themable font family.</param>
            <param name="second">The second themable font family.</param>
            <returns>If the two themable font families are not equal.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Spreadsheet.Model.ThemableFontFamily.op_Explicit(System.Windows.Media.FontFamily)~Telerik.Windows.Documents.Spreadsheet.Model.ThemableFontFamily">
            <summary>
            Casts explicitly FontFamily object to themable font family.
            </summary>
            <param name="value">The font family.</param>
            <returns>Themable font family.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Spreadsheet.Model.ThemableFontFamily.FromFontFamily(System.Windows.Media.FontFamily)">
            <summary>
            Converts <see cref="T:System.Windows.Media.FontFamily" /> to <see cref="T:Telerik.Windows.Documents.Spreadsheet.Model.ThemableFontFamily" />.
            </summary>
            <param name="value">The <see cref="T:System.Windows.Media.FontFamily" />.</param>
            <returns>The <see cref="T:Telerik.Windows.Documents.Spreadsheet.Model.ThemableFontFamily" />.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Spreadsheet.Model.ThemableFontFamily.GetActualValue(Telerik.Windows.Documents.Spreadsheet.Theming.DocumentTheme)">
            <summary>
            Gets the actual value.
            </summary>
            <param name="theme">The theme.</param>
            <returns>The actual value.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Spreadsheet.Model.ThemableFontFamily.ToString">
            <summary>
            Returns a <see cref="T:System.String" /> that represents the current
            <see cref="T:System.Object" />.
            </summary>
            <returns>
            A <see cref="T:System.String" /> that represents the current <see cref="T:System.Object" />.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Spreadsheet.Model.ThemableFontFamily.Equals(System.Object)">
            <summary>
            Determines whether the specified <see cref="T:System.Object" /> is equal
            to the current <see cref="T:System.Object" />.
            </summary>
            <param name="obj">The <see cref="T:System.Object" /> to compare with the current
            <see cref="T:System.Object" />.</param>
            <returns>
            True if the specified <see cref="T:System.Object" /> is equal to the
            current <see cref="T:System.Object" />; otherwise, false.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Spreadsheet.Model.ThemableFontFamily.GetHashCode">
            <summary>
            Serves as a hash function for a particular type.
            </summary>
            <returns>A hash code for the current <see cref="T:System.Object" />.</returns>
        </member>
        <member name="T:Telerik.Windows.Documents.Spreadsheet.Theming.DocumentTheme">
            <summary>
            Represents a theme.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Spreadsheet.Theming.DocumentTheme.#ctor(System.String,Telerik.Windows.Documents.Spreadsheet.Theming.ThemeColorScheme,Telerik.Windows.Documents.Spreadsheet.Theming.ThemeFontScheme)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Spreadsheet.Theming.DocumentTheme" /> class.
            </summary>
            <param name="name">The name.</param>
            <param name="colorScheme">The color scheme.</param>
            <param name="fontScheme">The font scheme.</param>
        </member>
        <member name="P:Telerik.Windows.Documents.Spreadsheet.Theming.DocumentTheme.ColorScheme">
            <summary>
            Gets the color scheme.
            </summary>
            <value>The color scheme.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Spreadsheet.Theming.DocumentTheme.FontScheme">
            <summary>
            Gets the font scheme.
            </summary>
            <value>The font scheme.</value>
        </member>
        <member name="M:Telerik.Windows.Documents.Spreadsheet.Theming.DocumentTheme.Clone">
            <summary>
            Creates deep copy of this document theme.
            </summary>
            <returns>The cloned document theme.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Spreadsheet.Theming.DocumentTheme.Equals(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Documents.Spreadsheet.Theming.DocumentTheme.GetHashCode">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Documents.Spreadsheet.Theming.IThemableObject`1">
            <summary>
            Defines the members of an object which is part of a theme.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Spreadsheet.Theming.IThemableObject`1.IsFromTheme">
            <summary>
            Gets the value indicating if the instance is from a theme.
            </summary>
            <value>The value indicating if the instance is from a theme.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Spreadsheet.Theming.IThemableObject`1.LocalValue">
            <summary>
            Gets the local value.
            </summary>
            <value>The local value.</value>
        </member>
        <member name="M:Telerik.Windows.Documents.Spreadsheet.Theming.IThemableObject`1.GetActualValue(Telerik.Windows.Documents.Spreadsheet.Theming.DocumentTheme)">
            <summary>
            Gets the actual value.
            </summary>
            <param name="theme">The theme.</param>
            <returns>The actual value.</returns>
        </member>
        <member name="T:Telerik.Windows.Documents.Spreadsheet.Theming.PredefinedThemeSchemes">
            <summary>
            Describes the color and font schemes for the predefined document themes.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Spreadsheet.Theming.PredefinedThemeSchemes.DefaultTheme">
            <summary>
            The default document theme.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Spreadsheet.Theming.PredefinedThemeSchemes.ColorSchemes">
            <summary>
            Predefined color schemes.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Spreadsheet.Theming.PredefinedThemeSchemes.FontSchemes">
            <summary>
            Predefined font schemes.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeColor">
            <summary>
            Represents a color in a theme.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeColor.#ctor(System.Windows.Media.Color,Telerik.Windows.Documents.Spreadsheet.Theming.ThemeColorType)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeColor" /> class.
            </summary>
            <param name="color">The color.</param>
            <param name="themeColorType">Type of the theme color.</param>
        </member>
        <member name="P:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeColor.ThemeColorType">
            <summary>
            Gets the type of the theme color.
            </summary>
            <value>The type of the theme color.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeColor.Color">
            <summary>
            Gets the color.
            </summary>
            <value>The color.</value>
        </member>
        <member name="M:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeColor.Clone">
            <summary>
            Creates deep copy of this theme color.
            </summary>
            <returns>The cloned theme color.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeColor.Equals(System.Object)">
            <summary>
            Determines whether the specified <see cref="T:System.Object" /> is equal
            to the current <see cref="T:System.Object" />.
            </summary>
            <param name="obj">The <see cref="T:System.Object" /> to compare with the current
            <see cref="T:System.Object" />.</param>
            <returns>
            true if the specified <see cref="T:System.Object" /> is equal to the
            current <see cref="T:System.Object" />; otherwise, false.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeColor.GetHashCode">
            <summary>
            Serves as a hash function for a particular type.
            </summary>
            <returns>A hash code for the current <see cref="T:System.Object" />.</returns>
        </member>
        <member name="T:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeColorScheme">
            <summary>
            Represents the color scheme of a theme.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeColorScheme.#ctor(System.String,System.Windows.Media.Color,System.Windows.Media.Color,System.Windows.Media.Color,System.Windows.Media.Color,System.Windows.Media.Color,System.Windows.Media.Color,System.Windows.Media.Color,System.Windows.Media.Color,System.Windows.Media.Color,System.Windows.Media.Color,System.Windows.Media.Color,System.Windows.Media.Color)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeColorScheme" /> class.
            </summary>
            <param name="name">The name.</param>
            <param name="background1">The first background.</param>
            <param name="text1">The first text color.</param>
            <param name="background2">The second background.</param>
            <param name="text2">The second text color.</param>
            <param name="accent1">The first accent.</param>
            <param name="accent2">The second accent.</param>
            <param name="accent3">The third accent.</param>
            <param name="accent4">The fourth accent.</param>
            <param name="accent5">The fifth accent.</param>
            <param name="accent6">The sixth accent.</param>
            <param name="hyperlink">The hyperlink color.</param>
            <param name="followedHyperlink">The followed hyperlink color.</param>
        </member>
        <member name="P:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeColorScheme.Item(Telerik.Windows.Documents.Spreadsheet.Theming.ThemeColorType)">
            <summary>
            Gets the <see cref="T:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeColor" /> with the specified color type.
            </summary>
            <value>The theme color.</value>
        </member>
        <member name="M:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeColorScheme.GetTintAndShade(Telerik.Windows.Documents.Spreadsheet.Theming.ThemeColorType,Telerik.Windows.Documents.Spreadsheet.Model.ColorShadeType)">
            <summary>
            Gets the tint and shade.
            </summary>
            <param name="themeColorType">Type of the theme color.</param>
            <param name="colorShadeType">Type of the color shade.</param>
            <returns>The tint and shade value.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeColorScheme.Clone">
            <summary>
            Creates deep copy of this theme color scheme.
            </summary>
            <returns>The cloned theme color scheme.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeColorScheme.Equals(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeColorScheme.GetHashCode">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeColorScheme.GetEnumerator">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeColorScheme.System#Collections#IEnumerable#GetEnumerator">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeColorType">
            <summary>
            Defines the types of theme colors.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeColorType.Background1">
            <summary>
            Represents Background1 theme color type.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeColorType.Text1">
            <summary>
            Represents Text1 theme color type.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeColorType.Background2">
            <summary>
            Represents Background2 theme color type.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeColorType.Text2">
            <summary>
            Represents Text2 theme color type.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeColorType.Accent1">
            <summary>
            Represents Accent1 theme color type.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeColorType.Accent2">
            <summary>
            Represents Accent2 theme color type.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeColorType.Accent3">
            <summary>
            Represents Accent3 theme color type.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeColorType.Accent4">
            <summary>
            Represents Accent4 theme color type.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeColorType.Accent5">
            <summary>
            Represents Accent5 theme color type.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeColorType.Accent6">
            <summary>
            Represents Accent6 theme color type.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeColorType.Hyperlink">
            <summary>
            Represents Hyperlink theme color type.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeColorType.FollowedHyperlink">
            <summary>
            Represents FollowedHyperlink theme color type.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeFont">
            <summary>
            Represents the font in a theme.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeFont.#ctor(System.Windows.Media.FontFamily,Telerik.Windows.Documents.Spreadsheet.Model.FontLanguageType)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeFont" /> class.
            </summary>
            <param name="fontFamily">The font family.</param>
            <param name="fontLanguageType">The type of font language.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeFont.#ctor(System.String,Telerik.Windows.Documents.Spreadsheet.Model.FontLanguageType)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeFont" /> class.
            </summary>
            <param name="fontName">Name of the font.</param>
            <param name="fontLanguageType">Type of the font language.</param>
        </member>
        <member name="P:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeFont.FontFamily">
            <summary>
            Gets the font family.
            </summary>
            <value>The font family.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeFont.FontLanguageType">
            <summary>
            Gets the type of the font language.
            </summary>
            <value>The type of font language.</value>
        </member>
        <member name="M:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeFont.Equals(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeFont.GetHashCode">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeFonts">
            <summary>
            A collection of fonts for a theme, each corresponding to a language type.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeFonts.#ctor(System.String,System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeFonts" /> class.
            </summary>
            <param name="latinFontName">Name of the latin font.</param>
            <param name="eastAsianFontName">Name of the east asian font.</param>
            <param name="complexScriptFontName">Name of the complex script font.</param>
        </member>
        <member name="P:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeFonts.Item(Telerik.Windows.Documents.Spreadsheet.Model.FontLanguageType)">
            <summary>
            Gets the <see cref="T:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeFont" /> with the specified font language type.
            </summary>
            <value>The theme font.</value>
        </member>
        <member name="M:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeFonts.Clone">
            <summary>
            Creates deep copy of this theme fonts.
            </summary>
            <returns>The cloned theme fonts.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeFonts.Equals(System.Object)">
            <summary>
            Determines whether the specified <see cref="T:System.Object" /> is equal
            to the current <see cref="T:System.Object" />.
            </summary>
            <param name="obj">The <see cref="T:System.Object" /> to compare with the current
            <see cref="T:System.Object" />.</param>
            <returns>
            true if the specified <see cref="T:System.Object" /> is equal to the
            current <see cref="T:System.Object" />; otherwise, false.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeFonts.GetHashCode">
            <summary>
            Serves as a hash function for a particular type.
            </summary>
            <returns>A hash code for the current <see cref="T:System.Object" />.</returns>
        </member>
        <member name="T:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeFontScheme">
            <summary>
            Represents the font scheme of a theme.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeFontScheme.#ctor(System.String,System.String,System.String,System.String,System.String,System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeFontScheme" /> class.
            </summary>
            <param name="name">The name.</param>
            <param name="latinMajorFontName">Name of the latin headings font.</param>
            <param name="latinMinorFontName">Name of the latin body font.</param>
            <param name="eastAsianMajorFontName">Name of the east asian headings font.</param>
            <param name="eastAsianMinorFontName">Name of the east asian body font.</param>
            <param name="complexScriptMajorFontName">Name of the complex script headings font.</param>
            <param name="complexScriptMinorFontName">Name of the complex script body font.</param>
        </member>
        <member name="P:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeFontScheme.Item(Telerik.Windows.Documents.Spreadsheet.Theming.ThemeFontType)">
            <summary>
            Gets the <see cref="T:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeFonts" /> with the specified font type.
            </summary>
            <value>The theme fonts.</value>
        </member>
        <member name="M:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeFontScheme.Clone">
            <summary>
            Creates deep copy of this theme font scheme.
            </summary>
            <returns>The cloned theme font scheme.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeFontScheme.Equals(System.Object)">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeFontScheme.GetHashCode">
            <inheritdoc />
        </member>
        <member name="T:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeFontType">
            <summary>
            Describes the types of theme fonts.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeFontType.Minor">
            <summary>
            The font of the body of the document.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Spreadsheet.Theming.ThemeFontType.Major">
            <summary>
            The font of the headings of the document.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Model.Drawing.Shapes.Image">
            <summary>
            Represents image element.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Shapes.Image.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Model.Drawing.Shapes.Image" /> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Shapes.Image.#ctor(Telerik.Windows.Documents.Model.Drawing.Shapes.Image)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Model.Drawing.Shapes.Image" /> class.
            </summary>
            <param name="other">The other image.</param>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Shapes.Image.ImageSource">
            <summary>
            Gets or sets the image source.
            </summary>
            <value>The image source.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Shapes.Image.PreferRelativeToOriginalResize">
            <summary>
            Gets or sets the value indicating whether the scale resizing should be relative to the original or the current size of the image.
            </summary>
            <value>The value indicating whether the scale resizing should be relative to the original or the current size of the image.</value>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Shapes.Image.InitializeSize">
            <summary>
            Initializes the size.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Model.Drawing.Shapes.ShapeBase">
            <summary>
            Represents shape base element.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Shapes.ShapeBase.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Model.Drawing.Shapes.ShapeBase" /> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Shapes.ShapeBase.#ctor(Telerik.Windows.Documents.Model.Drawing.Shapes.ShapeBase)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Model.Drawing.Shapes.ShapeBase" /> class by copying an Image instance.
            </summary>
            <param name="other">The other image.</param>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Shapes.ShapeBase.Link">
            <summary>
            Gets the link string.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Shapes.ShapeBase.Name">
            <summary>
            Gets or sets the name.
            </summary>
            <value>The name.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Shapes.ShapeBase.Description">
            <summary>
            Gets or sets the description (alternative text).
            </summary>
            <value>The description (alternative text).</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Shapes.ShapeBase.Title">
            <summary>
            Gets or sets the title (caption) of the current object.
            </summary>
            <value>The title (caption).</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Shapes.ShapeBase.Width">
            <summary>
            Gets or sets the width.
            </summary>
            <value>The width.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Shapes.ShapeBase.Height">
            <summary>
            Gets or sets the height.
            </summary>
            <value>The height.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Shapes.ShapeBase.Size">
            <summary>
            Gets or sets the size.
            </summary>
            <value>The size.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Shapes.ShapeBase.SizeInternal">
            <summary>
            Gets the size silently so the size's auto-initializing cannot be invoked.
            </summary>
            <value>The size.</value>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Shapes.ShapeBase.InitializeSize">
            <summary>
            Initializes the size.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Shapes.ShapeBase.IsVerticallyFlipped">
            <summary>
            Gets or sets the value indicating if the shape is vertically flipped.
            </summary>
            <value>The value indicating if the shape is vertically flipped.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Shapes.ShapeBase.IsHorizontallyFlipped">
            <summary>
            Gets or sets the value indicating if the shape is horizontally flipped.
            </summary>
            <value>The value indicating if the shape is horizontally flipped.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Shapes.ShapeBase.RotationAngle">
            <summary>
            Gets or sets the rotation angle.
            </summary>
            <value>The rotation angle.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Shapes.ShapeBase.LockAspectRatio">
            <summary>
            Gets or sets the value indicating whether the aspect ratio between the width and height should remain constant.
            </summary>
            <value>The value indicating whether the aspect ratio between the width and height should remain constant.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Shapes.ShapeBase.Fill">
            <summary>
            Gets or sets the fill of the shape.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Shapes.ShapeBase.Outline">
            <summary>
            Gets the outline of the shape.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Shapes.ShapeBase.SetWidth(System.Boolean,System.Double)">
            <summary>
            Sets the width of the shape.
            </summary>
            <param name="respectLockAspectRatio">A value indicating whether the aspect ratio lock should be respected.</param>
            <param name="width">The new width.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Shapes.ShapeBase.SetHeight(System.Boolean,System.Double)">
            <summary>
            Sets the height of the shape.
            </summary>
            <param name="respectLockAspectRatio">A value indicating whether the aspect ratio lock should be respected.</param>
            <param name="height">The new height.</param>
        </member>
        <member name="T:Telerik.Windows.Documents.Model.Drawing.Shapes.ChartLine">
            <summary>
            Represents a line in the chart.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Shapes.ChartLine.Outline">
            <summary>
            Gets the outline properties of the line.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Shapes.ChartLine.Clone">
            <summary>
            Creates a deep copy of the object.
            </summary>
            <returns>The deep copy of the object.</returns>
        </member>
        <member name="T:Telerik.Windows.Documents.Model.Drawing.Theming.Fill">
            <summary>
            Represents the fill of an object.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Theming.Fill.Clone">
            <summary>
            Creates a deep copy of the object.
            </summary>
            <returns>The deep copy of the object.</returns>
        </member>
        <member name="T:Telerik.Windows.Documents.Model.Drawing.Theming.NoFill">
            <summary>
            Represents the lack of fill of an object.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Theming.NoFill.Clone">
            <summary>
            Creates a deep copy of the object.
            </summary>
            <returns>The deep copy of the object.</returns>
        </member>
        <member name="T:Telerik.Windows.Documents.Model.Drawing.Theming.Outline">
            <summary>
            Represents the outline of an object.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Theming.Outline.Fill">
            <summary>
            Gets or sets the fill of the outline.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Theming.Outline.Width">
            <summary>
            Gets ot sets the width of the line in points.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Theming.Outline.Clone">
            <summary>
            Creates a deep copy of the object.
            </summary>
            <returns>The deep copy of the object.</returns>
        </member>
        <member name="T:Telerik.Windows.Documents.Model.Drawing.Theming.SolidFill">
            <summary>
            Represents a solid fill of an object.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Theming.SolidFill.Color">
            <summary>
            Represents the color of the fill.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Theming.SolidFill.#ctor(Telerik.Windows.Documents.Spreadsheet.Model.ThemableColor)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Model.Drawing.Theming.SolidFill" /> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Theming.SolidFill.#ctor(System.Windows.Media.Color)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Model.Drawing.Theming.SolidFill" /> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Theming.SolidFill.Clone">
            <summary>
            Creates a deep copy of the object.
            </summary>
            <returns>The deep copy of the object.</returns>
        </member>
        <member name="T:Telerik.Windows.Documents.Model.Drawing.Charts.Axis">
            <summary>
            Represents one of the axes of the chart.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Charts.Axis.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Model.Drawing.Charts.Axis" /> class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.Axis.AxisType">
            <summary>
            Gets the type of the axis.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.Axis.Min">
            <summary>
            Gets or sets the smallest value of the axis. If the value is null, the smallest value will be determined automatically.
            For data axes, this value is an OLE Automation date value. For categorical axis, this number is the number of the category in the
            succession of the category collection.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.Axis.Max">
            <summary>
            Gets or sets the largest value of the axis. If the value is null, the largest value will be determined automatically.
            For data axes, this value is an OLE Automation date value. For categorical axis, this number is the number of the category in the
            succession of the category collection.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.Axis.IsVisible">
            <summary>
            Gets or sets the value indicating whether the axis is visible.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.Axis.NumberFormat">
            <summary>
            Gets or sets the format string that is applied to the values of the axis.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.Axis.Outline">
            <summary>
            Gets the outline of the axis.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.Axis.MajorGridlines">
            <summary>
            Gets the major gridlines of the axis.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Charts.Axis.Clone">
            <summary>
            Creates a deep copy of the object.
            </summary>
            <returns>The deep copy of the object.</returns>
        </member>
        <member name="T:Telerik.Windows.Documents.Model.Drawing.Charts.AxisGroup">
            <summary>
            Represents a group of axes in the chart.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Charts.AxisGroup.#ctor(Telerik.Windows.Documents.Model.Drawing.Charts.Axis,Telerik.Windows.Documents.Model.Drawing.Charts.Axis)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Model.Drawing.Charts.AxisGroup" /> class.
            </summary>
            <param name="categoryAxis">The category axis.</param>
            <param name="valueAxis">The value axis.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Charts.AxisGroup.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Model.Drawing.Charts.AxisGroup" /> class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.AxisGroup.CategoryAxis">
            <summary>
            Gets or sets the axis agains which the categories of the series are plotted.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.AxisGroup.ValueAxis">
            <summary>
            Gets or sets the axis agains which the values of the series are plotted.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Charts.AxisGroup.Clone">
            <summary>
            Creates a deep copy of the object.
            </summary>
            <returns>The deep copy of the object.</returns>
        </member>
        <member name="T:Telerik.Windows.Documents.Model.Drawing.Charts.AxisGroupName">
            <summary>
            Represents the possible groups of axes a group of series can be associated with.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.Drawing.Charts.AxisGroupName.Primary">
            <summary>
            Denotes the primary axes.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.Drawing.Charts.AxisGroupName.Secondary">
            <summary>
            Denotes the secondary axes.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Model.Drawing.Charts.AxisType">
            <summary>
            Represents the possible types of axes of the chart.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.Drawing.Charts.AxisType.Value">
            <summary>
            Denotes the value type of axis.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.Drawing.Charts.AxisType.Category">
            <summary>
            Denotes the categorical type of axis.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.Drawing.Charts.AxisType.Date">
            <summary>
            Denotes the date type of axis.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Model.Drawing.Charts.CategoryAxis">
            <summary>
            Represents a categorical axis. The categorical axis is designed to contain discrete values.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.CategoryAxis.AxisType">
            <summary>
            Gets the type of the axis.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Model.Drawing.Charts.DateAxis">
            <summary>
            Represents a date axis. The date axis is designed to contain date values. 
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.DateAxis.AxisType">
            <summary>
            Gets the type of the axis.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Model.Drawing.Charts.ISupportAxes">
            <summary>
            Defines members for the classes representing series which can have axes. 
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.ISupportAxes.AxisGroupName">
            <summary>
            Gets or sets the value indicating which group of axes the series group is associated with.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Model.Drawing.Charts.ValueAxis">
            <summary>
            Represents a value axis. The value axis is designed to contain numeric values. 
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.ValueAxis.AxisType">
            <summary>
            Gets the type of the axis.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Model.Drawing.Charts.ChartType">
            <summary>
            Represents the possible types of chart. 
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.Drawing.Charts.ChartType.Bar">
            <summary>
            Denotes the bar type of chart.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.Drawing.Charts.ChartType.Column">
            <summary>
            Denotes the column type of chart.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.Drawing.Charts.ChartType.Pie">
            <summary>
            Denotes the pie type of chart.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.Drawing.Charts.ChartType.Line">
            <summary>
            Denotes the line type of chart.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.Drawing.Charts.ChartType.Area">
            <summary>
            Denotes the area type of chart.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.Drawing.Charts.ChartType.Doughnut">
            <summary>
            Denotes the doughnut type of chart.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.Drawing.Charts.ChartType.Scatter">
            <summary>
            Denotes the scatter type of chart.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.Drawing.Charts.ChartType.Bubble">
            <summary>
            Denotes the bubble type of chart.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Model.Drawing.Charts.DocumentChart">
            <summary>
            Represents a chart.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Charts.DocumentChart.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Model.Drawing.Charts.DocumentChart" /> class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.DocumentChart.SeriesGroups">
            <summary>
            Represents a collection of the groups in which the series of the chart are organized.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.DocumentChart.PrimaryAxes">
            <summary>
            Represents the primary group of axes of the chart.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.DocumentChart.SecondaryAxes">
            <summary>
            Represents the secondary group of axes of the chart. It is used when there is more than one group of series (combo chart).
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.DocumentChart.Title">
            <summary>
            Represents the title of the chart.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.DocumentChart.Legend">
            <summary>
            Gets or sets the chart legend of the chart.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Charts.DocumentChart.Clone">
            <summary>
            Creates a deep copy of the object.
            </summary>
            <returns>The deep copy of the object.</returns>
        </member>
        <member name="T:Telerik.Windows.Documents.Model.Drawing.Charts.Legend">
            <summary>
            Represents teh legend of the chart.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Charts.Legend.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Model.Drawing.Charts.Legend" /> class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.Legend.Position">
            <summary>
            Gets or sets the value indicating where the legend will be positioned.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Charts.Legend.Clone">
            <summary>
            Creates a deep copy of the object.
            </summary>
            <returns>The deep copy of the object.</returns>
        </member>
        <member name="T:Telerik.Windows.Documents.Model.Drawing.Charts.LegendPosition">
            <summary>
            Represents the possible positions of the legend.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.Drawing.Charts.LegendPosition.Right">
            <summary>
            Denotes the right-side positioning of the legend.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.Drawing.Charts.LegendPosition.Bottom">
            <summary>
            Denotes the bottom-side positioning of the legend.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.Drawing.Charts.LegendPosition.Left">
            <summary>
            Denotes the left-side positioning of the legend.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.Drawing.Charts.LegendPosition.Top">
            <summary>
            Denotes the top-side positioning of the legend.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Model.Drawing.Charts.Marker">
            <summary>
            Represents a marker for the chart series.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Charts.Marker.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Model.Drawing.Charts.Marker" /> class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.Marker.Symbol">
            <summary>
            Gets or sets the marker symbol.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.Marker.Size">
            <summary>
            Gets or sets the marker size. The alowed values are between 2 and 72.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.Marker.Fill">
            <summary>
            Gets or sets the marker fill.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.Marker.Outline">
            <summary>
            Gets the marker outline.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Charts.Marker.Clone">
            <summary>
            Creates a deep copy of the object.
            </summary>
            <returns>The deep copy of the object.</returns>
        </member>
        <member name="T:Telerik.Windows.Documents.Model.Drawing.Charts.MarkerStyle">
            <summary>
            Represents the possible marker styles.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.Drawing.Charts.MarkerStyle.Auto">
            <summary>
            Represents the automatic marker.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.Drawing.Charts.MarkerStyle.Circle">
            <summary>
            Represents the circle marker.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.Drawing.Charts.MarkerStyle.Dash">
            <summary>
            Represents the dash marker.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.Drawing.Charts.MarkerStyle.Diamond">
            <summary>
            Represents the diamond marker.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.Drawing.Charts.MarkerStyle.Dot">
            <summary>
            Represents the dot marker.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.Drawing.Charts.MarkerStyle.None">
            <summary>
            Represents no marker.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.Drawing.Charts.MarkerStyle.Plus">
            <summary>
            Represents the plus marker.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.Drawing.Charts.MarkerStyle.Square">
            <summary>
            Represents the square marker.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.Drawing.Charts.MarkerStyle.Star">
            <summary>
            Represents the star marker.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.Drawing.Charts.MarkerStyle.Triangle">
            <summary>
            Represents the triangle marker.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.Drawing.Charts.MarkerStyle.X">
            <summary>
            Represents the x marker.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Model.Drawing.Charts.AreaSeries">
            <summary>
            Represents a series of type area.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.AreaSeries.SeriesType">
            <summary>
            Gets the type of the series.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Model.Drawing.Charts.AreaSeriesGroup">
            <summary>
            Represents a group of series of type area.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Charts.AreaSeriesGroup.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Model.Drawing.Charts.AreaSeriesGroup" /> class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.AreaSeriesGroup.SeriesType">
            <summary>
            Gets the type of series this group holds.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.AreaSeriesGroup.AxisGroupName">
            <summary>
            Gets or sets the value indicating which group of axes the series group is associated with.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.AreaSeriesGroup.Grouping">
            <summary>
            Gets or sets the value indicating how the series are grouped.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Model.Drawing.Charts.BarDirection">
            <summary>
            Represents the possible types of direction the bar series can have.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.Drawing.Charts.BarDirection.Bar">
            <summary>
            Represents the horizontal direction.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.Drawing.Charts.BarDirection.Column">
            <summary>
            Represents the vertical direction.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Model.Drawing.Charts.BarSeries">
            <summary>
            Represents a series of type bar.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.BarSeries.SeriesType">
            <summary>
            Gets the type of the series.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Model.Drawing.Charts.BarSeriesGroup">
            <summary>
            Represents a group of series of type bar.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Charts.BarSeriesGroup.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Model.Drawing.Charts.BarSeriesGroup" /> class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.BarSeriesGroup.SeriesType">
            <summary>
            Gets the type of series this group holds.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.BarSeriesGroup.AxisGroupName">
            <summary>
            Gets or sets the value indicating which group of axes the series group is associated with.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.BarSeriesGroup.Grouping">
            <summary>
            Gets or sets the value indicating how the series are grouped.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.BarSeriesGroup.BarDirection">
            <summary>
            Gets or sets the direction of the bars.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Model.Drawing.Charts.BubbleSeries">
            <summary>
            Represents a series of type bubble.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.BubbleSeries.SeriesType">
            <summary>
            Gets the type of the series.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.BubbleSeries.BubbleSizes">
            <summary>
            Gets or sets the data for the bubble size of the series.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Model.Drawing.Charts.BubbleSeriesGroup">
            <summary>
            Represents a group of series of type bubble.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Charts.BubbleSeriesGroup.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Model.Drawing.Charts.BubbleSeriesGroup" /> class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.BubbleSeriesGroup.SeriesType">
            <summary>
            Gets the type of series this group holds.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.BubbleSeriesGroup.AxisGroupName">
            <summary>
            Gets or sets the value indicating which group of axes the series group is associated with.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Model.Drawing.Charts.CategorySeriesBase">
            <summary>
            Represents a base class for the category series.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.CategorySeriesBase.Values">
            <summary>
            Gets or sets the data for the values of the series.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.CategorySeriesBase.Categories">
            <summary>
            Gets or sets the data for the categories of the series.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Model.Drawing.Charts.ChartDataType">
            <summary>
            Represents the possible types of chart data.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.Drawing.Charts.ChartDataType.Formula">
            <summary>
            Represents the formula chart data type. This type of chart data holds a formula which refers to the actual data.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.Drawing.Charts.ChartDataType.NumericLiteral">
            <summary>
            Represents the numeric literals type. This type of chart data holds a series of numbers as data.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.Drawing.Charts.ChartDataType.StringLiteral">
            <summary>
            Represents the string literals type. This type of chart data holds a series of strings as data.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Model.Drawing.Charts.DoughnutSeriesGroup">
            <summary>
            Represents a group of series of type doughnut.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Charts.DoughnutSeriesGroup.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Model.Drawing.Charts.DoughnutSeriesGroup" /> class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.DoughnutSeriesGroup.HoleSizePercent">
            <summary>
            Gets or sets the relative size of the hole of the doughnut as percent of the hole. The value is limited between 0 and 90.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Model.Drawing.Charts.FormulaChartData">
            <summary>
            Represents formula chart data. This type of chart data holds a formula which refers to the actual data.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Charts.FormulaChartData.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Model.Drawing.Charts.FormulaChartData" /> class.
            </summary>
            <param name="formula">The formula which refers to the chart data.</param>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.FormulaChartData.ChartDataType">
            <summary>
            Gets the type of chart data.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.FormulaChartData.Formula">
            <summary>
            Gets the formula which refers to the chart data.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Charts.FormulaChartData.Clone">
            <summary>
            Creates a deep copy of the object.
            </summary>
            <returns>The deep copy of the object.</returns>
        </member>
        <member name="T:Telerik.Windows.Documents.Model.Drawing.Charts.IChartData">
            <summary>
            Defines the members for the classes representing chart data.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.IChartData.ChartDataType">
            <summary>
            Gets the type of chart data.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Charts.IChartData.Clone">
            <summary>
            Creates a deep copy of the object.
            </summary>
            <returns>The deep copy of the object.</returns>
        </member>
        <member name="T:Telerik.Windows.Documents.Model.Drawing.Charts.ISupportGrouping">
            <summary>
            Defines members for the classes representing series which can be grouped. 
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.ISupportGrouping.Grouping">
            <summary>
            Gets or sets the value indicating how the series are grouped.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Model.Drawing.Charts.LineSeries">
            <summary>
            Represents a series of type line.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.LineSeries.SeriesType">
            <summary>
            Gets the type of the series.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.LineSeries.IsSmooth">
            <summary>
            Gets or sets a value indicating if the series line will be smooth.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.LineSeries.Marker">
            <summary>
            Gets or sets the series marker.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Model.Drawing.Charts.LineSeriesGroup">
            <summary>
            Represents a group of series of type line.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Charts.LineSeriesGroup.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Model.Drawing.Charts.LineSeriesGroup" /> class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.LineSeriesGroup.SeriesType">
            <summary>
            Gets the type of series this group holds.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.LineSeriesGroup.AxisGroupName">
            <summary>
            Gets or sets the value indicating which group of axes the series group is associated with.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.LineSeriesGroup.Grouping">
            <summary>
            Gets or sets the value indicating how the series are grouped.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Model.Drawing.Charts.NumericChartData">
            <summary>
            Represents the numeric literals chart data. This type of chart data holds a series of numbers as data.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Charts.NumericChartData.#ctor(System.Collections.Generic.IEnumerable{System.Double})">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Model.Drawing.Charts.NumericChartData" /> class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.NumericChartData.ChartDataType">
            <summary>
            Gets the type of chart data.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.NumericChartData.NumericLiterals">
            <summary>
            Gets the numeric chart data.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Charts.NumericChartData.Clone">
            <summary>
            Creates a deep copy of the object.
            </summary>
            <returns>The deep copy of the object.</returns>
        </member>
        <member name="T:Telerik.Windows.Documents.Model.Drawing.Charts.PieSeries">
            <summary>
            Represents a series of type pie.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.PieSeries.SeriesType">
            <summary>
            Gets the type of the series.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Model.Drawing.Charts.PieSeriesGroup">
            <summary>
            Represents a group of series of type pie.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Charts.PieSeriesGroup.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Model.Drawing.Charts.PieSeriesGroup" /> class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.PieSeriesGroup.SeriesType">
            <summary>
            Gets the type of series this group holds.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Model.Drawing.Charts.PointSeriesBase">
            <summary>
            Represents a base class for the point series.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.PointSeriesBase.XValues">
            <summary>
            Gets or sets the data for the X values of the series.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.PointSeriesBase.YValues">
            <summary>
            Gets or sets the data for the Y values of the series.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Model.Drawing.Charts.ScatterSeries">
            <summary>
            Represents a series of type scatter.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.ScatterSeries.SeriesType">
            <summary>
            Gets the type of the series.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.ScatterSeries.ScatterStyle">
            <summary>
            Gets the style of the scatter series.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.ScatterSeries.IsSmooth">
            <summary>
            Gets or sets a value indicating if the series line will be smooth.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.ScatterSeries.Marker">
            <summary>
            Gets or sets the series marker.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Model.Drawing.Charts.ScatterSeriesGroup">
            <summary>
            Represents a group of series of type scatter.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Charts.ScatterSeriesGroup.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Model.Drawing.Charts.ScatterSeriesGroup" /> class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.ScatterSeriesGroup.SeriesType">
            <summary>
            Gets the type of series this group holds.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.ScatterSeriesGroup.AxisGroupName">
            <summary>
            Gets or sets the value indicating which group of axes the series group is associated with.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Model.Drawing.Charts.ScatterStyle">
            <summary>
            Represents the possible types of scatter styles.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.Drawing.Charts.ScatterStyle.None">
            <summary>
            Specifies the points on the scatter chart shall not be connected with straight lines and markers shall not be drawn.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.Drawing.Charts.ScatterStyle.Line">
            <summary>
            Specifies the points on the scatter chart shall be connected with straight lines but markers shall not be drawn.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.Drawing.Charts.ScatterStyle.LineMarker">
            <summary>
            Specifies the points on the scatter chart shall be connected with straight lines and markers shall be drawn.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.Drawing.Charts.ScatterStyle.Marker">
            <summary>
            Specifies the points on the scatter chart shall not be connected with lines and markers shall be drawn.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.Drawing.Charts.ScatterStyle.Smooth">
            <summary>
            Specifies the the points on the scatter chart shall be connected with smoothed lines and markers shall not be drawn.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.Drawing.Charts.ScatterStyle.SmoothMarker">
            <summary>
            Specifies the the points on the scatter chart shall be connected with smoothed lines and markers shall be drawn.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Model.Drawing.Charts.SeriesBase">
            <summary>
            Represents a base class for the series.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Charts.SeriesBase.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Model.Drawing.Charts.SeriesBase" /> class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.SeriesBase.SeriesType">
            <summary>
            Gets the type of the series.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.SeriesBase.Title">
            <summary>
            Gets or sets the title of the series.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.SeriesBase.Outline">
            <summary>
            Gets the series outline.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.SeriesBase.Fill">
            <summary>
            Gets or sets series fill.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Charts.SeriesBase.Clone">
            <summary>
            Creates a deep copy of the object.
            </summary>
            <returns>The deep copy of the object.</returns>
        </member>
        <member name="T:Telerik.Windows.Documents.Model.Drawing.Charts.SeriesCollection">
            <summary>
            Represents a base class for a collection of series.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Charts.SeriesCollection.GetEnumerator">
            <summary>
            Enumerates the series in the collection.
            </summary>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Charts.SeriesCollection.Add">
            <summary>
            Creates a new series, adds it to the collection and returns it.
            </summary>
            <returns>The new series object.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Charts.SeriesCollection.AddBubble(Telerik.Windows.Documents.Model.Drawing.Charts.IChartData,Telerik.Windows.Documents.Model.Drawing.Charts.IChartData,Telerik.Windows.Documents.Model.Drawing.Charts.IChartData,Telerik.Windows.Documents.Model.Drawing.Charts.Title)">
            <summary>
            Creates a new series with data siutable for bubble series, adds it to the collection and returns it.
            </summary>
            <param name="xValuesData">The data for the X values of the series.</param>
            <param name="yValuesData">The data for the Y values of the series.</param>
            <param name="bubbleSizesData">The data for the bubble size values of the series.</param>
            <param name="title">The title of the series.</param>
            <returns>The new series object.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Charts.SeriesCollection.AddScatter(Telerik.Windows.Documents.Model.Drawing.Charts.IChartData,Telerik.Windows.Documents.Model.Drawing.Charts.IChartData,Telerik.Windows.Documents.Model.Drawing.Charts.Title)">
            <summary>
            Creates a new series with data siutable for scatter series, adds it to the collection and returns it.
            </summary>
            <param name="xValuesData">The data for the X values of the series.</param>
            <param name="yValuesData">The data for the Y values of the series.</param>
            <param name="title">The title of the series.</param>
            <returns>The new series object.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Charts.SeriesCollection.Add(Telerik.Windows.Documents.Model.Drawing.Charts.IChartData,Telerik.Windows.Documents.Model.Drawing.Charts.IChartData,Telerik.Windows.Documents.Model.Drawing.Charts.Title)">
            <summary>
            Creates a new series using the specified data, adds it to the collection and returns it.
            </summary>
            <param name="categoriesData">The data for the categories of the series.</param>
            <param name="valuesData">The data for the values of the series.</param>
            <param name="title">The title of the series.</param>
            <returns>The new series object.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Charts.SeriesCollection.Add(Telerik.Windows.Documents.Model.Drawing.Charts.SeriesBase)">
            <summary>
            Adds a new series to the collection.
            </summary>
            <param name="series">The series to be added.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Charts.SeriesCollection.Remove(Telerik.Windows.Documents.Model.Drawing.Charts.SeriesBase)">
            <summary>
            Removes the specified series from the collection.
            </summary>
            <param name="series">The series to be removed.</param>
        </member>
        <member name="T:Telerik.Windows.Documents.Model.Drawing.Charts.SeriesCollection`1">
            <summary>
            Represents a collection of series.
            </summary>
            <typeparam name="T">The type of series the collection holds.</typeparam>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Charts.SeriesCollection`1.GetEnumerator">
            <summary>
            Enumerates the series in the collection as objects of a concrete series class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Charts.SeriesCollection`1.Add">
            <summary>
            Creates a new series, adds it to the collection and returns it.
            </summary>
            <returns>The new series object.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Charts.SeriesCollection`1.AddBubble(Telerik.Windows.Documents.Model.Drawing.Charts.IChartData,Telerik.Windows.Documents.Model.Drawing.Charts.IChartData,Telerik.Windows.Documents.Model.Drawing.Charts.IChartData,Telerik.Windows.Documents.Model.Drawing.Charts.Title)">
            <summary>
            Creates a new series with data siutable for bubble series, adds it to the collection and returns it.
            </summary>
            <param name="xValuesData">The data for the X values of the series.</param>
            <param name="yValuesData">The data for the Y values of the series.</param>
            <param name="bubbleSizesData">The data for the bubble size values of the series.</param>
            <param name="title">The title of the series.</param>
            <returns>The new series object.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Charts.SeriesCollection`1.AddScatter(Telerik.Windows.Documents.Model.Drawing.Charts.IChartData,Telerik.Windows.Documents.Model.Drawing.Charts.IChartData,Telerik.Windows.Documents.Model.Drawing.Charts.Title)">
            <summary>
            Creates a new series with data siutable for scatter series, adds it to the collection and returns it.
            </summary>
            <param name="xValuesData">The data for the X values of the series.</param>
            <param name="yValuesData">The data for the Y values of the series.</param>
            <param name="title">The title of the series.</param>
            <returns>The new series object.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Charts.SeriesCollection`1.Add(Telerik.Windows.Documents.Model.Drawing.Charts.IChartData,Telerik.Windows.Documents.Model.Drawing.Charts.IChartData,Telerik.Windows.Documents.Model.Drawing.Charts.Title)">
            <summary>
            Creates a new series using the specified data, adds it to the collection and returns it.
            </summary>
            <param name="categoriesData">The data for the categories of the series.</param>
            <param name="valuesData">The data for the values of the series.</param>
            <param name="title">The title of the series.</param>
            <returns>The new series object.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Charts.SeriesCollection`1.Remove(`0)">
            <summary>
            Removes the specified series from the collection.
            </summary>
            <param name="series">The series to be removed.</param>
        </member>
        <member name="T:Telerik.Windows.Documents.Model.Drawing.Charts.SeriesGroup">
            <summary>
            Represents a base class for a group of series.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.SeriesGroup.SeriesType">
            <summary>
            Gets the type of series this group holds.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.SeriesGroup.Series">
            <summary>
            Gets the collection of series of the group.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Charts.SeriesGroup.Clone">
            <summary>
            Creates a deep copy of the object.
            </summary>
            <returns>The deep copy of the object.</returns>
        </member>
        <member name="T:Telerik.Windows.Documents.Model.Drawing.Charts.SeriesGroupCollection">
            <summary>
            Represents a group of series.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Charts.SeriesGroupCollection.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Model.Drawing.Charts.SeriesGroupCollection" /> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Charts.SeriesGroupCollection.Add(Telerik.Windows.Documents.Model.Drawing.Charts.SeriesGroup)">
            <summary>
            Adds a new group of series to the collection.
            </summary>
            <param name="seriesGroup">The new group of series.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Charts.SeriesGroupCollection.Remove(Telerik.Windows.Documents.Model.Drawing.Charts.SeriesGroup)">
            <summary>
            Removes the specified group of series from the collection,
            </summary>
            <param name="seriesGroup"></param>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Charts.SeriesGroupCollection.GetEnumerator">
            <summary>
            Enumerates the elements of the collection.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Model.Drawing.Charts.SeriesGrouping">
            <summary>
            Denotes the possible types of grouping for a group of series.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.Drawing.Charts.SeriesGrouping.Standard">
            <summary>
            Denotes the standard type of grouping. For bar series, the series will be clustered.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.Drawing.Charts.SeriesGrouping.Stacked">
            <summary>
            Denotes the stacked type of grouping.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.Drawing.Charts.SeriesGrouping.PercentStacked">
            <summary>
            Denotes the percent stacked type of grouping.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Model.Drawing.Charts.SeriesGroup`1">
            <summary>
            Represents a base class for a group of series.
            </summary>
            <typeparam name="T">The type of the series that the group can hold.</typeparam>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Charts.SeriesGroup`1.#ctor(Telerik.Windows.Documents.Model.Drawing.Charts.SeriesType)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Model.Drawing.Charts.SeriesGroup" /> class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.SeriesGroup`1.Series">
            <summary>
            Gets the collection of series of the group. The series are represented by the <see cref="T:Telerik.Windows.Documents.Model.Drawing.Charts.SeriesBase" /> class.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Model.Drawing.Charts.SeriesType">
            <summary>
            Represents the possible types of series.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.Drawing.Charts.SeriesType.Bar">
            <summary>
            Represents the bar series type.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.Drawing.Charts.SeriesType.Line">
            <summary>
            Represents the line series type.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.Drawing.Charts.SeriesType.Pie">
            <summary>
            Represents the pie series type.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.Drawing.Charts.SeriesType.Area">
            <summary>
            Represents the area series type.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.Drawing.Charts.SeriesType.Scatter">
            <summary>
            Represents the scatter series type.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.Drawing.Charts.SeriesType.Bubble">
            <summary>
            Represents the bubble series type.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Model.Drawing.Charts.StringChartData">
            <summary>
            Represents the string literals chart data. This type of chart data holds a series of strings as data.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Charts.StringChartData.#ctor(System.Collections.Generic.IEnumerable{System.String})">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Model.Drawing.Charts.StringChartData" /> class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.StringChartData.ChartDataType">
            <summary>
            Gets the type of chart data.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.StringChartData.StringLiterals">
            <summary>
            Gets the string chart data.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Charts.StringChartData.Clone">
            <summary>
            Creates a deep copy of the object.
            </summary>
            <returns>The deep copy of the object.</returns>
        </member>
        <member name="T:Telerik.Windows.Documents.Model.Drawing.Charts.FormulaTitle">
            <summary>
            Represent the formula chart title. This type of title holds a formula which refers to the actual title.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Charts.FormulaTitle.#ctor(Telerik.Windows.Documents.Model.Drawing.Charts.FormulaChartData)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Model.Drawing.Charts.FormulaTitle" /> class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.FormulaTitle.TitleType">
            <summary>
            Gets the type of the title.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.FormulaTitle.Formula">
            <summary>
            Gets the formula referring to the actual title.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Charts.FormulaTitle.Clone">
            <summary>
            Creates a deep copy of the object.
            </summary>
            <returns>The deep copy of the object.</returns>
        </member>
        <member name="T:Telerik.Windows.Documents.Model.Drawing.Charts.TextTitle">
            <summary>
            Represents the text chart title. This type of title holds a text literal to be used as title.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Charts.TextTitle.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Model.Drawing.Charts.TextTitle" /> class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.TextTitle.TitleType">
            <summary>
            Gets the type of the title.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.TextTitle.Text">
            <summary>
            Gets the text of the title.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Charts.TextTitle.Clone">
            <summary>
            Creates a deep copy of the object.
            </summary>
            <returns>The deep copy of the object.</returns>
        </member>
        <member name="T:Telerik.Windows.Documents.Model.Drawing.Charts.Title">
            <summary>
            Represents a base class for the classes representing a title in the chart.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.Drawing.Charts.Title.TitleType">
            <summary>
            Gets the type of the title.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.Drawing.Charts.Title.Clone">
            <summary>
            Creates a deep copy of the object.
            </summary>
            <returns>The deep copy of the object.</returns>
        </member>
        <member name="T:Telerik.Windows.Documents.Model.Drawing.Charts.TitleType">
            <summary>
            Represents the possible types of chart title.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.Drawing.Charts.TitleType.Formula">
            <summary>
            Denotes the formula chart title type. This type of title holds a formula which refers to the actual title.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.Drawing.Charts.TitleType.Text">
            <summary>
            Denotes the text chart title type. This type of title holds a text literal to be used as title.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Model.DocumentInfo">
            <summary>
            Provides members for defining metadata information for an Open XML file.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.DocumentInfo.Title">
            <summary>
            Gets or sets the title.
            </summary>
            <value>
            The title.
            </value>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.DocumentInfo.Author">
            <summary>
            Gets or sets the author.
            </summary>
            <value>
            The author.
            </value>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.DocumentInfo.Subject">
            <summary>
            Gets or sets the subject.
            </summary>
            <value>
            The subject.
            </value>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.DocumentInfo.Keywords">
            <summary>
            Gets or sets the keywords.
            </summary>
            <value>
            The keywords.
            </value>
        </member>
        <member name="P:Telerik.Windows.Documents.Model.DocumentInfo.Description">
            <summary>
            Gets or sets the description.
            </summary>
            <value>
            The description.
            </value>
        </member>
        <member name="T:Telerik.Windows.Documents.Model.PageOrientation">
            <summary>
            Specifies page orientation.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.PageOrientation.Portrait">
            <summary> 
            Portrait page orientation.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.PageOrientation.Landscape">
            <summary> 
            Landscape page orientation.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.PageOrientation.Rotate180">
            <summary> 
            Page is rotated 180 degrees.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.PageOrientation.Rotate270">
            <summary> 
            Page is rotated 270 degrees
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.PageOrientation.Rotate0">
            <summary> 
            Portrait page orientation.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.PageOrientation.Rotate90">
            <summary> 
            Landscape page orientation.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Model.PaperTypeConverter">
            <summary>
            Provides methods for converting from standard PaperTypes to Size
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Model.PaperTypeConverter.ToSize(Telerik.Windows.Documents.Model.PaperTypes)">
            <summary>
            Converts the specified PaperType enumeration to a pair of pixel values in Size.
            </summary>
            <param name="type">PaperType</param>
            <returns></returns>
        </member>
        <member name="T:Telerik.Windows.Documents.Model.PaperTypes">
            <summary>
            Provides enumeration for the most commonly used paper sizes.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.PaperTypes.A0">
            <summary>
            Identifies a paper sheet size of 33.1 inches x 46.8 inches.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.PaperTypes.A1">
            <summary>
            Identifies a paper sheet size of 23.4 inches x 33.1 inches.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.PaperTypes.A2">
            <summary>
            Identifies a paper sheet size of 16.5 inches x 23.4 inches.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.PaperTypes.A3">
            <summary>
            Identifies a paper sheet size of 11.7 inches x 16.5 inches.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.PaperTypes.A4">
            <summary>
            Identifies a paper sheet size of 8.3 inches x 11.7 inches.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.PaperTypes.A5">
            <summary>
            Identifies a paper sheet size of 5.8 inches x 8.3 inches.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.PaperTypes.RA0">
            <summary>
            Identifies a paper sheet size of 33.9 inches x 48 inches.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.PaperTypes.RA1">
            <summary>
            Identifies a paper sheet size of 24 inches x 33.9 inches.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.PaperTypes.RA2">
            <summary>
            Identifies a paper sheet size of 16.9 inches x 24 inches.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.PaperTypes.RA3">
            <summary>
            Identifies a paper sheet size of 12 inches x 16.9 inches.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.PaperTypes.RA4">
            <summary>
            Identifies a paper sheet size of 8.5 inches x 12 inches.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.PaperTypes.RA5">
            <summary>
            Identifies a paper sheet size of 4.8 inches x 8.5 inches.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.PaperTypes.B0">
            <summary>
            Identifies a paper sheet size of 39.4 inches x 55.7 inches.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.PaperTypes.B1">
            <summary>
            Identifies a paper sheet size of 27.8 inches x 39.4 inches.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.PaperTypes.B2">
            <summary>
            Identifies a paper sheet size of 59.1 inches x 19.7 inches.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.PaperTypes.B3">
            <summary>
            Identifies a paper sheet size of 13.9 inches x 19.7 inches.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.PaperTypes.B4">
            <summary>
            Identifies a paper sheet size of 10.1 inches x 14.3 inches.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.PaperTypes.B5">
            <summary>
            Identifies a paper sheet size of 7.2 inches x 10.1 inches.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.PaperTypes.Quarto">
            <summary>
            Identifies a paper sheet size of 8 inches x 10 inches.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.PaperTypes.Foolscap">
            <summary>
            Identifies a paper sheet size of 8 inches x 13 inches.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.PaperTypes.Executive">
            <summary>
            Identifies a paper sheet size of 7.5 inches x 10 inches.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.PaperTypes.GovernmentLetter">
            <summary>
            Identifies a paper sheet size of 10.5 inches x 8 inches.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.PaperTypes.Letter">
            <summary>
            Identifies a paper sheet size of 8.5 inches x 11 inches.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.PaperTypes.Legal">
            <summary>
            Identifies a paper sheet size of 8.5 inches x 14 inches.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.PaperTypes.Ledger">
            <summary>
            Identifies a paper sheet size of 17 inches x 11 inches.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.PaperTypes.Tabloid">
            <summary>
            Identifies a paper sheet size of 11 inches x 17 inches.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.PaperTypes.Post">
            <summary>
            Identifies a paper sheet size of 15.6 inches x 19.2 inches.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.PaperTypes.Crown">
            <summary>
            Identifies a paper sheet size of 20 inches x 15 inches.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.PaperTypes.LargePost">
            <summary>
            Identifies a paper sheet size of 16.5 inches x 21 inches.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.PaperTypes.Demy">
            <summary>
            Identifies a paper sheet size of 17.5 inches x 22 inches.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.PaperTypes.Medium">
            <summary>
            Identifies a paper sheet size of 18 inches x 23 inches.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.PaperTypes.Royal">
            <summary>
            Identifies a paper sheet size of 20 inches x 25 inches.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.PaperTypes.Elephant">
            <summary>
            Identifies a paper sheet size of 21.7 inches x 28 inches.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.PaperTypes.DoubleDemy">
            <summary>
            Identifies a paper sheet size of 23.5 inches x 35 inches.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.PaperTypes.QuadDemy">
            <summary>
            Identifies a paper sheet size of 35 inches x 45 inches.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.PaperTypes.STMT">
            <summary>
            Identifies a paper sheet size of 5.5 inches x 8.5 inches.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.PaperTypes.Folio">
            <summary>
            Identifies a paper sheet size of 8.5 inches x 13 inches.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.PaperTypes.Statement">
            <summary>
            Identifies a paper sheet size of 5.5 inches x 8.5 inches.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Model.PaperTypes.Size10x14">
            <summary>
            Identifies a paper sheet size of 10 inches x 14 inches.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Core.Data.DocumentElementCollectionBase`2">
            <summary>
            Provides based functionality for collections of document elements.
            </summary>
            <typeparam name="T">The type of the document elements which should be added to the collection.</typeparam>
            <typeparam name="TOwner">The type of the owner of the collection.</typeparam>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Data.DocumentElementCollectionBase`2.Count">
            <summary>
            Gets the number of elements contained in the collection.
            </summary>
            <returns>The number of elements contained in the collection.</returns>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Data.DocumentElementCollectionBase`2.System#Collections#Generic#ICollection{T}#IsReadOnly">
            <summary>
            Gets a value indicating whether the collection is read-only.
            </summary>
            <returns><c>true</c> if the collection is read-only; otherwise, <c>false</c>.</returns>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Data.DocumentElementCollectionBase`2.Owner">
            <summary>
            Gets the element holding the collection.
            </summary>
            <value>The owner element.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Data.DocumentElementCollectionBase`2.Item(System.Int32)">
            <summary>
            Gets or sets the element at the specified index.
            </summary>
            <param name="index">The index.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Data.DocumentElementCollectionBase`2.GetEnumerator">
            <summary>
            Returns an enumerator that iterates through the collection.
            </summary>
            <returns>
            A <see cref="T:System.Collections.Generic.IEnumerator`1" /> that can
            be used to iterate through the collection.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Data.DocumentElementCollectionBase`2.IndexOf(`0)">
            <summary>
            Determines the index of a specific element in the collection.
            </summary>
            <param name="item">The element to locate in the collection.</param>
            <returns>
            The index of <paramref name="item" /> if found in the list; otherwise, -1.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Data.DocumentElementCollectionBase`2.Insert(System.Int32,`0)">
            <summary>
            Inserts an element to the collection at the specified index.
            </summary>
            <param name="index">The zero-based index at which <paramref name="item" /> should be inserted.</param>
            <param name="item">The element to insert into the collection.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Data.DocumentElementCollectionBase`2.InsertRange(System.Int32,System.Collections.Generic.IEnumerable{`0})">
            <summary>
            Inserts an elements to the collection at the specified index.
            </summary>
            <param name="index">The zero-based index at which <paramref name="items" /> should be inserted.</param>
            <param name="items">The elements to be inserted into the collection.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Data.DocumentElementCollectionBase`2.RemoveAt(System.Int32)">
            <summary>
            Removes the element at the specified index.
            </summary>
            <param name="index">The zero-based index of the item to remove.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Data.DocumentElementCollectionBase`2.RemoveRange(System.Int32,System.Int32)">
            <summary>
            Removes the range.
            </summary>
            <param name="index">The zero-based index of the item to remove.</param>
            <param name="count">The number of elements to remove.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Data.DocumentElementCollectionBase`2.OnAfterRemove(`0)">
            <summary>
            Called when the element is removed.
            </summary>
            <param name="item">The element.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Data.DocumentElementCollectionBase`2.Clear">
            <summary>
            Removes all items from the collection.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Data.DocumentElementCollectionBase`2.Contains(`0)">
            <summary>
            Determines whether the collection contains a specific element.
            </summary>
            <param name="item">The element to locate in the collection.</param>
            <returns>
            <c>true</c> if <paramref name="item" /> is found in the collection; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Data.DocumentElementCollectionBase`2.CopyTo(`0[],System.Int32)">
            <summary>
            Copies the elements of the collection to an <see cref="T:System.Array"/>, starting at a particular <see cref="T:System.Array"/> index.
            </summary>
            <param name="array">The array.</param>
            <param name="arrayIndex">Index of the array.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Data.DocumentElementCollectionBase`2.Remove(`0)">
            <summary>
            Removes the first occurrence of a specific object from the collection.
            </summary>
            <param name="item">The object to remove from the collection.</param>
            <returns>
            <c>true</c> if <paramref name="item" /> was successfully removed from the collection; otherwise, <c>false</c>. This method also returns false if <paramref name="item" /> is not found in the original <see cref="T:System.Collections.Generic.ICollection`1" />.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Data.DocumentElementCollectionBase`2.System#Collections#IEnumerable#GetEnumerator">
            <summary>
            Returns an enumerator that iterates through a collection.
            </summary>
            <returns>
            An <see cref="T:System.Collections.IEnumerator" /> object that can be
            used to iterate through the collection.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Data.DocumentElementCollectionBase`2.Add(`0)">
            <summary>
            Adds an item to the collection.
            </summary>
            <param name="item">The element to add to the collection.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Data.DocumentElementCollectionBase`2.VerifyDocumentElementOnInsert(`0)">
            <summary>
            Verifies the validity of the document element before it is inserted in the collection. 
            </summary>
            <param name="item">The item.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Data.DocumentElementCollectionBase`2.SetParent(`0,`1)">
            <summary>
            Sets the parent of the document element.
            </summary>
            <param name="item">The document element.</param>
            <param name="parent">The parent which should be set to the document element.</param>
        </member>
        <member name="T:Telerik.Windows.Documents.Core.Data.IStackCollectionElement">
            <summary>
            Represents StackCollection element.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Data.IStackCollectionElement.Name">
            <summary>
            Gets the name of the element.
            </summary>
            <value>The name.</value>
        </member>
        <member name="T:Telerik.Windows.Documents.Core.Data.StackCollection`1">
            <summary>
            Represents Stack collection.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Data.StackCollection`1.#ctor">
            <summary>
            Initializes a new instance of the StackCollection class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Data.StackCollection`1.Count">
            <summary>
            Gets the number of elements contained in the collection.
            </summary>
            <returns>The number of elements contained in the collection.
            </returns>
            <value></value>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Data.StackCollection`1.IsReadOnly">
            <summary>
            Gets a value indicating whether the <see cref="T:System.Collections.Generic.ICollection`1" />
            is read-only.
            </summary>
            <returns>true if the <see cref="T:System.Collections.Generic.ICollection`1" />
            is read-only; otherwise, false.</returns>
            <value></value>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Data.StackCollection`1.GetElementByName(System.String)">
            <summary>
            Gets the name of the element by.
            </summary>
            <param name="elementName">Name of the element.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Data.StackCollection`1.Add(`0)">
            <summary>
            Adds the specified item.
            </summary>
            <param name="item">The item.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Data.StackCollection`1.AddBefore(System.String,`0)">
            <summary>
            Adds the before.
            </summary>
            <param name="presentedElementName">Name of the presented element.</param>
            <param name="layer">The layer.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Data.StackCollection`1.AddAfter(System.String,`0)">
            <summary>
            Adds the after.
            </summary>
            <param name="presentedElementName">Name of the presented element.</param>
            <param name="element">The element.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Data.StackCollection`1.AddFirst(`0)">
            <summary>
            Adds the first.
            </summary>
            <param name="element">The element.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Data.StackCollection`1.AddLast(`0)">
            <summary>
            Adds the last.
            </summary>
            <param name="element">The element.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Data.StackCollection`1.Contains(`0)">
            <summary>
            Determines whether [contains] [the specified item].
            </summary>
            <param name="item">The item.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Data.StackCollection`1.Contains(System.String)">
            <summary>
            Determines whether [contains] [the specified element name].
            </summary>
            <param name="elementName">Name of the element.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Data.StackCollection`1.Remove(`0)">
            <summary>
            Removes the specified item.
            </summary>
            <param name="item">The item.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Data.StackCollection`1.Remove(System.String)">
            <summary>
            Removes the specified element name.
            </summary>
            <param name="elementName">Name of the element.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Data.StackCollection`1.CopyTo(`0[],System.Int32)">
            <summary>
            Copies to.
            </summary>
            <param name="array">The array.</param>
            <param name="arrayIndex">Index of the array.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Data.StackCollection`1.Clear">
            <summary>
            Removes all items from the <see cref="T:System.Collections.Generic.ICollection`1" />.
            </summary>
            <exception cref="T:System.NotSupportedException">The <see cref="T:System.Collections.Generic.ICollection`1" />
            is read-only. </exception>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Data.StackCollection`1.GetEnumerator">
            <summary>
            Returns an enumerator that iterates through the collection.
            </summary>
            <returns>
            A <see cref="T:System.Collections.Generic.IEnumerator`1" /> that can
            be used to iterate through the collection.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Data.StackCollection`1.System#Collections#IEnumerable#GetEnumerator">
            <summary>
            Returns an enumerator that iterates through a collection.
            </summary>
            <returns>
            An <see cref="T:System.Collections.IEnumerator" /> object that can be
            used to iterate through the collection.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Data.Matrix.op_Multiply(Telerik.Windows.Documents.Core.Data.Matrix,Telerik.Windows.Documents.Core.Data.Matrix)">
            <summary>
            Multiplies two transformations.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Data.Matrix.Scale(System.Double,System.Double,System.Double,System.Double)">
            <summary>
            Scales this matrix around the origin
            </summary>
            <param name='scaleX'>The scale factor in the x dimension</param>
            <param name='scaleY'>The scale factor in the y dimension</param>
            <param name='centerX'>The centerX of scaling</param>
            <param name='centerY'>The centerY of scaling</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Data.Matrix.ScalePrepend(System.Double,System.Double,System.Double,System.Double)">
            <summary>
            Prepends a scale around the origin to "this"
            </summary>
            <param name='scaleX'>The scale factor in the x dimension</param>
            <param name='scaleY'>The scale factor in the y dimension</param>
            <param name='centerX'>The centerX of scaling</param>
            <param name='centerY'>The centerY of scaling</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Data.Matrix.CreateScaling(System.Double,System.Double,System.Double,System.Double)">
            <summary>
            Creates a scaling transform around the given point
            </summary>
            <param name='scaleX'>The scale factor in the x dimension</param>
            <param name='scaleY'>The scale factor in the y dimension</param>
            <param name='centerX'>The centerX of scaling</param>
            <param name='centerY'>The centerY of scaling</param>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Imaging.Jpeg.Utils.BitsWriter.BitsLeft">
            <summary>
            Gets number of the bits left in the buffer.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Imaging.Jpeg.Utils.BitsWriter.IsEmpty">
            <summary>
            Gets value which indicates that buffer is empty (no bits have been written).
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Imaging.Jpeg.Utils.BitsWriter.IsFull">
            <summary>
            Gets value which indicates whether all bits in the buffer have been written.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Imaging.Jpeg.Utils.BitsWriter.Data">
            <summary>
            Gets writer data.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Imaging.Jpeg.Utils.BitsWriter.Clear">
            <summary>
            Clear buffer.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Imaging.Jpeg.Utils.BitsWriter.WriteBits(System.Byte,System.Int32)">
            <summary>
            Write bits into the internal buffer.
            </summary>
            <param name="value">Value to get bits from.</param>
            <param name="n">Number of bits.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Imaging.Jpeg.Encoder.BaselineDCTEncoder.PrepareHuffmanTables(Telerik.Windows.Documents.Core.Imaging.Jpeg.Encoder.JpegEncoder)">
            <summary>
            Prepare Huffman tables.
            </summary>
            <param name="jpegEncoder">Encoder to add table to.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Imaging.Jpeg.Encoder.BaselineDCTEncoder.PrepareQuantizationTables(Telerik.Windows.Documents.Core.Imaging.Jpeg.Encoder.JpegEncoder)">
            <summary>
            Prepare base quantization tables.
            </summary>
            <param name="encoder">Encoder to add table to.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Imaging.Jpeg.Encoder.JpegEncoder.#ctor(Telerik.Windows.Documents.Core.Imaging.Jpeg.JpegImage,Telerik.Windows.Documents.Core.Imaging.Jpeg.Encoder.JpegEncoderParameters)">
            <summary>
            Initializes a new instance of the JpegEncoder class.
            </summary>
            <param name="jpegImage">JPEG image to be encoded.</param>
            <param name="encoderParameters">JPEG encoder parameters.</param>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Imaging.Jpeg.Encoder.JpegEncoder.Height">
            <summary>
            Gets number of lines (height). 
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Imaging.Jpeg.Encoder.JpegEncoder.Parameters">
            <summary>
            Gets encoder parameters.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Imaging.Jpeg.Encoder.JpegEncoder.Width">
            <summary>
            Gets number of the samples per line (width).
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Core.Imaging.Jpeg.Encoder.JpegEncoderParameters">
            <summary>
            Represents parameters of the JPEG encoder.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Imaging.Jpeg.Encoder.JpegEncoderParameters.#ctor">
            <summary>
            Initializes a new instance of the JpegEncoderParameters class.
            </summary>
        </member>
        <member name="E:Telerik.Windows.Documents.Core.Imaging.Jpeg.Encoder.JpegEncoderParameters.PropertyChanged">
            <summary>
            Occurs when a property value changes.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Imaging.Jpeg.Encoder.JpegEncoderParameters.ChrominanceTable">
            <summary>
            Gets or sets a 64 byte array which corresponds to a JPEG Chrominance Quantization table.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Imaging.Jpeg.Encoder.JpegEncoderParameters.EncodingType">
            <summary>
            Gets or sets type of the JPEG encoder.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Imaging.Jpeg.Encoder.JpegEncoderParameters.LuminanceTable">
            <summary>
            Gets or sets a 64 byte array which corresponds to a JPEG Luminance Quantization table.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Imaging.Jpeg.Encoder.JpegEncoderParameters.QuantizingQuality">
            <summary>
            Gets or sets quantizing quality.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Imaging.Jpeg.Encoder.JpegEncoderParameters.SamplePrecision">
            <summary>
            Gets or sets the precision in bits for the samples of the components in the frame.
            </summary>
            <remarks>CCITT T.81, Annex B, section B.2.2 Frame header syntax (see P parameter in the Table B.2).</remarks>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Imaging.Jpeg.Encoder.JpegWriter.#ctor">
            <summary>
            Initializes a new instance of the JpegWriter class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Imaging.Jpeg.Encoder.JpegWriter.Write4(System.Byte)">
            <summary>
            Write 4 bits from the given byte value.
            </summary>
            <param name="value">Byte to get bits from.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Imaging.Jpeg.Encoder.JpegWriter.Write8(System.Byte)">
            <summary>
            Write byte,
            </summary>
            <param name="value">Byte to write.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Imaging.Jpeg.Encoder.JpegWriter.Write16(System.UInt16)">
            <summary>
            Write usignded short value.
            </summary>
            <param name="value">Value to write.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Imaging.Jpeg.Encoder.JpegWriter.WriteJpegTables``1(System.Collections.Generic.IEnumerable{``0},System.UInt16)">
            <summary>
            Write JPEG information structures.
            </summary>
            <typeparam name="T">Table type.</typeparam>
            <param name="tables">Tables to write.</param>
            <param name="extraBytesCount">Numbe of the extra bytes whould be added to the length of the table list.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Imaging.Jpeg.Encoder.JpegWriter.WriteBits(System.Int32,System.Int32)">
            <summary>
            Writes a bits.
            </summary>
            <param name="n">Number of bits.</param>
            <param name="bits">Value to get bits from.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Imaging.Jpeg.Encoder.JpegWriter.WriteJpegMarker(Telerik.Windows.Documents.Core.Imaging.Jpeg.Markers.JpegMarker)">
            <summary>
            Writes a JPEG marker.
            </summary>
            <param name="marker">Mrker to write.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Imaging.Jpeg.Encoder.JpegWriter.Restart">
            <summary>
            Restart buffer writer.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Imaging.Jpeg.Encoder.ScanEncoder.PrepareQuantizationTables(Telerik.Windows.Documents.Core.Imaging.Jpeg.Encoder.JpegEncoder)">
            <summary>
            Prepare base quantization tables.
            </summary>
            <param name="encoder">Encoder to add table to.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Imaging.Jpeg.Encoder.ScanEncoder.PrepareHuffmanTables(Telerik.Windows.Documents.Core.Imaging.Jpeg.Encoder.JpegEncoder)">
            <summary>
            Prepare Huffman tables.
            </summary>
            <param name="jpegEncoder">Encoder to add table to.</param>
        </member>
        <member name="T:Telerik.Windows.Documents.Core.Imaging.Jpeg.JpegEncodingType">
            <summary>
            Type of the JPEG encoding.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Core.Imaging.Jpeg.JpegEncodingType.BaselineDct">
            <summary>
            Baseline DCT encoding.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Core.Imaging.Jpeg.JpegEncodingType.ProgressiveDct">
            <summary>
            Progressive DCT encoding.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Core.Imaging.Jpeg.JpegEncodingType.NotSupported">
            <summary>
            Indicates that JPEG uses not-supported encoding type.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Core.Imaging.Jpeg.Markers.JfifMarker">
            <summary>
            Represents JFIF segment (APP0 marker).
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Core.Imaging.Jpeg.Markers.JfifMarker.JFIF_Identifier">
            <summary>
            JFIF segment format.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Core.Imaging.Jpeg.Markers.JfifMarker.JFXX_Identifier">
            <summary>
            JFIF extension (JFXX) segment format. Currently is not supported.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Imaging.Jpeg.Markers.JfifMarker.DensityUnits">
            <summary>
            Gets or sets units for pixel density fields.
            </summary>
            <remarks>
            <list type="bullet">
            <item>0 - No units, aspect ratio only specified.</item>
            <item>1 - Pixels per inch.</item>
            <item>2 - Pixels per centimetre.</item>
            </list>
            </remarks>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Imaging.Jpeg.Markers.JfifMarker.DensityX">
            <summary>
            Gets or sets horizontal pixel density.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Imaging.Jpeg.Markers.JfifMarker.DensityY">
            <summary>
            Gets or sets vertical pixel density.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Imaging.Jpeg.Markers.JfifMarker.Length">
            <summary>
            Gets length of the marker segment.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Imaging.Jpeg.Markers.JfifMarker.Identifier">
            <summary>
            Gets or sets identifier.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Imaging.Jpeg.Markers.JfifMarker.ThumbnailData">
            <summary>
            Gets or sets embedded JFIF thumbnail data.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Imaging.Jpeg.Markers.JfifMarker.ThumbnailHeight">
            <summary>
            Gets or sets vertical size of embedded JFIF thumbnail in pixels.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Imaging.Jpeg.Markers.JfifMarker.ThumbnailWidth">
            <summary>
            Gets or sets horizontal size of embedded JFIF thumbnail in pixels.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Imaging.Jpeg.Markers.AdobeMarker.Length">
            <summary>
            Gets length of the marker segment.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Imaging.Jpeg.Markers.DefineHuffmanTableMarker.Length">
            <summary>
            Gets length of the marker segment.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Imaging.Jpeg.Markers.DefineQuantizationTableMarker.Length">
            <summary>
            Gets length of the marker segment.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Imaging.Jpeg.Markers.DefineRestartIntervalMarker.Length">
            <summary>
            Gets length of the marker segment.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Imaging.Jpeg.Markers.EndOfImageMarker.Length">
            <summary>
            Gets length of the marker segment.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Imaging.Jpeg.Markers.JpegMarker.Length">
            <summary>
            Gets length of the marker segment.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Imaging.Jpeg.Markers.JpegMarker.MarkerType">
            <summary>
            Gets marker type.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Imaging.Jpeg.Markers.JpegMarker.Code">
            <summary>
            Gets marker code.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Core.Imaging.Jpeg.Markers.JpegMarkerType.APP0">
            <summary>
            JFIF application marker.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Core.Imaging.Jpeg.Markers.JpegMarkerType.APP14">
            <summary>
            Adobe application marker.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Core.Imaging.Jpeg.Markers.JpegMarkerType.COM">
            <summary>
            Comment.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Core.Imaging.Jpeg.Markers.JpegMarkerType.DHT">
            <summary>
            Define huffman table marker.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Core.Imaging.Jpeg.Markers.JpegMarkerType.DQT">
            <summary>
            Define quantization table marker.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Core.Imaging.Jpeg.Markers.JpegMarkerType.DRI">
            <summary>
            Define restart interval.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Core.Imaging.Jpeg.Markers.JpegMarkerType.EOI">
            <summary>
            End of image.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Core.Imaging.Jpeg.Markers.JpegMarkerType.RST">
            <summary>
            Restart scan.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Core.Imaging.Jpeg.Markers.JpegMarkerType.SOF">
            <summary>
            Start of frame.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Core.Imaging.Jpeg.Markers.JpegMarkerType.SOI">
            <summary>
            Start of image.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Core.Imaging.Jpeg.Markers.JpegMarkerType.SOS">
            <summary>
            Start of scan.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Core.Imaging.Jpeg.Markers.JpegMarkerType.NotSupported">
            <summary>
            Not supported.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Imaging.Jpeg.Markers.NotSupportedMarker.Length">
            <summary>
            Gets length of the marker segment.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Imaging.Jpeg.Markers.RestartMarker.Length">
            <summary>
            Gets length of the marker segment.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Imaging.Jpeg.Markers.StartOfFrameMarker.EncodingType">
            <summary>
            Gets encoding type.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Imaging.Jpeg.Markers.StartOfFrameMarker.Length">
            <summary>
            Gets length of the marker segment.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Imaging.Jpeg.Markers.StartOfImageMarker.Length">
            <summary>
            Gets length of the marker segment.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Imaging.Jpeg.Markers.StartOfScanMarker.Length">
            <summary>
            Gets length of the marker segment.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Core.Imaging.Jpeg.Tables.FloatBlock">
            <summary>
            Represents block of float values.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Imaging.Jpeg.Tables.FrameHeader.ImageComponents">
            <summary>
            Gets number of image components in frame.
            </summary>
            <remarks>CCITT T.81, Annex B, section B.2.2 Frame header syntax (see Nf parameter in the Table B.2).</remarks>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Imaging.Jpeg.Tables.FrameHeader.Height">
            <summary>
            Gets number of lines (height). 
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Imaging.Jpeg.Tables.FrameHeader.Length">
            <summary>
            Gets length of the frame header.
            </summary>
            <remarks>CCITT T.81, Annex B, section B.2.2 Frame header syntax (see Lf parameter in the Table B.2).</remarks>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Imaging.Jpeg.Tables.FrameHeader.SamplePrecision">
            <summary>
            Gets the precision in bits for the samples of the components in the frame.
            </summary>
            <remarks>CCITT T.81, Annex B, section B.2.2 Frame header syntax (see P parameter in the Table B.2).</remarks>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Imaging.Jpeg.Tables.FrameHeader.Width">
            <summary>
            Gets number of the samples per line (width).
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Imaging.Jpeg.Tables.FrameHeader.AddComponent(Telerik.Windows.Documents.Core.Imaging.Jpeg.Tables.JpegFrameComponent)">
            <summary>
            Add frame component (image color component).
            </summary>
            <param name="component">JPEG frame component to add.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Imaging.Jpeg.Tables.FrameHeader.Read(Telerik.Windows.Documents.Core.Imaging.Jpeg.Decoder.IJpegReader)">
            <summary>
            Reads JPEG frame header.
            </summary>
            <param name="reader">JPEG reader.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Imaging.Jpeg.Tables.FrameHeader.Write(Telerik.Windows.Documents.Core.Imaging.Jpeg.Encoder.JpegWriter)">
            <summary>
            Writes JPEG frame header.
            </summary>
            <param name="writer">JPEG writer.</param>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Imaging.Jpeg.Tables.HuffmanTable.Length">
            <summary>
            Gets a length of the Huffman table.
            </summary>
            <remarks>CCITT T.81, Annex B, section B.2.4.2 Huffman table-specification syntax (see Tc, Th, Li and Vi,j parameters in the Table B.5).</remarks>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Imaging.Jpeg.Tables.HuffmanTable.Read(Telerik.Windows.Documents.Core.Imaging.Jpeg.Decoder.IJpegReader)">
            <summary>
            Reads Huffman table.
            </summary>
            <param name="reader">JPEG reader.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Imaging.Jpeg.Tables.HuffmanTable.Write(Telerik.Windows.Documents.Core.Imaging.Jpeg.Encoder.JpegWriter)">
            <summary>
            Writes Huffman table.
            </summary>
            <param name="writer">JPEG writer.</param>
        </member>
        <member name="T:Telerik.Windows.Documents.Core.Imaging.Jpeg.Tables.JpegTable">
            <summary>
            Base class for the JPEG information structures.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Imaging.Jpeg.Tables.JpegTable.Length">
            <summary>
            Gets length of the table.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Imaging.Jpeg.Tables.JpegTable.Read(Telerik.Windows.Documents.Core.Imaging.Jpeg.Decoder.IJpegReader)">
            <summary>
            Reads JPEG information structure.
            </summary>
            <param name="reader">JPEG reader.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Imaging.Jpeg.Tables.JpegTable.Write(Telerik.Windows.Documents.Core.Imaging.Jpeg.Encoder.JpegWriter)">
            <summary>
            Writes JPEG information structure.
            </summary>
            <param name="writer">JPEG writer.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Imaging.Jpeg.Tables.QuantizationTable.#ctor">
            <summary>
            Initializes a new instance of the QuantizationTable class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Imaging.Jpeg.Tables.QuantizationTable.#ctor(System.Byte,System.Byte[])">
            <summary>
            Initializes a new instance of the QuantizationTable class.
            </summary>
            <param name="tableIndex">Table index</param>
            <param name="byteData">Table data.</param>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Imaging.Jpeg.Tables.QuantizationTable.Length">
            <summary>
            Gets a length of all quantization table parameters.
            </summary>
            <remarks>CCITT T.81, Annex B, section B.2.4.1 Quantization table-specification syntax (see Pq, Tq and Qr parameters in the Table B.4).</remarks>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Imaging.Jpeg.Tables.QuantizationTable.Precision">
            <summary>
            Gets Quantization table element precision.
            </summary>
            <remarks>CCITT T.81, Annex B, section B.2.4.1 Quantization table-specification syntax (see Pq parameter in the Table B.4).
            Specifies the precision of the Qk values. Value 0 indicates 8-bit Qk values; value 1 indicates 16-bit Qk values. 
            Pq shall be zero for 8 bit sample precision P (see B.2.2).
            </remarks>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Imaging.Jpeg.Tables.QuantizationTable.Read(Telerik.Windows.Documents.Core.Imaging.Jpeg.Decoder.IJpegReader)">
            <summary>
            Reads all quantization table parameters.
            </summary>
            <param name="reader">JPEG reader.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Imaging.Jpeg.Tables.QuantizationTable.Write(Telerik.Windows.Documents.Core.Imaging.Jpeg.Encoder.JpegWriter)">
            <summary>
            Writes all quantization table parameters.
            </summary>
            <param name="writer"></param>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Imaging.Jpeg.Tables.ScanHeader.BitPositionHigh">
            <summary>
            Gets Successive approximation bit position high.
            </summary>
            <remarks>CCITT T.81, Annex B, section B.2.3 Scan header syntax (see Ah parameter in the Table B.3).
            This parameter specifies the point transform used in the preceding scan (i.e. successive approximation bit position low 
            in the preceding scan) for the band of coefficients specified by Ss and Se. This parameter shall be set to zero for the 
            first scan of each band of coefficients. In the lossless mode of operations this parameter has no meaning. It shall be set to zero.
            </remarks>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Imaging.Jpeg.Tables.ScanHeader.BitPositionLow">
            <summary>
            Gets Successive approximation bit position low or point transform.
            </summary>
            <remarks>CCITT T.81, Annex B, section B.2.3 Scan header syntax (see Al parameter in the Table B.3).
            In the DCT modes of operation this parameter specifies the point transform, i.e. bit position low, used before coding the band 
            of coefficients specified by Ss and Se. This parameter shall be set to zero for the sequential DCT processes. In the lossless 
            mode of operations, this parameter specifies the point transform, Pt.
            </remarks>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Imaging.Jpeg.Tables.ScanHeader.Length">
            <summary>
            Gets a scan header length.
            </summary>
            <remarks>CCITT T.81, Annex B, section B.2.3 Scan header syntax (see Ls parameter in the Table B.3).</remarks>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Imaging.Jpeg.Tables.ScanHeader.SpectralSelectionEnd">
            <summary>
            Gets End of spectral selection.
            </summary>
            <remarks>CCITT T.81, Annex B, section B.2.3 Scan header syntax (see Se parameter in the Table B.3).
            Specifies the last DCT coefficient in each block in zig-zag order which shall be coded in the scan. 
            This parameter shall be set to 63 for the sequential DCT processes. In the lossless mode of operations 
            this parameter has no meaning. It shall be set to zero.
            </remarks>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Imaging.Jpeg.Tables.ScanHeader.SpectralSelectionStart">
            <summary>
            Gets Start of spectral or predictor selection.
            </summary>
            <remarks>CCITT T.81, Annex B, section B.2.3 Scan header syntax (see Ss parameter in the Table B.3).
            In the DCT modes of operation, this parameter specifies the first DCT coefficient in each block in zig-zag order 
            which shall be coded in the scan. This parameter shall be set to zero for the sequential DCT processes. 
            In the lossless mode of operations this parameter is used to select the predictor.
            </remarks>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Imaging.Jpeg.Tables.ScanHeader.AddComponent(System.Int32,Telerik.Windows.Documents.Core.Imaging.Jpeg.Tables.JpegScanComponent)">
            <summary>
            Add frame component (image color component).
            </summary>
            <param name="index">Index of the component.</param>
            <param name="component">JPEG frame component to add.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Imaging.Jpeg.Tables.ScanHeader.Read(Telerik.Windows.Documents.Core.Imaging.Jpeg.Decoder.IJpegReader)">
            <summary>
            Reads JPEG scan header.
            </summary>
            <param name="reader">JPEG reader.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Imaging.Jpeg.Tables.ScanHeader.Write(Telerik.Windows.Documents.Core.Imaging.Jpeg.Encoder.JpegWriter)">
            <summary>
            Writes JPEG scan header.
            </summary>
            <param name="writer">JPEG writer.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Imaging.Jpeg.DiscreteCosineTransform.ForwardDCT(Telerik.Windows.Documents.Core.Imaging.Jpeg.Tables.Block)">
            <summary>
            Implements Fast FDCT.
            </summary>
            <param name="input">Input block.</param>
            <returns>Output block.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Imaging.ImageSizeDecoderBase.ReadLittleEndianInt32(System.Byte[],System.Int32)">
            <summary>
            Reads the little endian 4 bytes from the given start index.
            </summary>
            <param name="imageBytes">The image bytes.</param>
            <param name="startIndex">The start index.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Imaging.ImageSizeDecoderBase.ReadInt16(System.Byte[],System.Int32)">
            <summary>
            Reads 2 bytes from the given start index.
            </summary>
            <param name="imageBytes">The image bytes.</param>
            <param name="startIndex">The start index.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Imaging.ImageSizeDecoderBase.ReadInt32(System.Byte[],System.Int32)">
            <summary>
            Reads 4 bytes from the given start index.
            </summary>
            <param name="imageBytes">The image bytes.</param>
            <param name="startIndex">The start index.</param>
            <returns></returns>
        </member>
        <member name="T:Telerik.Windows.Documents.Core.Imaging.Svg.Text.SvgTextPath">
            <summary>
            The <see cref="T:Telerik.Imaging.Svg.SvgText"/> element defines a graphics element consisting of text.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Core.Input.PointerHandlers.PointerHandlersControllerBase">
            <summary>
            Represents pointer handlers controller base class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Input.PointerHandlers.PointerHandlersControllerBase.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Core.Input.PointerHandlers.PointerHandlersControllerBase" /> class.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Core.Fonts.FontProperties">
            <summary>
            Represents fonts properties class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Fonts.FontProperties.#ctor(System.Windows.Media.FontFamily,System.Windows.FontStyle,System.Windows.FontWeight)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Core.Fonts.FontProperties" /> class.
            </summary>
            <param name="fontFamily">The font family.</param>
            <param name="fontStyle">The font style.</param>
            <param name="fontWeight">The font weight.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Fonts.FontProperties.#ctor(System.Windows.Media.FontFamily)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Core.Fonts.FontProperties" /> class.
            </summary>
            <param name="fontFamily">The font family.</param>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Fonts.FontProperties.FontFamilyName">
            <summary>
            Gets font family name.
            </summary>
            <value>The name of the font family.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Fonts.FontProperties.FontWeight">
            <summary>
            Gets the font weight.
            </summary>
            <value>The font weight.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Fonts.FontProperties.FontStyle">
            <summary>
            Gets the font style.
            </summary>
            <value>The font style.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Fonts.FontProperties.FontFamily">
            <summary>
            Gets the font family.
            </summary>
            <value>The font family.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.Fonts.FontProperties.IsMonoSpaced">
            <summary>
            Gets if font is mono spaced.
            </summary>
            <value>The is mono spaced.</value>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Fonts.FontProperties.Equals(System.Object)">
            <summary>
            Determines whether the specified <see cref="T:System.Object" /> is equal
            to the current <see cref="T:System.Object" />.
            </summary>
            <param name="obj">The <see cref="T:System.Object" /> to compare with the current
            <see cref="T:System.Object" />.</param>
            <returns>
            true if the specified <see cref="T:System.Object" /> is equal to the
            current <see cref="T:System.Object" />; otherwise, false.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Fonts.FontProperties.GetHashCode">
            <summary>
            Serves as a hash function for a particular type.
            </summary>
            <returns>A hash code for the current <see cref="T:System.Object" />.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.Fonts.SystemFontsManager.WarmUp">
            <summary>
            This method will trigger the initial load of system fonts.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Core.TextMeasurer.SubStringPosition">
            <summary>
            Represents sub string position enum. This position is used when RTL text is measured.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Core.TextMeasurer.SubStringPosition.None">
            <summary>
            Represents the default substring position.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Core.TextMeasurer.SubStringPosition.Start">
            <summary>
            Represents position in start of string.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Core.TextMeasurer.SubStringPosition.Middle">
            <summary>
            Represents position in middle of string.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Core.TextMeasurer.SubStringPosition.End">
            <summary>
            Represents position in end of string.
            </summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Core.TextMeasurer.ITextMeasurer">
            <summary>
            Represents base text measurer interface.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.TextMeasurer.ITextMeasurer.MeasureText(Telerik.Windows.Documents.Core.TextMeasurer.TextProperties,Telerik.Windows.Documents.Core.Fonts.FontProperties)">
            <summary>
            Measures the text.
            </summary>
            <param name="textProperties">The <see cref="T:Telerik.Windows.Documents.Core.TextMeasurer.TextProperties" /> class which defines the text, its size and the flow direction.</param>
            <param name="fontProperties">The <see cref="T:Telerik.Windows.Documents.Core.Fonts.FontProperties" /> class which defines the font family, font style and font weight.</param>
            <returns>The <see cref="T:TextMeasurementInfo" /> with the properties of the measured text.</returns>
        </member>
        <member name="T:Telerik.Windows.Documents.Core.TextMeasurer.RadTextMeasurer">
            <summary>
            Represents text measurer that can be used in multi-threaded applications.
            </summary>
            <summary>
            Represents text measurer that can be used in multi-threaded applications.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.TextMeasurer.RadTextMeasurer.MeasureText(Telerik.Windows.Documents.Core.TextMeasurer.TextProperties,Telerik.Windows.Documents.Core.Fonts.FontProperties)">
            <summary>
            Measures the text.
            </summary>
            <param name="textProperties"></param>
            <param name="fontProperties">The font properties.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.TextMeasurer.RadTextMeasurer.MeasureTextWithWrapping(Telerik.Windows.Documents.Core.TextMeasurer.TextProperties,Telerik.Windows.Documents.Core.Fonts.FontProperties,System.Double)">
            <summary>
            Measures the text with wrapping.
            </summary>
            <param name="textProperties">The text properties.</param>
            <param name="fontProperties">The font properties.</param>
            <param name="wrappingWidth">Width of the wrapping.</param>
            <returns></returns>
        </member>
        <member name="T:Telerik.Windows.Documents.Core.TextMeasurer.TextProperties">
            <summary>
            Represents text properties.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Core.TextMeasurer.TextProperties.#ctor(System.String,System.Double,Telerik.Windows.Documents.Core.TextMeasurer.SubStringPosition)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Core.TextMeasurer.TextProperties" /> class.
            </summary>
            <param name="text">The text.</param>
            <param name="size">The size.</param>
            <param name="subStringPosition">The sub string position.</param>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.TextMeasurer.TextProperties.SubStringPosition">
            <summary>
            Gets the sub string position.
            </summary>
            <value>The sub string position.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.TextMeasurer.TextProperties.Size">
            <summary>
            Gets the size.
            </summary>
            <value>The size.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.TextMeasurer.TextProperties.Text">
            <summary>
            Gets the text.
            </summary>
            <value>The text.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.TextMeasurer.TextProperties.FlowDirection">
            <summary>
            Gets the flow direction of the text.
            </summary>
            <value>The flow direction.</value>
        </member>
        <member name="T:Telerik.Windows.Documents.Core.TextMeasurer.TextMeasurementInfo">
            <summary>
            Represents text measurement info class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.TextMeasurer.TextMeasurementInfo.Empty">
            <summary>
            Gets empty text measurement info.
            </summary>
            <value>Empty text measurement info.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.TextMeasurer.TextMeasurementInfo.Size">
            <summary>
            Gets or sets the size.
            </summary>
            <value>The size.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Core.TextMeasurer.TextMeasurementInfo.BaselineOffset">
            <summary>
            Gets or sets the baseline offset.
            </summary>
            <value>The baseline offset.</value>
        </member>
        <member name="T:Telerik.Windows.Documents.Exceptions.NotSupportedCCITTFaxDecodeFilterException">
            <summary>
            Represents an exception for a scan decoder which is not supported.
            </summary> 
        </member>
        <member name="M:Telerik.Windows.Documents.Exceptions.NotSupportedCCITTFaxDecodeFilterException.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Exceptions.NotSupportedCCITTFaxDecodeFilterException" /> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Exceptions.NotSupportedCCITTFaxDecodeFilterException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Exceptions.NotSupportedCCITTFaxDecodeFilterException" /> class.
            </summary>
            <param name="message">The message.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Exceptions.NotSupportedCCITTFaxDecodeFilterException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Exceptions.NotSupportedCCITTFaxDecodeFilterException" /> class.
            </summary>
            <param name="message">The message.</param>
            <param name="cause">The cause.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Exceptions.NotSupportedCCITTFaxDecodeFilterException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Exceptions.NotSupportedCCITTFaxDecodeFilterException" /> class.
            </summary>
            <param name="info">The info.</param>
            <param name="context">The context.</param>
        </member>
        <member name="T:Telerik.Windows.Documents.Exceptions.NotSupportedFeatureException">
            <summary>
            Represents an exception for a feature which is not supported.
            </summary> 
        </member>
        <member name="M:Telerik.Windows.Documents.Exceptions.NotSupportedFeatureException.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Exceptions.NotSupportedFeatureException" /> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Exceptions.NotSupportedFeatureException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Exceptions.NotSupportedFeatureException" /> class.
            </summary>
            <param name="message">The message.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Exceptions.NotSupportedFeatureException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Exceptions.NotSupportedFeatureException" /> class.
            </summary>
            <param name="message">The message.</param>
            <param name="cause">The cause.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Exceptions.NotSupportedFeatureException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Exceptions.NotSupportedFeatureException" /> class.
            </summary>
            <param name="info">The info.</param>
            <param name="context">The context.</param>
        </member>
        <member name="T:Telerik.Windows.Documents.Exceptions.NotSupportedScanDecoderException">
            <summary>
            Represents an exception for a document with a scan decoder which is not supported.
            </summary> 
        </member>
        <member name="M:Telerik.Windows.Documents.Exceptions.NotSupportedScanDecoderException.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Exceptions.NotSupportedScanDecoderException" /> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Exceptions.NotSupportedScanDecoderException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Exceptions.NotSupportedScanDecoderException" /> class.
            </summary>
            <param name="message">The message.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Exceptions.NotSupportedScanDecoderException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Exceptions.NotSupportedScanDecoderException" /> class.
            </summary>
            <param name="message">The message.</param>
            <param name="cause">The cause.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Exceptions.NotSupportedScanDecoderException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Exceptions.NotSupportedScanDecoderException" /> class.
            </summary>
            <param name="info">The info.</param>
            <param name="context">The context.</param>
        </member>
        <member name="T:Telerik.Windows.Documents.Exceptions.NotSupportedScanEncoderException">
            <summary>
            Represents an exception for a scan decoder which is not supported.
            </summary> 
        </member>
        <member name="M:Telerik.Windows.Documents.Exceptions.NotSupportedScanEncoderException.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Exceptions.NotSupportedScanEncoderException" /> class.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Exceptions.NotSupportedScanEncoderException.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Exceptions.NotSupportedScanEncoderException" /> class.
            </summary>
            <param name="message">The message.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Exceptions.NotSupportedScanEncoderException.#ctor(System.String,System.Exception)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Exceptions.NotSupportedScanEncoderException" /> class.
            </summary>
            <param name="message">The message.</param>
            <param name="cause">The cause.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Exceptions.NotSupportedScanEncoderException.#ctor(System.Runtime.Serialization.SerializationInfo,System.Runtime.Serialization.StreamingContext)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Exceptions.NotSupportedScanEncoderException" /> class.
            </summary>
            <param name="info">The info.</param>
            <param name="context">The context.</param>
        </member>
        <member name="T:Telerik.Windows.Documents.Media.ImageSource">
            <summary>
            Encapsulates data needed for creation of an image
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Media.ImageSource.#ctor(System.IO.Stream,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Media.ImageSource" /> class.
            </summary>
            <param name="stream">The stream which represents the image.</param>
            <param name="extension">The format of the image.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Media.ImageSource.#ctor(System.Byte[],System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Media.ImageSource" /> class.
            </summary>
            <param name="data">Byte array containing representation of the image source.</param> 
            <param name="extension">The image file extension.</param>
        </member>
        <member name="P:Telerik.Windows.Documents.Media.ImageSource.Extension">
            <summary>
            Gets the image file extension.
            </summary>
            <value>The image file extension.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Media.ImageSource.Data">
            <summary>
            Gets the byte array representation of the image source.
            </summary>
            <value>The byte array representation of the image source.</value>
        </member>
        <member name="T:Telerik.Windows.Documents.Media.Unit">
            <summary>
            Contains methods for converting DIP (Device Independent Pixels) to other unit types.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Media.Unit.WavelengthToDegree(System.Int64)">
            <summary>
            Converts wave length to degree.
            </summary>
            <param name="wavelength">wave length.</param>
            <returns>Points.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Media.Unit.WavelengthToDegree(System.Int32)">
            <summary>
            Converts wave length to degree.
            </summary>
            <param name="wavelength">wave length.</param>
            <returns>Points.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Media.Unit.DegreeToWavelength(System.Double)">
            <summary>
            Converts degree to wave length.
            </summary>
            <param name="degrees">Angle in degrees.</param>
            <returns>Angle in 60000ths of degree.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Media.Unit.DipToPoint(System.Double)">
            <summary>
            Converts dips to points.
            </summary>
            <param name="value">Pixels.</param>
            <returns>Points.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Media.Unit.DipToPointI(System.Double)">
            <summary>
            Converts dips to points.
            </summary>
            <param name="pixels">Pixels.</param>
            <returns>Points.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Media.Unit.DipToPica(System.Double)">
            <summary>
            Converts dips to picas.
            </summary>
            <param name="value">Pixels.</param>
            <returns>Picas.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Media.Unit.DipToCm(System.Double)">
            <summary>
            Converts dips to centimeters.
            </summary>
            <param name="value">Pixels.</param>
            <returns>Centimeters.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Media.Unit.DipToMm(System.Double)">
            <summary>
            Converts dips to millimeters.
            </summary>
            <param name="value">Pixels.</param>
            <returns>Millimeters.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Media.Unit.DipToInch(System.Double)">
            <summary>
            Converts dips to inches.
            </summary>
            <param name="value">Pixels.</param>
            <returns>Inches.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Media.Unit.DipToTwip(System.Double)">
            <summary>
            Converts dips to twips.
            </summary>
            <param name="value">Pixels.</param>
            <returns>Twips.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Media.Unit.DipToEmu(System.Double)">
            <summary>
            Converts dips to EMUs.
            </summary>
            <param name="value">Pixels.</param>
            <returns>EMUs.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Media.Unit.DipToEmuI(System.Double)">
            <summary>
            Converts dips to EMUs.
            </summary>
            <param name="value">Pixels.</param>
            <returns>EMUs.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Media.Unit.DipToTwipI(System.Double)">
            <summary>
            Converts dips to twips.
            </summary>
            <param name="value">Pixels.</param>
            <returns>Twips.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Media.Unit.DipToTwipF(System.Double)">
            <summary>
            Converts dips to twips.
            </summary>
            <param name="value">Pixels.</param>
            <returns>Twips.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Media.Unit.TwipToDipF(System.Double)">
            <summary>
            Converts twips to dips.
            </summary>
            <param name="value">Twips.</param>
            <returns>Dips.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Media.Unit.TwipToDipI(System.Double)">
            <summary>
            Converts twips to dips.
            </summary>
            <param name="value">Twips.</param>
            <returns>Dips.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Media.Unit.EmuToInch(System.Int64)">
            <summary>
            Converts EMUs to inches.
            </summary>
            <param name="emu">EMUs.</param>
            <returns>Inches.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Media.Unit.InchToEmu(System.Double)">
            <summary>
            Converts inches to EMUs.
            </summary>
            <param name="inch">Inches.</param>
            <returns>EMUs.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Media.Unit.DipToUnit(System.Double,Telerik.Windows.Documents.Media.UnitType)">
            <summary>
            Converts DIPs to units.
            </summary>
            <param name="value">Pixels.</param>
            <param name="type">UnitType.</param>
            <returns>Units.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Media.Unit.PointToDip(System.Double)">
            <summary>
            Converts to points dips.
            </summary>
            <param name="value">Points.</param>
            <returns>Pixels.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Media.Unit.PicaToDip(System.Double)">
            <summary>
            Converts to points dips.
            </summary>
            <param name="value">Picas.</param>
            <returns>Pixels.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Media.Unit.EmuToDip(System.Double)">
            <summary>
            Converts EMUs to dips.
            </summary>
            <param name="value">EMUs.</param>
            <returns>Pixels.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Media.Unit.CmToDip(System.Double)">
            <summary>
            Converts centimeters to dips.
            </summary>
            <param name="value">Centimeters.</param>
            <returns>Pixels.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Media.Unit.MmToDip(System.Double)">
            <summary>
            Converts millimeters to dips.
            </summary>
            <param name="value">Millimeters.</param>
            <returns>Pixels.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Media.Unit.InchToDip(System.Double)">
            <summary>
            Converts inches to dips.
            </summary>
            <param name="value">Inches.</param>
            <returns>Pixels.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Media.Unit.TwipToDip(System.Double)">
            <summary>
            Converts twips to dips.
            </summary>
            <param name="value">Twips.</param>
            <returns>Pixels.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Media.Unit.UnitToDip(System.Double,Telerik.Windows.Documents.Media.UnitType)">
            <summary>
            Converts Units to dips.
            </summary>
            <param name="value">Units.</param>
            <param name="type">UnitType.</param>
            <returns>Pixels.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Media.Unit.PixelToEm(System.Double,System.Double)">
            <summary>
            Converts pixels to units of measurement.
            </summary>
            <param name="basePixelSize">Base pixel size.</param>
            <param name="value">Pixels.</param>
            <returns>Ems.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Media.Unit.EmToPixel(System.Double,System.Double)">
            <summary>
            Converts units of measurement to pixels.
            </summary>
            <param name="basePixelSize">Base pixel size.</param>
            <param name="value">Ems.</param>
            <returns>Pixels.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Media.Unit.PixelToPercent(System.Double,System.Double)">
            <summary>
            Converts pixels to percents.
            </summary>
            <param name="basePixelSize">Base pixel size.</param>
            <param name="value">Pixels.</param>
            <returns>Percents.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Media.Unit.PercentToPixel(System.Double,System.Double)">
            <summary>
            Converts percents to pixels.
            </summary>
            <param name="basePixelSize">Base pixel size.</param>
            <param name="value">Percents.</param>
            <returns>Pixels.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Media.Unit.UnitToPixel(System.Double,System.Double,Telerik.Windows.Documents.Media.UnitType)">
            <summary>
             Converts Units to pixel.
            </summary>
            <param name="basePixelSize">Base pixel size.</param>
            <param name="value">The value.</param>
            <param name="type">The type.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Media.Unit.PixelToUnit(System.Double,System.Double,Telerik.Windows.Documents.Media.UnitType)">
            <summary>
            Converts Pixels the unit.
            </summary>
            <param name="basePixelSize">Base pixel size.</param>
            <param name="value">Pixel.</param>
            <param name="type">The type.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Media.Unit.IsRelativeUnitType(Telerik.Windows.Documents.Media.UnitType)">
            <summary>
            Determines whether unit type is relative.
            </summary>
            <param name="type">The unit type.</param>
            <returns></returns>
        </member>
        <member name="T:Telerik.Windows.Documents.Media.UnitType">
            <summary>
            Defines different unit types.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Media.UnitType.Dip">
            <summary> Device independent pixel.</summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Media.UnitType.Point">
            <summary> Point.</summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Media.UnitType.Pica">
            <summary> Pica.</summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Media.UnitType.Inch">
            <summary> Inch.</summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Media.UnitType.Mm">
            <summary> Millimeter.</summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Media.UnitType.Cm">
            <summary> Centimeter.</summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Media.UnitType.Twip">
            <summary> Twip - twentieth of a point.</summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Media.UnitType.Emu">
            <summary> EMU - English Metric Unit.</summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Media.UnitType.Percent">
            <summary>Percentage.</summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Media.UnitType.Em">
            <summary>Em.</summary>
        </member>
        <member name="T:Telerik.Windows.Documents.Primitives.Padding">
            <summary>
            Represents padding or margin information.
            </summary>
        </member>
        <member name="F:Telerik.Windows.Documents.Primitives.Padding.Empty">
            <summary>
            An empty padding.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Primitives.Padding.#ctor(System.Double)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Primitives.Padding" /> class and sets all paddings to a given value.
            </summary>
            <param name="all">The value in device independent pixels (1/96 inch).</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Primitives.Padding.#ctor(System.Double,System.Double,System.Double,System.Double)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Windows.Documents.Primitives.Padding" /> class.
            </summary>
            <param name="left">The left padding in device independent pixels (1/96 inch).</param>
            <param name="top">The top padding in device independent pixels (1/96 inch).</param>
            <param name="right">The right padding in device independent pixels (1/96 inch).</param>
            <param name="bottom">The bottom padding in device independent pixels (1/96 inch).</param>
        </member>
        <member name="P:Telerik.Windows.Documents.Primitives.Padding.Top">
            <summary>
            Gets the top padding.
            The value is in device independent pixels (1/96 inch).
            </summary>
            <value>The top padding.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Primitives.Padding.Bottom">
            <summary>
            Gets the bottom padding.
            The value is in device independent pixels (1/96 inch).
            </summary>
            <value>The bottom padding.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Primitives.Padding.Left">
            <summary>
            Gets the left padding.
            The value is in device independent pixels (1/96 inch).
            </summary>
            <value>The left padding.</value>
        </member>
        <member name="P:Telerik.Windows.Documents.Primitives.Padding.Right">
            <summary>
            Gets the right padding.
            The value is in device independent pixels (1/96 inch).
            </summary>
            <value>The right padding.</value>
        </member>
        <member name="M:Telerik.Windows.Documents.Primitives.Padding.Equals(System.Object)">
            <summary>
            Determines whether the specified <see cref="T:System.Object" /> is equal
            to the current <see cref="T:System.Object" />.
            </summary>
            <param name="obj">The <see cref="T:System.Object" /> to compare with the current
            <see cref="T:System.Object" />.</param>
            <returns>
            Returns true if the specified <see cref="T:System.Object" /> is equal to the
            current <see cref="T:System.Object" />; otherwise, false.
            </returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Primitives.Padding.GetHashCode">
            <summary>
            Serves as a hash function for a particular type.
            </summary>
            <returns>A hash code for the current <see cref="T:System.Object" />.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Primitives.Padding.op_Equality(Telerik.Windows.Documents.Primitives.Padding,Telerik.Windows.Documents.Primitives.Padding)">
            <summary>
            Determines whether the specified paddings are equal.
            </summary>
            <returns>True if the paddings are equal.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Primitives.Padding.op_Inequality(Telerik.Windows.Documents.Primitives.Padding,Telerik.Windows.Documents.Primitives.Padding)">
            <summary>
            Determines whether the specified paddings are different.
            </summary>
            <returns>True if the paddings are different.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Primitives.Padding.ToString">
            <inheritdoc />
        </member>
        <member name="M:Telerik.Windows.Documents.Utilities.AlgebraExtensions.Minus(System.Windows.Point,System.Windows.Point)">
            <summary>
            Subtracts the specified p1.
            </summary>
            <param name="p1">The p1.</param>
            <param name="p2">The p2.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Utilities.AlgebraExtensions.Multiply(System.Windows.Media.Matrix,System.Windows.Media.Matrix)">
            <summary>
            Multiplies the given matrices.
            </summary>
            <param name="m1">A matrix.</param>
            <param name="m2">Another matrix.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Utilities.AlgebraExtensions.Shear(System.Windows.Media.Matrix,System.Single,System.Single)">
            <summary>
            Shears the matrix with the specified scalars.
            </summary>
            <param name="m">The matrix.</param>
            <param name="shearX">The shearX.</param>
            <param name="shearY">The shearY.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Utilities.MathUtilities.FindLocalMinimumAndMaximum(Telerik.Windows.Documents.Utilities.Interval,System.Double,System.Double,System.Double,System.Double)">
            <summary>
            Finds the minimum and maximum of a quadratic function a*x*x + b*x + c.
            </summary>
            <param name="interval">The interval.</param>
            <param name="a">The a coefficient.</param>
            <param name="b">The b coefficient.</param>
            <param name="c">The c coefficient.</param>
            <param name="d">The free coefficient.</param>
            <returns>The bounding interval.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Utilities.MathUtilities.FindLocalMinimumAndMaximum(Telerik.Windows.Documents.Utilities.Interval,System.Double,System.Double,System.Double)">
            <summary>
            Finds the minimum and maximum of a quadratic function a*x*x + b*x + c.
            </summary>
            <param name="interval">The interval.</param>
            <param name="a">The a coefficient.</param>
            <param name="b">The b coefficient.</param>
            <param name="c">The free coefficient.</param>
            <returns>The bounding interval.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Utilities.MathUtilities.FindLocalExtrema(Telerik.Windows.Documents.Utilities.Interval,System.Double,System.Double,System.Double)">
            <summary>
            Finds the local extrema of a quadratic function a*x*x + b*x + c.
            </summary>
            <param name="interval">The interval.</param>
            <param name="a">The a coefficient.</param>
            <param name="b">The b coefficient.</param>
            <param name="c">The free coefficient.</param>
            <returns>The local extrema.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Utilities.MathUtilities.FindLocalExtrema(Telerik.Windows.Documents.Utilities.Interval,System.Double,System.Double,System.Double,System.Double)">
            <summary>
            Finds the local extrema of a cubic function a*x*x*x + b*x*x + c*x + d.
            </summary>
            <param name="interval">The interval.</param>
            <param name="a">The a coefficient.</param>
            <param name="b">The b coefficient.</param>
            <param name="c">The c coefficient.</param>
            <param name="d">The free coefficient.</param>
            <returns>The local extrema.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Utilities.MathUtilities.TrySolveQuadratic(System.Double,System.Double,System.Double,System.Double[]@)">
            <summary>
            Solves the quadratic equation a*x*x + b*x + c = 0.
            </summary>
            <param name="a">The coefficient before x*x.</param>
            <param name="b">The coefficient before x.</param>
            <param name="c">The free coefficient.</param>
            <param name="x">The x.</param>
            <returns>True if finite number of finite real solutions exist. Else returns false.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Utilities.MathUtilities.SolveQuadratic(System.Double,System.Double,System.Double)">
            <summary>
            Solves the quadratic equation a*x*x + b*x + c = 0.
            </summary>
            <param name="a">The coefficient before x*x.</param>
            <param name="b">The coefficient before x.</param>
            <param name="c">The free coefficient.</param>
            <returns>Returns the real solutions of the equation. Returns PositiveInfinity if every real number is solution.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Utilities.MathUtilities.TrySolveLinear(System.Double,System.Double,System.Double@)">
            <summary>
            Solves the equation a*x + b = 0
            </summary>
            <param name="a">The coefficient before x.</param>
            <param name="b">The free coefficient.</param>
            <param name="x">The x.</param>
            <returns>True if finite number of finite real solution exists. Else returns false.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Utilities.MathUtilities.SolveLinear(System.Double,System.Double)">
            <summary>
            Solves the equation a*x + b = 0
            </summary>
            <param name="a">The coefficient before x.</param>
            <param name="b">The free coefficient.</param>
            <returns>The appropriate value of x. Returns NaN if no solution is available. Returns PositiveInfinity when every x is solution.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Utilities.Extensions.IsNullOrEmpty(System.Array)">
            <summary>Indicates whether the specified array is null or has a length of zero.</summary>
            <param name="array">The array to test.</param>
            <returns>true if the array parameter is null or has a length of zero; otherwise, false.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Utilities.PointExtensions.Add(System.Windows.Point,System.Windows.Vector)">
            <summary>
            Adds the specified point and vector together.
            </summary>
            <seealso cref="T:System.Windows.Vector">The Vector struct and its operations.</seealso>
            <param name="point">A point.</param>
            <param name="vector">A vector.</param>
            <returns>The augmented point.</returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Utilities.PointExtensions.Add(System.Windows.Point,System.Windows.Point)">
            <summary>
            Adds the specified points together.
            </summary>
            <param name="point">A point.</param>
            <param name="p2">The p2.</param>
            <returns>
            The augmented point.
            </returns>
            <seealso cref="T:System.Windows.Vector">The Vector struct and its operations.</seealso>
        </member>
        <member name="M:Telerik.Windows.Documents.Utilities.WriterBase.#ctor">
            <summary>
            Initializes a new instance of the WriterBase class.
            </summary>
        </member>
        <member name="P:Telerik.Windows.Documents.Utilities.WriterBase.Data">
            <summary>
            Gets writer data.
            </summary>
        </member>
        <member name="M:Telerik.Windows.Documents.Utilities.WriterBase.Write(System.Byte)">
            <summary>
            Writes single byte.
            </summary>
            <param name="byteToWrite"></param>
        </member>
        <member name="M:Telerik.Windows.Documents.Utilities.WriterBase.Write(System.Byte[],System.Int32)">
            <summary>
            Writes a block of bytes.
            </summary>
            <param name="buffer">Buffer.</param>
            <param name="count">Bytes count.</param>
        </member>
        <member name="M:Telerik.Windows.Documents.Utilities.WriterBase.WriteBE(System.Byte[],System.Int32)">
            <summary>
            Writes buffer in reverse direction.
            </summary>
            <param name="buffer">Buffer.</param>
            <param name="count">Bytes count.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Windows.Documents.Utilities.WriterBase.Seek(System.Int64,System.IO.SeekOrigin)">
            <summary>
            Sets the position.
            </summary>
            <param name="offset"></param>
            <param name="origin"></param>
        </member>
        <member name="T:Telerik.Documents.Core.FlowDirection">
            <summary>
            Defines constants that specify the content flow direction for text and user interface (UI) elements.
            </summary>
        </member>
        <member name="F:Telerik.Documents.Core.FlowDirection.LeftToRight">
            <summary>
            Indicates that content should flow from left to right.
            </summary>
        </member>
        <member name="F:Telerik.Documents.Core.FlowDirection.RightToLeft">
            <summary>
            Indicates that content should flow from right to left.
            </summary>
        </member>
        <member name="T:Telerik.Documents.Core.Fonts.FontFamily">
            <summary>
            Represents a family of fonts.
            </summary>
        </member>
        <member name="M:Telerik.Documents.Core.Fonts.FontFamily.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Documents.Core.Fonts.FontFamily"/> class.
            </summary>
            <param name="familyName"></param>
        </member>
        <member name="P:Telerik.Documents.Core.Fonts.FontFamily.Source">
            <summary>
            Gets the family name.
            </summary>
        </member>
        <member name="M:Telerik.Documents.Core.Fonts.FontFamily.Equals(System.Object)">
            <summary>
            Determines whether the specified object is equal to the current object.
            </summary>
            <param name="obj">The object to compare with the current object.</param>
            <returns>true if the specified object is equal to the current object; otherwise, false.</returns>
        </member>
        <member name="M:Telerik.Documents.Core.Fonts.FontFamily.GetHashCode">
            <summary>
            Serves as the default hash function.
            </summary>
            <returns>A hash code for the current object.</returns>
        </member>
        <member name="M:Telerik.Documents.Core.Fonts.FontFamily.ToString">
            <summary>
            Return familyName if there is one or String.Empty for unnamed
            font family.
            </summary>
        </member>
        <member name="T:Telerik.Documents.Core.Fonts.FontStyle">
            <summary>
            Structure that describes the style of a font, such as Normal, Italic or Oblique.
            </summary>
        </member>
        <member name="M:Telerik.Documents.Core.Fonts.FontStyle.#ctor(System.Int32)">
            <summary>
            Initializes a new instance of <see cref="T:Telerik.Documents.Core.Fonts.FontStyle"/> struct.
            </summary>
            <param name="styleId"></param>
        </member>
        <member name="M:Telerik.Documents.Core.Fonts.FontStyle.GetHashCode">
            <inheritdoc/>
        </member>
        <member name="M:Telerik.Documents.Core.Fonts.FontStyle.Equals(System.Object)">
            <inheritdoc/>
        </member>
        <member name="M:Telerik.Documents.Core.Fonts.FontStyle.op_Equality(Telerik.Documents.Core.Fonts.FontStyle,Telerik.Documents.Core.Fonts.FontStyle)">
            <summary>
            Checks whether two font style objects are equal.
            </summary>
            <param name="fs1">First object to compare.</param>
            <param name="fs2">Second object to compare.</param>
            <returns>Returns true when the font style values are equal for both objects,
            and false otherwise.</returns>
        </member>
        <member name="M:Telerik.Documents.Core.Fonts.FontStyle.op_Inequality(Telerik.Documents.Core.Fonts.FontStyle,Telerik.Documents.Core.Fonts.FontStyle)">
            <summary>
            Checks whether two font style objects are not equal.
            </summary>
            <param name="fs1">First object to compare.</param>
            <param name="fs2">Second object to compare.</param>
            <returns>Returns false when the font style values are equal for both objects,
            and true otherwise.</returns>
        </member>
        <member name="T:Telerik.Documents.Core.Fonts.FontStyles">
            <summary>
            Contains predefined <see cref="T:Telerik.Documents.Core.Fonts.FontStyle"/>s.
            </summary>
        </member>
        <member name="P:Telerik.Documents.Core.Fonts.FontStyles.Normal">
            <summary>
            Gets the normal <see cref="T:Telerik.Documents.Core.Fonts.FontStyle"/>.
            </summary>
        </member>
        <member name="P:Telerik.Documents.Core.Fonts.FontStyles.Oblique">
            <summary>
            Gets the oblique <see cref="T:Telerik.Documents.Core.Fonts.FontStyle"/>.
            </summary>
        </member>
        <member name="P:Telerik.Documents.Core.Fonts.FontStyles.Italic">
            <summary>
            Gets the italic <see cref="T:Telerik.Documents.Core.Fonts.FontStyle"/>.
            </summary>
        </member>
        <member name="T:Telerik.Documents.Core.Fonts.FontWeight">
            <summary>
            Structure describes the degree of thickness in a font characters.
            </summary>
        </member>
        <member name="M:Telerik.Documents.Core.Fonts.FontWeight.#ctor(System.Int32)">
            <summary>
            Initializes a new instance of <see cref="T:Telerik.Documents.Core.Fonts.FontWeight"/> struct.
            </summary>
            <param name="weight"></param>
        </member>
        <member name="M:Telerik.Documents.Core.Fonts.FontWeight.GetHashCode">
            <inheritdoc/>
        </member>
        <member name="M:Telerik.Documents.Core.Fonts.FontWeight.Equals(System.Object)">
            <inheritdoc/>
        </member>
        <member name="M:Telerik.Documents.Core.Fonts.FontWeight.ToString">
            <summary>
            Return string representation of the font`s weight.
            </summary>
        </member>
        <member name="M:Telerik.Documents.Core.Fonts.FontWeight.op_Equality(Telerik.Documents.Core.Fonts.FontWeight,Telerik.Documents.Core.Fonts.FontWeight)">
            <summary>
            Checks whether two font weight objects are equal.
            </summary>
            <param name="fw1">First object to compare.</param>
            <param name="fw2">Second object to compare.</param>
            <returns>Returns true when the font weight values are equal for both objects,
            and false otherwise.</returns>
        </member>
        <member name="M:Telerik.Documents.Core.Fonts.FontWeight.op_Inequality(Telerik.Documents.Core.Fonts.FontWeight,Telerik.Documents.Core.Fonts.FontWeight)">
            <summary>
            Checks whether two font weight objects are not equal.
            </summary>
            <param name="fw1">First object to compare.</param>
            <param name="fw2">Second object to compare.</param>
            <returns>Returns false when the font weight values are equal for both objects,
            and true otherwise.</returns>
            <returns></returns>
        </member>
        <member name="M:Telerik.Documents.Core.Fonts.FontWeight.op_LessThan(Telerik.Documents.Core.Fonts.FontWeight,Telerik.Documents.Core.Fonts.FontWeight)">
            <summary>
            Checks whether one instance is less than the other.
            </summary>
            <param name="fw1">First object to compare.</param>
            <param name="fw2">Second object to compare.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Documents.Core.Fonts.FontWeight.op_LessThanOrEqual(Telerik.Documents.Core.Fonts.FontWeight,Telerik.Documents.Core.Fonts.FontWeight)">
            <summary>
            Checks whether one instance is less or equal to the other.
            </summary>
            <param name="fw1">First object to compare.</param>
            <param name="fw2">Second object to compare.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Documents.Core.Fonts.FontWeight.op_GreaterThan(Telerik.Documents.Core.Fonts.FontWeight,Telerik.Documents.Core.Fonts.FontWeight)">
            <summary>
            Checks whether one instance is greater than the other.
            </summary>
            <param name="fw1">First object to compare.</param>
            <param name="fw2">Second object to compare.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Documents.Core.Fonts.FontWeight.op_GreaterThanOrEqual(Telerik.Documents.Core.Fonts.FontWeight,Telerik.Documents.Core.Fonts.FontWeight)">
            <summary>
            Checks whether one instance is greater or equal to the other.
            </summary>
            <param name="fw1">First object to compare.</param>
            <param name="fw2">Second object to compare.</param>
            <returns></returns>
        </member>
        <member name="T:Telerik.Documents.Core.Fonts.FontWeights">
            <summary>
            Contains predefined <see cref="T:Telerik.Documents.Core.Fonts.FontWeights"/>.
            </summary>
        </member>
        <member name="P:Telerik.Documents.Core.Fonts.FontWeights.Thin">
            <summary>
            Gets the thin <see cref="T:Telerik.Documents.Core.Fonts.FontWeight"/>.
            </summary>
        </member>
        <member name="P:Telerik.Documents.Core.Fonts.FontWeights.Light">
            <summary>
            Gets the light <see cref="T:Telerik.Documents.Core.Fonts.FontWeight"/>.
            </summary>
        </member>
        <member name="P:Telerik.Documents.Core.Fonts.FontWeights.Normal">
            <summary>
            Gets the normal <see cref="T:Telerik.Documents.Core.Fonts.FontWeight"/>.
            </summary>
        </member>
        <member name="P:Telerik.Documents.Core.Fonts.FontWeights.Bold">
            <summary>
            Gets the bold <see cref="T:Telerik.Documents.Core.Fonts.FontWeight"/>.
            </summary>
        </member>
        <member name="P:Telerik.Documents.Core.Fonts.FontWeights.Black">
            <summary>
            Gets the black <see cref="T:Telerik.Documents.Core.Fonts.FontWeight"/>.
            </summary>
        </member>
        <member name="P:Telerik.Documents.Core.Fonts.FontWeights.Heavy">
            <summary>
            Gets the heavy <see cref="T:Telerik.Documents.Core.Fonts.FontWeight"/>.
            </summary>
        </member>
        <member name="T:Telerik.Documents.Core.Fonts.TextAlignment">
            <summary>
            This property describes how content of a block is aligned.
            </summary>
        </member>
        <member name="F:Telerik.Documents.Core.Fonts.TextAlignment.Left">
            <summary>
            In horizontal inline progression, the text is aligned on the left.
            </summary>
        </member>
        <member name="F:Telerik.Documents.Core.Fonts.TextAlignment.Right">
            <summary>
            In horizontal inline progression, the text is aligned on the right.
            </summary>
        </member>
        <member name="F:Telerik.Documents.Core.Fonts.TextAlignment.Center">
            <summary>
            The text is center aligned.
            </summary>
        </member>
        <member name="F:Telerik.Documents.Core.Fonts.TextAlignment.Justify">
            <summary>
            The text is justified.
            </summary>
        </member>
        <member name="T:Telerik.Documents.Core.Fonts.TextDecoration">
            <summary>
            A text decoration
            </summary>
        </member>
        <member name="P:Telerik.Documents.Core.Fonts.TextDecoration.Location">
            <summary>
                Location - TextDecorationLocation.  Default value is TextDecorationLocation.Underline.
                The Location of the text decorations
            </summary>
        </member>
        <member name="T:Telerik.Documents.Core.Fonts.TextDecorationLocation">
            <summary>
                TextDecorationLocation - Referenced localization of the text decoration
            </summary>
        </member>
        <member name="F:Telerik.Documents.Core.Fonts.TextDecorationLocation.Underline">
            <summary>
                Underline - Underline position
            </summary>
        </member>
        <member name="F:Telerik.Documents.Core.Fonts.TextDecorationLocation.OverLine">
            <summary>
                OverLine - OverLine position
            </summary>
        </member>
        <member name="F:Telerik.Documents.Core.Fonts.TextDecorationLocation.Strikethrough">
            <summary>
                Strikethrough - Strikethrough position
            </summary>
        </member>
        <member name="F:Telerik.Documents.Core.Fonts.TextDecorationLocation.Baseline">
            <summary>
                Baseline - Baseline position
            </summary>
        </member>
        <member name="T:Telerik.Documents.Core.Fonts.TextDecorations">
            <summary>
            TextDecorations class contains a set of commonly used text decorations such as underline,
            strikethrough, baseline and over-line.
            </summary>
        </member>
        <member name="P:Telerik.Documents.Core.Fonts.TextDecorations.Underline">
            <summary>
            returns a frozen collection containing an underline
            </summary>
        </member>
        <member name="P:Telerik.Documents.Core.Fonts.TextDecorations.Strikethrough">
            <summary>
            returns a frozen collection containing a strikethrough
            </summary>
        </member>
        <member name="P:Telerik.Documents.Core.Fonts.TextDecorations.OverLine">
            <summary>
            returns a frozen collection containing an overline
            </summary>
        </member>
        <member name="P:Telerik.Documents.Core.Fonts.TextDecorations.Baseline">
            <summary>
            returns a frozen collection containing a baseline
            </summary>
        </member>
        <member name="T:Telerik.Documents.Core.Fonts.TextWrapping">
            <summary>
            This property controls whether or not text wraps when it reaches the edge
            of its containing box
            </summary>
        </member>
        <member name="F:Telerik.Documents.Core.Fonts.TextWrapping.WrapWithOverflow">
            <summary>
            Line-breaking occurs if the line overflows the available block width.
            However, a line may overflow the block width if the line breaking algorithm
            cannot determine a break opportunity, as in the case of a very long word.
            </summary>
        </member>
        <member name="F:Telerik.Documents.Core.Fonts.TextWrapping.NoWrap">
            <summary>
            No line wrapping is performed. In the case when lines are longer than the
            available block width, the overflow will be treated in accordance with the
            'overflow' property specified in the element.
            </summary>
        </member>
        <member name="F:Telerik.Documents.Core.Fonts.TextWrapping.Wrap">
            <summary>
            Line-breaking occurs if the line overflow the available block width, even
            if the standard line breaking algorithm cannot determine any opportunity.
            For example, this deals with the situation of very long words constrained in
            a fixed-width container with no scrolling allowed.
            </summary>
        </member>
        <member name="T:Telerik.Documents.Media.Color">
            <summary>
            Represents color object.
            </summary>
        </member>
        <member name="P:Telerik.Documents.Media.Color.A">
            <summary>
            Gets or sets the alpha component.
            </summary>
            <value>The alpha component.</value>
        </member>
        <member name="P:Telerik.Documents.Media.Color.R">
            <summary>
            Gets or sets the red component.
            </summary>
            <value>The red component.</value>
        </member>
        <member name="P:Telerik.Documents.Media.Color.G">
            <summary>
            Gets or sets the green component.
            </summary>
            <value>The green component.</value>
        </member>
        <member name="P:Telerik.Documents.Media.Color.B">
            <summary>
            Gets or sets the blue component.
            </summary>
            <value>The blue component.</value>
        </member>
        <member name="M:Telerik.Documents.Media.Color.FromArgb(System.Byte,System.Byte,System.Byte,System.Byte)">
            <summary>
            Creates new color from Argb.
            </summary>
            <param name="alfa">The alfa.</param>
            <param name="red">The red.</param>
            <param name="green">The green.</param>
            <param name="blue">The blue.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Documents.Media.Color.FromRgb(System.Byte,System.Byte,System.Byte)">
            <summary>
            Creates new color from RGB.
            </summary>
            <param name="red">The red.</param>
            <param name="green">The green.</param>
            <param name="blue">The blue.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Documents.Media.Color.op_Equality(Telerik.Documents.Media.Color,Telerik.Documents.Media.Color)">
            <summary>
            Compares two color instances.
            </summary>
            <param name="color1">First color.</param>
            <param name="color2">Second color.</param>
            <returns>If two colors are equal.</returns>
        </member>
        <member name="M:Telerik.Documents.Media.Color.op_Inequality(Telerik.Documents.Media.Color,Telerik.Documents.Media.Color)">
            <summary>
            Compares two color instances.
            </summary>
            <param name="color1">First color.</param>
            <param name="color2">Second color.</param>
            <returns>If two colors are not equal.</returns>
        </member>
        <member name="M:Telerik.Documents.Media.Color.GetHashCode">
            <summary>
            Serves as a hash function for a particular type.
            </summary>
            <returns>A hash code for the current <see cref="T:System.Object" />.</returns>
        </member>
        <member name="M:Telerik.Documents.Media.Color.Equals(Telerik.Documents.Media.Color)">
            <summary>
            Determines whether the specified <see cref="T:Telerik.Documents.Media.Color"/> is equal to the current object.
            </summary>
            <param name="value">The color.</param>
        </member>
        <member name="M:Telerik.Documents.Media.Color.Equals(System.Object)">
            <summary>
            Determines whether the specified <see cref="T:System.Object" /> is equal
            to the current <see cref="T:System.Object" />.
            </summary>
            <param name="obj">The <see cref="T:System.Object" /> to compare with the current
            <see cref="T:System.Object" />.</param>
            <returns>
            <c>true</c>if the specified <see cref="T:System.Object" /> is equal to the
            current <see cref="T:System.Object" />; otherwise, false.
            </returns>
        </member>
        <member name="T:Telerik.Documents.Media.Colors">
            <summary>
            Represents collection with predefined colors.
            </summary>
        </member>
        <member name="P:Telerik.Documents.Media.Colors.Black">
            <summary>
            Gets the black color.
            </summary>
            <value>The black color.</value>
        </member>
        <member name="P:Telerik.Documents.Media.Colors.Blue">
            <summary>
            Gets the blue color.
            </summary>
            <value>The blue color.</value>
        </member>
        <member name="P:Telerik.Documents.Media.Colors.Brown">
            <summary>
            Gets the brown color.
            </summary>
            <value>The brown color.</value>
        </member>
        <member name="P:Telerik.Documents.Media.Colors.Cyan">
            <summary>
            Gets the cyan color.
            </summary>
            <value>The cyan color.</value>
        </member>
        <member name="P:Telerik.Documents.Media.Colors.DarkGray">
            <summary>
            Gets the dark gray color.
            </summary>
            <value>The dark gray color.</value>
        </member>
        <member name="P:Telerik.Documents.Media.Colors.Gray">
            <summary>
            Gets the gray color.
            </summary>
            <value>The gray color.</value>
        </member>
        <member name="P:Telerik.Documents.Media.Colors.Green">
            <summary>
            Gets the green color.
            </summary>
            <value>The green color.</value>
        </member>
        <member name="P:Telerik.Documents.Media.Colors.LightGray">
            <summary>
            Gets the light gray color.
            </summary>
            <value>The light gray color.</value>
        </member>
        <member name="P:Telerik.Documents.Media.Colors.Magenta">
            <summary>
            Gets the magenta color.
            </summary>
            <value>The magenta color.</value>
        </member>
        <member name="P:Telerik.Documents.Media.Colors.Orange">
            <summary>
            Gets the orange color.
            </summary>
            <value>The orange color.</value>
        </member>
        <member name="P:Telerik.Documents.Media.Colors.Purple">
            <summary>
            Gets the purple color.
            </summary>
            <value>The purple color.</value>
        </member>
        <member name="P:Telerik.Documents.Media.Colors.Red">
            <summary>
            Gets the red color.
            </summary>
            <value>The red color.</value>
        </member>
        <member name="P:Telerik.Documents.Media.Colors.White">
            <summary>
            Gets the white color.
            </summary>
            <value>The white color.</value>
        </member>
        <member name="P:Telerik.Documents.Media.Colors.Yellow">
            <summary>
            Gets the yellow color.
            </summary>
            <value>The yellow color.</value>
        </member>
        <member name="P:Telerik.Documents.Media.Colors.Violet">
            <summary>
            Gets the violet color.
            </summary>
            <value>The violet color.</value>
        </member>
        <member name="P:Telerik.Documents.Media.Colors.BlueViolet">
            <summary>
            Gets the blue violet color.
            </summary>
            <value>The blue violet color.</value>
        </member>
        <member name="P:Telerik.Documents.Media.Colors.GreenYellow">
            <summary>
            Gets the green yellow color.
            </summary>
            <value>The green yellow color.</value>
        </member>
        <member name="P:Telerik.Documents.Media.Colors.Azure">
            <summary>
            Gets the azure color.
            </summary>
            <value>The azure color.</value>
        </member>
        <member name="P:Telerik.Documents.Media.Colors.Aquamarine">
            <summary>
            Gets the aquamarine color.
            </summary>
            <value>The aquamarine color.</value>
        </member>
        <member name="P:Telerik.Documents.Media.Colors.BlanchedAlmond">
            <summary>
            Gets the blanched almond color.
            </summary>
            <value>The blanched almond color.</value>
        </member>
        <member name="P:Telerik.Documents.Media.Colors.DarkGreen">
            <summary>
            Gets the dark green color.
            </summary>
            <value>The dark green color.</value>
        </member>
        <member name="P:Telerik.Documents.Media.Colors.DarkCyan">
            <summary>
            Gets the dark cyan color.
            </summary>
            <value>The dark cyan color.</value>
        </member>
        <member name="P:Telerik.Documents.Media.Colors.Aqua">
            <summary>
            Gets the aqua color.
            </summary>
            <value>The aqua color.</value>
        </member>
        <member name="P:Telerik.Documents.Media.Colors.Pink">
            <summary>
            Gets the pink color.
            </summary>
            <value>The pink color.</value>
        </member>
        <member name="P:Telerik.Documents.Media.Colors.Chocolate">
            <summary>
            Gets the chocolate color.
            </summary>
            <value>The chocolate color.</value>
        </member>
        <member name="P:Telerik.Documents.Media.Colors.Plum">
            <summary>
            Gets the plum color.
            </summary>
            <value>The plum color.</value>
        </member>
        <member name="P:Telerik.Documents.Media.Colors.Lime">
            <summary>
            Gets the lime color.
            </summary>
            <value>The lime color.</value>
        </member>
        <member name="P:Telerik.Documents.Media.Colors.LightSkyBlue">
            <summary>
            Gets the light sky blue color.
            </summary>
            <value>The light sky blue color.</value>
        </member>
        <member name="P:Telerik.Documents.Media.Colors.Gold">
            <summary>
            Gets the gold color.
            </summary>
            <value>The gold color.</value>
        </member>
        <member name="P:Telerik.Documents.Media.Colors.Gainsboro">
            <summary>
            Gets the gainsboro color.
            </summary>
            <value>The gainsboro color.</value>
        </member>
        <member name="P:Telerik.Documents.Media.Colors.Firebrick">
            <summary>
            Gets the firebrick color.
            </summary>
            <value>The firebrick color.</value>
        </member>
        <member name="P:Telerik.Documents.Media.Colors.Transparent">
            <summary>
            Gets the transparent color.
            </summary>
            <value>The transparent color.</value>
        </member>
        <member name="T:Telerik.Documents.Primitives.Matrix">
            <summary>
            Represents a Matrix structure.
            </summary>
        </member>
        <member name="M:Telerik.Documents.Primitives.Matrix.#ctor(System.Double,System.Double,System.Double,System.Double,System.Double,System.Double)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Documents.Primitives.Matrix"/> struct.
            m11, m12, 0
            m21, m22, 0 
            offsetX, offsetY, 1
            </summary>
        </member>
        <member name="P:Telerik.Documents.Primitives.Matrix.Identity">
            <summary>
            Gets an identity matrix.
            </summary>
        </member>
        <member name="P:Telerik.Documents.Primitives.Matrix.IsIdentity">
            <summary>
            Gets a value indicating whether this <see cref="T:Telerik.Documents.Primitives.Matrix"/> is equal to the identity matrix.
            </summary>
        </member>
        <member name="P:Telerik.Documents.Primitives.Matrix.Determinant">
            <summary>
            Gets the determinant.
            </summary>
        </member>
        <member name="P:Telerik.Documents.Primitives.Matrix.M11">
            <summary>
            Gets or sets the M11 value.
            </summary>
        </member>
        <member name="P:Telerik.Documents.Primitives.Matrix.M12">
            <summary>
            Gets or sets the M12 value.
            </summary>
        </member>
        <member name="P:Telerik.Documents.Primitives.Matrix.M21">
            <summary>
            Gets or sets the M21 value.
            </summary>
        </member>
        <member name="P:Telerik.Documents.Primitives.Matrix.M22">
            <summary>
            Gets or sets the M22 value.
            </summary>
        </member>
        <member name="P:Telerik.Documents.Primitives.Matrix.OffsetX">
            <summary>
            Gets or sets the OffsetX value.
            </summary>
        </member>
        <member name="P:Telerik.Documents.Primitives.Matrix.OffsetY">
            <summary>
            Gets or sets the OffsetY value.
            </summary>
        </member>
        <member name="P:Telerik.Documents.Primitives.Matrix.HasInverse">
            <summary>
            Determines whether the specified <see cref="T:Telerik.Documents.Primitives.Matrix"/> has inverse.
            </summary>
        </member>
        <member name="M:Telerik.Documents.Primitives.Matrix.op_Multiply(Telerik.Documents.Primitives.Matrix,Telerik.Documents.Primitives.Matrix)">
            <summary>
            Returns a new instance of <see cref="T:Telerik.Documents.Primitives.Matrix"/> that is the result of the multiplication if matrix1 and matrix2.
            </summary>
            <param name="matrix1">The left matrix to multiply.</param>
            <param name="matrix2">The right matrix to multiply.</param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Documents.Primitives.Matrix.op_Equality(Telerik.Documents.Primitives.Matrix,Telerik.Documents.Primitives.Matrix)">
            <summary>
            Compares two <see cref="T:Telerik.Documents.Primitives.Matrix"/> instances for exact equality.
            </summary>
            <param name="a">The first <see cref="T:Telerik.Documents.Primitives.Matrix"/> to compare.</param>
            <param name="b">The second <see cref="T:Telerik.Documents.Primitives.Matrix"/> to compare.</param>
            <returns>True if the two <see cref="T:Telerik.Documents.Primitives.Matrix"/>s are exactly equal; otherwise false.</returns>
        </member>
        <member name="M:Telerik.Documents.Primitives.Matrix.op_Inequality(Telerik.Documents.Primitives.Matrix,Telerik.Documents.Primitives.Matrix)">
            <summary>
            Compares two <see cref="T:Telerik.Documents.Primitives.Matrix"/> instances for exact inequality.
            </summary>
            <param name="a">The first <see cref="T:Telerik.Documents.Primitives.Matrix"/> to compare.</param>
            <param name="b">The second <see cref="T:Telerik.Documents.Primitives.Matrix"/> to compare.</param>
            <returns>True if the two <see cref="T:Telerik.Documents.Primitives.Matrix"/>s are exactly unequal; otherwise false.</returns>
        </member>
        <member name="M:Telerik.Documents.Primitives.Matrix.Translate(System.Double,System.Double)">
            <summary>
            Translates this <see cref="T:Telerik.Documents.Primitives.Matrix"/>.
            </summary>
            <param name='offsetX'>The offset in the x dimension.</param>
            <param name='offsetY'>The offset in the y dimension.</param>
        </member>
        <member name="M:Telerik.Documents.Primitives.Matrix.ScalePrepend(System.Double,System.Double,System.Double,System.Double)">
            <summary>
            Prepends a scale around the origin to this <see cref="T:Telerik.Documents.Primitives.Matrix"/>.
            </summary>
            <param name='scaleX'>The scale factor in the x dimension.</param>
            <param name='scaleY'>The scale factor in the y dimension.</param>
            <param name='centerX'>The centerX of scale. Default 0.</param>
            <param name='centerY'>The centerY of scale. Default 0.</param>
        </member>
        <member name="M:Telerik.Documents.Primitives.Matrix.Scale(System.Double,System.Double,System.Double,System.Double)">
            <summary>
            Scales this <see cref="T:Telerik.Documents.Primitives.Matrix"/> around the origin.
            </summary>
            <param name='scaleX'>The scale factor in the x dimension.</param>
            <param name='scaleY'>The scale factor in the y dimension.</param>
            <param name='centerX'>The centerX of scale. Default 0.</param>
            <param name='centerY'>The centerY of scale. Default 0.</param>
        </member>
        <member name="M:Telerik.Documents.Primitives.Matrix.Rotate(System.Double,System.Double,System.Double)">
            <summary>
            Rotates this <see cref="T:Telerik.Documents.Primitives.Matrix"/> at the given point.
            </summary>
            <param name='angle'>The angle to rotate.</param>
            <param name='centerX'>The centerX of rotation. Default 0.</param>
            <param name='centerY'>The centerY of rotation. Default 0.</param>
        </member>
        <member name="M:Telerik.Documents.Primitives.Matrix.Shear(System.Single,System.Single)">
            <summary>
            Shears the matrix with the specified scalars.
            </summary>
            <param name="shearX"></param>
            <param name="shearY"></param>
        </member>
        <member name="M:Telerik.Documents.Primitives.Matrix.ShearPrepend(System.Double,System.Double)">
            <summary>
            Shears the matrix with the specified scalars.
            </summary>
            <param name="shearX"></param>
            <param name="shearY"></param>
        </member>
        <member name="M:Telerik.Documents.Primitives.Matrix.Transform(Telerik.Documents.Primitives.Point[])">
            <summary>
            Returns the result of transforming given points by this <see cref="T:Telerik.Documents.Primitives.Matrix"/>.
            </summary>
            <param name="points"> The Points to transform.</param>
        </member>
        <member name="M:Telerik.Documents.Primitives.Matrix.Transform(Telerik.Documents.Primitives.Point)">
            <summary>
            Returns the result of transforming a given point by this <see cref="T:Telerik.Documents.Primitives.Matrix"/>.
            </summary>
            <param name="point"> The Point to transform.</param>
        </member>
        <member name="M:Telerik.Documents.Primitives.Matrix.Transform(Telerik.Documents.Primitives.Vector)">
            <summary>
            Returns the result of transforming a given vector by this <see cref="T:Telerik.Documents.Primitives.Matrix"/>.
            </summary>
            <param name="vector"> The Vector to transform.</param>
        </member>
        <member name="M:Telerik.Documents.Primitives.Matrix.GetHashCode">
            <inheritdoc/>
        </member>
        <member name="M:Telerik.Documents.Primitives.Matrix.Equals(System.Object)">
            <inheritdoc/>
        </member>
        <member name="M:Telerik.Documents.Primitives.Matrix.ToString">
            <inheritdoc/>
        </member>
        <member name="M:Telerik.Documents.Primitives.Matrix.CreateScaling(System.Double,System.Double,System.Double,System.Double)">
            <summary>
            Creates a scaling transform around the given point
            </summary>
            <param name='scaleX'>The scale factor in the x dimension</param>
            <param name='scaleY'>The scale factor in the y dimension</param>
            <param name='centerX'>The centerX of scaling</param>
            <param name='centerY'>The centerY of scaling</param>
        </member>
        <member name="T:Telerik.Documents.Primitives.MatrixOrder">
            <summary>
            Specifies the order for matrix transform operations.
            </summary>
        </member>
        <member name="F:Telerik.Documents.Primitives.MatrixOrder.Prepend">
            <summary>
            The new operation is applied before the old operation.
            </summary>
        </member>
        <member name="F:Telerik.Documents.Primitives.MatrixOrder.Append">
            <summary>
            The new operation is applied after the old operation.
            </summary>
        </member>
        <member name="T:Telerik.Documents.Primitives.Point">
            <summary>
            Point structure in a 2D space, defined by two double values for the coordinates.
            </summary>
        </member>
        <member name="M:Telerik.Documents.Primitives.Point.#ctor(System.Double,System.Double)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Documents.Primitives.Point"/> structure.
            </summary>
            <param name="x">The X coordinate.</param>
            <param name="y">The Y coordinate.</param>
        </member>
        <member name="P:Telerik.Documents.Primitives.Point.X">
            <summary>
            The X coordinate.  Default value is 0.
            </summary>
        </member>
        <member name="P:Telerik.Documents.Primitives.Point.Y">
            <summary>
            The Y coordinate.  Default value is 0.
            </summary>
        </member>
        <member name="M:Telerik.Documents.Primitives.Point.Offset(System.Double,System.Double)">
            <summary>
            Offset - update the location by adding offsetX to X and offsetY to Y
            </summary>
            <param name="offsetX"> The offset in the x dimension </param>
            <param name="offsetY"> The offset in the y dimension </param>
        </member>
        <member name="M:Telerik.Documents.Primitives.Point.op_Equality(Telerik.Documents.Primitives.Point,Telerik.Documents.Primitives.Point)">
            <summary>
            Compares two <see cref="T:Telerik.Documents.Primitives.Point"/> instances for exact equality.
            </summary>
            <param name='point1'>The first <see cref="T:Telerik.Documents.Primitives.Point"/> to compare.</param>
            <param name='point2'>The second <see cref="T:Telerik.Documents.Primitives.Point"/> to compare.</param>
            <returns> true if the two <see cref="T:Telerik.Documents.Primitives.Point"/> instances are exactly equal; false otherwise. </returns>
        </member>
        <member name="M:Telerik.Documents.Primitives.Point.op_Inequality(Telerik.Documents.Primitives.Point,Telerik.Documents.Primitives.Point)">
            <summary>
            Compares two <see cref="T:Telerik.Documents.Primitives.Point"/> instances for exact inequality.
            </summary>
            <param name='point1'>The first <see cref="T:Telerik.Documents.Primitives.Point"/> to compare</param>
            <param name='point2'>The second <see cref="T:Telerik.Documents.Primitives.Point"/> to compare</param>
            <returns>true if the two <see cref="T:Telerik.Documents.Primitives.Point"/> instances are exactly unequal; false otherwise.</returns>
        </member>
        <member name="M:Telerik.Documents.Primitives.Point.op_Addition(Telerik.Documents.Primitives.Point,Telerik.Documents.Primitives.Vector)">
            <summary>
            Operator Point + Vector
            </summary>
            <returns>
            Point - The result of the addition
            </returns>
            <param name="point"> The Point to be added to the Vector </param>
            <param name="vector"> The Vectr to be added to the Point </param>
        </member>
        <member name="M:Telerik.Documents.Primitives.Point.Subtract(Telerik.Documents.Primitives.Point,Telerik.Documents.Primitives.Point)">
            <summary>
            Subtract: Point - Point
            </summary>
            <returns>
            Vector - The result of the subtraction
            </returns>
            <param name="point1"> The Point from which point2 is subtracted </param>
            <param name="point2"> The Point subtracted from point1 </param>
        </member>
        <member name="M:Telerik.Documents.Primitives.Point.GetHashCode">
            <summary>
            Returns a hash code for this instance.
            </summary>
            <returns>
            A hash code for this instance, suitable for use in hashing algorithms and data structures like a hash table.
            </returns>
        </member>
        <member name="M:Telerik.Documents.Primitives.Point.Equals(System.Object)">
            <summary>
            Determines whether the specified <see cref="T:System.Object" />, is equal to this instance.
            </summary>
            <param name="obj">The <see cref="T:System.Object" /> to compare with this instance.</param>
            <returns>
              <c>true</c> if the specified <see cref="T:System.Object" /> is equal to this instance; otherwise, <c>false</c>.
            </returns>
        </member>
        <member name="T:Telerik.Documents.Primitives.Rect">
            <summary>
            Rect structure representing a rectangle, defined by two double coordinates and a double width and double height.
            </summary>
        </member>
        <member name="M:Telerik.Documents.Primitives.Rect.#ctor(System.Double,System.Double,System.Double,System.Double)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Documents.Primitives.Rect"/> structure.
            </summary>
            <param name="x">The X coordinate.</param>
            <param name="y">The Y coordinate.</param>
            <param name="width">The width. Should be non-negative.</param>
            <param name="height">The height. Should be non-negative.</param>
        </member>
        <member name="M:Telerik.Documents.Primitives.Rect.#ctor(Telerik.Documents.Primitives.Point,Telerik.Documents.Primitives.Size)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Documents.Primitives.Rect"/> structure.
            </summary>
            <param name="location">The <see cref="T:Telerik.Documents.Primitives.Point"/> location, that determines the coordinates.</param>
            <param name="size">The <see cref="P:Telerik.Documents.Primitives.Rect.Size"/> size, that defines the width and height. It should not be Empty. </param>
        </member>
        <member name="M:Telerik.Documents.Primitives.Rect.#ctor(Telerik.Documents.Primitives.Size)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Documents.Primitives.Rect"/> structure at (0;0) coordinates.
            </summary>
            <param name="size">The <see cref="P:Telerik.Documents.Primitives.Rect.Size"/> size, that defines the width and height. It should not be Empty. </param>
        </member>
        <member name="M:Telerik.Documents.Primitives.Rect.#ctor(Telerik.Documents.Primitives.Point,Telerik.Documents.Primitives.Point)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Documents.Primitives.Rect"/> structure. Sets the values of X, Y, Width and Height from the two points provided.
            </summary>
        </member>
        <member name="P:Telerik.Documents.Primitives.Rect.Empty">
            <summary>
            Gets an empty <see cref="T:Telerik.Documents.Primitives.Rect"/>. It has negative-infinity Width and Height and positive-infinity X and Y.
            </summary>
        </member>
        <member name="P:Telerik.Documents.Primitives.Rect.IsEmpty">
            <summary>
            Returns true if this is the Empty <see cref="T:Telerik.Documents.Primitives.Rect"/>.
            </summary>
        </member>
        <member name="P:Telerik.Documents.Primitives.Rect.Size">
            <summary>
            Size - The Size representing the area of the Rectangle
            </summary>
        </member>
        <member name="P:Telerik.Documents.Primitives.Rect.X">
            <summary>
            Gets or sets the X coordinate. Cannot set X if this is an Empty <see cref="T:Telerik.Documents.Primitives.Rect"/>.
            </summary>
        </member>
        <member name="P:Telerik.Documents.Primitives.Rect.Y">
            <summary>
            Gets or sets the Y coordinate. Cannot set Y if this is an Empty <see cref="T:Telerik.Documents.Primitives.Rect"/>.
            </summary>
        </member>
        <member name="P:Telerik.Documents.Primitives.Rect.Width">
            <summary>
            Gets or sets the Width. Cannot set Width if this is an Empty <see cref="T:Telerik.Documents.Primitives.Rect"/>. Should be non-negative.
            </summary>
        </member>
        <member name="P:Telerik.Documents.Primitives.Rect.Height">
            <summary>
            Gets or sets the Height. Cannot set Height if this is an Empty <see cref="T:Telerik.Documents.Primitives.Rect"/>. Should be non-negative.
            </summary>
        </member>
        <member name="P:Telerik.Documents.Primitives.Rect.Left">
            <summary>
            Gets the value of the X coordinate.
            </summary>
        </member>
        <member name="P:Telerik.Documents.Primitives.Rect.Top">
            <summary>
            Gets the value of the Y coordinate.
            </summary>
        </member>
        <member name="P:Telerik.Documents.Primitives.Rect.Right">
            <summary>
            Gets the X + Width value. If this is an Empty <see cref="T:Telerik.Documents.Primitives.Rect"/> returns negative-infinity.
            </summary>
        </member>
        <member name="P:Telerik.Documents.Primitives.Rect.Bottom">
            <summary>
            Gets the Y + Height value. If this is an Empty <see cref="T:Telerik.Documents.Primitives.Rect"/> gets negative-infinity.
            </summary>
        </member>
        <member name="P:Telerik.Documents.Primitives.Rect.TopLeft">
            <summary>
            TopLeft Property - This is a read-only alias for the Point which is at X, Y
            If this is the empty rectangle, the value will be positive infinity, positive infinity.
            </summary>
        </member>
        <member name="P:Telerik.Documents.Primitives.Rect.TopRight">
            <summary>
            TopRight Property - This is a read-only alias for the Point which is at X + Width, Y
            If this is the empty rectangle, the value will be negative infinity, positive infinity.
            </summary>
        </member>
        <member name="P:Telerik.Documents.Primitives.Rect.BottomLeft">
            <summary>
            BottomLeft Property - This is a read-only alias for the Point which is at X, Y + Height
            If this is the empty rectangle, the value will be positive infinity, negative infinity.
            </summary>
        </member>
        <member name="P:Telerik.Documents.Primitives.Rect.BottomRight">
            <summary>
            BottomRight Property - This is a read-only alias for the Point which is at X + Width, Y + Height
            If this is the empty rectangle, the value will be negative infinity, negative infinity.
            </summary>
        </member>
        <member name="P:Telerik.Documents.Primitives.Rect.Location">
            <summary>
            Gets or sets the coordinates of the upper-left corner of this Rect structure.
            </summary>
        </member>
        <member name="M:Telerik.Documents.Primitives.Rect.op_Equality(Telerik.Documents.Primitives.Rect,Telerik.Documents.Primitives.Rect)">
            <summary>
            Compares two <see cref="T:Telerik.Documents.Primitives.Rect"/> instances for exact equality.
            </summary>
            <param name="firstRect">The first <see cref="T:Telerik.Documents.Primitives.Rect"/> to compare.</param>
            <param name="secondRect">The second <see cref="T:Telerik.Documents.Primitives.Rect"/> to compare.</param>
            <returns>True if the two <see cref="T:Telerik.Documents.Primitives.Rect"/>s are exactly equal; otherwise false.</returns>
        </member>
        <member name="M:Telerik.Documents.Primitives.Rect.op_Inequality(Telerik.Documents.Primitives.Rect,Telerik.Documents.Primitives.Rect)">
            <summary>
            Compares two <see cref="T:Telerik.Documents.Primitives.Rect"/> instances for exact inequality.
            </summary>
            <param name="firstRect">The first <see cref="T:Telerik.Documents.Primitives.Rect"/> to compare.</param>
            <param name="secondRect">The second <see cref="T:Telerik.Documents.Primitives.Rect"/> to compare.</param>
            <returns>True if the two <see cref="T:Telerik.Documents.Primitives.Rect"/>s are exactly unequal; otherwise false.</returns>
        </member>
        <member name="M:Telerik.Documents.Primitives.Rect.GetHashCode">
            <inheritdoc/>
        </member>
        <member name="M:Telerik.Documents.Primitives.Rect.Equals(System.Object)">
            <inheritdoc/>
        </member>
        <member name="M:Telerik.Documents.Primitives.Rect.Contains(Telerik.Documents.Primitives.Point)">
             <summary>
             Returns a value indicating whether a Point is inside the rectangle, including the edges.
            </summary>
        </member>
        <member name="M:Telerik.Documents.Primitives.Rect.Contains(System.Double,System.Double)">
            <summary>
            Returns a value indicating whether the x, y coordinates are inside the rectangle, including the edges.
            </summary>
        </member>
        <member name="M:Telerik.Documents.Primitives.Rect.Contains(Telerik.Documents.Primitives.Rect)">
            <summary>
            Contains - Returns true if the Rect non-Empty and is entirely contained within the
            rectangle, inclusive of the edges.
            Returns false otherwise
            </summary>
        </member>
        <member name="M:Telerik.Documents.Primitives.Rect.IntersectsWith(Telerik.Documents.Primitives.Rect)">
            <summary>
            IntersectsWith - Returns true if the Rect intersects with this rectangle
            Returns false otherwise.
            Note that if one edge is coincident, this is considered an intersection.
            </summary>
            <returns>
            Returns true if the Rect intersects with this rectangle
            Returns false otherwise.
            or Height
            </returns>
            <param name="rect"> Rect </param>
        </member>
        <member name="M:Telerik.Documents.Primitives.Rect.Intersect(Telerik.Documents.Primitives.Rect)">
            <summary>
            Intersect - Update this rectangle to be the intersection of this and rect
            If either this or rect are Empty, the result is Empty as well.
            </summary>
            <param name="rect"> The rect to intersect with this </param>
        </member>
        <member name="M:Telerik.Documents.Primitives.Rect.Union(Telerik.Documents.Primitives.Rect)">
            <summary>
            This <see cref="T:Telerik.Documents.Primitives.Rect"/> becomes a the result of union of this and otherRect.
            </summary>
        </member>
        <member name="M:Telerik.Documents.Primitives.Rect.Union(Telerik.Documents.Primitives.Rect,Telerik.Documents.Primitives.Rect)">
            <summary>
            Union - Return the result of the union of rect1 and rect2.
            </summary>
        </member>
        <member name="M:Telerik.Documents.Primitives.Rect.Offset(System.Double,System.Double)">
            <summary>
            Offset - translate the Location by the offset provided
            If this is Empty, this method is illegal.
            </summary>
        </member>
        <member name="M:Telerik.Documents.Primitives.Rect.Inflate(Telerik.Documents.Primitives.Size)">
            <summary>
            Inflate - inflate the bounds by the size provided, in all directions
            If this is Empty, this method is illegal.
            </summary>
        </member>
        <member name="M:Telerik.Documents.Primitives.Rect.Inflate(System.Double,System.Double)">
            <summary>
            Inflate - inflate the bounds by the size provided, in all directions.
            If -width is > Width / 2 or -height is > Height / 2, this Rect becomes Empty
            If this is Empty, this method is illegal.
            </summary>
        </member>
        <member name="M:Telerik.Documents.Primitives.Rect.Inflate(Telerik.Documents.Primitives.Rect,Telerik.Documents.Primitives.Size)">
            <summary>
            Inflate - return the result of inflating rect by the size provided, in all directions
            If this is Empty, this method is illegal.
            </summary>
        </member>
        <member name="M:Telerik.Documents.Primitives.Rect.Inflate(Telerik.Documents.Primitives.Rect,System.Double,System.Double)">
            <summary>
            Inflate - return the result of inflating rect by the size provided, in all directions
            If this is Empty, this method is illegal.
            </summary>
        </member>
        <member name="M:Telerik.Documents.Primitives.Rect.Transform(Telerik.Documents.Primitives.Rect,Telerik.Documents.Primitives.Matrix)">
            <summary>
            Returns the bounds of the transformed rectangle.
            The Empty Rect is not affected by this call.
            </summary>
            <returns>
            The rect which results from the transformation.
            </returns>
            <param name="rect"> The Rect to transform. </param>
            <param name="matrix"> The Matrix by which to transform. </param>
        </member>
        <member name="M:Telerik.Documents.Primitives.Rect.Transform(Telerik.Documents.Primitives.Matrix)">
            <summary>
            Updates rectangle to be the bounds of the original value transformed
            by the matrix.
            The Empty Rect is not affected by this call.
            </summary>
            <param name="matrix"> Matrix </param>
        </member>
        <member name="T:Telerik.Documents.Primitives.Size">
            <summary>
            Size structure defined by width and height.
            </summary>
        </member>
        <member name="M:Telerik.Documents.Primitives.Size.#ctor(System.Double,System.Double)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Documents.Primitives.Size"/> structure.
            </summary>
            <param name="width">The Width. Should be non-negative.</param>
            <param name="height">The Height. Should be non-negative.</param>
        </member>
        <member name="P:Telerik.Documents.Primitives.Size.Empty">
            <summary>
            Gets an empty <see cref="T:Telerik.Documents.Primitives.Size"/>. It has negative-infinity Width and Height.
            </summary>
        </member>
        <member name="P:Telerik.Documents.Primitives.Size.IsEmpty">
            <summary>
            Returns true if this size is the Empty size.
            </summary>
        </member>
        <member name="P:Telerik.Documents.Primitives.Size.Width">
            <summary>
            Gets or sets the width. Should be non-negative.
            </summary>
        </member>
        <member name="P:Telerik.Documents.Primitives.Size.Height">
            <summary>
            Gets or sets the height. Should be non-negative.
            </summary>
        </member>
        <member name="M:Telerik.Documents.Primitives.Size.op_Equality(Telerik.Documents.Primitives.Size,Telerik.Documents.Primitives.Size)">
            <summary>
            Compares two <see cref="T:Telerik.Documents.Primitives.Size"/> instances for exact equality.
            </summary>
            <param name="firstSize">The first <see cref="T:Telerik.Documents.Primitives.Size"/> to compare.</param>
            <param name="secondSize">The second <see cref="T:Telerik.Documents.Primitives.Size"/> to compare.</param>
            <returns>True if the two <see cref="T:Telerik.Documents.Primitives.Size"/>s are exactly equal; otherwise false.</returns>
        </member>
        <member name="M:Telerik.Documents.Primitives.Size.op_Inequality(Telerik.Documents.Primitives.Size,Telerik.Documents.Primitives.Size)">
            <summary>
            Compares two <see cref="T:Telerik.Documents.Primitives.Size"/> instances for exact inequality.
            </summary>
            <param name="firstSize">The first <see cref="T:Telerik.Documents.Primitives.Size"/> to compare.</param>
            <param name="secondSize">The second <see cref="T:Telerik.Documents.Primitives.Size"/> to compare.</param>
            <returns>True if the two <see cref="T:Telerik.Documents.Primitives.Size"/>s are exactly unequal; otherwise false.</returns>
        </member>
        <member name="M:Telerik.Documents.Primitives.Size.GetHashCode">
            <summary>Returns the hash code for this instance.</summary>
            <returns>A 32-bit signed integer that is the hash code for this instance.</returns>
        </member>
        <member name="M:Telerik.Documents.Primitives.Size.Equals(System.Object)">
            <summary>Indicates whether this instance and a specified object are equal.</summary>
            <param name="obj">The object to compare with the current instance.</param>
            <returns>true if <paramref name="obj">obj</paramref> and this instance are the same type and represent the same value; otherwise, false.</returns>
        </member>
        <member name="M:Telerik.Documents.Primitives.Size.ToString">
            <summary>Returns the fully qualified type name of this instance.</summary>
            <returns>The fully qualified type name.</returns>
        </member>
        <member name="T:Telerik.Documents.Primitives.Vector">
            <summary>
            Vector - A value type which defined a vector in terms of X and Y
            </summary>
        </member>
        <member name="M:Telerik.Documents.Primitives.Vector.#ctor(System.Double,System.Double)">
            <summary>
            Constructor which sets the vector's initial values
            </summary>
            <param name="x"> double - The initial X </param>
            <param name="y"> double - THe initial Y </param>
        </member>
        <member name="P:Telerik.Documents.Primitives.Vector.X">
            <summary>
            The X coordinate.  Default value is 0.
            </summary>
        </member>
        <member name="P:Telerik.Documents.Primitives.Vector.Y">
            <summary>
            The Y coordinate.  Default value is 0.
            </summary>
        </member>
        <member name="P:Telerik.Documents.Primitives.Vector.Length">
            <summary>
            Length Property - the length of this Vector
            </summary>
        </member>
        <member name="P:Telerik.Documents.Primitives.Vector.LengthSquared">
            <summary>
            LengthSquared Property - the squared length of this Vector
            </summary>
        </member>
        <member name="M:Telerik.Documents.Primitives.Vector.op_Multiply(Telerik.Documents.Primitives.Vector,System.Double)">
            <summary>
            Operator Vector * double
            </summary>
        </member>
        <member name="M:Telerik.Documents.Primitives.Vector.op_Division(Telerik.Documents.Primitives.Vector,System.Double)">
            <summary>
            Operator Vector / double
            </summary>
        </member>
        <member name="T:Telerik.Imaging.Svg.AttributeEventArgs">
            <summary>
            Describes the Attribute which was set
            </summary>
        </member>
        <member name="T:Telerik.Imaging.Svg.SvgCircle">
            <summary>
            An SVG element to render circles to the document.
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgCircle.Center">
            <summary>
            Gets the center point of the circle.
            </summary>
            <value>The center.</value>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgCircle.Path(Telerik.Imaging.Svg.ISvgRenderer)">
            <summary>
            Gets the <see cref="T:Telerik.Windows.Documents.Fixed.Model.Graphics.PathGeometry"/> representing this element.
            </summary>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgCircle.Render(Telerik.Imaging.Svg.ISvgRenderer)">
            <summary>
            Renders the circle using the specified <see cref="T:Telerik.Imaging.Svg.ISvgRenderer"/> object.
            </summary>
            <param name="renderer">The renderer object.</param>
        </member>
        <member name="T:Telerik.Imaging.Svg.SvgEllipse">
            <summary>
            Represents and SVG ellipse element.
            </summary>
        </member>
        <member name="T:Telerik.Imaging.Svg.SvgLine">
            <summary>PathFigure
            Represents and SVG line element.
            </summary>
        </member>
        <member name="T:Telerik.Imaging.Svg.SvgMarkerElement">
            <summary>
            Represents a path based element that can have markers.
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgMarkerElement.MarkerEnd">
            <summary>
            Gets or sets the marker (end cap) of the path.
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgMarkerElement.MarkerMid">
            <summary>
            Gets or sets the marker (mid points) of the path.
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgMarkerElement.MarkerStart">
            <summary>
            Gets or sets the marker (start cap) of the path.
            </summary>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgMarkerElement.RenderStroke(Telerik.Imaging.Svg.ISvgRenderer)">
            <summary>
            Renders the stroke of the element to the specified <see cref="T:Telerik.Imaging.Svg.ISvgRenderer"/>.
            Includes rendering of all markers defined in attributes.
            </summary>
            <param name="renderer">The <see cref="T:Telerik.Imaging.Svg.ISvgRenderer"/> object to render to.</param>
        </member>
        <member name="T:Telerik.Imaging.Svg.SvgPathBasedElement">
            <summary>
            Represents an element that is using a GraphicsPath as rendering base.
            </summary>
        </member>
        <member name="T:Telerik.Imaging.Svg.SvgPolygon">
            <summary>
            SvgPolygon defines a closed shape consisting of a set of connected straight line segments.
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgPolygon.Points">
            <summary>
            The points that make up the SvgPolygon
            </summary>
        </member>
        <member name="T:Telerik.Imaging.Svg.SvgPolyline">
            <summary>
            SvgPolyline defines a set of connected straight line segments. Typically, <see cref="T:Telerik.Imaging.Svg.SvgPolyline"/> defines open shapes.
            </summary>
        </member>
        <member name="T:Telerik.Imaging.Svg.SvgRectangle">
            <summary>
            Represents an SVG rectangle that could also have rounded edges.
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgRectangle.Location">
            <summary>
            Gets an <see cref="T:Telerik.Imaging.Svg.SvgPoint"/> representing the top left point of the rectangle.
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgRectangle.X">
            <summary>
            Gets or sets the position where the left point of the rectangle should start.
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgRectangle.Y">
            <summary>
            Gets or sets the position where the top point of the rectangle should start.
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgRectangle.Width">
            <summary>
            Gets or sets the width of the rectangle.
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgRectangle.Height">
            <summary>
            Gets or sets the height of the rectangle.
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgRectangle.CornerRadiusX">
            <summary>
            Gets or sets the X-radius of the rounded edges of this rectangle.
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgRectangle.CornerRadiusY">
            <summary>
            Gets or sets the Y-radius of the rounded edges of this rectangle.
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgRectangle.RequiresSmoothRendering">
            <summary>
            Gets or sets a value to determine if anti-aliasing should occur when the element is being rendered.
            </summary>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgRectangle.Render(Telerik.Imaging.Svg.ISvgRenderer)">
            <summary>
            Renders the <see cref="T:Telerik.Imaging.Svg.SvgElement"/>.
            </summary>
        </member>
        <member name="T:Telerik.Imaging.Svg.SvgVisualElement">
            <summary>
            The class that all SVG elements should derive from when they are to be rendered.
            </summary>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgVisualElement.Path(Telerik.Imaging.Svg.ISvgRenderer)">
            <summary>
            Gets the <see cref="T:Telerik.Windows.Documents.Fixed.Model.Graphics.PathGeometry"/> for this element.
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgVisualElement.Bounds">
            <summary>
            Gets the bounds of the element.
            </summary>
            <value>The bounds.</value>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgVisualElement.Clip">
            <summary>
            Gets the associated <see cref="T:Telerik.Imaging.Svg.SvgClipPath"/> if one has been specified.
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgVisualElement.ClipPath">
            <summary>
            Gets the associated <see cref="T:Telerik.Imaging.Svg.SvgClipPath"/> if one has been specified.
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgVisualElement.ClipRule">
            <summary>
            Gets or sets the algorithm which is to be used to determine the clipping region.
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgVisualElement.Filter">
            <summary>
            Gets the associated <see cref="T:Telerik.Imaging.Svg.SvgFilter"/> if one has been specified.
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgVisualElement.RequiresSmoothRendering">
            <summary>
            Gets or sets a value to determine if anti-aliasing should occur when the element is being rendered.
            </summary>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgVisualElement.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Imaging.Svg.SvgVisualElement"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgVisualElement.Render(Telerik.Imaging.Svg.ISvgRenderer)">
            <summary>
            Renders the <see cref="T:Telerik.Imaging.Svg.SvgElement"/>.
            </summary>
            <param name="renderer">The <see cref="T:Telerik.Imaging.Svg.ISvgRenderer"/> object to render to.</param>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgVisualElement.RenderFill(Telerik.Imaging.Svg.ISvgRenderer)">
            <summary>
            Renders the fill of the <see cref="T:Telerik.Imaging.Svg.SvgVisualElement"/> to the specified <see cref="T:Telerik.Imaging.Svg.ISvgRenderer"/>
            </summary>
            <param name="renderer">The <see cref="T:Telerik.Imaging.Svg.ISvgRenderer"/> object to render to.</param>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgVisualElement.RenderStroke(Telerik.Imaging.Svg.ISvgRenderer)">
            <summary>
            Renders the stroke of the <see cref="T:Telerik.Imaging.Svg.SvgVisualElement"/> to the specified <see cref="T:Telerik.Imaging.Svg.ISvgRenderer"/>
            </summary>
            <param name="renderer">The <see cref="T:Telerik.Imaging.Svg.ISvgRenderer"/> object to render to.</param>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgVisualElement.SetClip(Telerik.Imaging.Svg.ISvgRenderer)">
            <summary>
            Sets the clipping region of the specified <see cref="T:Telerik.Imaging.Svg.ISvgRenderer"/>.
            </summary>
            <param name="renderer">The <see cref="T:Telerik.Imaging.Svg.ISvgRenderer"/> to have its clipping region set.</param>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgVisualElement.ResetClip(Telerik.Imaging.Svg.ISvgRenderer)">
            <summary>
            Resets the clipping region of the specified <see cref="T:Telerik.Imaging.Svg.ISvgRenderer"/> back to where it was before the <see cref="M:Telerik.Imaging.Svg.SvgVisualElement.SetClip(Telerik.Imaging.Svg.ISvgRenderer)"/> method was called.
            </summary>
            <param name="renderer">The <see cref="T:Telerik.Imaging.Svg.ISvgRenderer"/> to have its clipping region reset.</param>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgVisualElement.Telerik#Imaging#Svg#ISvgClipable#SetClip(Telerik.Imaging.Svg.ISvgRenderer)">
            <summary>
            Sets the clipping region of the specified <see cref="T:Telerik.Imaging.Svg.ISvgRenderer"/>.
            </summary>
            <param name="renderer">The <see cref="T:Telerik.Imaging.Svg.ISvgRenderer"/> to have its clipping region set.</param>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgVisualElement.Telerik#Imaging#Svg#ISvgClipable#ResetClip(Telerik.Imaging.Svg.ISvgRenderer)">
            <summary>
            Resets the clipping region of the specified <see cref="T:Telerik.Imaging.Svg.ISvgRenderer"/> back to where it was before the <see cref="M:Telerik.Imaging.Svg.SvgVisualElement.SetClip(Telerik.Imaging.Svg.ISvgRenderer)"/> method was called.
            </summary>
            <param name="renderer">The <see cref="T:Telerik.Imaging.Svg.ISvgRenderer"/> to have its clipping region reset.</param>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgVisualElement.EnableBackground">
            <summary>
            Gets or sets the fill <see cref="T:Telerik.Imaging.Svg.SvgPaintServer"/> of this element.
            </summary>
        </member>
        <member name="T:Telerik.Imaging.Svg.ChildAddedEventArgs">
            <summary>
            Describes the Attribute which was set
            </summary>
        </member>
        <member name="T:Telerik.Imaging.Svg.ISvgClipable">
            <summary>
            Defines the methods and properties that an <see cref="T:Telerik.Imaging.Svg.SvgElement"/> must implement to support clipping.
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.ISvgClipable.ClipPath">
            <summary>
            Gets or sets the ID of the associated <see cref="T:Telerik.Imaging.Svg.SvgClipPath"/> if one has been specified.
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.ISvgClipable.ClipRule">
            <summary>
            Specifies the rule used to define the clipping region when the element is within a <see cref="T:Telerik.Imaging.Svg.SvgClipPath"/>.
            </summary>
        </member>
        <member name="M:Telerik.Imaging.Svg.ISvgClipable.SetClip(Telerik.Imaging.Svg.ISvgRenderer)">
            <summary>
            Sets the clipping region of the specified <see cref="T:Telerik.Imaging.Svg.ISvgRenderer"/>.
            </summary>
            <param name="renderer">The <see cref="T:Telerik.Imaging.Svg.ISvgRenderer"/> to have its clipping region set.</param>
        </member>
        <member name="M:Telerik.Imaging.Svg.ISvgClipable.ResetClip(Telerik.Imaging.Svg.ISvgRenderer)">
            <summary>
            Resets the clipping region of the specified <see cref="T:Telerik.Imaging.Svg.ISvgRenderer"/> back to where it was before the <see cref="M:Telerik.Imaging.Svg.ISvgClipable.SetClip(Telerik.Imaging.Svg.ISvgRenderer)"/> method was called.
            </summary>
            <param name="renderer">The <see cref="T:Telerik.Imaging.Svg.ISvgRenderer"/> to have its clipping region reset.</param>
        </member>
        <member name="T:Telerik.Imaging.Svg.SvgClipPath">
            <summary>
            Defines a path that can be used by other <see cref="T:Telerik.Imaging.Svg.ISvgClipable"/> elements.
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgClipPath.ClipPathUnits">
            <summary>
            Specifies the coordinate system for the clipping path.
            </summary>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgClipPath.GetClipPath(Telerik.Imaging.Svg.SvgVisualElement,Telerik.Imaging.Svg.ISvgRenderer)">
            <summary>
            Gets this <see cref="T:Telerik.Imaging.Svg.SvgClipPath"/>'s region to be used as a clipping region.
            </summary>
            <param name="owner"></param>
            <param name="renderer"></param>
            <returns>A new <see cref="T:Telerik.Windows.Documents.Fixed.Model.Graphics.PathGeometry"/> containing the <see cref="T:Telerik.Windows.Documents.Fixed.Model.Graphics.PathGeometry"/> to be used for clipping.</returns>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgClipPath.CombinePaths(Telerik.Windows.Documents.Fixed.Model.Graphics.PathGeometry,Telerik.Imaging.Svg.SvgElement,Telerik.Imaging.Svg.ISvgRenderer)">
            <summary>
            
            </summary>
            <param name="path"></param>
            <param name="element"></param>
            <param name="renderer"></param>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgClipPath.AddElement(Telerik.Imaging.Svg.SvgElement,System.Int32)">
            <summary>
            Called by the underlying <see cref="T:Telerik.Imaging.Svg.SvgElement"/> when an element has been added to the
            'Children' collection.
            </summary>
            <param name="child">The <see cref="T:Telerik.Imaging.Svg.SvgElement"/> that has been added.</param>
            <param name="index">An <see cref="T:System.Int32"/> representing the index where the element was added to the collection.</param>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgClipPath.RemoveElement(Telerik.Imaging.Svg.SvgElement)">
            <summary>
            Called by the underlying <see cref="T:Telerik.Imaging.Svg.SvgElement"/> when an element has been removed from the
            <see cref="P:Telerik.Imaging.Svg.SvgElement.Children"/> collection.
            </summary>
            <param name="child">The <see cref="T:Telerik.Imaging.Svg.SvgElement"/> that has been removed.</param>
        </member>
        <member name="T:Telerik.Imaging.Svg.SvgClipRule">
            <summary>
            Indicates the algorithm which is to be used to determine the clipping region.
            </summary>
            <remarks>
                <para>This rule determines the "insideness" of a point on the canvas by drawing a ray from 
                that point to infinity in any direction and then examining the places where a segment of the 
                shape crosses the ray.</para>
            </remarks>
        </member>
        <member name="F:Telerik.Imaging.Svg.SvgClipRule.NonZero">
            <summary>
            This rule determines the "insideness" of a point on the canvas by drawing a ray from that point to infinity in any direction and then examining the places where a segment of the shape crosses the ray. Starting with a count of zero, add one each time a path segment crosses the ray from left to right and subtract one each time a path segment crosses the ray from right to left. After counting the crossings, if the result is zero then the point is outside the path. Otherwise, it is inside.
            </summary>
        </member>
        <member name="F:Telerik.Imaging.Svg.SvgClipRule.EvenOdd">
            <summary>
            This rule determines the "insideness" of a point on the canvas by drawing a ray from that point to infinity in any direction and counting the number of path segments from the given shape that the ray crosses. If this number is odd, the point is inside; if even, the point is outside.
            </summary>
        </member>
        <member name="F:Telerik.Imaging.Svg.SvgClipRule.Inherit">
            <summary>
            The value is inherited from the parent element.
            </summary>
        </member>
        <member name="T:Telerik.Imaging.Svg.SvgMask">
            <summary>
            Defines an alpha mask for compositing the current object into the background.
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgMask.MaskUnits">
            <summary>
            Defines the coordinate system for attributes <see cref="P:Telerik.Imaging.Svg.SvgMask.X"/>, <see cref="P:Telerik.Imaging.Svg.SvgMask.Y"/>, <see cref="P:Telerik.Imaging.Svg.SvgMask.Width"/> and <see cref="P:Telerik.Imaging.Svg.SvgMask.Height"/>.
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgMask.MaskContentUnits">
            <summary>
            Defines the coordinate system for the contents of the mask.
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgMask.X">
            <summary>
            The x-axis coordinate of one corner of the rectangle for the largest possible offscreen buffer.
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgMask.Y">
            <summary>
            The y-axis coordinate of one corner of the rectangle for the largest possible offscreen buffer.
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgMask.Width">
            <summary>
            The width of the largest possible offscreen buffer.
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgMask.Height">
            <summary>
            The height of the largest possible offscreen buffer.
            </summary>
        </member>
        <member name="T:Telerik.Imaging.Svg.ContentEventArgs">
            <summary>
            Content of this whas was set
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.EnumBaseConverter`1.CaseHandlingMode">
            <summary>Defines if the enum literal shall be converted to camelCase, PascalCase or kebab-case.</summary>
        </member>
        <member name="M:Telerik.Imaging.Svg.EnumBaseConverter`1.#ctor(Telerik.Imaging.Svg.CaseHandling)">
            <summary>Creates a new instance.</summary>
            <param name="caseHandling">Defines if the value shall be converted to camelCase, PascalCase, lowercase or kebab-case.</param>
        </member>
        <member name="M:Telerik.Imaging.Svg.EnumBaseConverter`1.ConvertFrom(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object)">
            <summary>Attempts to convert the provided value to <typeparamref name="T"/>.</summary>
        </member>
        <member name="M:Telerik.Imaging.Svg.EnumBaseConverter`1.ConvertTo(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object,System.Type)">
            <summary>Attempts to convert the value to the destination type.</summary>
        </member>
        <member name="T:Telerik.Imaging.Svg.ISvgViewPort">
            <summary>
            Provides properties and methods to be implemented by view port elements.
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.ISvgViewPort.ViewBox">
            <summary>
            Gets or sets the viewport of the element.
            </summary>
        </member>
        <member name="T:Telerik.Imaging.Svg.SvgAspectRatio">
            <summary>
            Description of SvgAspectRatio.
            </summary>
        </member>
        <member name="T:Telerik.Imaging.Svg.SvgColourInterpolation">
            <summary>Specifies the color space for gradient interpolations, color animations and alpha compositing.</summary>
            <remarks>When a child element is blended into a background, the value of the ‘color-interpolation’ property on the child determines the type of blending, not the value of the ‘color-interpolation’ on the parent. For gradients which make use of the ‘xlink:href’ attribute to reference another gradient, the gradient uses the ‘color-interpolation’ property value from the gradient element which is directly referenced by the ‘fill’ or ‘stroke’ property. When animating colors, color interpolation is performed according to the value of the ‘color-interpolation’ property on the element being animated.</remarks>
        </member>
        <member name="F:Telerik.Imaging.Svg.SvgColourInterpolation.Auto">
            <summary>Indicates that the user agent can choose either the sRGB or linearRGB spaces for color interpolation. This option indicates that the author doesn't require that color interpolation occur in a particular color space.</summary>
        </member>
        <member name="F:Telerik.Imaging.Svg.SvgColourInterpolation.SRGB">
            <summary>Indicates that color interpolation should occur in the sRGB color space.</summary>
        </member>
        <member name="F:Telerik.Imaging.Svg.SvgColourInterpolation.LinearRGB">
            <summary>Indicates that color interpolation should occur in the linearized RGB color space as described above.</summary>
        </member>
        <member name="F:Telerik.Imaging.Svg.SvgColourInterpolation.Inherit">
            <summary>The value is inherited from the parent element.</summary>
        </member>
        <member name="T:Telerik.Imaging.Svg.SvgCoordinateUnits">
            <summary>
            Defines the various coordinate units certain SVG elements may use.
            </summary>
        </member>
        <member name="F:Telerik.Imaging.Svg.SvgCoordinateUnits.ObjectBoundingBox">
            <summary>
            Indicates that the coordinate system of the owner element is to be used.
            </summary>
        </member>
        <member name="F:Telerik.Imaging.Svg.SvgCoordinateUnits.UserSpaceOnUse">
            <summary>
            Indicates that the coordinate system of the entire document is to be used.
            </summary>
        </member>
        <member name="T:Telerik.Imaging.Svg.SvgFontStretch">
            <summary>The desired amount of condensing or expansion in the glyphs used to render the text.</summary>
        </member>
        <member name="T:Telerik.Imaging.Svg.SvgFontStyle">
            <summary>This is the descriptor for the style of a font and takes the same values as the 'font-style' property, except that a comma-separated list is permitted.</summary>
        </member>
        <member name="F:Telerik.Imaging.Svg.SvgFontStyle.Inherit">
            <summary>The value is inherited from the parent element.</summary>
        </member>
        <member name="F:Telerik.Imaging.Svg.SvgFontStyle.Normal">
            <summary>Specifies a font that is classified as 'normal' in the UA's font database.</summary>
        </member>
        <member name="F:Telerik.Imaging.Svg.SvgFontStyle.Oblique">
            <summary>Specifies a font that is classified as 'oblique' in the UA's font database. Fonts with Oblique, Slanted, or Incline in their names will typically be labeled 'oblique' in the font database. A font that is labeled 'oblique' in the UA's font database may actually have been generated by electronically slanting a normal font.</summary>
        </member>
        <member name="F:Telerik.Imaging.Svg.SvgFontStyle.Italic">
            <summary>Specifies a font that is classified as 'italic' in the UA's font database, or, if that is not available, one labeled 'oblique'. Fonts with Italic, Cursive, or Kursiv in their names will typically be labeled 'italic'</summary>
        </member>
        <member name="F:Telerik.Imaging.Svg.SvgFontStyle.All">
            <summary>Indicates that the font-face supplies all styles (normal, oblique and italic).</summary>
        </member>
        <member name="T:Telerik.Imaging.Svg.SvgFontWeight">
            <summary>The weight of a face relative to others in the same font family.</summary>
        </member>
        <member name="F:Telerik.Imaging.Svg.SvgFontWeight.Inherit">
            <summary>The value is inherited from the parent element.</summary>
        </member>
        <member name="F:Telerik.Imaging.Svg.SvgFontWeight.Normal">
            <summary>Same as <see cref="F:Telerik.Imaging.Svg.SvgFontWeight.W400"/>.</summary>
        </member>
        <member name="F:Telerik.Imaging.Svg.SvgFontWeight.Bold">
            <summary>Same as <see cref="F:Telerik.Imaging.Svg.SvgFontWeight.W700"/>.</summary>
        </member>
        <member name="F:Telerik.Imaging.Svg.SvgFontWeight.Bolder">
            <summary>One font weight darker than the parent element.(do not use font-face.)</summary>
        </member>
        <member name="F:Telerik.Imaging.Svg.SvgFontWeight.Lighter">
            <summary>One font weight lighter than the parent element.(do not use font-face.)</summary>
        </member>
        <member name="F:Telerik.Imaging.Svg.SvgFontWeight.W100">
            <summary></summary>
        </member>
        <member name="F:Telerik.Imaging.Svg.SvgFontWeight.W200">
            <summary></summary>
        </member>
        <member name="F:Telerik.Imaging.Svg.SvgFontWeight.W300">
            <summary></summary>
        </member>
        <member name="F:Telerik.Imaging.Svg.SvgFontWeight.W400">
            <summary>Same as <see cref="F:Telerik.Imaging.Svg.SvgFontWeight.Normal"/>.</summary>
        </member>
        <member name="F:Telerik.Imaging.Svg.SvgFontWeight.W500">
            <summary></summary>
        </member>
        <member name="F:Telerik.Imaging.Svg.SvgFontWeight.W600">
            <summary></summary>
        </member>
        <member name="F:Telerik.Imaging.Svg.SvgFontWeight.W700">
            <summary>Same as <see cref="F:Telerik.Imaging.Svg.SvgFontWeight.Bold"/>.</summary>
        </member>
        <member name="F:Telerik.Imaging.Svg.SvgFontWeight.W800">
            <summary></summary>
        </member>
        <member name="F:Telerik.Imaging.Svg.SvgFontWeight.W900">
            <summary></summary>
        </member>
        <member name="F:Telerik.Imaging.Svg.SvgFontWeight.All">
            <summary>All font weights.</summary>
        </member>
        <member name="T:Telerik.Imaging.Svg.SvgMarkerUnits">
            <summary>Defines the coordinate system for attributes ‘markerWidth’, ‘markerHeight’ and the contents of the ‘marker’.</summary>
        </member>
        <member name="F:Telerik.Imaging.Svg.SvgMarkerUnits.StrokeWidth">
            <summary>If markerUnits="strokeWidth", ‘markerWidth’, ‘markerHeight’ and the contents of the ‘marker’ represent values in a coordinate system which has a single unit equal the size in user units of the current stroke width (see the ‘stroke-width’ property) in place for the graphic object referencing the marker.</summary>
        </member>
        <member name="F:Telerik.Imaging.Svg.SvgMarkerUnits.UserSpaceOnUse">
            <summary>If markerUnits="userSpaceOnUse", ‘markerWidth’, ‘markerHeight’ and the contents of the ‘marker’ represent values in the current user coordinate system in place for the graphic object referencing the marker (i.e., the user coordinate system for the element referencing the ‘marker’ element via a ‘marker’, ‘marker-start’, ‘marker-mid’ or ‘marker-end’ property).</summary>
        </member>
        <member name="T:Telerik.Imaging.Svg.SvgNumberCollection">
            <summary>
            Represents a list of <see cref="T:System.Single"/>.
            </summary>
        </member>
        <member name="T:Telerik.Imaging.Svg.SvgNumberCollectionConverter">
            <summary>
            A class to convert string into <see cref="T:Telerik.Imaging.Svg.SvgNumberCollection"/> instances.
            </summary>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgNumberCollectionConverter.ConvertFrom(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object)">
            <summary>
            Converts the given object to the type of this converter, using the specified context and culture information.
            </summary>
            <param name="context">An <see cref="T:System.ComponentModel.ITypeDescriptorContext"/> that provides a format context.</param>
            <param name="culture">The <see cref="T:System.Globalization.CultureInfo"/> to use as the current culture.</param>
            <param name="value">The <see cref="T:System.Object"/> to convert.</param>
            <returns>
            An <see cref="T:System.Object"/> that represents the converted value.
            </returns>
            <exception cref="T:System.NotSupportedException">The conversion cannot be performed. </exception>
        </member>
        <member name="T:Telerik.Imaging.Svg.SvgOrient">
            <summary>
            Represents an orientation in a Scalable Vector Graphics document.
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgOrient.Angle">
            <summary>
            Gets the value of the unit.
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgOrient.IsAuto">
            <summary>
            Gets the value of the unit.
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgOrient.IsAutoStartReverse">
            <summary>
            If IsAuto is true, indicates if the orientation of a 'marker-start' must be rotated of 180� from the original orientation
            </summary>
            This allows a single arrowhead marker to be defined that can be used for both the start and end of a path, point in the right directions.
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgOrient.Equals(System.Object)">
            <summary>
            Indicates whether this instance and a specified object are equal.
            </summary>
            <param name="obj">Another object to compare to.</param>
            <returns>
            true if <paramref name="obj"/> and this instance are the same type and represent the same value; otherwise, false.
            </returns>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgOrient.op_Implicit(System.Single)~Telerik.Imaging.Svg.SvgOrient">
            <summary>
            Performs an implicit conversion from <see cref="T:System.Single"/> to <see cref="T:Telerik.Imaging.Svg.SvgOrient"/>.
            </summary>
            <param name="value">The value.</param>
            <returns>The result of the conversion.</returns>
        </member>
        <member name="T:Telerik.Imaging.Svg.SvgOverflow">
            <summary>The ‘overflow’ property applies to elements that establish new viewports (e.g., ‘svg’ elements), ‘pattern’ elements and ‘marker’ elements. For all other elements, the property has no effect (i.e., a clipping rectangle is not created).</summary>
            <remarks>
                <para>The ‘overflow’ property has the same parameter values and has the same meaning as defined in CSS2 ([CSS2], section 11.1.1); however, the following additional points apply:</para>
                <para>The ‘overflow’ property applies to elements that establish new viewports (e.g., ‘svg’ elements), ‘pattern’ elements and ‘marker’ elements. For all other elements, the property has no effect (i.e., a clipping rectangle is not created).</para>
                <para>For those elements to which the ‘overflow’ property can apply, if the ‘overflow’ property has the value hidden or scroll, the effect is that a new clipping path in the shape of a rectangle is created. The result is equivalent to defining a ‘clipPath’ element whose content is a ‘rect’ element which defines the equivalent rectangle, and then specifying the 'uri' of this ‘clipPath’ element on the ‘clip-path’ property for the given element.</para>
                <para>If the ‘overflow’ property has a value other than hidden or scroll, the property has no effect (i.e., a clipping rectangle is not created).</para>
                <para>Within SVG content, the value auto is equivalent to the value visible.</para>
                <para>When an outermost svg element is embedded inline within a parent XML grammar which uses CSS layout ([CSS2], chapter 9) or XSL formatting [XSL], if the ‘overflow’ property has the value hidden or scroll, then the user agent will establish an initial clipping path equal to the bounds of the initial viewport; otherwise, the initial clipping path is set according to the clipping rules as defined in CSS2 ([CSS2], section 11.1.1).</para>
                <para>When an outermost svg element is stand-alone or embedded inline within a parent XML grammar which does not use CSS layout or XSL formatting, the ‘overflow’ property on the outermost svg element is ignored for the purposes of visual rendering and the initial clipping path is set to the bounds of the initial viewport.</para>
                <para>The initial value for ‘overflow’ as defined in [CSS2-overflow] is 'visible', and this applies also to the root ‘svg’ element; however, for child elements of an SVG document, SVG's user agent style sheet overrides this initial value and sets the ‘overflow’ property on elements that establish new viewports (e.g., ‘svg’ elements), ‘pattern’ elements and ‘marker’ elements to the value 'hidden'.</para>
                <para>As a result of the above, the default behavior of SVG user agents is to establish a clipping path to the bounds of the initial viewport and to establish a new clipping path for each element which establishes a new viewport and each ‘pattern’ and ‘marker’ element.</para>
            </remarks>
        </member>
        <member name="F:Telerik.Imaging.Svg.SvgOverflow.Hidden">
            <summary>Overflow is not rendered.</summary>
        </member>
        <member name="F:Telerik.Imaging.Svg.SvgOverflow.Inherit">
            <summary>The value is inherited from the parent element.</summary>
        </member>
        <member name="F:Telerik.Imaging.Svg.SvgOverflow.Auto">
            <summary>The overflow is rendered - same as "visible".</summary>
        </member>
        <member name="F:Telerik.Imaging.Svg.SvgOverflow.Visible">
            <summary>Overflow is rendered.</summary>
        </member>
        <member name="F:Telerik.Imaging.Svg.SvgOverflow.Scroll">
            <summary>Overflow causes a scrollbar to appear (horizontal, vertical or both).</summary>
        </member>
        <member name="T:Telerik.Imaging.Svg.SvgPointCollection">
            <summary>
            Represents a list of <see cref="T:Telerik.Imaging.Svg.SvgUnit"/> used with the <see cref="T:Telerik.Imaging.Svg.SvgPolyline"/> and <see cref="T:Telerik.Imaging.Svg.SvgPolygon"/>.
            </summary>
        </member>
        <member name="T:Telerik.Imaging.Svg.SvgPointCollectionConverter">
            <summary>
            A class to convert string into <see cref="T:Telerik.Imaging.Svg.SvgPointCollection"/> instances.
            </summary>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgPointCollectionConverter.ConvertFrom(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object)">
            <summary>
            Converts the given object to the type of this converter, using the specified context and culture information.
            </summary>
            <param name="context">An <see cref="T:System.ComponentModel.ITypeDescriptorContext"/> that provides a format context.</param>
            <param name="culture">The <see cref="T:System.Globalization.CultureInfo"/> to use as the current culture.</param>
            <param name="value">The <see cref="T:System.Object"/> to convert.</param>
            <returns>
            An <see cref="T:System.Object"/> that represents the converted value.
            </returns>
            <exception cref="T:System.NotSupportedException">The conversion cannot be performed. </exception>
        </member>
        <member name="T:Telerik.Imaging.Svg.SvgTextDecoration">
            <summary>This property describes decorations that are added to the text of an element. Conforming SVG Viewers are not required to support the blink value.</summary>
        </member>
        <member name="F:Telerik.Imaging.Svg.SvgTextDecoration.Inherit">
            <summary>The value is inherited from the parent element.</summary>
        </member>
        <member name="F:Telerik.Imaging.Svg.SvgTextDecoration.None">
            <summary>The text is not decorated</summary>
        </member>
        <member name="F:Telerik.Imaging.Svg.SvgTextDecoration.Underline">
            <summary>The text is underlined.</summary>
        </member>
        <member name="F:Telerik.Imaging.Svg.SvgTextDecoration.Overline">
            <summary>The text is overlined.</summary>
        </member>
        <member name="F:Telerik.Imaging.Svg.SvgTextDecoration.LineThrough">
            <summary>The text is struck through.</summary>
        </member>
        <member name="F:Telerik.Imaging.Svg.SvgTextDecoration.Blink">
            <summary>The text will blink.</summary>
        </member>
        <member name="T:Telerik.Imaging.Svg.SvgTextLengthAdjust">
            <summary>Indicates the type of adjustments which the user agent shall make to make the rendered length of the text match the value specified on the ‘textLength’ attribute.</summary>
            <remarks>
                <para>The user agent is required to achieve correct start and end positions for the text strings, but the locations of intermediate glyphs are not predictable because user agents might employ advanced algorithms to stretch or compress text strings in order to balance correct start and end positioning with optimal typography.</para>
                <para>Note that, for a text string that contains n characters, the adjustments to the advance values often occur only for n−1 characters (see description of attribute ‘textLength’), whereas stretching or compressing of the glyphs will be applied to all n characters.</para>
            </remarks>
        </member>
        <member name="F:Telerik.Imaging.Svg.SvgTextLengthAdjust.Spacing">
            <summary>Indicates that only the advance values are adjusted. The glyphs themselves are not stretched or compressed.</summary>
        </member>
        <member name="F:Telerik.Imaging.Svg.SvgTextLengthAdjust.SpacingAndGlyphs">
            <summary>Indicates that the advance values are adjusted and the glyphs themselves stretched or compressed in one axis (i.e., a direction parallel to the inline-progression-direction).</summary>
        </member>
        <member name="T:Telerik.Imaging.Svg.SvgTextPathMethod">
            <summary>Indicates the method by which text should be rendered along the path.</summary>
        </member>
        <member name="F:Telerik.Imaging.Svg.SvgTextPathMethod.Align">
            <summary>Indicates that the glyphs should be rendered using simple 2x3 transformations such that there is no stretching/warping of the glyphs. Typically, supplemental rotation, scaling and translation transformations are done for each glyph to be rendered. As a result, with align, fonts where the glyphs are designed to be connected (e.g., cursive fonts), the connections may not align properly when text is rendered along a path.</summary>
        </member>
        <member name="F:Telerik.Imaging.Svg.SvgTextPathMethod.Stretch">
            <summary>Indicates that the glyph outlines will be converted into paths, and then all end points and control points will be adjusted to be along the perpendicular vectors from the path, thereby stretching and possibly warping the glyphs. With this approach, connected glyphs, such as in cursive scripts, will maintain their connections.</summary>
        </member>
        <member name="T:Telerik.Imaging.Svg.SvgTextPathSpacing">
            <summary>Indicates how the user agent should determine the spacing between glyphs that are to be rendered along a path.</summary>
        </member>
        <member name="F:Telerik.Imaging.Svg.SvgTextPathSpacing.Exact">
            <summary>Indicates that the glyphs should be rendered exactly according to the spacing rules as specified in Text on a path layout rules.</summary>
        </member>
        <member name="F:Telerik.Imaging.Svg.SvgTextPathSpacing.Auto">
            <summary>Indicates that the user agent should use text-on-a-path layout algorithms to adjust the spacing between glyphs in order to achieve visually appealing results.</summary>
        </member>
        <member name="T:Telerik.Imaging.Svg.SvgTextTransformation">
            <summary>This property describes transformations that are added to the text of an element.</summary>
        </member>
        <member name="F:Telerik.Imaging.Svg.SvgTextTransformation.Inherit">
            <summary>The value is inherited from the parent element.</summary>
        </member>
        <member name="F:Telerik.Imaging.Svg.SvgTextTransformation.None">
            <summary>The text is not transformed.</summary>
        </member>
        <member name="F:Telerik.Imaging.Svg.SvgTextTransformation.Capitalize">
            <summary>First letter of each word of the text is converted to uppercase.</summary>
        </member>
        <member name="F:Telerik.Imaging.Svg.SvgTextTransformation.Uppercase">
            <summary>The text is converted to uppercase.</summary>
        </member>
        <member name="F:Telerik.Imaging.Svg.SvgTextTransformation.Lowercase">
            <summary>The text is converted to lowercase.</summary>
        </member>
        <member name="T:Telerik.Imaging.Svg.SvgUnit">
            <summary>
            Represents a unit in an Scalable Vector Graphics document.
            </summary>
        </member>
        <member name="F:Telerik.Imaging.Svg.SvgUnit.Empty">
            <summary>
            Gets and empty <see cref="T:Telerik.Imaging.Svg.SvgUnit"/>.
            </summary>
        </member>
        <member name="F:Telerik.Imaging.Svg.SvgUnit.None">
            <summary>
            Gets an <see cref="T:Telerik.Imaging.Svg.SvgUnit"/> with a value of none.
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgUnit.IsEmpty">
            <summary>
            Gets a value to determine whether the unit is empty.
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgUnit.IsNone">
            <summary>
            Gets whether this unit is none.
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgUnit.Value">
            <summary>
            Gets the value of the unit.
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgUnit.Type">
            <summary>
            Gets the <see cref="T:Telerik.Imaging.Svg.SvgUnitType"/> of unit.
            </summary>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgUnit.#ctor(Telerik.Imaging.Svg.SvgUnitType,System.Double)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Imaging.Svg.SvgUnit"/> struct.
            </summary>
            <param name="type">The type.</param>
            <param name="value">The value.</param>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgUnit.#ctor(System.Double)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Imaging.Svg.SvgUnit"/> struct.
            </summary>
            <param name="value">The value.</param>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgUnit.ToDeviceValue(Telerik.Imaging.Svg.ISvgRenderer,Telerik.Imaging.Svg.UnitRenderingType,Telerik.Imaging.Svg.SvgElement)">
            <summary>
            Converts the current unit to one that can be used at render time.
            </summary>
            <returns>The representation of the current unit in a device value (usually pixels).</returns>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgUnit.ToPercentage">
            <summary>
            Converts the current unit to a percentage, if applicable.
            </summary>
            <returns>An <see cref="T:Telerik.Imaging.Svg.SvgUnit"/> of type <see cref="F:Telerik.Imaging.Svg.SvgUnitType.Percentage"/>.</returns>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgUnit.op_Implicit(Telerik.Imaging.Svg.SvgUnit)~System.Double">
            <summary>
            Performs an implicit conversion from <see cref="T:Telerik.Imaging.Svg.SvgUnit"/> to <see cref="T:System.Double"/>.
            </summary>
            <param name="value">The value.</param>
            <returns>The result of the conversion.</returns>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgUnit.op_Implicit(System.Double)~Telerik.Imaging.Svg.SvgUnit">
            <summary>
            Performs an implicit conversion from <see cref="T:System.Double"/> to <see cref="T:Telerik.Imaging.Svg.SvgUnit"/>.
            </summary>
            <param name="value">The value.</param>
            <returns>The result of the conversion.</returns>
        </member>
        <member name="T:Telerik.Imaging.Svg.SvgUnitCollection">
            <summary>
            Represents a list of <see cref="T:Telerik.Imaging.Svg.SvgUnit"/>.
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgUnitCollection.StringForEmptyValue">
            <summary>
            Sets <see cref="F:Telerik.Imaging.Svg.SvgUnitCollection.None"/> or <see cref="F:Telerik.Imaging.Svg.SvgUnitCollection.Inherit"/> if needed.
            </summary>
        </member>
        <member name="T:Telerik.Imaging.Svg.SvgUnitCollectionConverter">
            <summary>
            A class to convert string into <see cref="T:Telerik.Imaging.Svg.SvgUnitCollection"/> instances.
            </summary>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgUnitCollectionConverter.ConvertFrom(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object)">
            <summary>
            Converts the given object to the type of this converter, using the specified context and culture information.
            </summary>
            <param name="context">An <see cref="T:System.ComponentModel.ITypeDescriptorContext"/> that provides a format context.</param>
            <param name="culture">The <see cref="T:System.Globalization.CultureInfo"/> to use as the current culture.</param>
            <param name="value">The <see cref="T:System.Object"/> to convert.</param>
            <returns>
            An <see cref="T:System.Object"/> that represents the converted value.
            </returns>
            <exception cref="T:System.NotSupportedException">The conversion cannot be performed. </exception>
        </member>
        <member name="T:Telerik.Imaging.Svg.SvgUnitType">
            <summary>
            Defines the various types of unit an <see cref="T:Telerik.Imaging.Svg.SvgUnit"/> can be.
            </summary>
        </member>
        <member name="F:Telerik.Imaging.Svg.SvgUnitType.None">
            <summary>
            Indicates that the unit holds no value.
            </summary>
        </member>
        <member name="F:Telerik.Imaging.Svg.SvgUnitType.Pixel">
            <summary>
            Indicates that the unit is in pixels.
            </summary>
        </member>
        <member name="F:Telerik.Imaging.Svg.SvgUnitType.Em">
            <summary>
            Indicates that the unit is equal to the pt size of the current font.
            </summary>
        </member>
        <member name="F:Telerik.Imaging.Svg.SvgUnitType.Ex">
            <summary>
            Indicates that the unit is equal to the x-height of the current font.
            </summary>
        </member>
        <member name="F:Telerik.Imaging.Svg.SvgUnitType.Percentage">
            <summary>
            Indicates that the unit is a percentage.
            </summary>
        </member>
        <member name="F:Telerik.Imaging.Svg.SvgUnitType.User">
            <summary>
            Indicates that the unit has no unit identifier and is a value in the current user coordinate system.
            </summary>
        </member>
        <member name="F:Telerik.Imaging.Svg.SvgUnitType.Inch">
            <summary>
            Indicates the the unit is in inches.
            </summary>
        </member>
        <member name="F:Telerik.Imaging.Svg.SvgUnitType.Centimeter">
            <summary>
            Indicates that the unit is in centimeters.
            </summary>
        </member>
        <member name="F:Telerik.Imaging.Svg.SvgUnitType.Millimeter">
            <summary>
            Indicates that the unit is in millimeters.
            </summary>
        </member>
        <member name="F:Telerik.Imaging.Svg.SvgUnitType.Pica">
            <summary>
            Indicates that the unit is in picas.
            </summary>
        </member>
        <member name="F:Telerik.Imaging.Svg.SvgUnitType.Point">
            <summary>
            Indicates that the unit is in points, the smallest unit of measure, being a subdivision of the larger <see cref="F:Telerik.Imaging.Svg.SvgUnitType.Pica"/>. There are 12 points in the <see cref="F:Telerik.Imaging.Svg.SvgUnitType.Pica"/>.
            </summary>
        </member>
        <member name="T:Telerik.Imaging.Svg.SvgViewBox">
            <summary>
            It is often desirable to specify that a given set of graphics stretch to fit a particular container element. The viewBox attribute provides this capability.
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgViewBox.MinX">
            <summary>
            Gets or sets the position where the viewport starts horizontally.
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgViewBox.MinY">
            <summary>
            Gets or sets the position where the viewport starts vertically.
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgViewBox.Width">
            <summary>
            Gets or sets the width of the viewport.
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgViewBox.Height">
            <summary>
            Gets or sets the height of the viewport.
            </summary>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgViewBox.op_Implicit(Telerik.Imaging.Svg.SvgViewBox)~Telerik.Documents.Primitives.Rect">
            <summary>
            Performs an implicit conversion from <see cref="T:Telerik.Imaging.Svg.SvgViewBox"/> to <see cref="T:Telerik.Documents.Primitives.Rect"/>.
            </summary>
            <param name="value">The value.</param>
            <returns>The result of the conversion.</returns>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgViewBox.op_Implicit(Telerik.Documents.Primitives.Rect)~Telerik.Imaging.Svg.SvgViewBox">
            <summary>
            Performs an implicit conversion from <see cref="T:Telerik.Documents.Primitives.Rect"/> to <see cref="T:Telerik.Imaging.Svg.SvgViewBox"/>.
            </summary>
            <param name="value">The value.</param>
            <returns>The result of the conversion.</returns>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgViewBox.#ctor(System.Double,System.Double,System.Double,System.Double)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Imaging.Svg.SvgViewBox"/> struct.
            </summary>
            <param name="minX">The min X.</param>
            <param name="minY">The min Y.</param>
            <param name="width">The width.</param>
            <param name="height">The height.</param>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgViewBoxConverter.ConvertFrom(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object)">
            <summary>
            Converts the given object to the type of this converter, using the specified context and culture information.
            </summary>
            <param name="context">An <see cref="T:System.ComponentModel.ITypeDescriptorContext"/> that provides a format context.</param>
            <param name="culture">The <see cref="T:System.Globalization.CultureInfo"/> to use as the current culture.</param>
            <param name="value">The <see cref="T:System.Object"/> to convert.</param>
            <returns>
            An <see cref="T:System.Object"/> that represents the converted value.
            </returns>
            <exception cref="T:System.NotSupportedException">The conversion cannot be performed. </exception>
        </member>
        <member name="T:Telerik.Imaging.Svg.SvgDefinitionList">
            <summary>
            Represents a list of re-usable SVG components.
            </summary>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgDefinitionList.Render(Telerik.Imaging.Svg.ISvgRenderer)">
            <summary>
            Renders the <see cref="T:Telerik.Imaging.Svg.SvgElement"/> and contents to the specified <see cref="T:Telerik.Imaging.Svg.ISvgRenderer"/> object.
            </summary>
            <param name="renderer">The <see cref="T:Telerik.Imaging.Svg.ISvgRenderer"/> object to render to.</param>
        </member>
        <member name="T:Telerik.Imaging.Svg.SvgDocumentMetadata">
            <summary>
            Represents a list of re-usable SVG components.
            </summary>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgDocumentMetadata.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Imaging.Svg.SvgDocumentMetadata"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgDocumentMetadata.Render(Telerik.Imaging.Svg.ISvgRenderer)">
            <summary>
            Renders the <see cref="T:Telerik.Imaging.Svg.SvgElement"/> and contents to the specified <see cref="T:Telerik.Imaging.Svg.ISvgRenderer"/> object.
            </summary>
            <param name="renderer">The <see cref="T:Telerik.Imaging.Svg.ISvgRenderer"/> object to render to.</param>
        </member>
        <member name="T:Telerik.Imaging.Svg.SvgFragment">
            <summary>
            An <see cref="T:Telerik.Imaging.Svg.SvgFragment"/> represents an SVG fragment that can be the root element or an embedded fragment of an SVG document.
            </summary>
        </member>
        <member name="F:Telerik.Imaging.Svg.SvgFragment.Namespace">
            <summary>
            Gets the SVG namespace string.
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgFragment.X">
            <summary>
            Gets or sets the position where the left point of the svg should start.
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgFragment.Y">
            <summary>
            Gets or sets the position where the top point of the svg should start.
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgFragment.Width">
            <summary>
            Gets or sets the width of the fragment.
            </summary>
            <value>The width.</value>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgFragment.Height">
            <summary>
            Gets or sets the height of the fragment.
            </summary>
            <value>The height.</value>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgFragment.ViewBox">
            <summary>
            Gets or sets the viewport of the element.
            </summary>
            <value></value>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgFragment.AspectRatio">
            <summary>
            Gets or sets the aspect of the viewport.
            </summary>
            <value></value>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgFragment.FontSize">
            <summary>
            Refers to the size of the font from baseline to baseline when multiple lines of text are set solid in a multiline layout environment.
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgFragment.FontFamily">
            <summary>
            Indicates which font family is to be used to render the text.
            </summary>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgFragment.PushTransforms(Telerik.Imaging.Svg.ISvgRenderer)">
            <summary>
            Applies the required transforms to <see cref="T:Telerik.Imaging.Svg.ISvgRenderer"/>.
            </summary>
            <param name="renderer">The <see cref="T:Telerik.Imaging.Svg.ISvgRenderer"/> to be transformed.</param>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgFragment.Bounds">
            <summary>
            Gets the bounds of the svg element.
            </summary>
            <value>The bounds.</value>
        </member>
        <member name="T:Telerik.Imaging.Svg.SvgGroup">
            <summary>
            An element used to group SVG shapes.
            </summary>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgGroup.AddMarkers">
            <summary>
            If the group has marker attributes defined, add them to all children
            that are able to display markers. Only done once.
            </summary>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgGroup.Render(Telerik.Imaging.Svg.ISvgRenderer)">
            <summary>
            Add group markers to children before rendering them.
            This is only done on first rendering.
            </summary>
            <param name="renderer">The <see cref="T:Telerik.Imaging.Svg.ISvgRenderer"/> to render the child <see cref="T:Telerik.Imaging.Svg.SvgElement"/>s to.</param>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgGroup.Path(Telerik.Imaging.Svg.ISvgRenderer)">
            <summary>
            Gets the <see cref="T:Telerik.Windows.Documents.Fixed.Model.Graphics.PathGeometry"/> for this element.
            </summary>
            <value></value>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgGroup.Bounds">
            <summary>
            Gets the bounds of the element.
            </summary>
            <value>The bounds.</value>
        </member>
        <member name="T:Telerik.Imaging.Svg.SvgImage">
            <summary>
            Represents and SVG image
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgImage.Location">
            <summary>
            Gets an <see cref="T:Telerik.Imaging.Svg.SvgPoint"/> representing the top left point of the rectangle.
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgImage.AspectRatio">
            <summary>
            Gets or sets the aspect of the viewport.
            </summary>
            <value></value>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgImage.Bounds">
            <summary>
            Gets the bounds of the element.
            </summary>
            <value>The bounds.</value>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgImage.Path(Telerik.Imaging.Svg.ISvgRenderer)">
            <summary>
            Gets the <see cref="T:Telerik.Windows.Documents.Fixed.Model.Graphics.PathGeometry"/> for this element.
            </summary>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgImage.Render(Telerik.Imaging.Svg.ISvgRenderer)">
            <summary>
            Renders the <see cref="T:Telerik.Imaging.Svg.SvgElement"/>.
            </summary>
        </member>
        <member name="T:Telerik.Imaging.Svg.SvgSwitch">
            <summary>
            The 'switch' element evaluates the 'requiredFeatures', 'requiredExtensions' and 'systemLanguage' attributes on its direct child elements in order, and then processes and renders the first child for which these attributes evaluate to true
            </summary>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgSwitch.Path(Telerik.Imaging.Svg.ISvgRenderer)">
            <summary>
            Gets the <see cref="T:Telerik.Windows.Documents.Fixed.Model.Graphics.PathGeometry"/> for this element.
            </summary>
            <value></value>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgSwitch.Bounds">
            <summary>
            Gets the bounds of the element.
            </summary>
            <value>The bounds.</value>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgSwitch.Render(Telerik.Imaging.Svg.ISvgRenderer)">
            <summary>
            Renders the <see cref="T:Telerik.Imaging.Svg.SvgElement"/>.
            </summary>
            <param name="renderer">The renderer.</param>
        </member>
        <member name="T:Telerik.Imaging.Svg.SvgSymbol">
            <summary>
            An element used to group SVG shapes.
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgSymbol.ViewBox">
            <summary>
            Gets or sets the viewport of the element.
            </summary>
            <value></value>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgSymbol.AspectRatio">
            <summary>
            Gets or sets the aspect of the viewport.
            </summary>
            <value></value>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgSymbol.Path(Telerik.Imaging.Svg.ISvgRenderer)">
            <summary>
            Gets the <see cref="T:Telerik.Windows.Documents.Fixed.Model.Graphics.PathGeometry"/> for this element.
            </summary>
            <value></value>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgSymbol.Bounds">
            <summary>
            Gets the bounds of the element.
            </summary>
            <value>The bounds.</value>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgSymbol.PushTransforms(Telerik.Imaging.Svg.ISvgRenderer)">
            <summary>
            Applies the required transforms to <see cref="T:Telerik.Imaging.Svg.ISvgRenderer"/>.
            </summary>
            <param name="renderer">The <see cref="T:Telerik.Imaging.Svg.ISvgRenderer"/> to be transformed.</param>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgUse.HasRecursiveReference">
            <summary>
            Checks for any direct or indirect recursions in referenced elements, 
            including recursions via groups.
            </summary>
            <returns>True if any recursions are found.</returns>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgUse.PushTransforms(Telerik.Imaging.Svg.ISvgRenderer)">
            <summary>
            Applies the required transforms to <see cref="T:Telerik.Imaging.Svg.ISvgRenderer"/>.
            </summary>
            <param name="renderer">The <see cref="T:Telerik.Imaging.Svg.ISvgRenderer"/> to be transformed.</param>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgUse.Location">
            <summary>
            Gets an <see cref="T:Telerik.Imaging.Svg.SvgPoint"/> representing the top left point of the rectangle.
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgUse.Bounds">
            <summary>
            Gets the bounds of the element.
            </summary>
            <value>The bounds.</value>
        </member>
        <member name="T:Telerik.Imaging.Svg.ElementInfo">
            <summary>
            Contains information about a type inheriting from <see cref="T:Telerik.Imaging.Svg.SvgElement"/>.
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.ElementInfo.ElementName">
            <summary>
            Gets the SVG name of the <see cref="T:Telerik.Imaging.Svg.SvgElement"/>.
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.ElementInfo.ElementType">
            <summary>
            Gets the <see cref="T:System.Type"/> of the <see cref="T:Telerik.Imaging.Svg.SvgElement"/> subclass.
            </summary>
        </member>
        <member name="M:Telerik.Imaging.Svg.ElementInfo.#ctor(System.String,System.Type)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Imaging.Svg.ElementInfo"/> struct.
            </summary>
            <param name="elementName">Name of the element.</param>
            <param name="elementType">Type of the element.</param>
        </member>
        <member name="M:Telerik.Imaging.Svg.ElementInfo.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Imaging.Svg.ElementInfo"/> class.
            </summary>
        </member>
        <member name="T:Telerik.Imaging.Svg.SvgForeignObject">
            <summary>
            The 'foreignObject' element allows for inclusion of a foreign namespace which has its graphical content drawn by a different user agent
            </summary>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgForeignObject.Path(Telerik.Imaging.Svg.ISvgRenderer)">
            <summary>
            Gets the <see cref="T:Telerik.Windows.Documents.Fixed.Model.Graphics.PathGeometry"/> for this element.
            </summary>
            <value></value>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgForeignObject.Bounds">
            <summary>
            Gets the bounds of the element.
            </summary>
            <value>The bounds.</value>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgForeignObject.Render(Telerik.Imaging.Svg.ISvgRenderer)">
            <summary>
            Renders the <see cref="T:Telerik.Imaging.Svg.SvgElement"/>.
            </summary>
            <param name="renderer">The renderer.</param>
        </member>
        <member name="M:Telerik.Imaging.Svg.External.ExCss.Specification.IsNonPrintable(System.Char)">
            The maximum allowed codepoint (defined in Unicode).
        </member>
        <member name="M:Telerik.Imaging.Svg.External.ExCss.HtmlColor.ToCss(System.Boolean)">
            <summary>
            Return the shortest form possible
            </summary>
        </member>
        <member name="T:Telerik.Imaging.Svg.External.ExCss.TermList.TermSeparator">
            <summary>
            exposed enumeration for the adding of separators into term lists
            </summary>
        </member>
        <member name="T:Telerik.Imaging.Svg.SvgColourMatrix">
            <summary>
            Note: this is not used in calculations to bitmap - used only to allow for svg xml output
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgColourMatrix.Type">
            <summary>
            matrix | saturate | hueRotate | luminanceToAlpha
            Indicates the type of matrix operation. The keyword 'matrix' indicates that a full 5x4 matrix of values will be provided. The other keywords represent convenience shortcuts to allow commonly used color operations to be performed without specifying a complete matrix. If attribute 'type' is not specified, then the effect is as if a value of matrix were specified.
            Note: this is not used in calculations to bitmap - used only to allow for svg xml output
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgColourMatrix.Values">
            <summary>
            list of numbers
            The contents of ?values? depends on the value of attribute ?type?: 
            Note: this is not used in calculations to bitmap - used only to allow for svg xml output
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgGaussianBlur.StdDeviation">
            <summary>
            Gets or sets the radius of the blur (only allows for one value - not the two specified in the SVG Spec)
            </summary>
        </member>
        <member name="T:Telerik.Imaging.Svg.SvgOffset">
            <summary>
            Note: this is not used in calculations to bitmap - used only to allow for svg xml output
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgOffset.Dx">
            <summary>
            The amount to offset the input graphic along the x-axis. The offset amount is expressed in the coordinate system established by attribute 'primitiveUnits' on the 'filter' element.
            If the attribute is not specified, then the effect is as if a value of 0 were specified.
            Note: this is not used in calculations to bitmap - used only to allow for svg xml output
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgOffset.Dy">
            <summary>
            The amount to offset the input graphic along the y-axis. The offset amount is expressed in the coordinate system established by attribute 'primitiveUnits' on the 'filter' element.
            If the attribute is not specified, then the effect is as if a value of 0 were specified.
            Note: this is not used in calculations to bitmap - used only to allow for svg xml output
            </summary>
        </member>
        <member name="T:Telerik.Imaging.Svg.SvgFilter">
            <summary>
            A filter effect consists of a series of graphics operations that are applied to a given source graphic to produce a modified graphical result.
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgFilter.X">
            <summary>
            Gets or sets the position where the left point of the filter.
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgFilter.Y">
            <summary>
            Gets or sets the position where the top point of the filter.
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgFilter.Width">
            <summary>
            Gets or sets the width of the resulting filter graphic.
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgFilter.Height">
            <summary>
            Gets or sets the height of the resulting filter graphic.
            </summary>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgFilter.Render(Telerik.Imaging.Svg.ISvgRenderer)">
            <summary>
            Renders the <see cref="T:Telerik.Imaging.Svg.SvgElement"/> and contents to the specified <see cref="T:Telerik.Imaging.Svg.ISvgRenderer"/> object.
            </summary>
            <param name="renderer">The <see cref="T:Telerik.Imaging.Svg.ISvgRenderer"/> object to render to.</param>
        </member>
        <member name="T:Telerik.Imaging.Svg.FontStyle">
            <devdoc>
               <para>
                  Specifies style information applied to
                  text.
               </para>
            </devdoc>
        </member>
        <member name="F:Telerik.Imaging.Svg.FontStyle.Regular">
            <devdoc>
               Normal text.
            </devdoc>
        </member>
        <member name="F:Telerik.Imaging.Svg.FontStyle.Bold">
            <devdoc>
               Bold text.
            </devdoc>
        </member>
        <member name="F:Telerik.Imaging.Svg.FontStyle.Italic">
            <devdoc>
               Italic text.
            </devdoc>
        </member>
        <member name="F:Telerik.Imaging.Svg.FontStyle.Underline">
            <devdoc>
               Underlined text.
            </devdoc>
        </member>
        <member name="F:Telerik.Imaging.Svg.FontStyle.Strikeout">
            <devdoc>
               Text with a line through the middle.
            </devdoc>
        </member>
        <member name="T:Telerik.Imaging.Svg.ISvgDescriptiveElement">
            <summary>This interface mostly indicates that a node is not to be drawn when rendering the SVG.</summary>
        </member>
        <member name="M:Telerik.Imaging.Svg.ISvgNode.DeepCopy">
            <summary>
            Create a deep copy of this <see cref="T:Telerik.Imaging.Svg.ISvgNode"/>.
            </summary>
            <returns>A deep copy of this <see cref="T:Telerik.Imaging.Svg.ISvgNode"/></returns>
        </member>
        <member name="T:Telerik.Imaging.Svg.MouseArg">
            <summary>
            Represents the state of the mouse at the moment the event occured.
            </summary>
        </member>
        <member name="F:Telerik.Imaging.Svg.MouseArg.Button">
            <summary>
            1 = left, 2 = middle, 3 = right
            </summary>
        </member>
        <member name="F:Telerik.Imaging.Svg.MouseArg.ClickCount">
            <summary>
            Amount of mouse clicks, e.g. 2 for double click
            </summary>
        </member>
        <member name="F:Telerik.Imaging.Svg.MouseArg.AltKey">
            <summary>
            Alt modifier key pressed
            </summary>
        </member>
        <member name="F:Telerik.Imaging.Svg.MouseArg.ShiftKey">
            <summary>
            Shift modifier key pressed
            </summary>
        </member>
        <member name="F:Telerik.Imaging.Svg.MouseArg.CtrlKey">
            <summary>
            Control modifier key pressed
            </summary>
        </member>
        <member name="F:Telerik.Imaging.Svg.MouseScrollArg.AltKey">
            <summary>
            Alt modifier key pressed
            </summary>
        </member>
        <member name="F:Telerik.Imaging.Svg.MouseScrollArg.ShiftKey">
            <summary>
            Shift modifier key pressed
            </summary>
        </member>
        <member name="F:Telerik.Imaging.Svg.MouseScrollArg.CtrlKey">
            <summary>
            Control modifier key pressed
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.NonSvgElement.Name">
            <summary>
            Publish the element name to be able to differentiate non-svg elements.
            </summary>
        </member>
        <member name="T:Telerik.Imaging.Svg.IColorBlend">
            <devdoc>
               Defines arrays of colors and positions used
               for interpolating color blending in a gradient.
            </devdoc>
        </member>
        <member name="P:Telerik.Imaging.Svg.IColorBlend.Colors">
            <devdoc>
               Represents an array of colors.
            </devdoc>
        </member>
        <member name="P:Telerik.Imaging.Svg.IColorBlend.Positions">
            <devdoc>
               Represents the positions along a gradient
               line.
            </devdoc>
        </member>
        <member name="T:Telerik.Imaging.Svg.ISvgStylable">
            <summary>
            Defines the methods and properties required for an SVG element to be styled.
            </summary>
        </member>
        <member name="M:Telerik.Imaging.Svg.Line.Intersection(Telerik.Imaging.Svg.Line)">
            <remarks>http://community.topcoder.com/tc?module=Static&amp;d1=tutorials&amp;d2=geometry2</remarks>
        </member>
        <member name="T:Telerik.Imaging.Svg.SvgColorConverter">
            <summary>
            Converts string representations of colours into <see cref="T:System.Drawing.Color"/> objects.
            </summary>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgColorConverter.ConvertFrom(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object)">
            <summary>
            Converts the given object to the converter's native type.
            </summary>
            <param name="context">A <see cref="T:System.ComponentModel.TypeDescriptor"/> that provides a format context. You can use this object to get additional information about the environment from which this converter is being invoked.</param>
            <param name="culture">A <see cref="T:System.Globalization.CultureInfo"/> that specifies the culture to represent the color.</param>
            <param name="value">The object to convert.</param>
            <returns>
            An <see cref="T:System.Object"/> representing the converted value.
            </returns>
            <exception cref="T:System.ArgumentException">The conversion cannot be performed.</exception>
            <PermissionSet>
                <IPermission class="System.Security.Permissions.FileIOPermission, mscorlib, Version=2.0.3600.0, Culture=neutral, PublicKeyToken=b77a5c561934e089" version="1" Unrestricted="true"/>
            </PermissionSet>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgColorConverter.Hsl2Rgb(System.Double,System.Double,System.Double)">
            <summary>
            Converts HSL color (with HSL specified from 0 to 1) to RGB color.
            Taken from http://www.geekymonkey.com/Programming/CSharp/RGB2HSL_HSL2RGB.htm
            </summary>
            <param name="h"></param>
            <param name="sl"></param>
            <param name="l"></param>
            <returns></returns>
        </member>
        <member name="T:Telerik.Imaging.Svg.SvgDeferredPaintServer">
            <summary>
            A wrapper for a paint server which isn't defined currently in the parse process,
            but should be defined by the time the image needs to render.
            </summary>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgDeferredPaintServer.#ctor(System.String)">
            <summary>
            Initializes new instance of <see cref="T:Telerik.Imaging.Svg.SvgDeferredPaintServer"/> class.
            </summary>
            <param name="id">&lt;FuncIRI&gt;, &lt;IRI&gt; or &quot;currentColor&quot;.</param>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgDeferredPaintServer.#ctor(System.String,Telerik.Imaging.Svg.SvgPaintServer)">
            <summary>
            Initializes new instance of <see cref="T:Telerik.Imaging.Svg.SvgDeferredPaintServer"/> class.
            </summary>
            <param name="id">&lt;FuncIRI&gt;, &lt;IRI&gt; or &quot;currentColor&quot;.</param>
            <param name="fallbackServer">&quot;none&quot;, &quot;currentColor&quot; or <see cref="T:Telerik.Imaging.Svg.SvgColorServer"/> server.</param>
        </member>
        <member name="T:Telerik.Imaging.Svg.SvgDeferredPaintServerFactory">
            <summary>
            Factory class for &lt;IRI&gt;.
            </summary>
        </member>
        <member name="T:Telerik.Imaging.Svg.SvgGradientServer">
            <summary>
            Provides the base class for all paint servers that wish to render a gradient.
            </summary>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgGradientServer.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Imaging.Svg.SvgGradientServer"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgGradientServer.AddElement(Telerik.Imaging.Svg.SvgElement,System.Int32)">
            <summary>
            Called by the underlying <see cref="T:Telerik.Imaging.Svg.SvgElement"/> when an element has been added to the
            'Children' collection.
            </summary>
            <param name="child">The <see cref="T:Telerik.Imaging.Svg.SvgElement"/> that has been added.</param>
            <param name="index">An <see cref="T:System.Int32"/> representing the index where the element was added to the collection.</param>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgGradientServer.RemoveElement(Telerik.Imaging.Svg.SvgElement)">
            <summary>
            Called by the underlying <see cref="T:Telerik.Imaging.Svg.SvgElement"/> when an element has been removed from the
            'Children' collection.
            </summary>
            <param name="child">The <see cref="T:Telerik.Imaging.Svg.SvgElement"/> that has been removed.</param>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgGradientServer.Stops">
            <summary>
            Gets the ramp of colors to use on a gradient.
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgGradientServer.SpreadMethod">
            <summary>
            Specifies what happens if the gradient starts or ends inside the bounds of the target rectangle.
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgGradientServer.GradientUnits">
            <summary>
            Gets or sets the coordinate system of the gradient.
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgGradientServer.InheritGradient">
            <summary>
            Gets or sets another gradient fill from which to inherit the stops from.
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgGradientServer.StopColor">
            <summary>
            Gets or sets the colour of the gradient stop.
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgGradientServer.StopOpacity">
            <summary>
            Gets or sets the opacity of the gradient stop (0-1).
            </summary>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgGradientServer.GetColorBlend(Telerik.Imaging.Svg.ISvgRenderer,System.Double,System.Boolean)">
            <summary>
            Gets a <see cref="T:Telerik.Imaging.Svg.IColorBlend"/> representing the <see cref="T:Telerik.Imaging.Svg.SvgGradientServer"/>'s gradient stops.
            </summary>
            <param name="renderer">The renderer <see cref="T:Telerik.Imaging.Svg.ISvgRenderer"/>.</param>
            <param name="opacity">The opacity of the colour blend.</param>
            <param name="radial">True if it's a radial gradiant.</param>
        </member>
        <member name="T:Telerik.Imaging.Svg.SvgGradientSpreadMethod">
            <summary>Indicates what happens if the gradient starts or ends inside the bounds of the target rectangle.</summary>
            <remarks>
                <para>Possible values are: 'pad', which says to use the terminal colors of the gradient to fill the remainder of the target region, 'reflect', which says to reflect the gradient pattern start-to-end, end-to-start, start-to-end, etc. continuously until the target rectangle is filled, and repeat, which says to repeat the gradient pattern start-to-end, start-to-end, start-to-end, etc. continuously until the target region is filled.</para>
                <para>If the attribute is not specified, the effect is as if a value of 'pad' were specified.</para>
            </remarks>
        </member>
        <member name="F:Telerik.Imaging.Svg.SvgGradientSpreadMethod.Pad">
            <summary>Use the terminal colors of the gradient to fill the remainder of the target region.</summary>
        </member>
        <member name="F:Telerik.Imaging.Svg.SvgGradientSpreadMethod.Reflect">
            <summary>Reflect the gradient pattern start-to-end, end-to-start, start-to-end, etc. continuously until the target rectangle is filled.</summary>
        </member>
        <member name="F:Telerik.Imaging.Svg.SvgGradientSpreadMethod.Repeat">
            <summary>Repeat the gradient pattern start-to-end, start-to-end, start-to-end, etc. continuously until the target region is filled.</summary>
        </member>
        <member name="T:Telerik.Imaging.Svg.SvgGradientStop">
            <summary>
            Represents a colour stop in a gradient.
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgGradientStop.Offset">
            <summary>
            Gets or sets the offset, i.e. where the stop begins from the beginning, of the gradient stop.
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgGradientStop.StopColor">
            <summary>
            Gets or sets the colour of the gradient stop.
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgGradientStop.StopOpacity">
            <summary>
            Gets or sets the opacity of the gradient stop (0-1).
            </summary>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgGradientStop.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Imaging.Svg.SvgGradientStop"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgGradientStop.#ctor(Telerik.Imaging.Svg.SvgUnit,System.Drawing.Color)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Imaging.Svg.SvgGradientStop"/> class.
            </summary>
            <param name="offset">The offset.</param>
            <param name="colour">The colour.</param>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgMarker.MarkerElement">
            <summary>
            Return the child element that represent the marker
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgMarker.Fill">
            <summary>
            If not set set in the marker, consider the attribute in the drawing element.
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgMarker.Stroke">
            <summary>
            If not set set in the marker, consider the attribute in the drawing element.
            </summary>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgMarker.RenderMarker(Telerik.Imaging.Svg.ISvgRenderer,Telerik.Imaging.Svg.SvgVisualElement,System.Windows.Point,System.Windows.Point,System.Windows.Point,System.Boolean)">
            <summary>
            Render this marker using the slope of the given line segment
            </summary>
            <param name="pRenderer"></param>
            <param name="pOwner"></param>
            <param name="pRefPoint"></param>
            <param name="pMarkerPoint1"></param>
            <param name="pMarkerPoint2"></param>
            <param name="isStartMarker"></param>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgMarker.RenderMarker(Telerik.Imaging.Svg.ISvgRenderer,Telerik.Imaging.Svg.SvgVisualElement,System.Windows.Point,System.Windows.Point,System.Windows.Point,System.Windows.Point)">
            <summary>
            Render this marker using the average of the slopes of the two given line segments
            </summary>
            <param name="pRenderer"></param>
            <param name="pOwner"></param>
            <param name="pRefPoint"></param>
            <param name="pMarkerPoint1"></param>
            <param name="pMarkerPoint2"></param>
            <param name="pMarkerPoint3"></param>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgMarker.RenderPart2(System.Double,Telerik.Imaging.Svg.ISvgRenderer,Telerik.Imaging.Svg.SvgVisualElement,System.Windows.Point)">
            <summary>
            Common code for rendering a marker once the orientation angle has been calculated
            </summary>
            <param name="fAngle"></param>
            <param name="pRenderer"></param>
            <param name="pOwner"></param>
            <param name="pMarkerPoint"></param>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgMarker.CreateGraphicProperties(Telerik.Imaging.Svg.SvgVisualElement,Telerik.Imaging.Svg.ISvgRenderer)">
            <summary>
            Create a pen that can be used to render this marker
            </summary>
            <returns></returns>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgMarker.GetClone(Telerik.Imaging.Svg.SvgVisualElement,Telerik.Imaging.Svg.ISvgRenderer)">
            <summary>
            Get a clone of the current path, scaled for the stroke width
            </summary>
            <returns></returns>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgMarker.AdjustForViewBoxWidth(System.Double)">
            <summary>
            Adjust the given value to account for the width of the viewbox in the viewport
            </summary>
            <param name="fWidth"></param>
            <returns></returns>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgMarker.AdjustForViewBoxHeight(System.Double)">
            <summary>
            Adjust the given value to account for the height of the viewbox in the viewport
            </summary>
            <param name="fHeight"></param>
            <returns></returns>
        </member>
        <member name="T:Telerik.Imaging.Svg.SvgPaintServer">
            <summary>
            Represents the base class for all paint servers that are intended to be used as a fill or stroke.
            </summary>
        </member>
        <member name="F:Telerik.Imaging.Svg.SvgPaintServer.None">
            <summary>
            An unspecified <see cref="T:Telerik.Imaging.Svg.SvgPaintServer"/>.
            </summary>
        </member>
        <member name="F:Telerik.Imaging.Svg.SvgPaintServer.Inherit">
            <summary>
            A <see cref="T:Telerik.Imaging.Svg.SvgPaintServer"/> that should inherit from its parent.
            </summary>
        </member>
        <member name="F:Telerik.Imaging.Svg.SvgPaintServer.NotSet">
            <summary>
            An unspecified <see cref="T:Telerik.Imaging.Svg.SvgPaintServer"/>.
            </summary>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgPaintServer.Render(Telerik.Imaging.Svg.ISvgRenderer)">
            <summary>
            Renders the <see cref="T:Telerik.Imaging.Svg.SvgElement"/> and contents to the specified <see cref="T:Telerik.Imaging.Svg.ISvgRenderer"/> object.
            </summary>
            <param name="renderer">The <see cref="T:Telerik.Imaging.Svg.ISvgRenderer"/> object to render to.</param>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgPaintServer.GetBrush(Telerik.Imaging.Svg.SvgVisualElement,Telerik.Imaging.Svg.ISvgRenderer,System.Single,System.Boolean)">
            <summary>
            Gets a <see cref="T:Telerik.Imaging.Svg.IBrush"/> representing the current paint server.
            </summary>
            <param name="styleOwner">The owner <see cref="T:Telerik.Imaging.Svg.SvgVisualElement"/>.</param>
            <param name="renderer">The renderer object.</param>
            <param name="opacity">The opacity of the brush.</param>
            <param name="forStroke">Not used.</param>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgPaintServer.ToString">
            <summary>
            Returns a <see cref="T:System.String"/> that represents the current <see cref="T:System.Object"/>.
            </summary>
            <returns>
            A <see cref="T:System.String"/> that represents the current <see cref="T:System.Object"/>.
            </returns>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgRadialGradientServer.CalcScale(System.Windows.Rect,Telerik.Windows.Documents.Fixed.Model.Graphics.PathGeometry)">
            <summary>
            Determine how much (approximately) the path must be scaled to contain the rectangle
            </summary>
            <param name="bounds">Bounds that the path must contain</param>
            <param name="path">Path of the gradient</param>
            <returns>Scale factor</returns>
            <remarks>
            This method continually transforms the rectangle (fewer points) until it is contained by the path
            and returns the result of the search.  The scale factor is set to a constant 95%
            </remarks>
        </member>
        <member name="T:Telerik.Imaging.Svg.SvgStrokeLineCap">
            <summary>Specifies the shape to be used at the end of open subpaths when they are stroked.</summary>
        </member>
        <member name="F:Telerik.Imaging.Svg.SvgStrokeLineCap.Inherit">
            <summary>The value is inherited from the parent element.</summary>
        </member>
        <member name="F:Telerik.Imaging.Svg.SvgStrokeLineCap.Butt">
            <summary>The ends of the subpaths are square but do not extend past the end of the subpath.</summary>
        </member>
        <member name="F:Telerik.Imaging.Svg.SvgStrokeLineCap.Round">
            <summary>The ends of the subpaths are rounded.</summary>
        </member>
        <member name="F:Telerik.Imaging.Svg.SvgStrokeLineCap.Square">
            <summary>The ends of the subpaths are square.</summary>
        </member>
        <member name="T:Telerik.Imaging.Svg.SvgStrokeLineJoin">
            <summary>Specifies the shape to be used at the corners of paths or basic shapes when they are stroked.</summary>
        </member>
        <member name="F:Telerik.Imaging.Svg.SvgStrokeLineJoin.Inherit">
            <summary>The value is inherited from the parent element.</summary>
        </member>
        <member name="F:Telerik.Imaging.Svg.SvgStrokeLineJoin.Miter">
            <summary>The corners of the paths are joined sharply.</summary>
        </member>
        <member name="F:Telerik.Imaging.Svg.SvgStrokeLineJoin.Round">
            <summary>The corners of the paths are rounded off.</summary>
        </member>
        <member name="F:Telerik.Imaging.Svg.SvgStrokeLineJoin.Bevel">
            <summary>The corners of the paths are "flattened".</summary>
        </member>
        <member name="T:Telerik.Imaging.Svg.WrapMode">
            Various wrap modes for brushes
            <devdoc>
               <para>
                  Specifies how a texture or gradient is tiled when it is
                  larger than the area being filled.
               </para>
            </devdoc>
        </member>
        <member name="F:Telerik.Imaging.Svg.WrapMode.Tile">
            <devdoc>
               Tiles the gradient or texture.
            </devdoc>
        </member>
        <member name="F:Telerik.Imaging.Svg.WrapMode.TileFlipX">
            <devdoc>
               Reverses the texture or gradient
               horizontally and then tiles the texture or gradient.
            </devdoc>
        </member>
        <member name="F:Telerik.Imaging.Svg.WrapMode.TileFlipY">
            <devdoc>
               Reverses the texture or
               gradient vertically and then tiles the texture or gradient.
            </devdoc>
        </member>
        <member name="F:Telerik.Imaging.Svg.WrapMode.TileFlipXY">
            <devdoc>
               Reverses the texture or gradient
               horizontally and vertically and then tiles the texture or gradient.
            </devdoc>
        </member>
        <member name="F:Telerik.Imaging.Svg.WrapMode.Clamp">
            <devdoc>
               Clamps the texture or gradient to the
               object boundary.
            </devdoc>
        </member>
        <member name="T:Telerik.Imaging.Svg.SvgPath">
            <summary>
            Represents an SVG path element.
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgPath.PathData">
            <summary>
            Gets or sets a <see cref="T:Telerik.Imaging.Svg.SvgPathSegmentList"/> of path data.
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgPath.PathLength">
            <summary>
            Gets or sets the length of the path.
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgPath.Bounds">
            <summary>
            Gets the bounds of the element.
            </summary>
            <value>The bounds.</value>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgPathBuilder.Parse(System.String)">
            <summary>
            Parses the specified string into a collection of path segments.
            </summary>
            <param name="path">A <see cref="T:System.String"/> containing path data.</param>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgPathBuilder.ToAbsolute(System.Single,System.Single,Telerik.Imaging.Svg.SvgPathSegmentList,System.Boolean)">
            <summary>
            Creates point with absolute coorindates.
            </summary>
            <param name="x">Raw X-coordinate value.</param>
            <param name="y">Raw Y-coordinate value.</param>
            <param name="segments">Current path segments.</param>
            <param name="isRelativeBoth"><b>true</b> if <paramref name="x"/> and <paramref name="y"/> contains relative coordinate values, otherwise <b>false</b>.</param>
            <returns><see cref="T:System.Drawing.PointF"/> that contains absolute coordinates.</returns>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgPathBuilder.ToAbsolute(System.Single,System.Single,Telerik.Imaging.Svg.SvgPathSegmentList,System.Boolean,System.Boolean)">
            <summary>
            Creates point with absolute coorindates.
            </summary>
            <param name="x">Raw X-coordinate value.</param>
            <param name="y">Raw Y-coordinate value.</param>
            <param name="segments">Current path segments.</param>
            <param name="isRelativeX"><b>true</b> if <paramref name="x"/> contains relative coordinate value, otherwise <b>false</b>.</param>
            <param name="isRelativeY"><b>true</b> if <paramref name="y"/> contains relative coordinate value, otherwise <b>false</b>.</param>
            <returns><see cref="T:System.Drawing.PointF"/> that contains absolute coordinates.</returns>
        </member>
        <member name="T:Telerik.Imaging.Svg.GraphicsUnit">
            <devdoc>
               Specifies the unit of measure for the given
               data.
            </devdoc>
        </member>
        <member name="F:Telerik.Imaging.Svg.GraphicsUnit.World">
            <devdoc>
               Specifies the world unit as the unit of
               measure.
            </devdoc>
        </member>
        <member name="F:Telerik.Imaging.Svg.GraphicsUnit.Display">
            <devdoc>
               Specifies 1/75 inch as the unit of measure.
            </devdoc>
        </member>
        <member name="F:Telerik.Imaging.Svg.GraphicsUnit.Pixel">
            <devdoc>
               Specifies a device pixel as the unit of
               measure.
            </devdoc>
        </member>
        <member name="F:Telerik.Imaging.Svg.GraphicsUnit.Point">
            <devdoc>
               Specifies a printer's point (1/72 inch) as
               the unit of measure.
            </devdoc>
        </member>
        <member name="F:Telerik.Imaging.Svg.GraphicsUnit.Inch">
            <devdoc>
               Specifies the inch as the unit of measure.
            </devdoc>
        </member>
        <member name="F:Telerik.Imaging.Svg.GraphicsUnit.Document">
            <devdoc>
               Specifes the document unit (1/300 inch) as
               the unit of measure.
            </devdoc>
        </member>
        <member name="F:Telerik.Imaging.Svg.GraphicsUnit.Millimeter">
            <devdoc>
               Specifies the millimeter as the unit of
               measure.
            </devdoc>
        </member>
        <member name="T:Telerik.Imaging.Svg.ImageLockMode">
            <devdoc>
               Indicates the access mode for an <see cref='T:Telerik.Imaging.Svg.ISvgBitmapData'/>.
            </devdoc>
        </member>
        <member name="F:Telerik.Imaging.Svg.ImageLockMode.ReadOnly">
            <devdoc>
               <para>
                  Specifies the image is read-only.
               </para>
            </devdoc>
        </member>
        <member name="F:Telerik.Imaging.Svg.ImageLockMode.WriteOnly">
            <devdoc>
               <para>
                  Specifies the image is
                  write-only.
               </para>
            </devdoc>
        </member>
        <member name="F:Telerik.Imaging.Svg.ImageLockMode.ReadWrite">
            <devdoc>
               <para>
                  Specifies the image is
                  read-write.
               </para>
            </devdoc>
        </member>
        <member name="F:Telerik.Imaging.Svg.ImageLockMode.UserInputBuffer">
            <devdoc>
               Indicates the image resides in a user input
               buffer, to which the user controls access.
            </devdoc>
        </member>
        <member name="M:Telerik.Imaging.Svg.ISvgBitmap.LockBits(System.Windows.Rect,Telerik.Imaging.Svg.ImageLockMode,System.Windows.Media.PixelFormat)">
            <devdoc>
               Locks a Bitmap into system memory.
            </devdoc>
        </member>
        <member name="M:Telerik.Imaging.Svg.ISvgBitmap.UnlockBits(Telerik.Imaging.Svg.ISvgBitmapData)">
            <devdoc>
               Unlocks this <see cref='T:Telerik.Imaging.Svg.ISvgBitmapData'/> from system memory.
            </devdoc>
        </member>
        <member name="M:Telerik.Imaging.Svg.ISvgBitmap.GetPixel(System.Int32,System.Int32)">
            <devdoc>
               <para>
                  Gets the color of the specified pixel
                  in this <see cref='T:System.Windows.Media.Color'/>.
               </para>
            </devdoc>
        </member>
        <member name="T:Telerik.Imaging.Svg.ISvgBitmapData">
            <devdoc>
               Specifies the attributes of a bitmap image.
            </devdoc>
        </member>
        <member name="P:Telerik.Imaging.Svg.ISvgBitmapData.Width">
            <devdoc>
               Specifies the pixel width of the <see cref='T:Telerik.Imaging.Svg.ISvgBitmapData'/>.
            </devdoc>
        </member>
        <member name="P:Telerik.Imaging.Svg.ISvgBitmapData.Height">
            <devdoc>
               Specifies the pixel height of the <see cref='T:Telerik.Imaging.Svg.ISvgBitmapData'/>.
            </devdoc>
        </member>
        <member name="P:Telerik.Imaging.Svg.ISvgBitmapData.Stride">
            <devdoc>
               Specifies the stride width of the <see cref='T:Telerik.Imaging.Svg.ISvgBitmapData'/>.
            </devdoc>
        </member>
        <member name="P:Telerik.Imaging.Svg.ISvgBitmapData.Scan0">
            <devdoc>
               Specifies the address of the pixel data.
            </devdoc>
        </member>
        <member name="T:Telerik.Imaging.Svg.SmoothingMode">
            <devdoc>
               <para>
                  Specifies the overall quality of rendering of graphics
                  shapes.
               </para>
            </devdoc>
        </member>
        <member name="F:Telerik.Imaging.Svg.SmoothingMode.Invalid">
            <devdoc>
               <para>
                  Specifies an invalid mode.
               </para>
            </devdoc>
        </member>
        <member name="F:Telerik.Imaging.Svg.SmoothingMode.Default">
            <devdoc>
               <para>
                  Specifies the default mode.
               </para>
            </devdoc>
        </member>
        <member name="F:Telerik.Imaging.Svg.SmoothingMode.HighSpeed">
            <devdoc>
               <para>
                  Specifies low quality, high performance rendering.
               </para>
            </devdoc>
        </member>
        <member name="F:Telerik.Imaging.Svg.SmoothingMode.HighQuality">
            <devdoc>
               <para>
                  Specifies high quality, lower performance rendering.
               </para>
            </devdoc>
        </member>
        <member name="F:Telerik.Imaging.Svg.SmoothingMode.None">
            <devdoc>
               Specifies no anti-aliasing.
            </devdoc>
        </member>
        <member name="F:Telerik.Imaging.Svg.SmoothingMode.AntiAlias">
            <devdoc>
               Specifies anti-aliased rendering.
            </devdoc>
        </member>
        <member name="T:Telerik.Imaging.Svg.SvgShapeRendering">
            <summary>
            The creator of SVG content might want to provide a hint about what tradeoffs to make as the browser renders 'path' element or basic shapes. The shape-rendering attribute provides these hints.
            </summary>
            <references>https://developer.mozilla.org/en-US/docs/Web/SVG/Attribute/shape-rendering</references>
            <remarks>
            Default is <see cref="F:Telerik.Imaging.Svg.SvgShapeRendering.Auto"/>.
            </remarks>
        </member>
        <member name="F:Telerik.Imaging.Svg.SvgShapeRendering.Inherit">
            <summary>
            Indicates that the SVG shape rendering properties from the parent will be used.
            </summary>
            <AnitAlias>Based of parent. If parents are also not set, then <see cref="F:Telerik.Imaging.Svg.SvgShapeRendering.Auto"/></AnitAlias>
        </member>
        <member name="F:Telerik.Imaging.Svg.SvgShapeRendering.Auto">
            <summary>
            Indicates that the user agent shall make appropriate tradeoffs to balance speed, crisp edges and geometric precision, but with geometric precision given more importance than speed and crisp edges.
            </summary>
            <AnitAlias>true</AnitAlias>
        </member>
        <member name="F:Telerik.Imaging.Svg.SvgShapeRendering.OptimizeSpeed">
            <summary>
            Indicates that the user agent shall emphasize rendering speed over geometric precision and crisp edges. This option will sometimes cause the user agent to turn off shape anti-aliasing.
            </summary>
            <AnitAlias>false</AnitAlias>
        </member>
        <member name="F:Telerik.Imaging.Svg.SvgShapeRendering.CrispEdges">
            <summary>
            Indicates that the user agent shall attempt to emphasize the contrast between clean edges of artwork over rendering speed and geometric precision. To achieve crisp edges, the user agent might turn off anti-aliasing for all lines and curves or possibly just for straight lines which are close to vertical or horizontal. Also, the user agent might adjust line positions and line widths to align edges with device pixels.
            </summary>
            <AnitAlias>false</AnitAlias>
        </member>
        <member name="F:Telerik.Imaging.Svg.SvgShapeRendering.GeometricPrecision">
            <summary>
            Indicates that the user agent shall emphasize geometric precision over speed and crisp edges.
            </summary>
            <AnitAlias>false</AnitAlias>
        </member>
        <member name="T:Telerik.Imaging.Svg.StringArg">
            <summary>
            Represents a string argument
            </summary>
        </member>
        <member name="T:Telerik.Imaging.Svg.SvgAttributeAttribute">
            <summary>
            Specifies the SVG attribute name of the associated property.
            </summary>
        </member>
        <member name="F:Telerik.Imaging.Svg.SvgAttributeAttribute.SvgNamespace">
            <summary>
            Gets a <see cref="T:System.String"/> containing the XLink namespace (http://www.w3.org/1999/xlink).
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgAttributeAttribute.NamespaceAndName">
            <summary>
            Gets the name of the SVG attribute.
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgAttributeAttribute.Name">
            <summary>
            Gets the name of the SVG attribute.
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgAttributeAttribute.NameSpace">
            <summary>
            Gets the namespace of the SVG attribute.
            </summary>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgAttributeAttribute.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Imaging.Svg.SvgAttributeAttribute"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgAttributeAttribute.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Imaging.Svg.SvgAttributeAttribute"/> class with the specified attribute name.
            </summary>
            <param name="name">The name of the SVG attribute.</param>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgAttributeAttribute.#ctor(System.String,System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Imaging.Svg.SvgAttributeAttribute"/> class with the specified SVG attribute name and namespace.
            </summary>
            <param name="name">The name of the SVG attribute.</param>
            <param name="nameSpace">The namespace of the SVG attribute (e.g. http://www.w3.org/2000/svg).</param>
        </member>
        <member name="T:Telerik.Imaging.Svg.SvgAttributeCollection">
            <summary>
            A collection of Scalable Vector Attributes that can be inherited from the owner elements ancestors.
            </summary>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgAttributeCollection.#ctor(Telerik.Imaging.Svg.SvgElement)">
            <summary>
            Initialises a new instance of a <see cref="T:Telerik.Imaging.Svg.SvgAttributeCollection"/> with the given <see cref="T:Telerik.Imaging.Svg.SvgElement"/> as the owner.
            </summary>
            <param name="owner">The <see cref="T:Telerik.Imaging.Svg.SvgElement"/> owner of the collection.</param>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgAttributeCollection.GetAttribute``1(System.String,``0)">
            <summary>
            Gets the attribute with the specified name.
            </summary>
            <typeparam name="TAttributeType">The type of the attribute value.</typeparam>
            <param name="attributeName">A <see cref="T:System.String"/> containing the name of the attribute.</param>
            <param name="defaultValue">The value to return if a value hasn't already been specified.</param>
            <returns>The attribute value if available; otherwise the default value of <typeparamref name="TAttributeType"/>.</returns>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgAttributeCollection.GetInheritedAttribute``1(System.String,System.Boolean,``0)">
            <summary>
            Gets the attribute with the specified name and inherits from ancestors if there is no attribute set.
            </summary>
            <typeparam name="TAttributeType">The type of the attribute value.</typeparam>
            <param name="attributeName">A <see cref="T:System.String"/> containing the name of the attribute.</param>
            <param name="inherited">Used only if the attribute value is not available. If set to true, the inherited value is returned in this case, otherwise the default value.</param>
            <param name="defaultValue">The value to return if a value hasn't already been specified.</param>
            <returns>The attribute value if available and not set to "inherit"; the ancestors value for the same attribute if it exists and if either the attribute value is set to "inherit", or <paramref name="inherited"/> is true; the default value otherwise.</returns>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgAttributeCollection.Item(System.String)">
            <summary>
            Gets the attribute with the specified name.
            </summary>
            <param name="attributeName">A <see cref="T:System.String"/> containing the attribute name.</param>
            <returns>The attribute value associated with the specified name; If there is no attribute the parent's value will be inherited.</returns>
        </member>
        <member name="E:Telerik.Imaging.Svg.SvgAttributeCollection.AttributeChanged">
            <summary>
            Fired when an Atrribute has changed
            </summary>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgContentNode.DeepCopy">
            <summary>
            Create a deep copy of this <see cref="T:Telerik.Imaging.Svg.ISvgNode"/>.
            </summary>
            <returns>A deep copy of this <see cref="T:Telerik.Imaging.Svg.ISvgNode"/></returns>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgCustomAttributeCollection.#ctor(Telerik.Imaging.Svg.SvgElement)">
            <summary>
            Initialises a new instance of a <see cref="T:Telerik.Imaging.Svg.SvgAttributeCollection"/> with the given <see cref="T:Telerik.Imaging.Svg.SvgElement"/> as the owner.
            </summary>
            <param name="owner">The <see cref="T:Telerik.Imaging.Svg.SvgElement"/> owner of the collection.</param>
        </member>
        <member name="E:Telerik.Imaging.Svg.SvgCustomAttributeCollection.AttributeChanged">
            <summary>
            Fired when an Atrribute has changed
            </summary>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgDtdResolver.GetEntity(System.Uri,System.String,System.Type)">
            <summary>
            Maps a URI to an object containing the actual resource.
            </summary>
            <param name="absoluteUri">The URI returned from <see cref="M:System.Xml.XmlResolver.ResolveUri(System.Uri,System.String)"/></param>
            <param name="role">The current implementation does not use this parameter when resolving URIs. This is provided for future extensibility purposes. For example, this can be mapped to the xlink:role and used as an implementation specific argument in other scenarios.</param>
            <param name="ofObjectToReturn">The type of object to return. The current implementation only returns System.IO.Stream objects.</param>
            <returns>
            A System.IO.Stream object or null if a type other than stream is specified.
            </returns>
            <exception cref="T:System.Xml.XmlException">
                <paramref name="ofObjectToReturn"/> is neither null nor a Stream type. </exception>
            <exception cref="T:System.UriFormatException">The specified URI is not an absolute URI. </exception>
            <exception cref="T:System.NullReferenceException">
                <paramref name="absoluteUri"/> is null. </exception>
            <exception cref="T:System.Exception">There is a runtime error (for example, an interrupted server connection). </exception>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgElement.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Imaging.Svg.SvgElement"/> class.
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgElement.Parent">
            <summary>
            Gets the parent <see cref="T:Telerik.Imaging.Svg.SvgElement"/>.
            </summary>
            <value>An <see cref="T:Telerik.Imaging.Svg.SvgElement"/> if one exists; otherwise null.</value>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgElement.ColorServer">
            <summary>
            Gets or sets the color <see cref="T:Telerik.Imaging.Svg.SvgPaintServer"/> of this element which drives the currentColor property.
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgElement.Content">
            <summary>
            Gets or sets the content of the element.
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgElement.OwnerDocument">
            <summary>
            Gets the owner <see cref="T:Telerik.Imaging.Svg.SvgObject"/>.
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgElement.Children">
            <summary>
            Gets a collection of all child <see cref="T:Telerik.Imaging.Svg.SvgElement"/> objects.
            </summary>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgElement.RenderElement(Telerik.Imaging.Svg.ISvgRenderer)">
            <summary>
            Renders this element to the <see cref="T:Telerik.Imaging.Svg.ISvgRenderer"/>.
            </summary>
            <param name="renderer">The <see cref="T:Telerik.Imaging.Svg.ISvgRenderer"/> that the element should use to render itself.</param>
        </member>
        <member name="E:Telerik.Imaging.Svg.SvgElement.AttributeChanged">
            <summary>
            Fired when an Atrribute of this Element has changed
            </summary>
        </member>
        <member name="E:Telerik.Imaging.Svg.SvgElement.ContentChanged">
            <summary>
            Fired when an Atrribute of this Element has changed
            </summary>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgElement.RegisterEvents(Telerik.Imaging.Svg.ISvgEventCaller)">
            <summary>
            Use this method to provide your implementation ISvgEventCaller which can register Actions 
            and call them if one of the events occurs. Make sure, that your SvgElement has a unique ID.
            The SvgTextElement overwrites this and regsiters the Change event tor its text content.
            </summary>
            <param name="caller"></param>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgElement.UnregisterEvents(Telerik.Imaging.Svg.ISvgEventCaller)">
            <summary>
            Use this method to provide your implementation ISvgEventCaller to unregister Actions
            </summary>
            <param name="caller"></param>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgElement.ElementName">
            <summary>
            Gets the name of the element.
            </summary>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgElement.AddStyle(System.String,System.String,System.Int32)">
            <summary>
            Add style.
            </summary>
            <param name="name">The style name.</param>
            <param name="value">The style value.</param>
            <param name="specificity">The specificity value.</param>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgElement.FlushStyles(System.Boolean)">
            <summary>
            Flush styles.
            </summary>
            <param name="children">If true, flush styles to the children.</param>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgElement.RemoveElement(Telerik.Imaging.Svg.SvgElement)">
            <summary>
            Called by the underlying <see cref="T:Telerik.Imaging.Svg.SvgElement"/> when an element has been removed from the
            <see cref="P:Telerik.Imaging.Svg.SvgElement.Children"/> collection.
            </summary>
            <param name="child">The <see cref="T:Telerik.Imaging.Svg.SvgElement"/> that has been removed.</param>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgElement.OnElementRemoved(Telerik.Imaging.Svg.SvgElement)">
            <summary>
            Calls the <see cref="M:Telerik.Imaging.Svg.SvgElement.RemoveElement(Telerik.Imaging.Svg.SvgElement)"/> method with the specified <see cref="T:Telerik.Imaging.Svg.SvgElement"/> as the parameter.
            </summary>
            <param name="child">The <see cref="T:Telerik.Imaging.Svg.SvgElement"/> that has been removed.</param>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgElement.CustomAttributes">
            <summary>
            Gets a collection of custom attributes
            </summary>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgElement.PushTransforms(Telerik.Imaging.Svg.ISvgRenderer)">
            <summary>
            Applies the required transforms to <see cref="T:Telerik.Imaging.Svg.ISvgRenderer"/>.
            </summary>
            <param name="renderer">The <see cref="T:Telerik.Imaging.Svg.ISvgRenderer"/> to be transformed.</param>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgElement.PopTransforms(Telerik.Imaging.Svg.ISvgRenderer)">
            <summary>
            Removes any previously applied transforms from the specified <see cref="T:Telerik.Imaging.Svg.ISvgRenderer"/>.
            </summary>
            <param name="renderer">The <see cref="T:Telerik.Imaging.Svg.ISvgRenderer"/> that should have transforms removed.</param>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgElement.Telerik#Imaging#Svg#ISvgTransformable#PushTransforms(Telerik.Imaging.Svg.ISvgRenderer)">
            <summary>
            Applies the required transforms to <see cref="T:Telerik.Imaging.Svg.ISvgRenderer"/>.
            </summary>
            <param name="renderer">The <see cref="T:Telerik.Imaging.Svg.ISvgRenderer"/> to be transformed.</param>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgElement.Telerik#Imaging#Svg#ISvgTransformable#PopTransforms(Telerik.Imaging.Svg.ISvgRenderer)">
            <summary>
            Removes any previously applied transforms from the specified <see cref="T:Telerik.Imaging.Svg.ISvgRenderer"/>.
            </summary>
            <param name="renderer">The <see cref="T:Telerik.Imaging.Svg.ISvgRenderer"/> that should have transforms removed.</param>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgElement.Transforms">
            <summary>
            Gets or sets the element transforms.
            </summary>
            <value>The transforms.</value>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgElement.TransformedBounds(System.Windows.Rect)">
            <summary>
            Transforms the given rectangle with the set transformation, if any.
            Can be applied to bounds calculated without considering the element transformation. 
            </summary>
            <param name="bounds">The rectangle to be transformed.</param>
            <returns>The transformed rectangle, or the original rectangle if no transformation exists.</returns>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgElement.Attributes">
            <summary>
            Gets a collection of element attributes.
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgElement.ID">
            <summary>
            Gets or sets the ID of the element.
            </summary>
            <exception cref="T:Telerik.Imaging.Svg.SvgException">The ID is already used within the <see cref="T:Telerik.Imaging.Svg.SvgObject"/>.</exception>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgElement.SpaceHandling">
            <summary>
            Gets or sets the space handling.
            </summary>
            <value>The space handling.</value>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgElement.ForceUniqueID(System.String)">
            <summary>
            Only used by the ID Manager
            </summary>
            <param name="newID"></param>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgElement.Clone">
            <summary>
            Creates a new object that is a copy of the current instance.
            </summary>
            <returns>
            A new object that is a copy of this instance.
            </returns>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgElement.AddElement(Telerik.Imaging.Svg.SvgElement,System.Int32)">
            <summary>
            Called by the underlying <see cref="T:Telerik.Imaging.Svg.SvgElement"/> when an element has been added to the
            <see cref="P:Telerik.Imaging.Svg.SvgElement.Children"/> collection.
            </summary>
            <param name="child">The <see cref="T:Telerik.Imaging.Svg.SvgElement"/> that has been added.</param>
            <param name="index">An <see cref="T:System.Int32"/> representing the index where the element was added to the collection.</param>
        </member>
        <member name="E:Telerik.Imaging.Svg.SvgElement.ChildAdded">
            <summary>
            Fired when an Element was added to the children of this Element
            </summary>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgElement.OnElementAdded(Telerik.Imaging.Svg.SvgElement,System.Int32)">
            <summary>
            Calls the <see cref="M:Telerik.Imaging.Svg.SvgElement.AddElement(Telerik.Imaging.Svg.SvgElement,System.Int32)"/> method with the specified parameters.
            </summary>
            <param name="child">The <see cref="T:Telerik.Imaging.Svg.SvgElement"/> that has been added.</param>
            <param name="index">An <see cref="T:System.Int32"/> representing the index where the element was added to the collection.</param>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgElement.Render(Telerik.Imaging.Svg.ISvgRenderer)">
            <summary>
            Renders the <see cref="T:Telerik.Imaging.Svg.SvgElement"/> and contents to the specified <see cref="T:Telerik.Imaging.Svg.ISvgRenderer"/> object.
            </summary>
            <param name="renderer">The <see cref="T:Telerik.Imaging.Svg.ISvgRenderer"/> object to render to.</param>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgElement.RenderChildren(Telerik.Imaging.Svg.ISvgRenderer)">
            <summary>
            Renders the children of this <see cref="T:Telerik.Imaging.Svg.SvgElement"/>.
            </summary>
            <param name="renderer">The <see cref="T:Telerik.Imaging.Svg.ISvgRenderer"/> to render the child <see cref="T:Telerik.Imaging.Svg.SvgElement"/>s to.</param>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgElement.AddPaths(Telerik.Imaging.Svg.SvgElement,Telerik.Windows.Documents.Fixed.Model.Graphics.PathGeometry)">
            <summary>
            Recursive method to add up the paths of all children
            </summary>
            <param name="elem"></param>
            <param name="path"></param>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgElement.GetPaths(Telerik.Imaging.Svg.SvgElement,Telerik.Imaging.Svg.ISvgRenderer)">
            <summary>
            Recursive method to add up the paths of all children
            </summary>
            <param name="elem"></param>
            <param name="renderer"></param>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgElement.IsPathDirty">
            <summary>
            Gets or sets a value indicating whether this element's 'Path' is dirty.
            </summary>
            <value>
                <c>true</c> if the path is dirty; otherwise, <c>false</c>.
            </value>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgElement.InvalidateChildPaths">
            <summary>
            Force recreation of the paths for the element and it's children.
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgElement.Fill">
            <summary>
            Gets or sets the fill <see cref="T:Telerik.Imaging.Svg.SvgPaintServer"/> of this element.
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgElement.Stroke">
            <summary>
            Gets or sets the <see cref="T:Telerik.Imaging.Svg.SvgPaintServer"/> to be used when rendering a stroke around this element.
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgElement.FillOpacity">
            <summary>
            Gets or sets the opacity of this element's <see cref="P:Telerik.Imaging.Svg.SvgElement.Fill"/>.
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgElement.StrokeWidth">
            <summary>
            Gets or sets the width of the stroke (if the <see cref="P:Telerik.Imaging.Svg.SvgElement.Stroke"/> property has a valid value specified.
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgElement.StrokeOpacity">
            <summary>
            Gets or sets the opacity of the stroke, if the <see cref="P:Telerik.Imaging.Svg.SvgElement.Stroke"/> property has been specified. 1.0 is fully opaque; 0.0 is transparent.
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgElement.Opacity">
            <summary>
            Gets or sets the opacity of the element. 1.0 is fully opaque; 0.0 is transparent.
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgElement.ShapeRendering">
            <summary>
            Refers to the AnitAlias rendering of shapes.
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgElement.ColorInterpolation">
            <summary>
            Gets or sets the color space for gradient interpolations, color animations and alpha compositing.
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgElement.ColorInterpolationFilters">
            <summary>
            Gets or sets the color space for imaging operations performed via filter effects.
            NOT currently mapped through to bitmap
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgElement.Visibility">
            <summary>
            Gets or sets a value to determine whether the element will be rendered.
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgElement.Display">
            <summary>
            Gets or sets a value to determine whether the element will be rendered.
            Needed to support SVG attribute display="none"
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgElement.TextAnchor">
            <summary>
            Gets or sets the text anchor.
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgElement.BaselineShift">
            <summary>
            Specifies dominant-baseline positioning of text.
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgElement.FontFamily">
            <summary>
            Indicates which font family is to be used to render the text.
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgElement.FontSize">
            <summary>
            Refers to the size of the font from baseline to baseline when multiple lines of text are set solid in a multiline layout environment.
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgElement.FontStyle">
            <summary>
            Refers to the style of the font.
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgElement.FontVariant">
            <summary>
            Refers to the varient of the font.
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgElement.TextDecoration">
            <summary>
            Refers to the boldness of the font.
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgElement.FontWeight">
            <summary>
            Refers to the boldness of the font.
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgElement.FontStretch">
            <summary>
            Indicates the desired amount of condensing or expansion in the glyphs used to render the text.
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgElement.TextTransformation">
            <summary>
            Refers to the text transformation.
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgElement.Font">
            <summary>
            Set all font information.
            </summary>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgElement.GetFont(Telerik.Imaging.Svg.ISvgRenderer)">
            <summary>
            Get the font information based on data stored with the text object or inherited from the parent.
            </summary>
            <returns></returns>
        </member>
        <member name="T:Telerik.Imaging.Svg.SvgElementAttribute">
            <summary>
            Specifies the SVG name of an <see cref="T:Telerik.Imaging.Svg.SvgElement"/>.
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgElementAttribute.ElementName">
            <summary>
            Gets the name of the SVG element.
            </summary>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgElementAttribute.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Imaging.Svg.SvgElementAttribute"/> class with the specified element name;
            </summary>
            <param name="elementName">The name of the SVG element.</param>
        </member>
        <member name="T:Telerik.Imaging.Svg.SvgElementCollection">
            <summary>
            Represents a collection of <see cref="T:Telerik.Imaging.Svg.SvgElement"/>s.
            </summary>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgElementCollection.#ctor(Telerik.Imaging.Svg.SvgElement)">
            <summary>
            Initialises a new instance of an <see cref="T:Telerik.Imaging.Svg.SvgElementCollection"/> class.
            </summary>
            <param name="owner">The owner <see cref="T:Telerik.Imaging.Svg.SvgElement"/> of the collection.</param>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgElementCollection.IndexOf(Telerik.Imaging.Svg.SvgElement)">
            <summary>
            Returns the index of the specified <see cref="T:Telerik.Imaging.Svg.SvgElement"/> in the collection.
            </summary>
            <param name="item">The <see cref="T:Telerik.Imaging.Svg.SvgElement"/> to search for.</param>
            <returns>The index of the element if it is present; otherwise -1.</returns>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgElementCollection.Insert(System.Int32,Telerik.Imaging.Svg.SvgElement)">
            <summary>
            Inserts the given <see cref="T:Telerik.Imaging.Svg.SvgElement"/> to the collection at the specified index.
            </summary>
            <param name="index">The index that the <paramref name="item"/> should be added at.</param>
            <param name="item">The <see cref="T:Telerik.Imaging.Svg.SvgElement"/> to be added.</param>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgElementCollection.FindSvgElementsOf``1">
            <summary>
            expensive recursive search for nodes of type T
            </summary>
            <typeparam name="T"></typeparam>
            <returns></returns>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgElementCollection.FindSvgElementOf``1">
            <summary>
            expensive recursive search for first node of type T
            </summary>
            <typeparam name="T"></typeparam>
            <returns></returns>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgElementFactory.AvailableElements">
            <summary>
            Gets a list of available types that can be used when creating an <see cref="T:Telerik.Imaging.Svg.SvgElement"/>.
            </summary>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgElementFactory.CreateDocument``1(System.Xml.XmlReader)">
            <summary>
            Creates an <see cref="T:Telerik.Imaging.Svg.SvgObject"/> from the current node in the specified <see cref="T:System.Xml.XmlTextReader"/>.
            </summary>
            <param name="reader">The <see cref="T:System.Xml.XmlTextReader"/> containing the node to parse into an <see cref="T:Telerik.Imaging.Svg.SvgObject"/>.</param>
            <exception cref="T:System.ArgumentNullException">The <paramref name="reader"/> parameter cannot be <c>null</c>.</exception>
            <exception cref="T:System.InvalidOperationException">The CreateDocument method can only be used to parse root &lt;svg&gt; elements.</exception>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgElementFactory.CreateElement(System.Xml.XmlReader,Telerik.Imaging.Svg.SvgObject)">
            <summary>
            Creates an <see cref="T:Telerik.Imaging.Svg.SvgElement"/> from the current node in the specified <see cref="T:System.Xml.XmlTextReader"/>.
            </summary>
            <param name="reader">The <see cref="T:System.Xml.XmlTextReader"/> containing the node to parse into a subclass of <see cref="T:Telerik.Imaging.Svg.SvgElement"/>.</param>
            <param name="document">The <see cref="T:Telerik.Imaging.Svg.SvgObject"/> that the created element belongs to.</param>
            <exception cref="T:System.ArgumentNullException">The <paramref name="reader"/> and <paramref name="document"/> parameters cannot be <c>null</c>.</exception>
        </member>
        <member name="T:Telerik.Imaging.Svg.SvgElementIdManager">
            <summary>
            Provides methods to ensure element ID's are valid and unique.
            </summary>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgElementIdManager.GetElementById(System.String)">
            <summary>
            Retrieves the <see cref="T:Telerik.Imaging.Svg.SvgElement"/> with the specified ID.
            </summary>
            <param name="id">A <see cref="T:System.String"/> containing the ID of the element to find.</param>
            <returns>An <see cref="T:Telerik.Imaging.Svg.SvgElement"/> of one exists with the specified ID; otherwise false.</returns>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgElementIdManager.Add(Telerik.Imaging.Svg.SvgElement)">
            <summary>
            Adds the specified <see cref="T:Telerik.Imaging.Svg.SvgElement"/> for ID management.
            </summary>
            <param name="element">The <see cref="T:Telerik.Imaging.Svg.SvgElement"/> to be managed.</param>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgElementIdManager.AddAndForceUniqueID(Telerik.Imaging.Svg.SvgElement,Telerik.Imaging.Svg.SvgElement,System.Boolean,System.Action{Telerik.Imaging.Svg.SvgElement,System.String,System.String})">
            <summary>
            Adds the specified <see cref="T:Telerik.Imaging.Svg.SvgElement"/> for ID management. 
            And can auto fix the ID if it already exists or it starts with a number.
            </summary>
            <param name="element">The <see cref="T:Telerik.Imaging.Svg.SvgElement"/> to be managed.</param>
            <param name="sibling">Not used.</param>
            <param name="autoForceUniqueID">Pass true here, if you want the ID to be fixed</param>
            <param name="logElementOldIDNewID">If not null, the action is called before the id is fixed</param>
            <returns>true, if ID was altered</returns>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgElementIdManager.Remove(Telerik.Imaging.Svg.SvgElement)">
            <summary>
            Removed the specified <see cref="T:Telerik.Imaging.Svg.SvgElement"/> from ID management.
            </summary>
            <param name="element">The <see cref="T:Telerik.Imaging.Svg.SvgElement"/> to be removed from ID management.</param>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgElementIdManager.EnsureValidId(System.String,System.Boolean)">
            <summary>
            Ensures that the specified ID is unique within the containing <see cref="T:Telerik.Imaging.Svg.SvgObject"/>.
            </summary>
            <param name="id">A <see cref="T:System.String"/> containing the ID to validate.</param>
            <param name="autoForceUniqueID">Creates a new unique id <see cref="T:System.String"/>.</param>
            <exception cref="T:Telerik.Imaging.Svg.SvgException">
            <para>An element with the same ID already exists within the containing <see cref="T:Telerik.Imaging.Svg.SvgObject"/>.</para>
            </exception>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgElementIdManager.#ctor(Telerik.Imaging.Svg.SvgObject)">
            <summary>
            Initialises a new instance of an <see cref="T:Telerik.Imaging.Svg.SvgElementIdManager"/>.
            </summary>
            <param name="document">The <see cref="T:Telerik.Imaging.Svg.SvgObject"/> containing the <see cref="T:Telerik.Imaging.Svg.SvgElement"/>s to manage.</param>
        </member>
        <member name="T:Telerik.Imaging.Svg.SvgFontManager">
            <summary>
            Manages access to <see cref="F:Telerik.Imaging.Svg.SvgFontManager.SystemFonts"/> and any privately loaded fonts.
            When a font is requested in the render process, if the font is not found as an embedded SvgFont, the render
            process will SvgFontManager.FindFont method.
            </summary>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgFontManager.FindFont(System.String)">
            <summary>
            This method searches a dictionary of fonts (pre loaded with the system fonts). If a
            font can't be found and a callback has been provided - then the callback should perform
            any validation and return a font (or null if not found/error).
            Where a font can't be located it is the responsibility of the caller to perform any
            exception handling.
            </summary>
            <param name="name">A <see cref="T:System.String"/> containing the FamilyName of the font.</param>
            <returns>The name of the loaded font or null is not located.</returns>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgNodeReader.Value">
            <summary>
            Gets the text value of the current node.
            </summary>
            <value></value>
            <returns>The value returned depends on the <see cref="P:System.Xml.XmlTextReader.NodeType"/> of the node. The following table lists node types that have a value to return. All other node types return String.Empty.Node Type Value AttributeThe value of the attribute. CDATAThe content of the CDATA section. CommentThe content of the comment. DocumentTypeThe internal subset. ProcessingInstructionThe entire content, excluding the target. SignificantWhitespaceThe white space within an xml:space= 'preserve' scope. TextThe content of the text node. WhitespaceThe white space between markup. XmlDeclarationThe content of the declaration. </returns>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgNodeReader.LocalName">
            <summary>
            Gets the local name of the current node.
            </summary>
            <value></value>
            <returns>The name of the current node with the prefix removed. For example, LocalName is book for the element &lt;bk:book&gt;.For node types that do not have a name (like Text, Comment, and so on), this property returns String.Empty.</returns>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgNodeReader.MoveToNextAttribute">
            <summary>
            Moves to the next attribute.
            </summary>
            <returns>
            true if there is a next attribute; false if there are no more attributes.
            </returns>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgNodeReader.Read">
            <summary>
            Reads the next node from the stream.
            </summary>
            <returns>
            true if the next node was read successfully; false if there are no more nodes to read.
            </returns>
            <exception cref="T:System.Xml.XmlException">An error occurred while parsing the XML. </exception>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgNodeReader.ResolveEntity">
            <summary>
            Resolves the entity reference for EntityReference nodes.
            </summary>
        </member>
        <member name="T:Telerik.Imaging.Svg.SvgObject">
            <summary>
            The class used to create and load SVG documents.
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgObject.SkipGdiPlusCapabilityCheck">
            <summary>
            Skip check whether the GDI+ can be loaded.
            </summary>
            <remarks>
            Set to true on systems that do not support GDI+ like UWP.
            </remarks>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgObject.#ctor">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Imaging.Svg.SvgObject"/> class.
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgObject.IdManager">
            <summary>
            Gets an <see cref="T:Telerik.Imaging.Svg.SvgElementIdManager"/> for this document.
            </summary>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgObject.OverwriteIdManager(Telerik.Imaging.Svg.SvgElementIdManager)">
            <summary>
            Overwrites the current IdManager with a custom implementation. 
            Be careful with this: If elements have been inserted into the document before,
            you have to take care that the new IdManager also knows of them.
            </summary>
            <param name="manager"></param>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgObject.Ppi">
            <summary>
            Gets or sets the Pixels Per Inch of the rendered image.
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgObject.ExternalCSSHref">
            <summary>
            Gets or sets an external Cascading Style Sheet (CSS)
            </summary>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgObject.GetElementById(System.String)">
            <summary>
            Retrieves the <see cref="T:Telerik.Imaging.Svg.SvgElement"/> with the specified ID.
            </summary>
            <param name="id">A <see cref="T:System.String"/> containing the ID of the element to find.</param>
            <returns>An <see cref="T:Telerik.Imaging.Svg.SvgElement"/> of one exists with the specified ID; otherwise false.</returns>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgObject.GetElementById``1(System.String)">
            <summary>
            Retrieves the <see cref="T:Telerik.Imaging.Svg.SvgElement"/> with the specified ID.
            </summary>
            <param name="id">A <see cref="T:System.String"/> containing the ID of the element to find.</param>
            <returns>An <see cref="T:Telerik.Imaging.Svg.SvgElement"/> of one exists with the specified ID; otherwise false.</returns>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgObject.Open(System.String)">
            <summary>
            Opens the document at the specified path and loads the SVG contents.
            </summary>
            <param name="path">A <see cref="T:System.String"/> containing the path of the file to open.</param>
            <returns>An <see cref="T:Telerik.Imaging.Svg.SvgObject"/> with the contents loaded.</returns>
            <exception cref="T:System.IO.FileNotFoundException">The document at the specified <paramref name="path"/> cannot be found.</exception>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgObject.Open``1(System.String)">
            <summary>
            Opens the document at the specified path and loads the SVG contents.
            </summary>
            <param name="path">A <see cref="T:System.String"/> containing the path of the file to open.</param>
            <returns>An <see cref="T:Telerik.Imaging.Svg.SvgObject"/> with the contents loaded.</returns>
            <exception cref="T:System.IO.FileNotFoundException">The document at the specified <paramref name="path"/> cannot be found.</exception>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgObject.Open``1(System.String,System.Collections.Generic.Dictionary{System.String,System.String})">
            <summary>
            Opens the document at the specified path and loads the SVG contents.
            </summary>
            <param name="path">A <see cref="T:System.String"/> containing the path of the file to open.</param>
            <param name="entities">A dictionary of custom entity definitions to be used when resolving XML entities within the document.</param>
            <returns>An <see cref="T:Telerik.Imaging.Svg.SvgObject"/> with the contents loaded.</returns>
            <exception cref="T:System.IO.FileNotFoundException">The document at the specified <paramref name="path"/> cannot be found.</exception>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgObject.Open``1(System.IO.Stream)">
            <summary>
            Attempts to open an SVG document from the specified <see cref="T:System.IO.Stream"/>.
            </summary>
            <param name="stream">The <see cref="T:System.IO.Stream"/> containing the SVG document to open.</param>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgObject.FromSvg``1(System.String)">
            <summary>
            Attempts to create an SVG document from the specified string data.
            </summary>
            <param name="svg">The SVG data.</param>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgObject.Open``1(System.IO.Stream,System.Collections.Generic.Dictionary{System.String,System.String})">
            <summary>
            Opens an SVG document from the specified <see cref="T:System.IO.Stream"/> and adds the specified entities.
            </summary>
            <param name="stream">The <see cref="T:System.IO.Stream"/> containing the SVG document to open.</param>
            <param name="entities">Custom entity definitions.</param>
            <exception cref="T:System.ArgumentNullException">The <paramref name="stream"/> parameter cannot be <c>null</c>.</exception>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgObject.Open(System.Xml.XmlDocument)">
            <summary>
            Opens an SVG document from the specified <see cref="T:System.Xml.XmlDocument"/>.
            </summary>
            <param name="document">The <see cref="T:System.Xml.XmlDocument"/> containing the SVG document XML.</param>
            <exception cref="T:System.ArgumentNullException">The <paramref name="document"/> parameter cannot be <c>null</c>.</exception>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgObject.Draw(Telerik.Imaging.Svg.ISvgRenderer)">
            <summary>
            Renders the <see cref="T:Telerik.Imaging.Svg.SvgObject"/> to the specified <see cref="T:Telerik.Imaging.Svg.ISvgRenderer"/>.
            </summary>
            <param name="renderer">The <see cref="T:Telerik.Imaging.Svg.ISvgRenderer"/> to render the document with.</param>
            <exception cref="T:System.ArgumentNullException">The <paramref name="renderer"/> parameter cannot be <c>null</c>.</exception>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgObject.RasterizeDimensions(System.Windows.Size@,System.Int32,System.Int32)">
            <summary>
            If both or one of raster height and width is not given (0), calculate that missing value from original SVG size
            while keeping original SVG size ratio
            </summary>
            <param name="size"></param>
            <param name="rasterWidth"></param>
            <param name="rasterHeight"></param>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgTextReader.Value">
            <summary>
            Gets the text value of the current node.
            </summary>
            <value></value>
            <returns>The value returned depends on the <see cref="P:System.Xml.XmlTextReader.NodeType"/> of the node. The following table lists node types that have a value to return. All other node types return String.Empty.Node Type Value AttributeThe value of the attribute. CDATAThe content of the CDATA section. CommentThe content of the comment. DocumentTypeThe internal subset. ProcessingInstructionThe entire content, excluding the target. SignificantWhitespaceThe white space within an xml:space= 'preserve' scope. TextThe content of the text node. WhitespaceThe white space between markup. XmlDeclarationThe content of the declaration. </returns>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgTextReader.LocalName">
            <summary>
            Gets the local name of the current node.
            </summary>
            <value></value>
            <returns>The name of the current node with the prefix removed. For example, LocalName is book for the element &lt;bk:book&gt;.For node types that do not have a name (like Text, Comment, and so on), this property returns String.Empty.</returns>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgTextReader.MoveToNextAttribute">
            <summary>
            Moves to the next attribute.
            </summary>
            <returns>
            true if there is a next attribute; false if there are no more attributes.
            </returns>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgTextReader.Read">
            <summary>
            Reads the next node from the stream.
            </summary>
            <returns>
            true if the next node was read successfully; false if there are no more nodes to read.
            </returns>
            <exception cref="T:System.Xml.XmlException">An error occurred while parsing the XML. </exception>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgTextReader.ResolveEntity">
            <summary>
            Resolves the entity reference for EntityReference nodes.
            </summary>
        </member>
        <member name="T:Telerik.Imaging.Svg.FontFamily">
            <summary>
            http://stackoverflow.com/questions/3633000/net-enumerate-winforms-font-styles
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgGlyph.PathData">
            <summary>
            Gets or sets a <see cref="T:Telerik.Imaging.Svg.SvgPathSegmentList"/> of path data.
            </summary>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgGlyph.Path(Telerik.Imaging.Svg.ISvgRenderer)">
            <summary>
            Gets the <see cref="T:Telerik.Windows.Documents.Fixed.Model.Graphics.PathGeometry"/> for this element.
            </summary>
        </member>
        <member name="T:Telerik.Imaging.Svg.SvgText">
            <summary>
            The <see cref="T:Telerik.Imaging.Svg.SvgText"/> element defines a graphics element consisting of text.
            </summary>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgText.#ctor">
            <summary>
            Initializes the <see cref="T:Telerik.Imaging.Svg.SvgText"/> class.
            </summary>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgText.#ctor(System.String)">
            <summary>
            Initializes a new instance of the <see cref="T:Telerik.Imaging.Svg.SvgText"/> class.
            </summary>
            <param name="text">The text.</param>
        </member>
        <member name="T:Telerik.Imaging.Svg.SvgTextAnchor">
            <summary>
            Text anchor is used to align (start-, middle- or end-alignment) a string of text relative to a given point.
            </summary>
        </member>
        <member name="F:Telerik.Imaging.Svg.SvgTextAnchor.Inherit">
            <summary>The value is inherited from the parent element.</summary>
        </member>
        <member name="F:Telerik.Imaging.Svg.SvgTextAnchor.Start">
            <summary>
            The rendered characters are aligned such that the start of the text string is at the initial current text position.
            </summary>
        </member>
        <member name="F:Telerik.Imaging.Svg.SvgTextAnchor.Middle">
            <summary>
            The rendered characters are aligned such that the middle of the text string is at the current text position.
            </summary>
        </member>
        <member name="F:Telerik.Imaging.Svg.SvgTextAnchor.End">
            <summary>
            The rendered characters are aligned such that the end of the text string is at the initial current text position.
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgTextBase.Text">
            <summary>
            Gets or sets the text to be rendered.
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgTextBase.X">
            <summary>
            Gets or sets the X.
            </summary>
            <value>The X.</value>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgTextBase.Dx">
            <summary>
            Gets or sets the dX.
            </summary>
            <value>The dX.</value>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgTextBase.Y">
            <summary>
            Gets or sets the Y.
            </summary>
            <value>The Y.</value>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgTextBase.Dy">
            <summary>
            Gets or sets the dY.
            </summary>
            <value>The dY.</value>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgTextBase.Rotate">
            <summary>
            Gets or sets the rotate.
            </summary>
            <value>The rotate.</value>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgTextBase.TextLength">
            <summary>
            The pre-calculated length of the text
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgTextBase.LengthAdjust">
            <summary>
            Gets or sets the text anchor.
            </summary>
            <value>The text anchor.</value>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgTextBase.LetterSpacing">
            <summary>
            Specifies spacing behavior between text characters.
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgTextBase.WordSpacing">
            <summary>
            Specifies spacing behavior between words.
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgTextBase.Fill">
            <summary>
            Gets or sets the fill.
            </summary>
            <remarks>
            <para>Unlike other <see cref="T:Telerik.Imaging.Svg.SvgVisualElement"/>s, <see cref="T:Telerik.Imaging.Svg.SvgText"/> has a default fill of black rather than transparent.</para>
            </remarks>
            <value>The fill.</value>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgTextBase.ToString">
            <summary>
            Returns a <see cref="T:System.String"/> that represents the current <see cref="T:System.Object"/>.
            </summary>
            <returns>
            A <see cref="T:System.String"/> that represents the current <see cref="T:System.Object"/>.
            </returns>
        </member>
        <member name="P:Telerik.Imaging.Svg.SvgTextBase.Bounds">
            <summary>
            Gets the bounds of the element.
            </summary>
            <value>The bounds.</value>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgTextBase.Path(Telerik.Imaging.Svg.ISvgRenderer)">
            <summary>
            Gets the <see cref="T:Telerik.Windows.Documents.Fixed.Model.Graphics.PathGeometry"/> for this element.
            </summary>
            <value></value>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgTextBase.SetPath(Telerik.Imaging.Svg.TextDrawingState,System.Boolean)">
            <summary>
            Sets the path on this element and all child elements.  Uses the state
            object to track the state of the drawing
            </summary>
            <param name="state">State of the drawing operation</param>
            <param name="doMeasurements">If true, calculate and apply text length adjustments.</param>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgTextBase.PrepareText(System.String)">
            <summary>
            Prepare the text according to the whitespace handling rules and text transformations.  <see href="http://www.w3.org/TR/SVG/text.html">SVG Spec</see>.
            </summary>
            <param name="value">Text to be prepared</param>
            <returns>Prepared text</returns>
        </member>
        <member name="T:Telerik.Imaging.Svg.ISvgTransformable">
            <summary>
            Represents and element that may be transformed.
            </summary>
        </member>
        <member name="P:Telerik.Imaging.Svg.ISvgTransformable.Transforms">
            <summary>
            Gets or sets an <see cref="T:Telerik.Imaging.Svg.SvgTransformCollection"/> of element transforms.
            </summary>
        </member>
        <member name="M:Telerik.Imaging.Svg.ISvgTransformable.PushTransforms(Telerik.Imaging.Svg.ISvgRenderer)">
            <summary>
            Applies the required transforms to <see cref="T:Telerik.Imaging.Svg.ISvgRenderer"/>.
            </summary>
            <param name="renderer">The <see cref="T:Telerik.Imaging.Svg.ISvgRenderer"/> to be transformed.</param>
        </member>
        <member name="M:Telerik.Imaging.Svg.ISvgTransformable.PopTransforms(Telerik.Imaging.Svg.ISvgRenderer)">
            <summary>
            Removes any previously applied transforms from the specified <see cref="T:Telerik.Imaging.Svg.ISvgRenderer"/>.
            </summary>
            <param name="renderer">The <see cref="T:Telerik.Imaging.Svg.ISvgRenderer"/> that should have transforms removed.</param>
        </member>
        <member name="T:Telerik.Imaging.Svg.SvgMatrix">
            <summary>
            The class which applies custom transform to this Matrix (Required for projects created by the Inkscape).
            </summary>
        </member>
        <member name="T:Telerik.Imaging.Svg.SvgShear">
            <summary>
            The class which applies the specified shear vector to this Matrix.
            </summary>
        </member>
        <member name="T:Telerik.Imaging.Svg.SvgSkew">
            <summary>
            The class which applies the specified skew vector to this Matrix.
            </summary>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgTransformCollection.GetMatrix">
            <summary>
            Multiplies all matrices
            </summary>
            <returns>The result of all transforms</returns>
        </member>
        <member name="E:Telerik.Imaging.Svg.SvgTransformCollection.TransformChanged">
            <summary>
            Fired when an SvgTransform has changed
            </summary>
        </member>
        <member name="M:Telerik.Imaging.Svg.SvgTransformConverter.ConvertFrom(System.ComponentModel.ITypeDescriptorContext,System.Globalization.CultureInfo,System.Object)">
            <summary>
            Converts the given object to the type of this converter, using the specified context and culture information.
            </summary>
            <param name="context">An <see cref="T:System.ComponentModel.ITypeDescriptorContext"/> that provides a format context.</param>
            <param name="culture">The <see cref="T:System.Globalization.CultureInfo"/> to use as the current culture.</param>
            <param name="value">The <see cref="T:System.Object"/> to convert.</param>
            <returns>
            An <see cref="T:System.Object"/> that represents the converted value.
            </returns>
            <exception cref="T:System.NotSupportedException">The conversion cannot be performed. </exception>
        </member>
        <member name="T:System.Collections.Generic.DictionaryExtensions">
            <summary>
            Extension methods for dictionaries.
            </summary>
        </member>
        <member name="M:System.Collections.Generic.DictionaryExtensions.GetValueOrNull``2(System.Collections.Generic.IDictionary{``0,``1},``0)">
            <summary>
            Gets the value or null.
            </summary>
            <typeparam name="TKey">The type of the T key.</typeparam>
            <typeparam name="TValue">The type of the T value.</typeparam>
            <param name="dictionary">The dictionary.</param>
            <param name="key">The key.</param>
            <returns></returns>
        </member>
    </members>
</doc>
