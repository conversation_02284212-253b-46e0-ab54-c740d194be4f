`# Form Design Guidelines

This document provides guidelines for implementing consistent form design principles across the ProManage application.

## Table of Contents

1. [Form Design Principles](#form-design-principles)
2. [Grid Configuration](#grid-configuration)
3. [Navigation Controls](#navigation-controls)
4. [Status Indicators](#status-indicators)
5. [Implementation Examples](#implementation-examples)

## Form Design Principles

### Row-wise Data Entry (Excel-like)

- Controls should be organized in rows, with labels on the left and input controls on the right
- Each row should contain related data fields
- Tab order should follow the row order, moving from left to right within each row
- Use consistent spacing between rows (typically 10-15 pixels)

### Visible Black Borders on All Controls

- All input controls should have visible black borders
- Use BorderStyle.FixedSingle for standard controls
- Use BorderStyles.Simple with BorderColor = Color.Black for DevExpress controls
- Maintain consistent border thickness across all controls

### Light Gray Backgrounds for Input Areas

- Use Color.FromArgb(245, 245, 245) for form backgrounds
- Use Color.White for input control backgrounds
- Use Color.FromArgb(240, 240, 240) for header areas
- Maintain consistent background colors across all forms

### Consistent Spacing and Alignment

- Align labels to the right and input controls to the left
- Maintain consistent spacing between labels and input controls (typically 10 pixels)
- Use consistent control heights (typically 25 pixels for text boxes)
- Align controls with the same function across different forms

### Logical Tab Order Within Rows

- Tab order should move from left to right within each row
- After reaching the end of a row, tab should move to the first control in the next row
- Skip non-input controls (labels, panels, etc.) in the tab order
- Ensure tab order is set correctly for all input controls

## Grid Configuration

### Excel-like Layout with Visible Gridlines

- Use GridLineColor = Color.FromArgb(220, 220, 220) for subtle gridlines
- Enable both horizontal and vertical gridlines
- Use a light header background color (HeaderBackgroundColor = Color.FromArgb(240, 240, 240))
- Configure consistent column widths based on content type

### Automatic Row Numbering

- Enable the indicator column (gridView.OptionsView.ShowIndicator = true)
- Implement custom drawing for the indicator column to show row numbers
- Ensure row numbers update correctly when rows are added, removed, or reordered
- Use consistent formatting for row numbers

### Footer Rows Showing Sums and Counts

- Enable the footer (gridView.OptionsView.ShowFooter = true)
- Configure sum columns for numeric fields
- Configure count columns for key fields
- Use consistent formatting for footer values
- Make footer visually distinct from data rows

### Automatic Updates When Data Changes

- Configure the grid to automatically update when data changes
- Use even/odd row styling for better readability
- Implement proper data binding to ensure updates are reflected immediately
- Handle data source changes appropriately

### Visual Distinction Between Data and Footer Rows

- Use different background colors for data rows and footer rows
- Use bold font for footer rows
- Use consistent styling for focused and selected rows
- Ensure proper contrast for all row types

## Navigation Controls

### First/Previous/Next/Last Buttons

- Implement consistent navigation buttons across all forms
- Use standard icons for navigation buttons
- Place navigation buttons in a consistent location (typically in the ribbon or toolbar)
- Ensure buttons have appropriate tooltips

### Conditional Enabling Based on Record Position

- Disable First and Previous buttons when on the first record
- Disable Next and Last buttons when on the last record
- Disable all navigation buttons when there are no records
- Update button states whenever the current record changes

### Navigation Retrieves Complete Record Details

- Implement navigation to load the complete record when a button is clicked
- Ensure all form fields are updated with the current record's data
- Handle any validation or data saving before navigation
- Provide visual feedback during navigation

### Editing Only Enabled When Edit Button is Clicked

- Implement a consistent edit mode across all forms
- Disable input controls when not in edit mode
- Enable input controls only when the Edit button is clicked
- Provide visual feedback about the current edit state

### Sidebar Controls and Grid Display Update with Current Record's Data

- Ensure all form controls update when the current record changes
- Synchronize sidebar controls with the grid selection
- Maintain consistent state between different views of the same data
- Handle any validation or data saving before updating

## Status Indicators

### Clear Status Messages

- Implement consistent status messages across all forms
- Use a dedicated status label for messages
- Place status indicators in a consistent location (typically at the bottom of the form)
- Use appropriate colors for different types of messages

### Color-coded Indicators

- Use consistent colors for different status types:
  - Success: Color.FromArgb(16, 124, 16) (green)
  - Error: Color.FromArgb(232, 17, 35) (red)
  - Warning: Color.FromArgb(255, 128, 0) (orange)
  - Info: Color.FromArgb(0, 120, 212) (blue)
- Ensure sufficient contrast for all status colors
- Use consistent styling for status indicators

### Progress Bars for Long Operations

- Implement progress bars for operations that take more than a few seconds
- Use consistent styling for progress bars
- Provide meaningful progress updates
- Allow cancellation of long operations where appropriate

### Toggle Buttons for State Changes (Active/Closed)

- Implement toggle buttons for binary state changes
- Use consistent styling for toggle buttons
- Provide clear visual feedback about the current state
- Update related UI elements when the state changes

### Visual Feedback for Validation Errors

- Implement consistent validation error indicators
- Use an ErrorProvider for showing validation errors
- Provide clear error messages
- Ensure errors are cleared when the issue is resolved

## Implementation Examples

### Using FormDesignHelper

The `FormDesignHelper` class provides methods for implementing these design principles:

```csharp
// Apply all form design principles
FormDesignHelper.ApplyFormDesignPrinciples(this);

// Configure a grid for Excel-like appearance
FormDesignHelper.ConfigureGridForExcelLikeAppearance(gridControl1, gridView1);
FormDesignHelper.SetupRowNumbering(gridView1);
FormDesignHelper.ConfigureFooterRows(gridView1, 
    new[] { "Quantity", "Price", "Total" },  // Sum columns
    new[] { "PartNumber" });                 // Count columns
FormDesignHelper.SetupAutoUpdates(gridView1);
FormDesignHelper.ConfigureRowAppearance(gridView1);

// Set up navigation controls
FormDesignHelper.SetupNavigationControls(btnFirst, btnPrevious, btnNext, btnLast);
FormDesignHelper.UpdateNavigationButtonState(btnFirst, btnPrevious, btnNext, btnLast, currentIndex, totalRecords);

// Configure status indicators
FormDesignHelper.SetupStatusIndicators(lblStatus);
FormDesignHelper.UpdateStatusMessage(lblStatus, "Record saved successfully", StatusMessageType.Success);
FormDesignHelper.SetupProgressBar(progressBarControl1);
FormDesignHelper.ConfigureToggleButton(barToggleSwitchItem1, "Status: Active", "Status: Closed");
FormDesignHelper.SetupValidationFeedback(errorProvider1);
```

### Example Form Implementation

Here's an example of how to implement these design principles in a form:

```csharp
public partial class ExampleForm : DevExpress.XtraEditors.XtraForm
{
    private ErrorProvider _errorProvider;
    
    public ExampleForm()
    {
        InitializeComponent();
        
        // Set up error provider
        _errorProvider = new ErrorProvider(this);
        
        // Apply form design principles
        FormDesignHelper.ApplyFormDesignPrinciples(this);
        
        // Configure grid
        FormDesignHelper.ConfigureGridForExcelLikeAppearance(gridControl1, gridView1);
        FormDesignHelper.SetupRowNumbering(gridView1);
        FormDesignHelper.ConfigureFooterRows(gridView1, 
            new[] { "Quantity", "Price", "Total" },
            new[] { "PartNumber" });
        FormDesignHelper.SetupAutoUpdates(gridView1);
        FormDesignHelper.ConfigureRowAppearance(gridView1);
        
        // Set up navigation controls
        FormDesignHelper.SetupNavigationControls(btnFirst, btnPrevious, btnNext, btnLast);
        
        // Configure status indicators
        FormDesignHelper.SetupStatusIndicators(lblStatus);
        FormDesignHelper.SetupProgressBar(progressBarControl1);
        FormDesignHelper.ConfigureToggleButton(barToggleSwitchItem1, "Status: Active", "Status: Closed");
        FormDesignHelper.SetupValidationFeedback(_errorProvider);
    }
    
    // Update navigation button state when the current record changes
    private void UpdateNavigation(int currentIndex, int totalRecords)
    {
        FormDesignHelper.UpdateNavigationButtonState(
            btnFirst, btnPrevious, btnNext, btnLast, currentIndex, totalRecords);
    }
    
    // Show a validation error
    private void ShowError(Control control, string errorMessage)
    {
        FormDesignHelper.ShowValidationError(_errorProvider, control, errorMessage);
    }
    
    // Clear all validation errors
    private void ClearErrors()
    {
        FormDesignHelper.ClearValidationErrors(_errorProvider, this);
    }
}
```
