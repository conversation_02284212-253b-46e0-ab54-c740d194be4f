<?xml version="1.0" encoding="utf-8"?>
<Project>
  <PropertyGroup>
    <RuntimeIdentifiers>win-x86</RuntimeIdentifiers>
    <!-- Explicitly clear any post-build event that might be causing issues -->
    <PostBuildEvent></PostBuildEvent>
  </PropertyGroup>

  <!-- Custom target to copy the System.Resources.Extensions.dll after build -->
  <Target Name="CopySystemResourcesExtensionsToOutput" AfterTargets="Build">
    <Copy
      SourceFiles="$(SolutionDir)packages\System.Resources.Extensions.8.0.0\lib\net462\System.Resources.Extensions.dll"
      DestinationFolder="$(TargetDir)"
      SkipUnchangedFiles="true"
      ContinueOnError="true" />
    <Message Importance="high" Text="Copied System.Resources.Extensions.dll to $(TargetDir)" />
  </Target>

  <!-- Visual Studio IDE copy target removed to prevent access denied warnings -->
  <!-- The System.Resources.Extensions.dll is already copied to the output directory above -->
  <!-- Copying to Visual Studio IDE directory is not necessary and causes permission issues -->
</Project>
