// EstimateForm Grid Manager - Manages grid operations like adding/removing rows
// Usage: Handles grid row operations and grid state management

using System;
using System.Data;
using System.Diagnostics;
using System.Windows.Forms;

namespace ProManage.Modules.Helpers.EstimateForm
{
    public static class EstimateFormGridManager
    {
        public static void AddNewRow(dynamic form, bool isEditMode)
        {
            try
            {
                if (!isEditMode)
                {
                    MessageBox.Show("Please enter edit mode first to add new rows.", "Information",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                Debug.WriteLine("=== AddNewRow: Starting ===");

                var gridDataTable = form.GridDataTable as DataTable;
                if (gridDataTable == null)
                {
                    Debug.WriteLine("GridDataTable is null, cannot add row");
                    MessageBox.Show("Grid is not properly initialized.", "Error",
                        MessageBoxButtons.OK, MessageBoxIcon.Error);
                    return;
                }

                // Calculate next serial number
                int nextSerialNumber = gridDataTable.Rows.Count + 1;

                // Create new row
                var newRow = gridDataTable.NewRow();
                newRow["SerialNumber"] = nextSerialNumber;
                newRow["PartNumber"] = "";
                newRow["Description"] = "";
                newRow["Quantity"] = 0;
                newRow["OEPrice"] = 0.00m;
                newRow["AFMPrice"] = 0.00m;
                newRow["Remarks"] = "";
                newRow["Status"] = false;

                // Add row to table
                gridDataTable.Rows.Add(newRow);

                // Refresh grid display
                EstimateFormDataMapper.RefreshGridDisplay(form);

                // Focus on the new row
                try
                {
                    int newRowIndex = gridDataTable.Rows.Count - 1;
                    form.GridView1.FocusedRowHandle = newRowIndex;
                    form.GridView1.FocusedColumn = form.GridView1.Columns["PartNumber"];
                }
                catch (Exception ex)
                {
                    Debug.WriteLine($"Could not focus on new row: {ex.Message}");
                }

                Debug.WriteLine($"Added new row with serial number {nextSerialNumber}");
                Debug.WriteLine("=== AddNewRow: Completed ===");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in AddNewRow: {ex.Message}");
                MessageBox.Show($"Error adding new row: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        public static void RemoveSelectedRow(dynamic form, bool isEditMode)
        {
            try
            {
                if (!isEditMode)
                {
                    MessageBox.Show("Please enter edit mode first to remove rows.", "Information",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                Debug.WriteLine("=== RemoveSelectedRow: Starting ===");

                var gridDataTable = form.GridDataTable as DataTable;
                if (gridDataTable == null || gridDataTable.Rows.Count == 0)
                {
                    MessageBox.Show("No rows to remove.", "Information",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // Get selected row index
                int selectedRowHandle = form.GridView1.FocusedRowHandle;
                if (selectedRowHandle < 0 || selectedRowHandle >= gridDataTable.Rows.Count)
                {
                    MessageBox.Show("Please select a row to remove.", "Information",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // Confirm deletion
                var result = MessageBox.Show("Are you sure you want to remove the selected row?", "Confirm Remove",
                    MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result != DialogResult.Yes)
                {
                    return;
                }

                // Remove the row
                gridDataTable.Rows.RemoveAt(selectedRowHandle);

                // Renumber serial numbers
                RenumberSerialNumbers(gridDataTable);

                // Refresh grid display
                EstimateFormDataMapper.RefreshGridDisplay(form);

                Debug.WriteLine($"Removed row at index {selectedRowHandle}");
                Debug.WriteLine("=== RemoveSelectedRow: Completed ===");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in RemoveSelectedRow: {ex.Message}");
                MessageBox.Show($"Error removing row: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        public static void ClearAllRows(dynamic form, bool isEditMode)
        {
            try
            {
                if (!isEditMode)
                {
                    MessageBox.Show("Please enter edit mode first to clear rows.", "Information",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                Debug.WriteLine("=== ClearAllRows: Starting ===");

                var gridDataTable = form.GridDataTable as DataTable;
                if (gridDataTable == null || gridDataTable.Rows.Count == 0)
                {
                    MessageBox.Show("Grid is already empty.", "Information",
                        MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }

                // Confirm clearing
                var result = MessageBox.Show($"Are you sure you want to clear all {gridDataTable.Rows.Count} rows?", "Confirm Clear",
                    MessageBoxButtons.YesNo, MessageBoxIcon.Question);

                if (result != DialogResult.Yes)
                {
                    return;
                }

                // Clear all rows
                gridDataTable.Clear();

                // Refresh grid display
                EstimateFormDataMapper.RefreshGridDisplay(form);

                Debug.WriteLine("Cleared all rows from grid");
                Debug.WriteLine("=== ClearAllRows: Completed ===");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in ClearAllRows: {ex.Message}");
                MessageBox.Show($"Error clearing rows: {ex.Message}", "Error",
                    MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        public static void RenumberSerialNumbers(DataTable gridDataTable)
        {
            try
            {
                Debug.WriteLine("=== RenumberSerialNumbers: Starting ===");

                for (int i = 0; i < gridDataTable.Rows.Count; i++)
                {
                    gridDataTable.Rows[i]["SerialNumber"] = i + 1;
                }

                Debug.WriteLine($"Renumbered {gridDataTable.Rows.Count} rows");
                Debug.WriteLine("=== RenumberSerialNumbers: Completed ===");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in RenumberSerialNumbers: {ex.Message}");
            }
        }

        /// <summary>
        /// Updates serial numbers after row deletion (alias for RenumberSerialNumbers)
        /// </summary>
        /// <param name="gridDataTable">The data table to update</param>
        public static void UpdateSerialNumbers(DataTable gridDataTable)
        {
            RenumberSerialNumbers(gridDataTable);
        }

        public static void ValidateGridData(dynamic form)
        {
            try
            {
                Debug.WriteLine("=== ValidateGridData: Starting ===");

                var gridDataTable = form.GridDataTable as DataTable;
                if (gridDataTable == null)
                {
                    Debug.WriteLine("GridDataTable is null");
                    return;
                }

                int validRows = 0;
                int emptyRows = 0;
                int errorRows = 0;

                foreach (DataRow row in gridDataTable.Rows)
                {
                    try
                    {
                        string partNo = row["PartNumber"]?.ToString()?.Trim();
                        string description = row["Description"]?.ToString()?.Trim();

                        if (string.IsNullOrWhiteSpace(partNo) && string.IsNullOrWhiteSpace(description))
                        {
                            emptyRows++;
                        }
                        else
                        {
                            validRows++;
                        }
                    }
                    catch
                    {
                        errorRows++;
                    }
                }

                Debug.WriteLine($"Grid validation results: Valid={validRows}, Empty={emptyRows}, Errors={errorRows}");
                Debug.WriteLine("=== ValidateGridData: Completed ===");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in ValidateGridData: {ex.Message}");
            }
        }

        public static void SetGridEditMode(dynamic form, bool editMode)
        {
            try
            {
                Debug.WriteLine($"=== SetGridEditMode: Setting to {editMode} ===");

                // Keep grid enabled for navigation but control editing behavior
                form.GridControl1.Enabled = true; // Always keep grid enabled for navigation

                // Control editing capabilities
                form.GridView1.OptionsBehavior.Editable = editMode;
                form.GridView1.OptionsBehavior.AllowAddRows = editMode ? DevExpress.Utils.DefaultBoolean.True : DevExpress.Utils.DefaultBoolean.False;
                form.GridView1.OptionsBehavior.AllowDeleteRows = editMode ? DevExpress.Utils.DefaultBoolean.True : DevExpress.Utils.DefaultBoolean.False;

                // Set read-only mode for all columns when not in edit mode
                foreach (DevExpress.XtraGrid.Columns.GridColumn column in form.GridView1.Columns)
                {
                    if (column.FieldName != "SerialNumber") // Serial number is always read-only
                    {
                        column.OptionsColumn.AllowEdit = editMode;
                        column.OptionsColumn.ReadOnly = !editMode;
                    }
                }

                // Update grid appearance based on edit mode
                if (editMode)
                {
                    // Edit mode - normal appearance
                    form.GridView1.Appearance.Row.BackColor = System.Drawing.Color.White;
                    form.GridView1.Appearance.Row.ForeColor = System.Drawing.Color.Black;
                    form.GridView1.Appearance.FocusedRow.BackColor = System.Drawing.Color.FromArgb(49, 106, 197);
                    form.GridView1.Appearance.FocusedRow.ForeColor = System.Drawing.Color.White;
                }
                else
                {
                    // Read-only mode - subtle visual indication
                    form.GridView1.Appearance.Row.BackColor = System.Drawing.Color.FromArgb(248, 248, 248);
                    form.GridView1.Appearance.Row.ForeColor = System.Drawing.Color.FromArgb(64, 64, 64);
                    form.GridView1.Appearance.FocusedRow.BackColor = System.Drawing.Color.FromArgb(220, 220, 220);
                    form.GridView1.Appearance.FocusedRow.ForeColor = System.Drawing.Color.Black;
                }

                form.GridView1.RefreshData();

                Debug.WriteLine($"Grid edit mode set to: {editMode} (Grid remains enabled for navigation)");
                Debug.WriteLine("=== SetGridEditMode: Completed ===");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in SetGridEditMode: {ex.Message}");
            }
        }
    }
}
