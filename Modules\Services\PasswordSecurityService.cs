using System;
using System.Security.Cryptography;
using System.Text;
using System.Diagnostics;

namespace ProManage.Modules.Services
{
    /// <summary>
    /// Service for handling password security operations including hashing and validation.
    /// Supports dual password system: login passwords (hash + salt) and edit passwords (hash only).
    /// </summary>
    public static class PasswordSecurityService
    {
        #region Constants

        /// <summary>
        /// Length of the salt in bytes
        /// </summary>
        private const int SaltLength = 32;

        /// <summary>
        /// Number of iterations for PBKDF2
        /// </summary>
        private const int Iterations = 10000;

        #endregion

        #region Login Password Methods (Hash + Salt)

        /// <summary>
        /// Generates a secure hash and salt for login passwords
        /// </summary>
        /// <param name="password">The plain text password</param>
        /// <param name="hash">Output: The generated hash</param>
        /// <param name="salt">Output: The generated salt</param>
        public static void GenerateLoginPasswordHash(string password, out string hash, out string salt)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(password))
                {
                    throw new ArgumentException("Password cannot be null or empty", nameof(password));
                }

                // Generate a random salt
                byte[] saltBytes = GenerateSalt();
                salt = Convert.ToBase64String(saltBytes);

                // Generate hash using PBKDF2
                hash = GenerateHashWithSalt(password, saltBytes);

                Debug.WriteLine("Login password hash and salt generated successfully");
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error generating login password hash: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Validates a login password against stored hash and salt
        /// </summary>
        /// <param name="password">The plain text password to validate</param>
        /// <param name="storedHash">The stored hash</param>
        /// <param name="storedSalt">The stored salt</param>
        /// <returns>True if password is valid, false otherwise</returns>
        public static bool ValidateLoginPassword(string password, string storedHash, string storedSalt)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(password) || 
                    string.IsNullOrWhiteSpace(storedHash) || 
                    string.IsNullOrWhiteSpace(storedSalt))
                {
                    return false;
                }

                // Convert salt back to bytes
                byte[] saltBytes = Convert.FromBase64String(storedSalt);

                // Generate hash with the stored salt
                string computedHash = GenerateHashWithSalt(password, saltBytes);

                // Compare hashes
                bool isValid = string.Equals(computedHash, storedHash, StringComparison.Ordinal);
                
                Debug.WriteLine($"Login password validation result: {isValid}");
                return isValid;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error validating login password: {ex.Message}");
                return false;
            }
        }

        #endregion

        #region Edit Password Methods (Hash Only)

        /// <summary>
        /// Generates a simple hash for edit passwords (no salt)
        /// </summary>
        /// <param name="password">The plain text password</param>
        /// <returns>The generated hash</returns>
        public static string GenerateEditPasswordHash(string password)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(password))
                {
                    throw new ArgumentException("Password cannot be null or empty", nameof(password));
                }

                using (var sha256 = SHA256.Create())
                {
                    byte[] passwordBytes = Encoding.UTF8.GetBytes(password);
                    byte[] hashBytes = sha256.ComputeHash(passwordBytes);
                    string hash = Convert.ToBase64String(hashBytes);
                    
                    Debug.WriteLine("Edit password hash generated successfully");
                    return hash;
                }
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error generating edit password hash: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Validates an edit password against stored hash
        /// </summary>
        /// <param name="password">The plain text password to validate</param>
        /// <param name="storedHash">The stored hash</param>
        /// <returns>True if password is valid, false otherwise</returns>
        public static bool ValidateEditPassword(string password, string storedHash)
        {
            try
            {
                if (string.IsNullOrWhiteSpace(password) || string.IsNullOrWhiteSpace(storedHash))
                {
                    return false;
                }

                string computedHash = GenerateEditPasswordHash(password);
                bool isValid = string.Equals(computedHash, storedHash, StringComparison.Ordinal);
                
                Debug.WriteLine($"Edit password validation result: {isValid}");
                return isValid;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error validating edit password: {ex.Message}");
                return false;
            }
        }

        #endregion

        #region Helper Methods

        /// <summary>
        /// Generates a random salt
        /// </summary>
        /// <returns>Random salt bytes</returns>
        private static byte[] GenerateSalt()
        {
            using (var rng = RandomNumberGenerator.Create())
            {
                byte[] salt = new byte[SaltLength];
                rng.GetBytes(salt);
                return salt;
            }
        }

        /// <summary>
        /// Generates a hash using PBKDF2 with the provided salt
        /// </summary>
        /// <param name="password">The password to hash</param>
        /// <param name="salt">The salt to use</param>
        /// <returns>The generated hash</returns>
        private static string GenerateHashWithSalt(string password, byte[] salt)
        {
            using (var pbkdf2 = new Rfc2898DeriveBytes(password, salt, Iterations))
            {
                byte[] hashBytes = pbkdf2.GetBytes(32); // 256 bits
                return Convert.ToBase64String(hashBytes);
            }
        }

        /// <summary>
        /// Validates password strength
        /// </summary>
        /// <param name="password">The password to validate</param>
        /// <returns>True if password meets minimum requirements</returns>
        public static bool ValidatePasswordStrength(string password)
        {
            if (string.IsNullOrWhiteSpace(password))
                return false;

            // Minimum requirements: at least 6 characters
            // You can enhance this with more complex rules as needed
            return password.Length >= 6;
        }

        /// <summary>
        /// Gets password strength description
        /// </summary>
        /// <param name="password">The password to evaluate</param>
        /// <returns>Description of password strength</returns>
        public static string GetPasswordStrengthDescription(string password)
        {
            if (string.IsNullOrWhiteSpace(password))
                return "Password is required";

            if (password.Length < 6)
                return "Password must be at least 6 characters";

            if (password.Length < 8)
                return "Weak - Consider using at least 8 characters";

            bool hasUpper = false, hasLower = false, hasDigit = false, hasSpecial = false;
            
            foreach (char c in password)
            {
                if (char.IsUpper(c)) hasUpper = true;
                else if (char.IsLower(c)) hasLower = true;
                else if (char.IsDigit(c)) hasDigit = true;
                else if (!char.IsLetterOrDigit(c)) hasSpecial = true;
            }

            int score = (hasUpper ? 1 : 0) + (hasLower ? 1 : 0) + (hasDigit ? 1 : 0) + (hasSpecial ? 1 : 0);

            switch (score)
            {
                case 4:
                    return "Strong";
                case 3:
                    return "Good";
                case 2:
                    return "Fair";
                default:
                    return "Weak - Use uppercase, lowercase, numbers, and special characters";
            }
        }

        #endregion
    }
}
