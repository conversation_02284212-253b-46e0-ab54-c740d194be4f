-- RBAC-Initial-Data.sql
-- Initial data setup for Role-Based Access Control (RBAC) system
-- Populates default roles and permissions for ProManage application

-- [InsertDefaultRoles] --
-- Insert default roles with conflict handling
INSERT INTO roles (role_name, description) VALUES
('Administrator', 'Full system access with all permissions'),
('Manager', 'Management level access with most permissions'),
('User', 'Standard user with basic permissions'),
('ReadOnly', 'Read-only access to most forms')
ON CONFLICT (role_name) DO NOTHING;
-- [End] --

-- [InsertAdministratorPermissions] --
-- Administrator: All permissions for all forms
INSERT INTO role_permissions (role_id, form_name, read_permission, new_permission, edit_permission, delete_permission, print_permission)
SELECT 
    r.role_id, 
    forms.form_name, 
    TRUE, TRUE, TRUE, TRUE, TRUE
FROM roles r
CROSS JOIN (VALUES 
    ('DatabaseForm'), 
    ('ParametersForm'), 
    ('RoleMasterForm'), 
    ('SQLQueryForm'), 
    ('UserManagementListForm'), 
    ('UserMasterForm')
) AS forms(form_name)
WHERE r.role_name = 'Administrator'
ON CONFLICT (role_id, form_name) DO NOTHING;
-- [End] --

-- [InsertManagerPermissions] --
-- Manager: Most permissions except user management (read-only for user forms)
INSERT INTO role_permissions (role_id, form_name, read_permission, new_permission, edit_permission, delete_permission, print_permission)
SELECT 
    r.role_id, 
    forms.form_name,
    TRUE, -- All managers can read
    CASE WHEN forms.form_name IN ('UserManagementListForm', 'UserMasterForm') THEN FALSE ELSE TRUE END,
    CASE WHEN forms.form_name IN ('UserManagementListForm', 'UserMasterForm') THEN FALSE ELSE TRUE END,
    CASE WHEN forms.form_name IN ('UserManagementListForm', 'UserMasterForm') THEN FALSE ELSE TRUE END,
    TRUE -- All managers can print
FROM roles r
CROSS JOIN (VALUES 
    ('DatabaseForm'), 
    ('ParametersForm'), 
    ('RoleMasterForm'), 
    ('SQLQueryForm'), 
    ('UserManagementListForm'), 
    ('UserMasterForm')
) AS forms(form_name)
WHERE r.role_name = 'Manager'
ON CONFLICT (role_id, form_name) DO NOTHING;
-- [End] --

-- [InsertUserPermissions] --
-- User: Basic permissions (no access to admin forms)
INSERT INTO role_permissions (role_id, form_name, read_permission, new_permission, edit_permission, delete_permission, print_permission)
SELECT 
    r.role_id, 
    forms.form_name,
    CASE WHEN forms.form_name IN ('DatabaseForm', 'SQLQueryForm', 'UserManagementListForm', 'UserMasterForm', 'RoleMasterForm') 
         THEN FALSE ELSE TRUE END,
    CASE WHEN forms.form_name IN ('DatabaseForm', 'SQLQueryForm', 'UserManagementListForm', 'UserMasterForm', 'RoleMasterForm') 
         THEN FALSE ELSE FALSE END,
    CASE WHEN forms.form_name IN ('DatabaseForm', 'SQLQueryForm', 'UserManagementListForm', 'UserMasterForm', 'RoleMasterForm') 
         THEN FALSE ELSE FALSE END,
    FALSE, -- Users cannot delete
    TRUE  -- Users can print
FROM roles r
CROSS JOIN (VALUES 
    ('DatabaseForm'), 
    ('ParametersForm'), 
    ('RoleMasterForm'), 
    ('SQLQueryForm'), 
    ('UserManagementListForm'), 
    ('UserMasterForm')
) AS forms(form_name)
WHERE r.role_name = 'User'
ON CONFLICT (role_id, form_name) DO NOTHING;
-- [End] --

-- [InsertReadOnlyPermissions] --
-- ReadOnly: Only read and print permissions
INSERT INTO role_permissions (role_id, form_name, read_permission, new_permission, edit_permission, delete_permission, print_permission)
SELECT 
    r.role_id, 
    forms.form_name, 
    TRUE, FALSE, FALSE, FALSE, TRUE
FROM roles r
CROSS JOIN (VALUES 
    ('DatabaseForm'), 
    ('ParametersForm'), 
    ('RoleMasterForm'), 
    ('SQLQueryForm'), 
    ('UserManagementListForm'), 
    ('UserMasterForm')
) AS forms(form_name)
WHERE r.role_name = 'ReadOnly'
ON CONFLICT (role_id, form_name) DO NOTHING;
-- [End] --

-- [VerifyRolesCreated] --
-- Verify all default roles were created
SELECT 
    role_id,
    role_name,
    description,
    is_active,
    created_date
FROM roles
ORDER BY role_id;
-- [End] --

-- [VerifyPermissionsCreated] --
-- Verify permissions were created for all roles and forms
SELECT 
    r.role_name,
    rp.form_name,
    rp.read_permission,
    rp.new_permission,
    rp.edit_permission,
    rp.delete_permission,
    rp.print_permission
FROM roles r
JOIN role_permissions rp ON r.role_id = rp.role_id
ORDER BY r.role_name, rp.form_name;
-- [End] --

-- [GetPermissionSummary] --
-- Get summary of permissions by role
SELECT 
    r.role_name,
    COUNT(*) as total_forms,
    SUM(CASE WHEN rp.read_permission THEN 1 ELSE 0 END) as read_count,
    SUM(CASE WHEN rp.new_permission THEN 1 ELSE 0 END) as new_count,
    SUM(CASE WHEN rp.edit_permission THEN 1 ELSE 0 END) as edit_count,
    SUM(CASE WHEN rp.delete_permission THEN 1 ELSE 0 END) as delete_count,
    SUM(CASE WHEN rp.print_permission THEN 1 ELSE 0 END) as print_count
FROM roles r
LEFT JOIN role_permissions rp ON r.role_id = rp.role_id
GROUP BY r.role_id, r.role_name
ORDER BY r.role_name;
-- [End] --
