# ProManage - Reporting Architecture Guide

## Overview

ProManage uses a **centralized reporting system** with DevExpress XtraReports. All reports display through a single `PrintPreviewForm` located in `Forms/ReusableForms/`.

**Key Benefits:**
- One reusable PrintPreviewForm for all reports
- Consistent UI across all forms
- Single point of maintenance

**Architecture:**
- **Reports**: `Modules/Reports/{FormName}/`
- **PrintPreviewForm**: `Forms/ReusableForms/PrintPreviewForm.cs`
- **Namespace**: `ProManage.Forms.UserControlsForms.PrintPreviewForm`

## File Organization

```
Modules/Reports/{FormName}/
├── {FormName}-PrintLayout.cs           # Report template
├── {FormName}-PrintLayout.Designer.cs  # Designer code
├── {FormName}-PrintLayout.resx         # Resources
└── {FormName}ReportService.cs          # Service class
```

**Naming:**
- Report Class: `{FormName}Print` (e.g., `EstimatePrint`)
- Service Class: `{FormName}ReportService` (e.g., `EstimateReportService`)
- Namespace: `ProManage.Modules.Reports`

## Implementation Pattern

**Report Service:**
```csharp
namespace ProManage.Modules.Reports
{
    public static class {FormName}ReportService
    {
        public static {FormName}Print CreateReport(dynamic form)
        {
            var report = new {FormName}Print();
            // Extract and populate data from form
            return report;
        }
    }
}
```

**Form Integration:**
```csharp
private void ShowPrintPreview()
{
    try
    {
        var report = {FormName}ReportService.CreateReport(this);
        if (report == null) return;

        var printPreviewForm = new ProManage.Forms.UserControlsForms.PrintPreviewForm();
        string reportTitle = $"{FormName} - {currentDataObject.Identifier}";
        printPreviewForm.LoadReport(report, reportTitle);
        printPreviewForm.ShowDialog(this);
    }
    catch (Exception ex)
    {
        MessageBox.Show($"Error: {ex.Message}", "Print Error", MessageBoxButtons.OK, MessageBoxIcon.Error);
    }
}
```

## PrintPreviewForm Features

The centralized PrintPreviewForm provides:
- **LoadReport(XtraReport, string)** - Main method to display any report
- **Print/Export Toolbar** - Direct print, PDF/Excel/Word export
- **Zoom/Search** - Standard preview functionality
- **Error Handling** - Built-in validation and error display

## Implementation Steps

1. **Create Report Template**
   - Add DevExpress Report to `Modules/Reports/{FormName}/`
   - Name: `{FormName}-PrintLayout.cs`

2. **Create Report Service**
   - File: `{FormName}ReportService.cs`
   - Extract form data and populate report

3. **Add Print Button to Form**
   - Add `BarButtonItem` to ribbon/toolbar
   - Wire to `ShowPrintPreview()` method

4. **Implement ShowPrintPreview Method**
   - Use centralized PrintPreviewForm pattern above

## Best Practices

- **Always use centralized PrintPreviewForm** - Never create form-specific viewers
- **Modal dialogs** - Use `ShowDialog(this)` for focused viewing
- **Error handling** - Validate data before generating reports
- **Descriptive titles** - Include form name and record identifier

## Reference Implementation

Study `EstimateForm` for complete example:
- `Forms/ChildForms/EstimateForm.cs` (ShowPrintPreview method)
- `Modules/Reports/Estimate/EstimateForm-PrintLayout.cs`
- `Modules/Reports/Estimate/EstimateReportService.cs`
