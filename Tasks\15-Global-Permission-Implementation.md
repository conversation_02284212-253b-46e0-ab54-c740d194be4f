# Task 15: Global Permission Implementation

## Objective
Implement global permission checks for user management operations throughout the application. These permissions control who can create, edit, delete, and print user-related information, providing an additional layer of security beyond form-level permissions.

## Priority
**INTEGRATION** - Depends on Tasks 01-14

## Estimated Time
1.5 hours

## Dependencies
- Task 06: Core Permission Service Logic
- Task 11: User Master Form Permission Integration
- Task 14: Individual Form Permission Checks

## Files to Create/Modify
- `Modules/Services/GlobalPermissionService.cs` (create new)
- Enhance existing user management forms
- `Modules/UI/GlobalPermissionIndicator.cs`

## Global Permission Types

### User Management Permissions
- **CanCreateUsers**: Create new user accounts
- **CanEditUsers**: Modify existing user accounts
- **CanDeleteUsers**: Delete user accounts
- **CanPrintUsers**: Print user reports and information

## Implementation

### GlobalPermissionService.cs
```csharp
using System;
using System.Collections.Generic;
using System.Linq;
using ProManage.Modules.Services;
using ProManage.Modules.Models;
using ProManage.Modules.Connections;

namespace ProManage.Modules.Services
{
    public static class GlobalPermissionService
    {
        private static readonly PermissionDatabaseService _dbService = new PermissionDatabaseService();
        
        #region Global Permission Checking
        
        /// <summary>
        /// Check if user has global permission for user management
        /// </summary>
        public static bool HasGlobalPermission(int userId, GlobalPermissionType permissionType)
        {
            return PermissionService.HasGlobalPermission(userId, permissionType);
        }
        
        /// <summary>
        /// Check if user can create users
        /// </summary>
        public static bool CanCreateUsers(int userId)
        {
            return HasGlobalPermission(userId, GlobalPermissionType.CanCreateUsers);
        }
        
        /// <summary>
        /// Check if user can edit users
        /// </summary>
        public static bool CanEditUsers(int userId)
        {
            return HasGlobalPermission(userId, GlobalPermissionType.CanEditUsers);
        }
        
        /// <summary>
        /// Check if user can delete users
        /// </summary>
        public static bool CanDeleteUsers(int userId)
        {
            return HasGlobalPermission(userId, GlobalPermissionType.CanDeleteUsers);
        }
        
        /// <summary>
        /// Check if user can print user reports
        /// </summary>
        public static bool CanPrintUsers(int userId)
        {
            return HasGlobalPermission(userId, GlobalPermissionType.CanPrintUsers);
        }
        
        #endregion
        
        #region User Management Validation
        
        /// <summary>
        /// Validate user creation operation
        /// </summary>
        public static bool ValidateUserCreation(int currentUserId)
        {
            if (!CanCreateUsers(currentUserId))
            {
                ShowGlobalPermissionError("create new users");
                return false;
            }
            return true;
        }
        
        /// <summary>
        /// Validate user editing operation
        /// </summary>
        public static bool ValidateUserEdit(int currentUserId, int targetUserId)
        {
            // Check global permission
            if (!CanEditUsers(currentUserId))
            {
                ShowGlobalPermissionError("edit users");
                return false;
            }
            
            // Additional validation: prevent users from editing themselves in certain scenarios
            if (currentUserId == targetUserId)
            {
                return ValidateSelfEdit(currentUserId);
            }
            
            return true;
        }
        
        /// <summary>
        /// Validate user deletion operation
        /// </summary>
        public static bool ValidateUserDeletion(int currentUserId, int targetUserId)
        {
            // Check global permission
            if (!CanDeleteUsers(currentUserId))
            {
                ShowGlobalPermissionError("delete users");
                return false;
            }
            
            // Prevent self-deletion
            if (currentUserId == targetUserId)
            {
                ShowGlobalPermissionError("delete your own account");
                return false;
            }
            
            // Prevent deletion of system administrator (if applicable)
            if (IsSystemAdministrator(targetUserId))
            {
                ShowGlobalPermissionError("delete the system administrator");
                return false;
            }
            
            return true;
        }
        
        /// <summary>
        /// Validate user printing operation
        /// </summary>
        public static bool ValidateUserPrint(int currentUserId)
        {
            if (!CanPrintUsers(currentUserId))
            {
                ShowGlobalPermissionError("print user reports");
                return false;
            }
            return true;
        }
        
        #endregion
        
        #region Permission Management Validation
        
        /// <summary>
        /// Check if user can manage permissions for another user
        /// </summary>
        public static bool CanManageUserPermissions(int currentUserId, int targetUserId)
        {
            // Must have edit users permission
            if (!CanEditUsers(currentUserId))
            {
                return false;
            }
            
            // Additional checks can be added here
            // For example: prevent managing permissions of higher-level users
            
            return true;
        }
        
        /// <summary>
        /// Check if user can manage role permissions
        /// </summary>
        public static bool CanManageRolePermissions(int currentUserId)
        {
            // For now, use edit users permission
            // This could be expanded to a separate permission in the future
            return CanEditUsers(currentUserId);
        }
        
        #endregion
        
        #region Helper Methods
        
        /// <summary>
        /// Validate self-editing operations
        /// </summary>
        private static bool ValidateSelfEdit(int userId)
        {
            // Users can generally edit their own basic information
            // but not their permissions or role
            return true;
        }
        
        /// <summary>
        /// Check if user is system administrator
        /// </summary>
        private static bool IsSystemAdministrator(int userId)
        {
            try
            {
                // Implement based on your system's admin identification logic
                // This could check for a specific role, user ID, or flag
                return userId == 1; // Assuming user ID 1 is system admin
            }
            catch
            {
                return false;
            }
        }
        
        /// <summary>
        /// Show global permission error message
        /// </summary>
        private static void ShowGlobalPermissionError(string action)
        {
            System.Windows.Forms.MessageBox.Show(
                $"You do not have permission to {action}.",
                "Insufficient Global Permissions",
                System.Windows.Forms.MessageBoxButtons.OK,
                System.Windows.Forms.MessageBoxIcon.Warning);
        }
        
        #endregion
        
        #region Bulk Operations
        
        /// <summary>
        /// Get all global permissions for a user
        /// </summary>
        public static GlobalPermission GetUserGlobalPermissions(int userId)
        {
            return _dbService.GetGlobalPermissions(userId);
        }
        
        /// <summary>
        /// Get global permission summary for multiple users
        /// </summary>
        public static Dictionary<int, GlobalPermissionSummary> GetGlobalPermissionSummary(List<int> userIds)
        {
            var summary = new Dictionary<int, GlobalPermissionSummary>();
            
            foreach (var userId in userIds)
            {
                try
                {
                    var permissions = GetUserGlobalPermissions(userId);
                    summary[userId] = new GlobalPermissionSummary
                    {
                        UserId = userId,
                        CanCreateUsers = permissions?.CanCreateUsers ?? false,
                        CanEditUsers = permissions?.CanEditUsers ?? false,
                        CanDeleteUsers = permissions?.CanDeleteUsers ?? false,
                        CanPrintUsers = permissions?.CanPrintUsers ?? false,
                        TotalPermissions = (permissions?.CanCreateUsers == true ? 1 : 0) +
                                         (permissions?.CanEditUsers == true ? 1 : 0) +
                                         (permissions?.CanDeleteUsers == true ? 1 : 0) +
                                         (permissions?.CanPrintUsers == true ? 1 : 0)
                    };
                }
                catch
                {
                    summary[userId] = new GlobalPermissionSummary { UserId = userId };
                }
            }
            
            return summary;
        }
        
        #endregion
    }
    
    public class GlobalPermissionSummary
    {
        public int UserId { get; set; }
        public bool CanCreateUsers { get; set; }
        public bool CanEditUsers { get; set; }
        public bool CanDeleteUsers { get; set; }
        public bool CanPrintUsers { get; set; }
        public int TotalPermissions { get; set; }
    }
}
```

### GlobalPermissionIndicator.cs
```csharp
using System;
using System.Drawing;
using System.Windows.Forms;
using DevExpress.XtraEditors;
using ProManage.Modules.Services;
using ProManage.Modules.Models;

namespace ProManage.Modules.UI
{
    public class GlobalPermissionIndicator : UserControl
    {
        private GroupControl groupGlobalPermissions;
        private LabelControl lblCreateUsers;
        private LabelControl lblEditUsers;
        private LabelControl lblDeleteUsers;
        private LabelControl lblPrintUsers;
        private LabelControl lblSummary;
        
        public int UserId { get; set; }
        
        public GlobalPermissionIndicator()
        {
            InitializeComponent();
        }
        
        private void InitializeComponent()
        {
            this.Size = new Size(250, 120);
            
            groupGlobalPermissions = new GroupControl
            {
                Text = "Global Permissions",
                Dock = DockStyle.Fill
            };
            
            lblCreateUsers = new LabelControl { Location = new Point(10, 25), Size = new Size(100, 15) };
            lblEditUsers = new LabelControl { Location = new Point(10, 45), Size = new Size(100, 15) };
            lblDeleteUsers = new LabelControl { Location = new Point(10, 65), Size = new Size(100, 15) };
            lblPrintUsers = new LabelControl { Location = new Point(10, 85), Size = new Size(100, 15) };
            lblSummary = new LabelControl { Location = new Point(120, 25), Size = new Size(120, 60) };
            
            groupGlobalPermissions.Controls.AddRange(new Control[]
            {
                lblCreateUsers, lblEditUsers, lblDeleteUsers, lblPrintUsers, lblSummary
            });
            
            this.Controls.Add(groupGlobalPermissions);
        }
        
        public void LoadPermissions(int userId)
        {
            UserId = userId;
            
            try
            {
                var canCreate = GlobalPermissionService.CanCreateUsers(userId);
                var canEdit = GlobalPermissionService.CanEditUsers(userId);
                var canDelete = GlobalPermissionService.CanDeleteUsers(userId);
                var canPrint = GlobalPermissionService.CanPrintUsers(userId);
                
                // Update labels with permission status
                lblCreateUsers.Text = $"Create: {GetPermissionText(canCreate)}";
                lblCreateUsers.ForeColor = GetPermissionColor(canCreate);
                
                lblEditUsers.Text = $"Edit: {GetPermissionText(canEdit)}";
                lblEditUsers.ForeColor = GetPermissionColor(canEdit);
                
                lblDeleteUsers.Text = $"Delete: {GetPermissionText(canDelete)}";
                lblDeleteUsers.ForeColor = GetPermissionColor(canDelete);
                
                lblPrintUsers.Text = $"Print: {GetPermissionText(canPrint)}";
                lblPrintUsers.ForeColor = GetPermissionColor(canPrint);
                
                // Update summary
                var totalPermissions = (canCreate ? 1 : 0) + (canEdit ? 1 : 0) + 
                                     (canDelete ? 1 : 0) + (canPrint ? 1 : 0);
                
                lblSummary.Text = $"Total: {totalPermissions}/4\n" +
                                $"Level: {GetPermissionLevel(totalPermissions)}";
                lblSummary.ForeColor = GetLevelColor(totalPermissions);
            }
            catch (Exception ex)
            {
                lblSummary.Text = "Error loading\npermissions";
                lblSummary.ForeColor = Color.Red;
                System.Diagnostics.Debug.WriteLine($"Error loading global permissions: {ex.Message}");
            }
        }
        
        private string GetPermissionText(bool hasPermission)
        {
            return hasPermission ? "✓ Yes" : "✗ No";
        }
        
        private Color GetPermissionColor(bool hasPermission)
        {
            return hasPermission ? Color.Green : Color.Red;
        }
        
        private string GetPermissionLevel(int totalPermissions)
        {
            return totalPermissions switch
            {
                0 => "None",
                1 => "Limited",
                2 => "Basic",
                3 => "Advanced",
                4 => "Full",
                _ => "Unknown"
            };
        }
        
        private Color GetLevelColor(int totalPermissions)
        {
            return totalPermissions switch
            {
                0 => Color.Red,
                1 => Color.Orange,
                2 => Color.Blue,
                3 => Color.Green,
                4 => Color.DarkGreen,
                _ => Color.Gray
            };
        }
        
        public void RefreshPermissions()
        {
            if (UserId > 0)
            {
                LoadPermissions(UserId);
            }
        }
    }
}
```

### Enhanced UserMasterForm Integration

Add to existing UserMasterForm:

```csharp
public partial class UserMasterForm : BaseFormWithPermissions
{
    private GlobalPermissionIndicator globalPermissionIndicator;
    
    protected override void OnLoad(EventArgs e)
    {
        base.OnLoad(e);
        
        // Apply global permissions
        ApplyGlobalPermissions();
    }
    
    private void ApplyGlobalPermissions()
    {
        var currentUserId = GetCurrentLoggedInUserId();
        
        // Check global permissions for user management operations
        var canCreateUsers = GlobalPermissionService.CanCreateUsers(currentUserId);
        var canEditUsers = GlobalPermissionService.CanEditUsers(currentUserId);
        var canDeleteUsers = GlobalPermissionService.CanDeleteUsers(currentUserId);
        
        // Override form permissions with global permissions
        if (!canCreateUsers) HasNewPermission = false;
        if (!canEditUsers) HasEditPermission = false;
        if (!canDeleteUsers) HasDeletePermission = false;
        
        // Reapply control permissions
        ApplyPermissionsToControls();
        
        // Add global permission indicator
        if (globalPermissionIndicator == null)
        {
            globalPermissionIndicator = new GlobalPermissionIndicator();
            // Add to form layout
        }
        
        globalPermissionIndicator.LoadPermissions(currentUserId);
    }
    
    protected override void PerformNewRecord()
    {
        if (!GlobalPermissionService.ValidateUserCreation(GetCurrentLoggedInUserId()))
            return;
            
        base.PerformNewRecord();
    }
    
    protected override void PerformSaveRecord()
    {
        var currentUserId = GetCurrentLoggedInUserId();
        var targetUserId = GetCurrentUserId(); // User being edited
        
        if (!GlobalPermissionService.ValidateUserEdit(currentUserId, targetUserId))
            return;
            
        base.PerformSaveRecord();
    }
    
    protected override void PerformDeleteRecord()
    {
        var currentUserId = GetCurrentLoggedInUserId();
        var targetUserId = GetCurrentUserId(); // User being deleted
        
        if (!GlobalPermissionService.ValidateUserDeletion(currentUserId, targetUserId))
            return;
            
        base.PerformDeleteRecord();
    }
    
    protected override void PerformPrintRecord()
    {
        if (!GlobalPermissionService.ValidateUserPrint(GetCurrentLoggedInUserId()))
            return;
            
        base.PerformPrintRecord();
    }
    
    private int GetCurrentLoggedInUserId()
    {
        // Return currently logged-in user ID
        return UserManager.Instance?.CurrentUser?.UserId ?? 0;
    }
}
```

### Enhanced PermissionManagementForm Integration

Add to existing PermissionManagementForm:

```csharp
protected override void OnLoad(EventArgs e)
{
    base.OnLoad(e);
    
    // Check if user can manage permissions
    var currentUserId = GetCurrentUserId();
    if (!GlobalPermissionService.CanManageRolePermissions(currentUserId))
    {
        MessageBox.Show("You do not have permission to manage permissions.", 
            "Access Denied", MessageBoxButtons.OK, MessageBoxIcon.Warning);
        this.Close();
        return;
    }
}

private void CmbUsers_EditValueChanged(object sender, EventArgs e)
{
    if (cmbUsers.EditValue == null) return;
    
    var currentUserId = GetCurrentUserId();
    var targetUserId = (int)cmbUsers.EditValue;
    
    // Check if current user can manage permissions for target user
    var canManage = GlobalPermissionService.CanManageUserPermissions(currentUserId, targetUserId);
    
    // Enable/disable permission editing based on global permissions
    gridControlUserPermissions.Enabled = canManage;
    btnResetUserPermissions.Enabled = canManage;
    
    if (!canManage)
    {
        lblPermissionWarning.Text = "You cannot modify permissions for this user.";
        lblPermissionWarning.Visible = true;
    }
    else
    {
        lblPermissionWarning.Visible = false;
    }
    
    LoadUserPermissions();
}
```

## Integration Points

### 1. User Management Forms
- UserMasterForm: Apply global permissions to CRUD operations
- UserManagementListForm: Filter operations based on global permissions
- PermissionManagementForm: Validate permission management access

### 2. Reporting and Printing
- User reports: Check CanPrintUsers permission
- Permission reports: Validate access before generating

### 3. Audit and Logging
- Log global permission checks for security auditing
- Track permission changes and who made them

## Acceptance Criteria

- [ ] Global permission service with validation methods
- [ ] User creation/edit/delete operations respect global permissions
- [ ] Permission management forms check global permissions
- [ ] Visual indicators for global permission status
- [ ] Prevention of self-deletion and system admin deletion
- [ ] Integration with existing user management forms
- [ ] Proper error messages for insufficient global permissions
- [ ] Audit trail for global permission usage

## Dependencies
- Task 06: Core Permission Service Logic
- Task 11: User Master Form Permission Integration
- Task 14: Individual Form Permission Checks

## Next Tasks
This task enables:
- Task 16: Testing and Validation Suite
