# SQL Procedures Organization

This directory contains SQL query files organized by module. Each module has its own subdirectory containing SQL files specific to that module.

## Directory Structure

```
Modules/Procedures/
├── Estimate/           # Estimate-related SQL queries
├── SQLQuery/           # SQL Query tool-related SQL queries

├── User/               # User-related SQL queries
└── README.md           # This file
```

## File Naming Convention

SQL files are named according to their purpose, using PascalCase. For example:
- `GetAllEstimates.sql`
- `SaveEstimate.sql`
- `GetUser.sql`

## Named Queries

Some SQL files contain multiple related queries. These are organized using a special comment format:

```sql
-- [QueryName] --
SELECT * FROM table WHERE condition;
-- [End] --
```

These named queries can be accessed using the `SQLQueryLoader.LoadNamedQuery()` method.

## Parameter Substitution

SQL queries use PostgreSQL parameter format with the `@` symbol:

```sql
SELECT * FROM users WHERE username = @username;
```

When executing queries, parameters are passed as a dictionary:

```csharp
var parameters = new Dictionary<string, object>
{
    { "@username", "admin" }
};
```

## Module Organization

### Estimate Module

Contains queries for estimate management:
- `GetAllEstimates.sql`: Retrieves all estimates
- `GetEstimateById.sql`: Retrieves an estimate by ID
- `GetEstimateByNumber.sql`: Retrieves an estimate by number
- `SearchEstimatesByCustomer.sql`: Searches for estimates by customer name
- `SaveEstimate.sql`: Contains queries for saving estimate data
- `GetNextEstimateNumber.sql`: Gets the next available estimate number

### SQLQuery Module

Contains queries for the SQL Query tool:
- `GetAllTables.sql`: Retrieves all tables in the database
- `GetTableColumns.sql`: Retrieves columns for a specific table
- `GetTableData.sql`: Retrieves data from a specific table
- `GetTableConstraints.sql`: Retrieves constraints for a specific table
- `GetTableIndexes.sql`: Retrieves indexes for a specific table
- `GetTableForeignKeys.sql`: Retrieves foreign keys for a specific table
- `GetTablePrimaryKey.sql`: Retrieves the primary key for a specific table

### User Module

Contains queries for user management:
- `GetUser.sql`: Retrieves a user by username
- `ValidateUser.sql`: Validates user credentials
- `SaveUser.sql`: Contains queries for saving user data



## Usage

SQL queries are loaded using the `SQLQueryLoader` class:

```csharp
// Load a complete SQL file
string query = SQLQueryLoader.LoadQuery("Estimate", "GetAllEstimates");

// Load a named query from a file
string namedQuery = SQLQueryLoader.LoadNamedQuery("Estimate", "SaveEstimate", "InsertHeader");
```

Queries are executed using the `QueryExecutor` class:

```csharp
// Execute a query from a file
var parameters = new Dictionary<string, object>
{
    { "@estimate_id", 1 }
};
string errorMessage;
DataTable result = QueryExecutor.ExecuteQueryFromFile("Estimate", "GetEstimateById", parameters, out errorMessage);

// Execute a named query from a file
DataTable namedResult = QueryExecutor.ExecuteNamedQueryFromFile("Estimate", "SaveEstimate", "InsertHeader", parameters, out errorMessage);
```
