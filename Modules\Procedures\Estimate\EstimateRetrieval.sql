-- EstimateRetrieval.sql
-- Consolidated retrieval queries for estimates by different criteria
-- Replaces: GetEstimateById, GetEstimateByNumber
--
-- All queries use consistent column naming and include created/modified dates

-- [GetById] --
-- Retrieve estimate header by ID
SELECT
    id AS estimate_id,
    estimate_no AS estimate_number,
    customer_name,
    vin AS vehicle_vin,
    brand AS vehicle_brand,
    date AS estimate_date,
    location,
    vehicle_model,
    salesman_name,
    status
FROM
    estimateheader
WHERE
    id = @estimate_id;
-- [End] --

-- [GetByNumber] --
-- Retrieve estimate header by estimate number
SELECT
    id AS estimate_id,
    estimate_no AS estimate_number,
    customer_name,
    vin AS vehicle_vin,
    brand AS vehicle_brand,
    date AS estimate_date,
    location,
    vehicle_model,
    salesman_name,
    status
FROM
    estimateheader
WHERE
    UPPER(TRIM(estimate_no)) = UPPER(TRIM(@estimate_number));
-- [End] --

-- [GetDetailsById] --
-- Retrieve estimate details by estimate ID
SELECT
    id AS detail_id,
    estimate_id,
    serial_number,
    part_number,
    description,
    quantity,
    oe_price,
    afm_price,
    remarks
FROM
    estimatedetails
WHERE
    estimate_id = @estimate_id
ORDER BY
    serial_number;
-- [End] --

-- [GetDetailsByNumber] --
-- Retrieve estimate details by estimate number
SELECT
    d.id AS detail_id,
    d.estimate_id,
    d.serial_number,
    d.part_number,
    d.description,
    d.quantity,
    d.oe_price,
    d.afm_price,
    d.remarks
FROM
    estimatedetails d
INNER JOIN
    estimateheader h ON d.estimate_id = h.id
WHERE
    UPPER(TRIM(h.estimate_no)) = UPPER(TRIM(@estimate_number))
ORDER BY
    d.serial_number;
-- [End] --
