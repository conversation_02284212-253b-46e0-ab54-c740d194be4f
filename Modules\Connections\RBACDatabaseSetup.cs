using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.Text;
using Npgsql;
using ProManage.Modules.Helpers;

namespace ProManage.Modules.Connections
{
    /// <summary>
    /// Service class for setting up and managing the RBAC (Role-Based Access Control) database schema.
    /// Provides methods to create tables, populate initial data, and verify the setup.
    /// </summary>
    public static class RBACDatabaseSetup
    {
        /// <summary>
        /// Sets up the complete RBAC database schema including all tables and initial data.
        /// This method can be run multiple times safely (idempotent operation).
        /// </summary>
        /// <returns>True if setup was successful, false otherwise</returns>
        public static bool SetupCompleteRBACSystem(out string errorMessage)
        {
            errorMessage = string.Empty;
            
            try
            {
                Debug.WriteLine("Starting complete RBAC system setup...");
                
                // Step 1: Setup schema
                if (!SetupRBACSchema(out string schemaError))
                {
                    errorMessage = $"Schema setup failed: {schemaError}";
                    return false;
                }

                // Step 1.5: Apply schema fixes
                if (!ApplySchemaFixes(out string fixError))
                {
                    errorMessage = $"Schema fixes failed: {fixError}";
                    return false;
                }

                // Step 2: Populate initial data
                if (!PopulateInitialData(out string dataError))
                {
                    errorMessage = $"Initial data population failed: {dataError}";
                    return false;
                }
                
                // Step 3: Verify setup
                if (!VerifyRBACSetup(out string verifyError))
                {
                    errorMessage = $"Setup verification failed: {verifyError}";
                    return false;
                }
                
                Debug.WriteLine("Complete RBAC system setup completed successfully");
                return true;
            }
            catch (Exception ex)
            {
                errorMessage = $"Unexpected error during RBAC setup: {ex.Message}";
                Debug.WriteLine($"Error in SetupCompleteRBACSystem: {ex.Message}");
                if (ex.InnerException != null)
                {
                    Debug.WriteLine($"Inner exception: {ex.InnerException.Message}");
                }
                return false;
            }
        }
        
        /// <summary>
        /// Creates all RBAC database tables with proper structure and relationships.
        /// </summary>
        /// <returns>True if schema setup was successful, false otherwise</returns>
        public static bool SetupRBACSchema(out string errorMessage)
        {
            errorMessage = string.Empty;
            
            try
            {
                Debug.WriteLine("Setting up RBAC database schema...");
                
                using (var conn = DatabaseConnectionManager.Instance.CreateNewConnection())
                {
                    conn.Open();
                    using (var transaction = conn.BeginTransaction())
                    {
                        try
                        {
                            // Execute each table creation query
                            var queries = new[]
                            {
                                "CreateRolesTable",
                                "CreateRolePermissionsTable", 
                                "CreateUserPermissionsTable",
                                "CreateGlobalPermissionsTable",
                                "AddRoleIdToUsers"
                            };
                            
                            foreach (var queryName in queries)
                            {
                                Debug.WriteLine($"Executing {queryName}...");
                                
                                string sql = SQLQueryLoader.ExtractNamedQuery("Permissions", "RBAC-Schema-Setup", queryName);
                                if (string.IsNullOrEmpty(sql))
                                {
                                    throw new Exception($"Could not load query: {queryName}");
                                }
                                
                                using (var cmd = new NpgsqlCommand(sql, conn, transaction))
                                {
                                    cmd.CommandTimeout = 60; // 1 minute timeout for schema operations
                                    cmd.ExecuteNonQuery();
                                }
                                
                                Debug.WriteLine($"{queryName} executed successfully");
                            }
                            
                            transaction.Commit();
                            Debug.WriteLine("RBAC schema setup completed successfully");
                            return true;
                        }
                        catch (Exception ex)
                        {
                            transaction.Rollback();
                            throw new Exception($"Schema setup failed and was rolled back: {ex.Message}", ex);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                errorMessage = ex.Message;
                Debug.WriteLine($"Error in SetupRBACSchema: {ex.Message}");
                if (ex.InnerException != null)
                {
                    Debug.WriteLine($"Inner exception: {ex.InnerException.Message}");
                }
                return false;
            }
        }

        /// <summary>
        /// Apply schema fixes for RBAC system
        /// </summary>
        /// <param name="errorMessage">Error message if fixes fail</param>
        /// <returns>True if successful, false otherwise</returns>
        private static bool ApplySchemaFixes(out string errorMessage)
        {
            errorMessage = string.Empty;

            try
            {
                Debug.WriteLine("Applying RBAC schema fixes...");

                var queries = new[]
                {
                    "FixUserPermissionsTable",
                    "FixUsersTablePasswordSalt",
                    "FixUsersTableCreatedDate",
                    "VerifySchemaFixes"
                };

                using (var conn = DatabaseConnectionManager.Instance.CreateNewConnection())
                {
                    conn.Open();
                    using (var transaction = conn.BeginTransaction())
                    {
                        try
                        {
                            foreach (var queryName in queries)
                            {
                                Debug.WriteLine($"Executing schema fix: {queryName}...");

                                string sql = SQLQueryLoader.ExtractNamedQuery("Permissions", "RBAC-Schema-Fix", queryName);
                                if (string.IsNullOrEmpty(sql))
                                {
                                    throw new Exception($"Could not load schema fix query: {queryName}");
                                }

                                using (var cmd = new NpgsqlCommand(sql, conn, transaction))
                                {
                                    cmd.CommandTimeout = 60;
                                    cmd.ExecuteNonQuery();
                                }

                                Debug.WriteLine($"Schema fix {queryName} executed successfully");
                            }

                            transaction.Commit();
                            Debug.WriteLine("RBAC schema fixes applied successfully");
                            return true;
                        }
                        catch (Exception ex)
                        {
                            transaction.Rollback();
                            throw new Exception($"Schema fixes failed and were rolled back: {ex.Message}", ex);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                errorMessage = ex.Message;
                Debug.WriteLine($"Error applying schema fixes: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// Populates the RBAC tables with initial default roles and permissions.
        /// </summary>
        /// <returns>True if data population was successful, false otherwise</returns>
        public static bool PopulateInitialData(out string errorMessage)
        {
            errorMessage = string.Empty;
            
            try
            {
                Debug.WriteLine("Populating RBAC initial data...");
                
                using (var conn = DatabaseConnectionManager.Instance.CreateNewConnection())
                {
                    conn.Open();
                    using (var transaction = conn.BeginTransaction())
                    {
                        try
                        {
                            // Execute each data population query
                            var queries = new[]
                            {
                                "InsertDefaultRoles",
                                "InsertAdministratorPermissions",
                                "InsertManagerPermissions",
                                "InsertUserPermissions",
                                "InsertReadOnlyPermissions"
                            };
                            
                            foreach (var queryName in queries)
                            {
                                Debug.WriteLine($"Executing {queryName}...");
                                
                                string sql = SQLQueryLoader.ExtractNamedQuery("Permissions", "RBAC-Initial-Data", queryName);
                                if (string.IsNullOrEmpty(sql))
                                {
                                    throw new Exception($"Could not load query: {queryName}");
                                }
                                
                                using (var cmd = new NpgsqlCommand(sql, conn, transaction))
                                {
                                    cmd.CommandTimeout = 30; // 30 seconds timeout for data operations
                                    int rowsAffected = cmd.ExecuteNonQuery();
                                    Debug.WriteLine($"{queryName} executed successfully, {rowsAffected} rows affected");
                                }
                            }
                            
                            transaction.Commit();
                            Debug.WriteLine("RBAC initial data population completed successfully");
                            return true;
                        }
                        catch (Exception ex)
                        {
                            transaction.Rollback();
                            throw new Exception($"Data population failed and was rolled back: {ex.Message}", ex);
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                errorMessage = ex.Message;
                Debug.WriteLine($"Error in PopulateInitialData: {ex.Message}");
                if (ex.InnerException != null)
                {
                    Debug.WriteLine($"Inner exception: {ex.InnerException.Message}");
                }
                return false;
            }
        }
        
        /// <summary>
        /// Verifies that the RBAC system is properly set up with all required tables and data.
        /// </summary>
        /// <returns>True if verification passed, false otherwise</returns>
        public static bool VerifyRBACSetup(out string errorMessage)
        {
            errorMessage = string.Empty;
            
            try
            {
                Debug.WriteLine("Verifying RBAC setup...");
                
                using (var conn = DatabaseConnectionManager.Instance.CreateNewConnection())
                {
                    conn.Open();
                    
                    // Verify tables exist
                    string verifyTablesQuery = SQLQueryLoader.ExtractNamedQuery("Permissions", "RBAC-Schema-Setup", "VerifyTablesExist");
                    using (var cmd = new NpgsqlCommand(verifyTablesQuery, conn))
                    {
                        using (var reader = cmd.ExecuteReader())
                        {
                            var tables = new List<string>();
                            while (reader.Read())
                            {
                                tables.Add(reader["table_name"].ToString());
                            }
                            
                            var requiredTables = new[] { "roles", "role_permissions", "user_permissions", "global_permissions", "users" };
                            foreach (var table in requiredTables)
                            {
                                if (!tables.Contains(table))
                                {
                                    errorMessage = $"Required table '{table}' not found";
                                    return false;
                                }
                            }
                        }
                    }
                    
                    Debug.WriteLine("All required tables verified successfully");
                    return true;
                }
            }
            catch (Exception ex)
            {
                errorMessage = ex.Message;
                Debug.WriteLine($"Error in VerifyRBACSetup: {ex.Message}");
                return false;
            }
        }
        
        /// <summary>
        /// Gets a summary of the current RBAC setup including roles and permission counts.
        /// </summary>
        /// <returns>DataTable with setup summary or null if error occurred</returns>
        public static DataTable GetRBACSetupSummary(out string errorMessage)
        {
            errorMessage = string.Empty;
            
            try
            {
                Debug.WriteLine("Getting RBAC setup summary...");
                
                using (var conn = DatabaseConnectionManager.Instance.CreateNewConnection())
                {
                    conn.Open();
                    
                    string summaryQuery = SQLQueryLoader.ExtractNamedQuery("Permissions", "RBAC-Initial-Data", "GetPermissionSummary");
                    using (var cmd = new NpgsqlCommand(summaryQuery, conn))
                    {
                        using (var adapter = new NpgsqlDataAdapter(cmd))
                        {
                            var dataTable = new DataTable();
                            adapter.Fill(dataTable);
                            
                            Debug.WriteLine($"Retrieved RBAC summary with {dataTable.Rows.Count} roles");
                            return dataTable;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                errorMessage = ex.Message;
                Debug.WriteLine($"Error in GetRBACSetupSummary: {ex.Message}");
                return null;
            }
        }
    }
}
