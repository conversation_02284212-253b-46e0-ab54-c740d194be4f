using System;

namespace ProManage.Modules.Models.LoginForm
{
    /// <summary>
    /// Data model for user information
    /// </summary>
    public class LoginFormUserModel
    {
        /// <summary>
        /// Primary key ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// Username (unique)
        /// </summary>
        public string Username { get; set; }

        /// <summary>
        /// User's full name
        /// </summary>
        public string FullName { get; set; }

        /// <summary>
        /// User's email address
        /// </summary>
        public string Email { get; set; }

        /// <summary>
        /// Whether the user is active
        /// </summary>
        public bool IsActive { get; set; }

        /// <summary>
        /// Created date
        /// </summary>
        public DateTime? CreatedAt { get; set; }

        /// <summary>
        /// Constructor
        /// </summary>
        public LoginFormUserModel()
        {
        }

        /// <summary>
        /// Constructor with username
        /// </summary>
        public LoginFormUserModel(string username)
        {
            this.Username = username;
        }

        /// <summary>
        /// Constructor with username and full name
        /// </summary>
        public LoginFormUserModel(string username, string fullName)
        {
            this.Username = username;
            this.FullName = fullName;
        }
    }
}
