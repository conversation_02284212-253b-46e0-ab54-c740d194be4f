using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using ProManage.Modules.Models.EstimateForm;
using ProManage.Modules.Helpers.EstimateForm;

namespace ProManage.Modules.Reports
{
    /// <summary>
    /// Service for generating and managing estimate reports
    /// </summary>
    public static class EstimateReportService
    {
        /// <summary>
        /// Creates and populates an estimate report with current form data
        /// </summary>
        /// <param name="form">The EstimateForm instance</param>
        /// <returns>Populated EstimatePrint report</returns>
        public static ProManage.Reports.EstimatePrint CreateEstimateReport(dynamic form)
        {
            try
            {
                Debug.WriteLine("=== CreateEstimateReport: Starting ===");

                // Extract current estimate data from form
                Debug.WriteLine("Extracting header data...");
                var headerData = ExtractHeaderDataFromForm(form);
                Debug.WriteLine($"Header data extracted: Customer={headerData?.CustomerName}, Estimate={headerData?.EstimateNo}");

                Debug.WriteLine("Extracting detail data...");
                var detailData = ExtractDetailDataFromForm(form);
                Debug.WriteLine($"Detail data extracted: {detailData?.Count ?? 0} items");

                // Create and populate the report
                Debug.WriteLine("Creating report instance...");
                var report = new ProManage.Reports.EstimatePrint();
                Debug.WriteLine("Populating report data...");
                report.PopulateReportData(headerData, detailData);
                Debug.WriteLine("Report data populated successfully");

                Debug.WriteLine($"=== CreateEstimateReport: Completed - Header: {headerData?.EstimateNo}, Details: {detailData?.Count ?? 0} ===");
                return report;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error in CreateEstimateReport: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Extracts header data from the EstimateForm
        /// </summary>
        /// <param name="form">The EstimateForm instance</param>
        /// <returns>EstimateFormHeaderModel with current form data</returns>
        private static EstimateFormHeaderModel ExtractHeaderDataFromForm(dynamic form)
        {
            try
            {
                Debug.WriteLine("Extracting header data from form");

                var headerData = new EstimateFormHeaderModel
                {
                    EstimateNo = form.txtEstimate.Text?.Trim(),
                    CustomerName = form.txtCustomer.Text?.Trim(),
                    VehicleModel = form.txtVehicle.Text?.Trim(),
                    VIN = form.txtVIN.Text?.Trim(),
                    Brand = form.cbBrand.Text?.Trim(),
                    Location = form.cbLocation.Text?.Trim(),
                    SalesmanName = form.txtSalesman.Text?.Trim(),
                    DocDate = form.dpDocDate.DateTime,
                    Remarks = form.txtDocRemarks.Text?.Trim()
                };

                Debug.WriteLine($"Header extracted - Customer: {headerData.CustomerName}, Estimate: {headerData.EstimateNo}");
                return headerData;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error extracting header data: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Extracts detail data from the EstimateForm grid
        /// </summary>
        /// <param name="form">The EstimateForm instance</param>
        /// <returns>List of EstimateFormDetailModel with current grid data</returns>
        private static List<EstimateFormDetailModel> ExtractDetailDataFromForm(dynamic form)
        {
            try
            {
                Debug.WriteLine("Extracting detail data from form grid");

                var detailData = new List<EstimateFormDetailModel>();

                // Get the grid data table using the public property
                var gridDataTable = form.GridDataTable;
                if (gridDataTable == null || gridDataTable.Rows.Count == 0)
                {
                    Debug.WriteLine("No grid data available");
                    return detailData;
                }

                int serialNumber = 1;
                foreach (System.Data.DataRow row in gridDataTable.Rows)
                {
                    // Skip empty rows
                    if (IsEmptyRow(row))
                    {
                        Debug.WriteLine($"Skipping empty row {serialNumber}");
                        continue;
                    }

                    var detail = new EstimateFormDetailModel
                    {
                        SerialNo = serialNumber++,
                        PartNo = GetStringValue(row, "PartNumber"),
                        Description = GetStringValue(row, "Description"),
                        Qty = GetIntValue(row, "Quantity"),
                        OEPrice = GetDecimalValue(row, "OEPrice"),
                        AFMPrice = GetDecimalValue(row, "AFMPrice"),
                        Remarks = GetStringValue(row, "Remarks"),
                        ApproveStatus = GetBooleanValue(row, "Status")
                    };

                    detailData.Add(detail);
                    Debug.WriteLine($"Extracted detail {detail.SerialNo}: {detail.PartNo} - {detail.Description}");
                }

                Debug.WriteLine($"Total details extracted: {detailData.Count}");
                return detailData;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error extracting detail data: {ex.Message}");
                throw;
            }
        }

        /// <summary>
        /// Checks if a data row is empty (all key fields are null or empty)
        /// </summary>
        /// <param name="row">DataRow to check</param>
        /// <returns>True if row is empty</returns>
        private static bool IsEmptyRow(System.Data.DataRow row)
        {
            try
            {
                var partNumber = GetStringValue(row, "PartNumber");
                var description = GetStringValue(row, "Description");
                var quantity = GetIntValue(row, "Quantity");

                return string.IsNullOrWhiteSpace(partNumber) &&
                       string.IsNullOrWhiteSpace(description) &&
                       quantity == 0;
            }
            catch
            {
                return true; // If we can't read the row, consider it empty
            }
        }

        /// <summary>
        /// Safely gets a string value from a DataRow
        /// </summary>
        private static string GetStringValue(System.Data.DataRow row, string columnName)
        {
            try
            {
                if (row.Table.Columns.Contains(columnName) && row[columnName] != DBNull.Value)
                {
                    return row[columnName]?.ToString() ?? "";
                }
                return "";
            }
            catch
            {
                return "";
            }
        }

        /// <summary>
        /// Safely gets an integer value from a DataRow
        /// </summary>
        private static int GetIntValue(System.Data.DataRow row, string columnName)
        {
            try
            {
                if (row.Table.Columns.Contains(columnName) && row[columnName] != DBNull.Value)
                {
                    if (int.TryParse(row[columnName]?.ToString(), out int result))
                        return result;
                    if (decimal.TryParse(row[columnName]?.ToString(), out decimal decimalResult))
                        return (int)decimalResult;
                }
                return 0;
            }
            catch
            {
                return 0;
            }
        }

        /// <summary>
        /// Safely gets a decimal value from a DataRow
        /// </summary>
        private static decimal GetDecimalValue(System.Data.DataRow row, string columnName)
        {
            try
            {
                if (row.Table.Columns.Contains(columnName) && row[columnName] != DBNull.Value)
                {
                    if (decimal.TryParse(row[columnName]?.ToString(), out decimal result))
                        return result;
                }
                return 0m;
            }
            catch
            {
                return 0m;
            }
        }

        /// <summary>
        /// Safely gets a boolean value from a DataRow
        /// </summary>
        private static bool GetBooleanValue(System.Data.DataRow row, string columnName)
        {
            try
            {
                if (row.Table.Columns.Contains(columnName) && row[columnName] != DBNull.Value)
                {
                    if (bool.TryParse(row[columnName]?.ToString(), out bool result))
                        return result;
                    // Handle checkbox values that might be stored as 1/0
                    if (int.TryParse(row[columnName]?.ToString(), out int intResult))
                        return intResult != 0;
                }
                return false;
            }
            catch
            {
                return false;
            }
        }
    }
}
