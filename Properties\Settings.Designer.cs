﻿//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//     Runtime Version:4.0.30319.42000
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

namespace ProManage.Properties
{
    [global::System.Runtime.CompilerServices.CompilerGeneratedAttribute()]
    [global::System.CodeDom.Compiler.GeneratedCodeAttribute("Microsoft.VisualStudio.Editors.SettingsDesigner.SettingsSingleFileGenerator", "17.13.0.0")]
    internal sealed partial class Settings : global::System.Configuration.ApplicationSettingsBase
    {
        private static Settings defaultInstance = ((Settings)(global::System.Configuration.ApplicationSettingsBase.Synchronized(new Settings())));

        #region Auto-Save Functionality
        private static bool addedHandler = false;
        private static object addedHandlerLockObject = new object();

        private static void AutoSaveSettings(object sender, System.EventArgs e)
        {
            if (ProManage.Application.SaveMySettingsOnExit)
            {
                Settings.Default.Save();
            }
        }
        #endregion

        public static Settings Default
        {
            get
            {
                if (!addedHandler)
                {
                    lock (addedHandlerLockObject)
                    {
                        if (!addedHandler)
                        {
                            System.Windows.Forms.Application.ApplicationExit += AutoSaveSettings;
                            addedHandler = true;
                        }
                    }
                }
                return defaultInstance;
            }
        }

        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("False")]
        public bool SidebarExpanded
        {
            get
            {
                return ((bool)(this["SidebarExpanded"]));
            }
            set
            {
                this["SidebarExpanded"] = value;
            }
        }

        [global::System.Configuration.UserScopedSettingAttribute()]
        [global::System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [global::System.Configuration.DefaultSettingValueAttribute("Office 2019 Colorful")]
        public string Theme
        {
            get
            {
                return ((string)(this["Theme"]));
            }
            set
            {
                this["Theme"] = value;
            }
        }
    }
}
