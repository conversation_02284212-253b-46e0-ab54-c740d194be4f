using System;

namespace ProManage.Modules.Models.EstimateForm
{
    /// <summary>
    /// Data model for estimate detail line items
    /// </summary>
    public class EstimateFormDetailModel
    {
        /// <summary>
        /// Primary key ID
        /// </summary>
        public int Id { get; set; }

        /// <summary>
        /// Foreign key to EstimateHeader
        /// </summary>
        public int EstimateId { get; set; }

        /// <summary>
        /// Serial number (maps to 'serial_no' in database)
        /// </summary>
        public int? SerialNo { get; set; }

        /// <summary>
        /// Part number (maps to 'part_no' in database)
        /// </summary>
        public string PartNo { get; set; }

        /// <summary>
        /// Part description
        /// </summary>
        public string Description { get; set; }

        /// <summary>
        /// Quantity
        /// </summary>
        public int? Qty { get; set; }

        /// <summary>
        /// OE Price (maps to 'oe_price' in database)
        /// </summary>
        public decimal? OEPrice { get; set; }

        /// <summary>
        /// AFM Price (maps to 'afm_price' in database)
        /// </summary>
        public decimal? AFMPrice { get; set; }

        /// <summary>
        /// Remarks
        /// </summary>
        public string Remarks { get; set; }

        /// <summary>
        /// Created date (maps to 'created_at' in database)
        /// </summary>
        public DateTime? CreatedAt { get; set; }

        /// <summary>
        /// Modified date (maps to 'modify_at' in database)
        /// </summary>
        public DateTime? ModifyAt { get; set; }

        /// <summary>
        /// Approval status (maps to 'approve_status' in database)
        /// </summary>
        public bool? ApproveStatus { get; set; } = false;
    }
}
