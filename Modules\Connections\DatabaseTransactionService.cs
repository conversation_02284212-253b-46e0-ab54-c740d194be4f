using System;
using System.Collections.Generic;
using System.Data;
using System.Diagnostics;
using System.IO;
using Npgsql;

namespace ProManage.Modules.Connections
{
    /// <summary>
    /// Service class for handling database operations with proper transaction management.
    /// Provides methods for executing database operations with or without transactions.
    ///
    /// This service uses DatabaseConnectionManager for connection management and adds
    /// transaction handling capabilities on top of it.
    /// </summary>
    public class DatabaseTransactionService
    {
        // Connection pooling settings
        private const int MaxPoolSize = 100;
        private const int MinPoolSize = 1;
        private const int ConnectionLifetime = 300; // 5 minutes
        private const int ConnectionIdleLifetime = 60; // 1 minute

        // Transaction logging
        private const string TransactionLogFile = "transaction_log.txt";
        private static readonly object LogLock = new object();

        // Default isolation level for transactions
        private IsolationLevel _defaultIsolationLevel = IsolationLevel.ReadCommitted;

        /// <summary>
        /// Creates a new instance of the DatabaseTransactionService with default isolation level (ReadCommitted)
        /// </summary>
        public DatabaseTransactionService()
        {
            // No initialization needed - we'll use DatabaseConnectionManager for connections
        }

        /// <summary>
        /// Creates a new instance of the DatabaseTransactionService with specified isolation level
        /// </summary>
        /// <param name="isolationLevel">The isolation level to use for transactions</param>
        public DatabaseTransactionService(IsolationLevel isolationLevel)
        {
            _defaultIsolationLevel = isolationLevel;
        }

        /// <summary>
        /// Gets or sets the default isolation level for transactions
        /// </summary>
        public IsolationLevel DefaultIsolationLevel
        {
            get { return _defaultIsolationLevel; }
            set { _defaultIsolationLevel = value; }
        }

        /// <summary>
        /// Creates a new database connection using the DatabaseConnectionManager
        /// </summary>
        /// <returns>An open NpgsqlConnection</returns>
        /// <exception cref="Exception">Thrown when connection cannot be opened</exception>
        public NpgsqlConnection CreateConnection()
        {
            try
            {
                // Get a new connection from the connection manager
                var conn = DatabaseConnectionManager.Instance.CreateNewConnection();

                // Open the connection
                conn.Open();
                Debug.WriteLine("New connection opened successfully by DatabaseTransactionService");

                return conn;
            }
            catch (Exception ex)
            {
                Debug.WriteLine($"Error opening new connection in DatabaseTransactionService: {ex.Message}");
                if (ex.InnerException != null)
                {
                    Debug.WriteLine($"Inner error: {ex.InnerException.Message}");
                }
                throw new Exception($"Failed to open database connection: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Executes a database operation within a transaction
        /// </summary>
        /// <typeparam name="T">The return type of the operation</typeparam>
        /// <param name="operation">The database operation to execute</param>
        /// <returns>The result of the operation</returns>
        /// <exception cref="Exception">Thrown when the operation fails</exception>
        public T ExecuteWithTransaction<T>(Func<NpgsqlConnection, NpgsqlTransaction, T> operation)
        {
            return ExecuteWithTransaction(operation, _defaultIsolationLevel);
        }

        /// <summary>
        /// Executes a database operation within a transaction with the specified isolation level
        /// </summary>
        /// <typeparam name="T">The return type of the operation</typeparam>
        /// <param name="operation">The database operation to execute</param>
        /// <param name="isolationLevel">The isolation level for the transaction</param>
        /// <returns>The result of the operation</returns>
        /// <exception cref="Exception">Thrown when the operation fails</exception>
        public T ExecuteWithTransaction<T>(Func<NpgsqlConnection, NpgsqlTransaction, T> operation, IsolationLevel isolationLevel)
        {
            NpgsqlConnection conn = null;
            NpgsqlTransaction transaction = null;
            string transactionId = Guid.NewGuid().ToString().Substring(0, 8);

            try
            {
                // Create a new connection for this transaction
                conn = CreateConnection();
                LogTransaction($"Transaction {transactionId}: Created new connection for transaction management");

                // Begin a transaction with the specified isolation level
                transaction = conn.BeginTransaction(isolationLevel);
                LogTransaction($"Transaction {transactionId}: Started with isolation level {isolationLevel}");

                // Execute the operation
                T result = operation(conn, transaction);

                // Commit the transaction
                transaction.Commit();
                LogTransaction($"Transaction {transactionId}: Committed successfully");

                return result;
            }
            catch (Exception ex)
            {
                string errorMessage = $"Transaction {transactionId}: Error in transaction: {ex.Message}";
                if (ex.InnerException != null)
                {
                    errorMessage += $", Inner error: {ex.InnerException.Message}";
                }
                LogTransaction(errorMessage, true);

                // Rollback the transaction if it exists
                if (transaction != null)
                {
                    try
                    {
                        transaction.Rollback();
                        LogTransaction($"Transaction {transactionId}: Rolled back due to error", true);
                    }
                    catch (Exception rollbackEx)
                    {
                        LogTransaction($"Transaction {transactionId}: Error rolling back transaction: {rollbackEx.Message}", true);
                    }
                }

                throw new Exception($"Database transaction failed: {ex.Message}", ex);
            }
            finally
            {
                // Close and dispose the connection
                if (conn != null)
                {
                    if (conn.State == ConnectionState.Open)
                    {
                        conn.Close();
                    }
                    conn.Dispose();
                    LogTransaction($"Transaction {transactionId}: Connection closed and disposed after transaction");
                }
            }
        }

        /// <summary>
        /// Executes a database operation without a transaction
        /// </summary>
        /// <typeparam name="T">The return type of the operation</typeparam>
        /// <param name="operation">The database operation to execute</param>
        /// <returns>The result of the operation</returns>
        /// <exception cref="Exception">Thrown when the operation fails</exception>
        public T ExecuteWithoutTransaction<T>(Func<NpgsqlConnection, T> operation)
        {
            NpgsqlConnection conn = null;
            string operationId = Guid.NewGuid().ToString().Substring(0, 8);

            try
            {
                // Create a new connection
                conn = CreateConnection();
                LogTransaction($"Operation {operationId}: Created new connection for operation without transaction");

                // Execute the operation
                T result = operation(conn);
                LogTransaction($"Operation {operationId}: Completed successfully");

                return result;
            }
            catch (Exception ex)
            {
                string errorMessage = $"Operation {operationId}: Error in operation: {ex.Message}";
                if (ex.InnerException != null)
                {
                    errorMessage += $", Inner error: {ex.InnerException.Message}";
                }
                LogTransaction(errorMessage, true);

                throw new Exception($"Database operation failed: {ex.Message}", ex);
            }
            finally
            {
                // Close and dispose the connection
                if (conn != null)
                {
                    if (conn.State == ConnectionState.Open)
                    {
                        conn.Close();
                    }
                    conn.Dispose();
                    LogTransaction($"Operation {operationId}: Connection closed and disposed");
                }
            }
        }

        /// <summary>
        /// Executes a database operation within an existing transaction or creates a new one if none exists
        /// This supports nested transactions by using savepoints
        /// </summary>
        /// <typeparam name="T">The return type of the operation</typeparam>
        /// <param name="operation">The database operation to execute</param>
        /// <param name="existingConnection">An existing connection to use, or null to create a new one</param>
        /// <param name="existingTransaction">An existing transaction to use, or null to create a new one</param>
        /// <returns>The result of the operation</returns>
        /// <exception cref="Exception">Thrown when the operation fails</exception>
        public T ExecuteWithNestedTransaction<T>(
            Func<NpgsqlConnection, NpgsqlTransaction, T> operation,
            NpgsqlConnection existingConnection = null,
            NpgsqlTransaction existingTransaction = null)
        {
            bool ownsConnection = (existingConnection == null);
            bool ownsTransaction = (existingTransaction == null);
            NpgsqlConnection conn = existingConnection;
            NpgsqlTransaction transaction = existingTransaction;
            string savepointName = null;
            string transactionId = Guid.NewGuid().ToString().Substring(0, 8);

            try
            {
                // Create a new connection if one wasn't provided
                if (ownsConnection)
                {
                    conn = CreateConnection();
                    LogTransaction($"Nested Transaction {transactionId}: Created new connection");
                }
                else
                {
                    LogTransaction($"Nested Transaction {transactionId}: Using existing connection");
                }

                // Create a new transaction or savepoint
                if (ownsTransaction)
                {
                    transaction = conn.BeginTransaction(_defaultIsolationLevel);
                    LogTransaction($"Nested Transaction {transactionId}: Started new transaction with isolation level {_defaultIsolationLevel}");
                }
                else
                {
                    // Create a savepoint for nested transaction
                    savepointName = $"SP_{Guid.NewGuid().ToString().Replace("-", "").Substring(0, 10)}";
                    transaction.Save(savepointName);
                    LogTransaction($"Nested Transaction {transactionId}: Created savepoint '{savepointName}' in existing transaction");
                }

                // Execute the operation
                T result = operation(conn, transaction);

                // Commit the transaction if we own it
                if (ownsTransaction)
                {
                    transaction.Commit();
                    LogTransaction($"Nested Transaction {transactionId}: Committed new transaction");
                }
                // No need to explicitly commit a savepoint

                return result;
            }
            catch (Exception ex)
            {
                string errorMessage = $"Nested Transaction {transactionId}: Error: {ex.Message}";
                if (ex.InnerException != null)
                {
                    errorMessage += $", Inner error: {ex.InnerException.Message}";
                }
                LogTransaction(errorMessage, true);

                // Handle rollback based on whether we own the transaction
                if (ownsTransaction && transaction != null)
                {
                    try
                    {
                        transaction.Rollback();
                        LogTransaction($"Nested Transaction {transactionId}: Rolled back transaction due to error", true);
                    }
                    catch (Exception rollbackEx)
                    {
                        LogTransaction($"Nested Transaction {transactionId}: Error rolling back transaction: {rollbackEx.Message}", true);
                    }
                }
                else if (!ownsTransaction && transaction != null && savepointName != null)
                {
                    try
                    {
                        // Rollback to savepoint for nested transaction
                        transaction.Rollback(savepointName);
                        LogTransaction($"Nested Transaction {transactionId}: Rolled back to savepoint '{savepointName}' due to error", true);
                    }
                    catch (Exception rollbackEx)
                    {
                        LogTransaction($"Nested Transaction {transactionId}: Error rolling back to savepoint: {rollbackEx.Message}", true);
                    }
                }

                throw new Exception($"Nested database transaction failed: {ex.Message}", ex);
            }
            finally
            {
                // Only dispose resources we own
                if (ownsTransaction && transaction != null)
                {
                    transaction.Dispose();
                    LogTransaction($"Nested Transaction {transactionId}: Disposed transaction");
                }

                if (ownsConnection && conn != null)
                {
                    if (conn.State == ConnectionState.Open)
                    {
                        conn.Close();
                    }
                    conn.Dispose();
                    LogTransaction($"Nested Transaction {transactionId}: Closed and disposed connection");
                }
            }
        }

        /// <summary>
        /// Executes a database operation with retry logic for handling deadlocks
        /// </summary>
        /// <typeparam name="T">The return type of the operation</typeparam>
        /// <param name="operation">The database operation to execute</param>
        /// <param name="maxRetries">Maximum number of retry attempts</param>
        /// <param name="initialDelayMs">Initial delay between retries in milliseconds</param>
        /// <returns>The result of the operation</returns>
        /// <exception cref="Exception">Thrown when the operation fails after all retries</exception>
        public T ExecuteWithRetry<T>(Func<NpgsqlConnection, NpgsqlTransaction, T> operation, int maxRetries = 3, int initialDelayMs = 100)
        {
            int retryCount = 0;
            int delayMs = initialDelayMs;
            string retryId = Guid.NewGuid().ToString().Substring(0, 8);

            while (true)
            {
                try
                {
                    if (retryCount > 0)
                    {
                        LogTransaction($"Retry {retryId}: Attempt {retryCount} of {maxRetries}");
                    }

                    // Execute with transaction
                    return ExecuteWithTransaction(operation);
                }
                catch (NpgsqlException ex) when (
                    // Check for deadlock or serialization failure based on error message
                    (ex.Message.Contains("deadlock") ||
                     ex.Message.Contains("serialization failure") ||
                     ex.Message.Contains("40P01") ||
                     ex.Message.Contains("40001")) &&
                    retryCount < maxRetries)
                {
                    retryCount++;
                    LogTransaction($"Retry {retryId}: Deadlock or serialization failure detected. Retry {retryCount} of {maxRetries}. Waiting {delayMs}ms before retry.", true);

                    // Wait before retrying
                    System.Threading.Thread.Sleep(delayMs);

                    // Exponential backoff
                    delayMs *= 2;
                }
                catch (Exception ex)
                {
                    // For other exceptions, don't retry
                    LogTransaction($"Retry {retryId}: Operation failed with non-retryable exception: {ex.Message}", true);
                    throw;
                }

                // If we've reached max retries, throw an exception
                if (retryCount >= maxRetries)
                {
                    string errorMessage = $"Retry {retryId}: Maximum retry attempts ({maxRetries}) reached. Operation failed.";
                    LogTransaction(errorMessage, true);
                    throw new Exception(errorMessage);
                }
            }
        }

        /// <summary>
        /// Tests database permissions for sequences and tables
        /// </summary>
        /// <returns>Dictionary of permission test results</returns>
        public Dictionary<string, bool> TestDatabasePermissions()
        {
            var results = new Dictionary<string, bool>();

            try
            {
                return ExecuteWithoutTransaction<Dictionary<string, bool>>(
                    conn =>
                    {
                        // Test SELECT permission on estimateheader table
                        try
                        {
                            using (var cmd = new NpgsqlCommand("SELECT COUNT(*) FROM estimateheader", conn))
                            {
                                cmd.ExecuteScalar();
                                results["SELECT estimateheader"] = true;
                            }
                        }
                        catch (Exception ex)
                        {
                            LogTransaction($"Permission test failed - SELECT estimateheader: {ex.Message}", true);
                            results["SELECT estimateheader"] = false;
                        }

                        // Test INSERT permission on estimateheader table
                        try
                        {
                            using (var cmd = new NpgsqlCommand("BEGIN; INSERT INTO estimateheader (estimate_no, customer_name) VALUES ('TEST', 'TEST'); ROLLBACK;", conn))
                            {
                                cmd.ExecuteNonQuery();
                                results["INSERT estimateheader"] = true;
                            }
                        }
                        catch (Exception ex)
                        {
                            LogTransaction($"Permission test failed - INSERT estimateheader: {ex.Message}", true);
                            results["INSERT estimateheader"] = false;
                        }

                        // Test UPDATE permission on estimateheader table
                        try
                        {
                            using (var cmd = new NpgsqlCommand("BEGIN; UPDATE estimateheader SET customer_name = 'TEST' WHERE 1=0; ROLLBACK;", conn))
                            {
                                cmd.ExecuteNonQuery();
                                results["UPDATE estimateheader"] = true;
                            }
                        }
                        catch (Exception ex)
                        {
                            LogTransaction($"Permission test failed - UPDATE estimateheader: {ex.Message}", true);
                            results["UPDATE estimateheader"] = false;
                        }

                        // Test DELETE permission on estimateheader table
                        try
                        {
                            using (var cmd = new NpgsqlCommand("BEGIN; DELETE FROM estimateheader WHERE 1=0; ROLLBACK;", conn))
                            {
                                cmd.ExecuteNonQuery();
                                results["DELETE estimateheader"] = true;
                            }
                        }
                        catch (Exception ex)
                        {
                            LogTransaction($"Permission test failed - DELETE estimateheader: {ex.Message}", true);
                            results["DELETE estimateheader"] = false;
                        }

                        // Test sequence permissions
                        try
                        {
                            using (var cmd = new NpgsqlCommand("SELECT nextval('estimateheader_id_seq'::regclass)", conn))
                            {
                                cmd.ExecuteScalar();
                                results["estimateheader_id_seq"] = true;
                            }
                        }
                        catch (Exception ex)
                        {
                            LogTransaction($"Permission test failed - estimateheader_id_seq: {ex.Message}", true);
                            results["estimateheader_id_seq"] = false;
                        }

                        try
                        {
                            using (var cmd = new NpgsqlCommand("SELECT nextval('estimatedetails_id_seq'::regclass)", conn))
                            {
                                cmd.ExecuteScalar();
                                results["estimatedetails_id_seq"] = true;
                            }
                        }
                        catch (Exception ex)
                        {
                            LogTransaction($"Permission test failed - estimatedetails_id_seq: {ex.Message}", true);
                            results["estimatedetails_id_seq"] = false;
                        }

                        return results;
                    });
            }
            catch (Exception ex)
            {
                LogTransaction($"Error testing database permissions: {ex.Message}", true);
                results["ERROR"] = true;
                return results;
            }
        }

        /// <summary>
        /// Gets the next ID for a table without using a sequence directly
        /// This is a workaround for sequence permission issues
        /// </summary>
        /// <param name="tableName">The name of the table</param>
        /// <returns>The next available ID</returns>
        public int GetNextId(string tableName)
        {
            string operationId = Guid.NewGuid().ToString().Substring(0, 8);
            LogTransaction($"GetNextId {operationId}: Getting next ID for table {tableName}");

            try
            {
                return ExecuteWithoutTransaction<int>(
                    conn =>
                    {
                        // Get the maximum ID from the table and add 1
                        using (var cmd = new NpgsqlCommand($"SELECT COALESCE(MAX(id), 0) + 1 FROM {tableName}", conn))
                        {
                            var result = cmd.ExecuteScalar();
                            if (result != null && !Convert.IsDBNull(result))
                            {
                                int nextId = Convert.ToInt32(result);
                                LogTransaction($"GetNextId {operationId}: Next ID for {tableName} is {nextId}");
                                return nextId;
                            }
                            LogTransaction($"GetNextId {operationId}: No records found in {tableName}, returning ID 1");
                            return 1; // Default to 1 if no records exist
                        }
                    });
            }
            catch (Exception ex)
            {
                string errorMessage = $"GetNextId {operationId}: Error getting next ID for {tableName}: {ex.Message}";
                if (ex.InnerException != null)
                {
                    errorMessage += $", Inner error: {ex.InnerException.Message}";
                }
                LogTransaction(errorMessage, true);
                throw new Exception($"Failed to get next ID for {tableName}: {ex.Message}", ex);
            }
        }

        /// <summary>
        /// Gets the next ID for a table using the sequence directly
        /// </summary>
        /// <param name="sequenceName">The name of the sequence</param>
        /// <returns>The next available ID</returns>
        public int GetNextIdFromSequence(string sequenceName)
        {
            string operationId = Guid.NewGuid().ToString().Substring(0, 8);
            LogTransaction($"GetNextIdFromSequence {operationId}: Getting next ID from sequence {sequenceName}");

            try
            {
                return ExecuteWithoutTransaction<int>(
                    conn =>
                    {
                        // Get the next value from the sequence
                        using (var cmd = new NpgsqlCommand($"SELECT nextval('{sequenceName}'::regclass)", conn))
                        {
                            var result = cmd.ExecuteScalar();
                            if (result != null && !Convert.IsDBNull(result))
                            {
                                int nextId = Convert.ToInt32(result);
                                LogTransaction($"GetNextIdFromSequence {operationId}: Next ID from sequence {sequenceName} is {nextId}");
                                return nextId;
                            }

                            // This should never happen with a sequence
                            LogTransaction($"GetNextIdFromSequence {operationId}: Unexpected null result from sequence {sequenceName}", true);
                            throw new Exception($"Unexpected null result from sequence {sequenceName}");
                        }
                    });
            }
            catch (Exception ex)
            {
                string errorMessage = $"GetNextIdFromSequence {operationId}: Error getting next ID from sequence {sequenceName}: {ex.Message}";
                if (ex.InnerException != null)
                {
                    errorMessage += $", Inner error: {ex.InnerException.Message}";
                }
                LogTransaction(errorMessage, true);

                // Try the fallback method if sequence access fails
                LogTransaction($"GetNextIdFromSequence {operationId}: Falling back to MAX(id)+1 method for {sequenceName}", true);

                // Extract table name from sequence name (assuming standard naming convention: tablename_id_seq)
                string tableName = sequenceName.Replace("_id_seq", "");
                return GetNextId(tableName);
            }
        }

        /// <summary>
        /// Tests the database connection and executes a simple query
        /// </summary>
        /// <returns>True if successful, False otherwise</returns>
        public bool TestConnection()
        {
            string testId = Guid.NewGuid().ToString().Substring(0, 8);
            LogTransaction($"TestConnection {testId}: Testing database connection");

            try
            {
                return ExecuteWithoutTransaction<bool>(
                    conn =>
                    {
                        // Execute a simple query
                        using (var cmd = new NpgsqlCommand("SELECT 1", conn))
                        {
                            var result = cmd.ExecuteScalar();
                            bool success = (result != null && Convert.ToInt32(result) == 1);
                            LogTransaction($"TestConnection {testId}: Test query result: {result}, Success: {success}");
                            return success;
                        }
                    });
            }
            catch (Exception ex)
            {
                string errorMessage = $"TestConnection {testId}: Error testing connection: {ex.Message}";
                if (ex.InnerException != null)
                {
                    errorMessage += $", Inner error: {ex.InnerException.Message}";
                }
                LogTransaction(errorMessage, true);
                return false;
            }
        }

        /// <summary>
        /// Monitors the connection state and logs any changes
        /// </summary>
        /// <param name="connection">The connection to monitor</param>
        /// <returns>A disposable object that will stop monitoring when disposed</returns>
        public IDisposable MonitorConnectionState(NpgsqlConnection connection)
        {
            if (connection == null)
            {
                throw new ArgumentNullException(nameof(connection));
            }

            string monitorId = Guid.NewGuid().ToString().Substring(0, 8);
            LogTransaction($"ConnectionMonitor {monitorId}: Starting connection state monitoring");

            // Create a state object to track the connection
            var stateObject = new ConnectionStateMonitor(connection, monitorId, this);

            // Return the monitor which can be disposed to stop monitoring
            return stateObject;
        }

        /// <summary>
        /// Private class for monitoring connection state changes
        /// </summary>
        private class ConnectionStateMonitor : IDisposable
        {
            private readonly NpgsqlConnection _connection;
            private readonly string _monitorId;
            private readonly DatabaseTransactionService _service;
            private ConnectionState _lastState;

            public ConnectionStateMonitor(NpgsqlConnection connection, string monitorId, DatabaseTransactionService service)
            {
                _connection = connection;
                _monitorId = monitorId;
                _service = service;
                _lastState = connection.State;

                // Subscribe to state change events
                _connection.StateChange += Connection_StateChange;

                _service.LogTransaction($"ConnectionMonitor {_monitorId}: Initial connection state is {_lastState}");
            }

            private void Connection_StateChange(object sender, StateChangeEventArgs e)
            {
                _lastState = e.CurrentState;
                _service.LogTransaction($"ConnectionMonitor {_monitorId}: Connection state changed from {e.OriginalState} to {e.CurrentState}");
            }

            public void Dispose()
            {
                // Unsubscribe from events
                _connection.StateChange -= Connection_StateChange;
                _service.LogTransaction($"ConnectionMonitor {_monitorId}: Stopped connection state monitoring");
            }
        }

        /// <summary>
        /// Logs transaction information to a file
        /// </summary>
        /// <param name="message">The message to log</param>
        /// <param name="isError">Whether this is an error message</param>
        private void LogTransaction(string message, bool isError = false)
        {
            try
            {
                lock (LogLock)
                {
                    string logEntry = $"{DateTime.Now:yyyy-MM-dd HH:mm:ss.fff} - {(isError ? "ERROR" : "INFO")} - {message}";
                    Debug.WriteLine(logEntry);

                    // Create logs directory if it doesn't exist
                    string logDirectory = Path.Combine(AppDomain.CurrentDomain.BaseDirectory, "Logs");
                    if (!Directory.Exists(logDirectory))
                    {
                        Directory.CreateDirectory(logDirectory);
                    }

                    // Write to log file
                    string logPath = Path.Combine(logDirectory, TransactionLogFile);
                    File.AppendAllText(logPath, logEntry + Environment.NewLine);
                }
            }
            catch (Exception ex)
            {
                // Just write to debug output if file logging fails
                Debug.WriteLine($"Error writing to transaction log: {ex.Message}");
            }
        }
    }
}
