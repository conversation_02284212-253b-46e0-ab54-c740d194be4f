-- Database-Schema-Alignment.sql
-- Align database schema with corrected C# code expectations
-- This script ensures all column names match what the application expects

-- ============================================================================
-- COLUMN ALIGNMENT FIXES
-- ============================================================================

-- [FixRolesTable] --
-- Ensure roles table has updated_date instead of modified_date
DO $$
BEGIN
    -- Check if roles table has modified_date column
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'roles' 
        AND column_name = 'modified_date'
    ) AND NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'roles' 
        AND column_name = 'updated_date'
    ) THEN
        -- Rename modified_date to updated_date
        ALTER TABLE roles RENAME COLUMN modified_date TO updated_date;
        RAISE NOTICE 'Renamed modified_date to updated_date in roles table';
    END IF;
END $$;
-- [End] --

-- [FixRolePermissionsTable] --
-- Ensure role_permissions table has perm_id instead of permission_id
DO $$
BEGIN
    -- Check if role_permissions table has permission_id column
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'role_permissions' 
        AND column_name = 'permission_id'
    ) AND NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'role_permissions' 
        AND column_name = 'perm_id'
    ) THEN
        -- Rename permission_id to perm_id
        ALTER TABLE role_permissions RENAME COLUMN permission_id TO perm_id;
        RAISE NOTICE 'Renamed permission_id to perm_id in role_permissions table';
    END IF;
END $$;
-- [End] --

-- [FixUserPermissionsTable] --
-- Ensure user_permissions table has perm_id instead of user_permission_id or permission_id
DO $$
BEGIN
    -- Check if user_permissions table has user_permission_id column
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'user_permissions' 
        AND column_name = 'user_permission_id'
    ) AND NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'user_permissions' 
        AND column_name = 'perm_id'
    ) THEN
        -- Rename user_permission_id to perm_id
        ALTER TABLE user_permissions RENAME COLUMN user_permission_id TO perm_id;
        RAISE NOTICE 'Renamed user_permission_id to perm_id in user_permissions table';
    ELSIF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'user_permissions' 
        AND column_name = 'permission_id'
    ) AND NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'user_permissions' 
        AND column_name = 'perm_id'
    ) THEN
        -- Rename permission_id to perm_id
        ALTER TABLE user_permissions RENAME COLUMN permission_id TO perm_id;
        RAISE NOTICE 'Renamed permission_id to perm_id in user_permissions table';
    END IF;
END $$;
-- [End] --

-- [FixGlobalPermissionsTable] --
-- Ensure global_permissions table has perm_id instead of global_permission_id
DO $$
BEGIN
    -- Check if global_permissions table has global_permission_id column
    IF EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'global_permissions' 
        AND column_name = 'global_permission_id'
    ) AND NOT EXISTS (
        SELECT 1 FROM information_schema.columns 
        WHERE table_schema = 'public' 
        AND table_name = 'global_permissions' 
        AND column_name = 'perm_id'
    ) THEN
        -- Rename global_permission_id to perm_id
        ALTER TABLE global_permissions RENAME COLUMN global_permission_id TO perm_id;
        RAISE NOTICE 'Renamed global_permission_id to perm_id in global_permissions table';
    END IF;
END $$;
-- [End] --

-- ============================================================================
-- VERIFICATION QUERIES
-- ============================================================================

-- [VerifySchemaAlignment] --
-- Verify all tables have the correct column names
SELECT 
    'SCHEMA_VERIFICATION' as check_type,
    table_name,
    column_name,
    data_type,
    'CORRECT' as status
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name IN ('roles', 'role_permissions', 'user_permissions', 'global_permissions')
AND column_name IN ('perm_id', 'updated_date')
UNION ALL
SELECT 
    'SCHEMA_ISSUES' as check_type,
    table_name,
    column_name,
    data_type,
    'NEEDS_FIX' as status
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND table_name IN ('roles', 'role_permissions', 'user_permissions', 'global_permissions')
AND column_name IN ('permission_id', 'user_permission_id', 'global_permission_id', 'modified_date')
ORDER BY check_type, table_name, column_name;
-- [End] --

-- [TestCorrectedQueries] --
-- Test the queries that the application will use
-- These should all work without errors after the schema alignment

-- Test GetAllRoles query
SELECT 'GetAllRoles' as query_name, COUNT(*) as result_count
FROM (
    SELECT role_id, role_name, description, is_active, created_date, updated_date 
    FROM roles 
    WHERE is_active = true 
    ORDER BY role_name
) subquery;

-- Test GetRolePermissions query
SELECT 'GetRolePermissions' as query_name, COUNT(*) as result_count
FROM (
    SELECT perm_id, role_id, form_name, read_permission, new_permission, 
           edit_permission, delete_permission, print_permission, created_date
    FROM role_permissions 
    WHERE role_id = (SELECT role_id FROM roles WHERE is_active = true ORDER BY role_id LIMIT 1)
    ORDER BY form_name
) subquery;

-- Test GetUserPermissions query
SELECT 'GetUserPermissions' as query_name, COUNT(*) as result_count
FROM (
    SELECT perm_id, user_id, form_name, read_permission, new_permission,
           edit_permission, delete_permission, print_permission, created_date
    FROM user_permissions
    WHERE user_id = (SELECT user_id FROM users WHERE is_active = true ORDER BY user_id LIMIT 1)
    ORDER BY form_name
) subquery;

-- Test GetGlobalPermissions query
SELECT 'GetGlobalPermissions' as query_name, COUNT(*) as result_count
FROM (
    SELECT perm_id, user_id, can_create_users, can_edit_users,
           can_delete_users, can_print_users, created_date
    FROM global_permissions
    WHERE user_id = (SELECT user_id FROM users WHERE is_active = true ORDER BY user_id LIMIT 1)
) subquery;
-- [End] --

-- ============================================================================
-- FINAL STATUS CHECK
-- ============================================================================

-- [FinalStatusCheck] --
-- Comprehensive check of the permission system readiness
SELECT 
    'PERMISSION_SYSTEM_STATUS' as check_category,
    'Tables Exist' as check_name,
    CASE WHEN COUNT(*) = 5 THEN 'PASS' ELSE 'FAIL' END as status,
    COUNT(*) as actual_count,
    5 as expected_count
FROM information_schema.tables 
WHERE table_schema = 'public' 
AND table_name IN ('users', 'roles', 'role_permissions', 'user_permissions', 'global_permissions')

UNION ALL

SELECT 
    'PERMISSION_SYSTEM_STATUS' as check_category,
    'Correct Columns' as check_name,
    CASE WHEN COUNT(*) >= 2 THEN 'PASS' ELSE 'FAIL' END as status,
    COUNT(*) as actual_count,
    2 as expected_count
FROM information_schema.columns 
WHERE table_schema = 'public' 
AND ((table_name = 'roles' AND column_name = 'updated_date') 
     OR (table_name IN ('role_permissions', 'user_permissions', 'global_permissions') AND column_name = 'perm_id'))

UNION ALL

SELECT 
    'PERMISSION_SYSTEM_STATUS' as check_category,
    'Data Available' as check_name,
    CASE WHEN (SELECT COUNT(*) FROM users WHERE is_active = true) > 0 
         AND (SELECT COUNT(*) FROM roles WHERE is_active = true) > 0 
         THEN 'PASS' ELSE 'FAIL' END as status,
    (SELECT COUNT(*) FROM users WHERE is_active = true) + (SELECT COUNT(*) FROM roles WHERE is_active = true) as actual_count,
    2 as expected_count;
-- [End] --
